from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    AIModelTemplate, TrainingDataset, ModelTrainingJob, ModelVersion,
    ModelEvaluation, ModelOptimization, ModelDeployment, ModelMonitoring,
    ExperimentTracking, AIModelConfig
)

User = get_user_model()


class AIModelTemplateSerializer(serializers.ModelSerializer):
    """AI模型模板序列化器"""
    model_type_display = serializers.CharField(source='get_model_type_display', read_only=True)
    framework_display = serializers.CharField(source='get_framework_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = AIModelTemplate
        fields = [
            'id', 'name', 'model_type', 'model_type_display', 'framework',
            'framework_display', 'description', 'model_config', 'training_config',
            'preprocessing_config', 'input_schema', 'output_schema', 'data_requirements',
            'baseline_metrics', 'target_metrics', 'version', 'status', 'status_display',
            'is_default', 'usage_count', 'success_rate', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]


class TrainingDatasetSerializer(serializers.ModelSerializer):
    """训练数据集序列化器"""
    dataset_type_display = serializers.CharField(source='get_dataset_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    file_size_display = serializers.SerializerMethodField()
    
    class Meta:
        model = TrainingDataset
        fields = [
            'id', 'name', 'dataset_type', 'dataset_type_display', 'description',
            'data_source', 'data_path', 'data_format', 'total_samples', 'train_samples',
            'validation_samples', 'test_samples', 'data_quality_score', 'missing_rate',
            'duplicate_rate', 'class_distribution', 'feature_statistics', 'data_profile',
            'preprocessing_steps', 'preprocessing_config', 'status', 'status_display',
            'last_updated', 'file_size', 'file_size_display', 'checksum',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
    
    def get_file_size_display(self, obj):
        size = obj.file_size
        if size >= 1024**3:  # GB
            return f"{size / (1024**3):.1f} GB"
        elif size >= 1024**2:  # MB
            return f"{size / (1024**2):.1f} MB"
        elif size >= 1024:  # KB
            return f"{size / 1024:.1f} KB"
        else:
            return f"{size} B"


class ModelTrainingJobSerializer(serializers.ModelSerializer):
    """模型训练任务序列化器"""
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    model_template_name = serializers.CharField(source='model_template.name', read_only=True)
    dataset_name = serializers.CharField(source='dataset.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    duration = serializers.SerializerMethodField()
    
    class Meta:
        model = ModelTrainingJob
        fields = [
            'id', 'job_id', 'name', 'model_template', 'model_template_name',
            'dataset', 'dataset_name', 'training_config', 'hyperparameters',
            'compute_resources', 'estimated_duration', 'max_duration', 'status',
            'status_display', 'priority', 'priority_display', 'progress',
            'scheduled_time', 'start_time', 'end_time', 'training_duration',
            'duration', 'final_metrics', 'best_metrics', 'training_history',
            'model_path', 'model_size', 'model_checksum', 'error_message',
            'error_traceback', 'cpu_usage', 'memory_usage', 'gpu_usage',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
    
    def get_duration(self, obj):
        return obj.get_duration()


class ModelVersionSerializer(serializers.ModelSerializer):
    """模型版本序列化器"""
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    model_template_name = serializers.CharField(source='model_template.name', read_only=True)
    training_job_name = serializers.CharField(source='training_job.name', read_only=True)
    success_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = ModelVersion
        fields = [
            'id', 'model_template', 'model_template_name', 'training_job',
            'training_job_name', 'version_number', 'version_name', 'description',
            'model_path', 'model_size', 'model_format', 'performance_metrics',
            'benchmark_results', 'deployment_config', 'api_endpoint', 'status',
            'status_display', 'is_production', 'prediction_count', 'success_count',
            'error_count', 'success_rate', 'avg_response_time', 'avg_accuracy',
            'last_prediction_time', 'created_at', 'updated_at'
        ]
    
    def get_success_rate(self, obj):
        return obj.get_success_rate()


class ModelEvaluationSerializer(serializers.ModelSerializer):
    """模型评估序列化器"""
    evaluation_type_display = serializers.CharField(source='get_evaluation_type_display', read_only=True)
    model_version_name = serializers.CharField(source='model_version.__str__', read_only=True)
    test_dataset_name = serializers.CharField(source='test_dataset.name', read_only=True)
    evaluated_by_name = serializers.CharField(source='evaluated_by.get_full_name', read_only=True)
    
    class Meta:
        model = ModelEvaluation
        fields = [
            'id', 'model_version', 'model_version_name', 'evaluation_type',
            'evaluation_type_display', 'evaluation_config', 'test_dataset',
            'test_dataset_name', 'metrics', 'confusion_matrix', 'classification_report',
            'predictions', 'feature_importance', 'model_interpretation',
            'inference_time', 'memory_usage', 'cpu_usage', 'is_passed',
            'evaluation_notes', 'evaluated_by', 'evaluated_by_name', 'evaluated_at'
        ]


class ModelOptimizationSerializer(serializers.ModelSerializer):
    """模型优化序列化器"""
    optimization_type_display = serializers.CharField(source='get_optimization_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    model_template_name = serializers.CharField(source='model_template.name', read_only=True)
    base_version_name = serializers.CharField(source='base_version.__str__', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = ModelOptimization
        fields = [
            'id', 'optimization_id', 'model_template', 'model_template_name',
            'base_version', 'base_version_name', 'optimization_type',
            'optimization_type_display', 'description', 'optimization_config',
            'search_space', 'optimization_objective', 'status', 'status_display',
            'progress', 'start_time', 'end_time', 'optimization_duration',
            'best_config', 'best_metrics', 'optimization_history', 'baseline_metrics',
            'improvement_metrics', 'improvement_percentage', 'trials_count',
            'successful_trials', 'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]


class ModelDeploymentSerializer(serializers.ModelSerializer):
    """模型部署序列化器"""
    deployment_type_display = serializers.CharField(source='get_deployment_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    model_version_name = serializers.CharField(source='model_version.__str__', read_only=True)
    deployed_by_name = serializers.CharField(source='deployed_by.get_full_name', read_only=True)
    success_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = ModelDeployment
        fields = [
            'id', 'deployment_id', 'model_version', 'model_version_name',
            'deployment_type', 'deployment_type_display', 'environment',
            'deployment_config', 'resource_config', 'scaling_config',
            'service_name', 'api_endpoint', 'health_check_url', 'status',
            'status_display', 'is_active', 'deployed_at', 'last_health_check',
            'request_count', 'success_count', 'error_count', 'success_rate',
            'avg_response_time', 'cpu_usage', 'memory_usage', 'disk_usage',
            'deployed_by', 'deployed_by_name', 'created_at', 'updated_at'
        ]
    
    def get_success_rate(self, obj):
        return obj.get_success_rate()


class ModelMonitoringSerializer(serializers.ModelSerializer):
    """模型监控序列化器"""
    monitoring_type_display = serializers.CharField(source='get_monitoring_type_display', read_only=True)
    alert_level_display = serializers.CharField(source='get_alert_level_display', read_only=True)
    model_deployment_name = serializers.CharField(source='model_deployment.service_name', read_only=True)
    handled_by_name = serializers.CharField(source='handled_by.get_full_name', read_only=True)
    
    class Meta:
        model = ModelMonitoring
        fields = [
            'id', 'monitoring_id', 'model_deployment', 'model_deployment_name',
            'monitoring_type', 'monitoring_type_display', 'metric_name', 'metric_value',
            'threshold_value', 'baseline_value', 'monitoring_config', 'alert_config',
            'is_alert', 'alert_level', 'alert_level_display', 'alert_message',
            'monitored_at', 'is_handled', 'handled_by', 'handled_by_name',
            'handled_at', 'handling_notes'
        ]


class ExperimentTrackingSerializer(serializers.ModelSerializer):
    """实验跟踪序列化器"""
    experiment_type_display = serializers.CharField(source='get_experiment_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    model_template_name = serializers.CharField(source='model_template.name', read_only=True)
    parent_experiment_name = serializers.CharField(source='parent_experiment.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = ExperimentTracking
        fields = [
            'id', 'experiment_id', 'name', 'experiment_type', 'experiment_type_display',
            'description', 'experiment_config', 'parameters', 'model_template',
            'model_template_name', 'parent_experiment', 'parent_experiment_name',
            'status', 'status_display', 'start_time', 'end_time', 'duration',
            'metrics', 'artifacts', 'logs', 'baseline_metrics', 'improvement',
            'tags', 'notes', 'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]


class AIModelConfigSerializer(serializers.ModelSerializer):
    """AI模型配置序列化器"""
    config_type_display = serializers.CharField(source='get_config_type_display', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    model_template_name = serializers.CharField(source='model_template.name', read_only=True)
    
    class Meta:
        model = AIModelConfig
        fields = [
            'id', 'config_type', 'config_type_display', 'config_key',
            'config_value', 'description', 'is_system', 'is_active',
            'is_encrypted', 'model_template', 'model_template_name',
            'version', 'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]


# 简化版序列化器用于列表显示
class AIModelTemplateListSerializer(serializers.ModelSerializer):
    """AI模型模板列表序列化器"""
    model_type_display = serializers.CharField(source='get_model_type_display', read_only=True)
    framework_display = serializers.CharField(source='get_framework_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = AIModelTemplate
        fields = [
            'id', 'name', 'model_type_display', 'framework_display',
            'status_display', 'success_rate', 'usage_count', 'updated_at'
        ]


class ModelTrainingJobListSerializer(serializers.ModelSerializer):
    """模型训练任务列表序列化器"""
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    model_template_name = serializers.CharField(source='model_template.name', read_only=True)
    
    class Meta:
        model = ModelTrainingJob
        fields = [
            'id', 'job_id', 'name', 'model_template_name', 'status_display',
            'priority_display', 'progress', 'created_at'
        ]


class ModelVersionListSerializer(serializers.ModelSerializer):
    """模型版本列表序列化器"""
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    model_template_name = serializers.CharField(source='model_template.name', read_only=True)
    success_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = ModelVersion
        fields = [
            'id', 'version_number', 'model_template_name', 'status_display',
            'is_production', 'success_rate', 'prediction_count', 'created_at'
        ]
    
    def get_success_rate(self, obj):
        return obj.get_success_rate()


# 业务操作序列化器
class ModelTrainingRequestSerializer(serializers.Serializer):
    """模型训练请求序列化器"""
    template_id = serializers.IntegerField(help_text='模型模板ID')
    dataset_id = serializers.IntegerField(help_text='训练数据集ID')
    training_config = serializers.JSONField(
        required=False,
        help_text='训练配置'
    )
    hyperparameters = serializers.JSONField(
        required=False,
        help_text='超参数'
    )
    priority = serializers.ChoiceField(
        choices=ModelTrainingJob.PRIORITY_CHOICES,
        default=2,
        help_text='优先级'
    )
    scheduled_time = serializers.DateTimeField(
        required=False,
        help_text='计划开始时间'
    )


class ModelEvaluationRequestSerializer(serializers.Serializer):
    """模型评估请求序列化器"""
    model_version_id = serializers.IntegerField(help_text='模型版本ID')
    evaluation_type = serializers.ChoiceField(
        choices=ModelEvaluation.EVALUATION_TYPES,
        help_text='评估类型'
    )
    test_dataset_id = serializers.IntegerField(
        required=False,
        help_text='测试数据集ID'
    )
    evaluation_config = serializers.JSONField(
        required=False,
        help_text='评估配置'
    )


class ModelOptimizationRequestSerializer(serializers.Serializer):
    """模型优化请求序列化器"""
    model_template_id = serializers.IntegerField(help_text='模型模板ID')
    base_version_id = serializers.IntegerField(help_text='基础版本ID')
    optimization_type = serializers.ChoiceField(
        choices=ModelOptimization.OPTIMIZATION_TYPES,
        help_text='优化类型'
    )
    optimization_config = serializers.JSONField(
        required=False,
        help_text='优化配置'
    )
    search_space = serializers.JSONField(
        required=False,
        help_text='搜索空间'
    )


class ModelDeploymentRequestSerializer(serializers.Serializer):
    """模型部署请求序列化器"""
    model_version_id = serializers.IntegerField(help_text='模型版本ID')
    deployment_type = serializers.ChoiceField(
        choices=ModelDeployment.DEPLOYMENT_TYPES,
        help_text='部署类型'
    )
    environment = serializers.CharField(
        max_length=50,
        default='production',
        help_text='部署环境'
    )
    service_name = serializers.CharField(
        max_length=200,
        help_text='服务名称'
    )
    deployment_config = serializers.JSONField(
        required=False,
        help_text='部署配置'
    )

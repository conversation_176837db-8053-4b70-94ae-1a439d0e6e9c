from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q, Count, Avg, Sum, Max, Min
from django.shortcuts import get_object_or_404
from datetime import datetime, timedelta
import uuid
import json
import random
from .models import (
    AIModelTemplate, TrainingDataset, ModelTrainingJob, ModelVersion,
    ModelEvaluation, ModelOptimization, ModelDeployment, ModelMonitoring,
    ExperimentTracking, AIModelConfig
)
from .serializers import (
    AIModelTemplateSerializer, TrainingDatasetSerializer, ModelTrainingJobSerializer,
    ModelVersionSerializer, ModelEvaluationSerializer, ModelOptimizationSerializer,
    ModelDeploymentSerializer, ModelMonitoringSerializer, ExperimentTrackingSerializer,
    AIModelConfigSerializer, AIModelTemplateListSerializer, ModelTrainingJobListSerializer,
    ModelVersionListSerializer, ModelTrainingRequestSerializer, ModelEvaluationRequestSerializer,
    ModelOptimizationRequestSerializer, ModelDeploymentRequestSerializer
)
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class AIModelTemplateViewSet(viewsets.ModelViewSet):
    """AI模型模板ViewSet"""
    queryset = AIModelTemplate.objects.all()
    serializer_class = AIModelTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按模型类型过滤
        model_type = self.request.query_params.get('model_type')
        if model_type:
            queryset = queryset.filter(model_type=model_type)

        # 按框架过滤
        framework = self.request.query_params.get('framework')
        if framework:
            queryset = queryset.filter(framework=framework)

        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # 只显示启用的
        if self.request.query_params.get('active_only') == 'true':
            queryset = queryset.filter(status='active')

        # 搜索
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )

        return queryset.order_by('-created_at')

    def get_serializer_class(self):
        if self.action == 'list':
            return AIModelTemplateListSerializer
        return AIModelTemplateSerializer

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def create_training_job(self, request, pk=None):
        """创建训练任务"""
        template = self.get_object()
        serializer = ModelTrainingRequestSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data

        # 检查模板状态
        if template.status != 'active':
            return Response(
                {'error': 'AI模型模板未启用'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 获取数据集
        dataset = get_object_or_404(TrainingDataset, id=data['dataset_id'])
        if dataset.status != 'ready':
            return Response(
                {'error': '训练数据集未就绪'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 创建训练任务
        job = ModelTrainingJob.objects.create(
            job_id=f"JOB_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}",
            name=f"{template.name} 训练任务 - {timezone.now().strftime('%Y-%m-%d %H:%M')}",
            model_template=template,
            dataset=dataset,
            training_config=data.get('training_config', template.training_config),
            hyperparameters=data.get('hyperparameters', {}),
            priority=data.get('priority', 2),
            scheduled_time=data.get('scheduled_time'),
            created_by=request.user
        )

        # 更新模板使用统计
        template.usage_count += 1
        template.save()

        return Response({
            'message': '训练任务创建成功',
            'job_id': job.job_id,
            'job': ModelTrainingJobSerializer(job).data
        })

    @action(detail=True, methods=['get'])
    def performance_analysis(self, request, pk=None):
        """性能分析"""
        template = self.get_object()

        # 获取训练任务统计
        jobs = ModelTrainingJob.objects.filter(model_template=template)

        stats = {
            'template_info': {
                'name': template.name,
                'type': template.get_model_type_display(),
                'framework': template.get_framework_display(),
                'success_rate': template.success_rate
            },
            'training_stats': {
                'total_jobs': jobs.count(),
                'completed_jobs': jobs.filter(status='completed').count(),
                'failed_jobs': jobs.filter(status='failed').count(),
                'running_jobs': jobs.filter(status='training').count(),
                'usage_count': template.usage_count
            },
            'performance_metrics': self._calculate_template_performance(jobs),
            'recent_jobs': [
                {
                    'job_id': job.job_id,
                    'name': job.name,
                    'status': job.get_status_display(),
                    'progress': job.progress,
                    'created_at': job.created_at
                }
                for job in jobs.order_by('-created_at')[:5]
            ]
        }

        return Response(stats)

    def _calculate_template_performance(self, jobs):
        """计算模板性能指标"""
        completed_jobs = jobs.filter(status='completed', training_duration__isnull=False)

        if not completed_jobs.exists():
            return {
                'average_training_time': 0,
                'median_training_time': 0,
                'min_training_time': 0,
                'max_training_time': 0,
                'completion_rate': 0
            }

        durations = [job.training_duration for job in completed_jobs]
        durations.sort()

        return {
            'average_training_time': sum(durations) / len(durations),
            'median_training_time': durations[len(durations) // 2],
            'min_training_time': min(durations),
            'max_training_time': max(durations),
            'completion_rate': completed_jobs.count() / jobs.count() * 100
        }


class TrainingDatasetViewSet(viewsets.ModelViewSet):
    """训练数据集ViewSet"""
    queryset = TrainingDataset.objects.all()
    serializer_class = TrainingDatasetSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按数据类型过滤
        dataset_type = self.request.query_params.get('dataset_type')
        if dataset_type:
            queryset = queryset.filter(dataset_type=dataset_type)

        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # 只显示就绪的
        if self.request.query_params.get('ready_only') == 'true':
            queryset = queryset.filter(status='ready')

        return queryset.order_by('-created_at')

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def validate_data(self, request, pk=None):
        """验证数据"""
        dataset = self.get_object()

        if dataset.status != 'preparing':
            return Response(
                {'error': '数据集状态不允许验证'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 模拟数据验证过程
        dataset.status = 'processing'
        dataset.save()

        # 这里可以实现实际的数据验证逻辑
        # 模拟验证结果
        validation_result = {
            'is_valid': True,
            'quality_score': random.uniform(7.0, 9.5),
            'missing_rate': random.uniform(0.0, 0.1),
            'duplicate_rate': random.uniform(0.0, 0.05),
            'issues': []
        }

        if validation_result['is_valid']:
            dataset.status = 'ready'
            dataset.data_quality_score = validation_result['quality_score']
            dataset.missing_rate = validation_result['missing_rate']
            dataset.duplicate_rate = validation_result['duplicate_rate']
            dataset.last_updated = timezone.now()
            dataset.save()

            return Response({
                'message': '数据验证成功',
                'validation_result': validation_result
            })
        else:
            dataset.status = 'error'
            dataset.save()

            return Response({
                'message': '数据验证失败',
                'validation_result': validation_result
            }, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'])
    def data_profile(self, request, pk=None):
        """数据概况"""
        dataset = self.get_object()

        profile = {
            'basic_info': {
                'name': dataset.name,
                'type': dataset.get_dataset_type_display(),
                'total_samples': dataset.total_samples,
                'quality_score': dataset.data_quality_score
            },
            'data_distribution': {
                'train_ratio': dataset.train_samples / dataset.total_samples if dataset.total_samples > 0 else 0,
                'validation_ratio': dataset.validation_samples / dataset.total_samples if dataset.total_samples > 0 else 0,
                'test_ratio': dataset.test_samples / dataset.total_samples if dataset.total_samples > 0 else 0
            },
            'quality_metrics': {
                'missing_rate': dataset.missing_rate,
                'duplicate_rate': dataset.duplicate_rate,
                'completeness': 1 - dataset.missing_rate
            },
            'class_distribution': dataset.class_distribution,
            'feature_statistics': dataset.feature_statistics
        }

        return Response(profile)


class ModelTrainingJobViewSet(viewsets.ModelViewSet):
    """模型训练任务ViewSet"""
    queryset = ModelTrainingJob.objects.all()
    serializer_class = ModelTrainingJobSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # 按优先级过滤
        priority = self.request.query_params.get('priority')
        if priority:
            queryset = queryset.filter(priority=priority)

        # 按模板过滤
        template_id = self.request.query_params.get('template')
        if template_id:
            queryset = queryset.filter(model_template_id=template_id)

        # 我的训练任务
        if self.request.query_params.get('my_jobs') == 'true':
            queryset = queryset.filter(created_by=self.request.user)

        return queryset.order_by('-created_at')

    def get_serializer_class(self):
        if self.action == 'list':
            return ModelTrainingJobListSerializer
        return ModelTrainingJobSerializer

    def perform_create(self, serializer):
        job_id = f"JOB_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
        serializer.save(created_by=self.request.user, job_id=job_id)

    @action(detail=True, methods=['post'])
    def start_training(self, request, pk=None):
        """开始训练"""
        job = self.get_object()

        if job.status != 'pending':
            return Response(
                {'error': '训练任务状态不允许启动'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 启动训练
        job.status = 'training'
        job.start_time = timezone.now()
        job.progress = 5
        job.save()

        return Response({
            'message': '训练任务启动成功',
            'job_id': job.job_id,
            'status': job.get_status_display()
        })

    @action(detail=True, methods=['post'])
    def pause_training(self, request, pk=None):
        """暂停训练"""
        job = self.get_object()

        if job.status != 'training':
            return Response(
                {'error': '训练任务状态不允许暂停'},
                status=status.HTTP_400_BAD_REQUEST
            )

        job.status = 'paused'
        job.save()

        return Response({'message': '训练任务暂停成功'})

    @action(detail=True, methods=['post'])
    def cancel_training(self, request, pk=None):
        """取消训练"""
        job = self.get_object()

        if job.status in ['completed', 'failed', 'cancelled']:
            return Response(
                {'error': '训练任务已结束，无法取消'},
                status=status.HTTP_400_BAD_REQUEST
            )

        job.status = 'cancelled'
        job.end_time = timezone.now()
        job.save()

        return Response({'message': '训练任务取消成功'})

    @action(detail=True, methods=['get'])
    def training_progress(self, request, pk=None):
        """训练进度"""
        job = self.get_object()

        progress_info = {
            'job_id': job.job_id,
            'status': job.get_status_display(),
            'progress': job.progress,
            'start_time': job.start_time,
            'duration': job.get_duration(),
            'estimated_remaining': self._estimate_remaining_time(job),
            'current_metrics': job.training_history.get('current_metrics', {}) if job.training_history else {},
            'resource_usage': {
                'cpu': job.cpu_usage,
                'memory': job.memory_usage,
                'gpu': job.gpu_usage
            }
        }

        return Response(progress_info)

    def _estimate_remaining_time(self, job):
        """估算剩余时间"""
        if job.progress <= 0 or job.progress >= 100:
            return 0

        duration = job.get_duration()
        if duration <= 0:
            return job.estimated_duration * 60  # 转换为秒

        # 基于当前进度估算
        estimated_total = duration / (job.progress / 100)
        return max(0, estimated_total - duration)


class ModelVersionViewSet(viewsets.ModelViewSet):
    """模型版本ViewSet"""
    queryset = ModelVersion.objects.all()
    serializer_class = ModelVersionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # 按模板过滤
        template_id = self.request.query_params.get('template')
        if template_id:
            queryset = queryset.filter(model_template_id=template_id)

        # 只显示生产版本
        if self.request.query_params.get('production_only') == 'true':
            queryset = queryset.filter(is_production=True)

        return queryset.order_by('-created_at')

    def get_serializer_class(self):
        if self.action == 'list':
            return ModelVersionListSerializer
        return ModelVersionSerializer

    @action(detail=True, methods=['post'])
    def evaluate(self, request, pk=None):
        """评估模型"""
        version = self.get_object()
        serializer = ModelEvaluationRequestSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data

        # 创建评估记录
        evaluation = ModelEvaluation.objects.create(
            model_version=version,
            evaluation_type=data['evaluation_type'],
            evaluation_config=data.get('evaluation_config', {}),
            test_dataset_id=data.get('test_dataset_id'),
            evaluated_by=request.user
        )

        # 模拟评估过程
        evaluation_result = self._simulate_evaluation(version, data['evaluation_type'])

        # 更新评估结果
        evaluation.metrics = evaluation_result['metrics']
        evaluation.is_passed = evaluation_result['is_passed']
        evaluation.evaluation_notes = evaluation_result['notes']
        evaluation.save()

        return Response({
            'message': '模型评估完成',
            'evaluation_id': evaluation.id,
            'result': ModelEvaluationSerializer(evaluation).data
        })

    @action(detail=True, methods=['post'])
    def deploy(self, request, pk=None):
        """部署模型"""
        version = self.get_object()
        serializer = ModelDeploymentRequestSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data

        if version.status != 'ready':
            return Response(
                {'error': '模型版本状态不允许部署'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 创建部署记录
        deployment = ModelDeployment.objects.create(
            deployment_id=f"DEP_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}",
            model_version=version,
            deployment_type=data['deployment_type'],
            environment=data['environment'],
            service_name=data['service_name'],
            deployment_config=data.get('deployment_config', {}),
            deployed_by=request.user
        )

        # 模拟部署过程
        deployment.status = 'running'
        deployment.is_active = True
        deployment.deployed_at = timezone.now()
        deployment.api_endpoint = f"https://api.example.com/models/{deployment.deployment_id}"
        deployment.save()

        # 更新版本状态
        version.status = 'deployed'
        version.save()

        return Response({
            'message': '模型部署成功',
            'deployment_id': deployment.deployment_id,
            'deployment': ModelDeploymentSerializer(deployment).data
        })

    def _simulate_evaluation(self, version, evaluation_type):
        """模拟评估过程"""
        # 生成模拟评估结果
        base_accuracy = random.uniform(0.85, 0.98)

        metrics = {
            'accuracy': base_accuracy,
            'precision': random.uniform(0.80, 0.95),
            'recall': random.uniform(0.75, 0.92),
            'f1_score': random.uniform(0.78, 0.93)
        }

        if evaluation_type == 'benchmark':
            metrics.update({
                'inference_time': random.uniform(10, 100),  # ms
                'memory_usage': random.uniform(100, 500),   # MB
                'throughput': random.uniform(100, 1000)     # requests/sec
            })

        is_passed = base_accuracy >= 0.85
        notes = '评估通过，模型性能良好' if is_passed else '评估未通过，需要进一步优化'

        return {
            'metrics': metrics,
            'is_passed': is_passed,
            'notes': notes
        }


# 其他ViewSet的简化版本
class ModelEvaluationViewSet(viewsets.ReadOnlyModelViewSet):
    """模型评估ViewSet"""
    queryset = ModelEvaluation.objects.all()
    serializer_class = ModelEvaluationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按评估类型过滤
        evaluation_type = self.request.query_params.get('evaluation_type')
        if evaluation_type:
            queryset = queryset.filter(evaluation_type=evaluation_type)

        # 按模型版本过滤
        version_id = self.request.query_params.get('version')
        if version_id:
            queryset = queryset.filter(model_version_id=version_id)

        return queryset.order_by('-evaluated_at')


class ModelOptimizationViewSet(viewsets.ModelViewSet):
    """模型优化ViewSet"""
    queryset = ModelOptimization.objects.all()
    serializer_class = ModelOptimizationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按优化类型过滤
        optimization_type = self.request.query_params.get('optimization_type')
        if optimization_type:
            queryset = queryset.filter(optimization_type=optimization_type)

        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        return queryset.order_by('-created_at')

    def perform_create(self, serializer):
        optimization_id = f"OPT_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
        serializer.save(created_by=self.request.user, optimization_id=optimization_id)


class ModelDeploymentViewSet(viewsets.ModelViewSet):
    """模型部署ViewSet"""
    queryset = ModelDeployment.objects.all()
    serializer_class = ModelDeploymentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按部署类型过滤
        deployment_type = self.request.query_params.get('deployment_type')
        if deployment_type:
            queryset = queryset.filter(deployment_type=deployment_type)

        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # 只显示活跃的
        if self.request.query_params.get('active_only') == 'true':
            queryset = queryset.filter(is_active=True)

        return queryset.order_by('-created_at')


class ModelMonitoringViewSet(viewsets.ReadOnlyModelViewSet):
    """模型监控ViewSet"""
    queryset = ModelMonitoring.objects.all()
    serializer_class = ModelMonitoringSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按监控类型过滤
        monitoring_type = self.request.query_params.get('monitoring_type')
        if monitoring_type:
            queryset = queryset.filter(monitoring_type=monitoring_type)

        # 按告警级别过滤
        alert_level = self.request.query_params.get('alert_level')
        if alert_level:
            queryset = queryset.filter(alert_level=alert_level)

        # 只显示告警
        if self.request.query_params.get('alerts_only') == 'true':
            queryset = queryset.filter(is_alert=True)

        return queryset.order_by('-monitored_at')


class ExperimentTrackingViewSet(viewsets.ModelViewSet):
    """实验跟踪ViewSet"""
    queryset = ExperimentTracking.objects.all()
    serializer_class = ExperimentTrackingSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按实验类型过滤
        experiment_type = self.request.query_params.get('experiment_type')
        if experiment_type:
            queryset = queryset.filter(experiment_type=experiment_type)

        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        return queryset.order_by('-created_at')

    def perform_create(self, serializer):
        experiment_id = f"EXP_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
        serializer.save(created_by=self.request.user, experiment_id=experiment_id)


class AIModelConfigViewSet(viewsets.ModelViewSet):
    """AI模型配置ViewSet"""
    queryset = AIModelConfig.objects.all()
    serializer_class = AIModelConfigSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按配置类型过滤
        config_type = self.request.query_params.get('config_type')
        if config_type:
            queryset = queryset.filter(config_type=config_type)

        # 只显示启用的
        if self.request.query_params.get('active_only') == 'true':
            queryset = queryset.filter(is_active=True)

        return queryset.order_by('config_type', 'config_key')

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


# 业务操作视图
class AIModelTrainingViewSet(viewsets.ViewSet):
    """AI模型训练业务操作ViewSet"""
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """AI模型训练仪表板"""
        # 模板统计
        template_stats = AIModelTemplate.objects.aggregate(
            total=Count('id'),
            active=Count('id', filter=Q(status='active')),
            avg_success_rate=Avg('success_rate'),
            total_usage=Sum('usage_count')
        )

        # 训练任务统计
        job_stats = ModelTrainingJob.objects.aggregate(
            total=Count('id'),
            pending=Count('id', filter=Q(status='pending')),
            training=Count('id', filter=Q(status='training')),
            completed=Count('id', filter=Q(status='completed')),
            failed=Count('id', filter=Q(status='failed'))
        )

        # 模型版本统计
        version_stats = ModelVersion.objects.aggregate(
            total=Count('id'),
            production=Count('id', filter=Q(is_production=True)),
            deployed=Count('id', filter=Q(status='deployed'))
        )

        # 计算平均成功率
        versions_with_predictions = ModelVersion.objects.filter(prediction_count__gt=0)
        if versions_with_predictions.exists():
            total_predictions = sum(v.prediction_count for v in versions_with_predictions)
            total_successes = sum(v.success_count for v in versions_with_predictions)
            version_stats['avg_success_rate'] = (total_successes / total_predictions * 100) if total_predictions > 0 else 0
        else:
            version_stats['avg_success_rate'] = 0

        # 部署统计
        deployment_stats = ModelDeployment.objects.aggregate(
            total=Count('id'),
            running=Count('id', filter=Q(status='running')),
            active=Count('id', filter=Q(is_active=True))
        )

        # 计算平均成功率
        deployments_with_requests = ModelDeployment.objects.filter(request_count__gt=0)
        if deployments_with_requests.exists():
            total_requests = sum(d.request_count for d in deployments_with_requests)
            total_successes = sum(d.success_count for d in deployments_with_requests)
            deployment_stats['avg_success_rate'] = (total_successes / total_requests * 100) if total_requests > 0 else 0
        else:
            deployment_stats['avg_success_rate'] = 0

        # 最近7天的训练趋势
        seven_days_ago = timezone.now() - timedelta(days=7)
        daily_training = []
        for i in range(7):
            date = (timezone.now() - timedelta(days=i)).date()
            day_jobs = ModelTrainingJob.objects.filter(created_at__date=date)

            daily_training.insert(0, {
                'date': date.strftime('%Y-%m-%d'),
                'total': day_jobs.count(),
                'completed': day_jobs.filter(status='completed').count(),
                'failed': day_jobs.filter(status='failed').count()
            })

        # 热门模型类型
        popular_types = AIModelTemplate.objects.values('model_type').annotate(
            count=Count('id'),
            usage=Sum('usage_count')
        ).order_by('-usage')[:5]

        type_popularity = [
            {
                'type': item['model_type'],
                'type_display': dict(AIModelTemplate.MODEL_TYPES)[item['model_type']],
                'count': item['count'],
                'usage': item['usage'] or 0
            }
            for item in popular_types
        ]

        # 资源使用情况
        resource_usage = self._calculate_resource_usage()

        return Response({
            'template_stats': template_stats,
            'job_stats': job_stats,
            'version_stats': version_stats,
            'deployment_stats': deployment_stats,
            'daily_training': daily_training,
            'type_popularity': type_popularity,
            'resource_usage': resource_usage
        })

    @action(detail=False, methods=['post'])
    def auto_optimize(self, request):
        """自动优化"""
        model_version_id = request.data.get('model_version_id')
        optimization_types = request.data.get('optimization_types', ['hyperparameter_tuning'])

        if not model_version_id:
            return Response(
                {'error': '请提供模型版本ID'},
                status=status.HTTP_400_BAD_REQUEST
            )

        version = get_object_or_404(ModelVersion, id=model_version_id)

        # 创建优化任务
        optimizations = []
        for opt_type in optimization_types:
            optimization = ModelOptimization.objects.create(
                optimization_id=f"OPT_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}",
                model_template=version.model_template,
                base_version=version,
                optimization_type=opt_type,
                description=f"自动{dict(ModelOptimization.OPTIMIZATION_TYPES)[opt_type]}",
                created_by=request.user
            )
            optimizations.append(optimization)

        return Response({
            'message': f'成功创建 {len(optimizations)} 个优化任务',
            'optimizations': [
                {
                    'optimization_id': opt.optimization_id,
                    'type': opt.get_optimization_type_display()
                }
                for opt in optimizations
            ]
        })

    @action(detail=False, methods=['post'])
    def batch_deploy(self, request):
        """批量部署"""
        version_ids = request.data.get('version_ids', [])
        deployment_config = request.data.get('deployment_config', {})

        if not version_ids:
            return Response(
                {'error': '请提供模型版本ID列表'},
                status=status.HTTP_400_BAD_REQUEST
            )

        deployments = []
        for version_id in version_ids:
            try:
                version = ModelVersion.objects.get(id=version_id)

                deployment = ModelDeployment.objects.create(
                    deployment_id=f"DEP_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}",
                    model_version=version,
                    deployment_type=deployment_config.get('deployment_type', 'api'),
                    environment=deployment_config.get('environment', 'production'),
                    service_name=f"{version.model_template.name}_v{version.version_number}",
                    deployment_config=deployment_config,
                    deployed_by=request.user
                )

                # 模拟部署
                deployment.status = 'running'
                deployment.is_active = True
                deployment.deployed_at = timezone.now()
                deployment.save()

                deployments.append(deployment)

            except ModelVersion.DoesNotExist:
                continue

        return Response({
            'message': f'成功部署 {len(deployments)} 个模型版本',
            'deployments': [
                {
                    'deployment_id': dep.deployment_id,
                    'model_version': dep.model_version.__str__(),
                    'status': dep.get_status_display()
                }
                for dep in deployments
            ]
        })

    @action(detail=False, methods=['get'])
    def performance_report(self, request):
        """性能报告"""
        # 获取时间范围
        days = int(request.query_params.get('days', 30))
        start_date = timezone.now() - timedelta(days=days)

        # 训练性能
        training_performance = self._analyze_training_performance(start_date)

        # 模型性能
        model_performance = self._analyze_model_performance(start_date)

        # 部署性能
        deployment_performance = self._analyze_deployment_performance(start_date)

        # 资源利用率
        resource_utilization = self._analyze_resource_utilization(start_date)

        return Response({
            'report_period': f'{days} 天',
            'training_performance': training_performance,
            'model_performance': model_performance,
            'deployment_performance': deployment_performance,
            'resource_utilization': resource_utilization,
            'generated_at': timezone.now()
        })

    def _calculate_resource_usage(self):
        """计算资源使用情况"""
        # 模拟资源使用数据
        return {
            'cpu_usage': random.uniform(30, 80),
            'memory_usage': random.uniform(40, 85),
            'gpu_usage': random.uniform(20, 90),
            'storage_usage': random.uniform(50, 75),
            'active_jobs': ModelTrainingJob.objects.filter(status='training').count(),
            'active_deployments': ModelDeployment.objects.filter(is_active=True).count()
        }

    def _analyze_training_performance(self, start_date):
        """分析训练性能"""
        jobs = ModelTrainingJob.objects.filter(created_at__gte=start_date)
        completed_jobs = jobs.filter(status='completed')

        return {
            'total_jobs': jobs.count(),
            'completed_jobs': completed_jobs.count(),
            'success_rate': completed_jobs.count() / jobs.count() * 100 if jobs.count() > 0 else 0,
            'avg_training_time': completed_jobs.aggregate(avg_time=Avg('training_duration'))['avg_time'] or 0,
            'total_training_time': completed_jobs.aggregate(total_time=Sum('training_duration'))['total_time'] or 0
        }

    def _analyze_model_performance(self, start_date):
        """分析模型性能"""
        versions = ModelVersion.objects.filter(created_at__gte=start_date)

        return {
            'total_versions': versions.count(),
            'production_versions': versions.filter(is_production=True).count(),
            'avg_accuracy': versions.aggregate(avg_acc=Avg('avg_accuracy'))['avg_acc'] or 0,
            'total_predictions': versions.aggregate(total_pred=Sum('prediction_count'))['total_pred'] or 0
        }

    def _analyze_deployment_performance(self, start_date):
        """分析部署性能"""
        deployments = ModelDeployment.objects.filter(created_at__gte=start_date)

        return {
            'total_deployments': deployments.count(),
            'active_deployments': deployments.filter(is_active=True).count(),
            'avg_response_time': deployments.aggregate(avg_resp=Avg('avg_response_time'))['avg_resp'] or 0,
            'total_requests': deployments.aggregate(total_req=Sum('request_count'))['total_req'] or 0
        }

    def _analyze_resource_utilization(self, start_date):
        """分析资源利用率"""
        # 模拟资源利用率分析
        return {
            'avg_cpu_utilization': random.uniform(40, 70),
            'avg_memory_utilization': random.uniform(50, 80),
            'avg_gpu_utilization': random.uniform(30, 85),
            'peak_usage_time': '14:00-16:00',
            'efficiency_score': random.uniform(0.7, 0.9)
        }

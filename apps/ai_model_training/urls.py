from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views

router = DefaultRouter()
router.register(r'templates', views.AIModelTemplateViewSet, basename='ai-model-template')
router.register(r'datasets', views.TrainingDatasetViewSet, basename='training-dataset')
router.register(r'jobs', views.ModelTrainingJobViewSet, basename='model-training-job')
router.register(r'versions', views.ModelVersionViewSet, basename='model-version')
router.register(r'evaluations', views.ModelEvaluationViewSet, basename='model-evaluation')
router.register(r'optimizations', views.ModelOptimizationViewSet, basename='model-optimization')
router.register(r'deployments', views.ModelDeploymentViewSet, basename='model-deployment')
router.register(r'monitoring', views.ModelMonitoringViewSet, basename='model-monitoring')
router.register(r'experiments', views.ExperimentTrackingViewSet, basename='experiment-tracking')
router.register(r'configs', views.AIModelConfigViewSet, basename='ai-model-config')
router.register(r'training', views.AIModelTrainingViewSet, basename='ai-model-training')

app_name = 'ai_model_training'

urlpatterns = [
    path('', include(router.urls)),
]

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.db.models import Count, Avg, Sum
import uuid
from .models import (
    AIModelTemplate, TrainingDataset, ModelTrainingJob, ModelVersion,
    ModelEvaluation, ModelOptimization, ModelDeployment, ModelMonitoring,
    ExperimentTracking, AIModelConfig
)


@admin.register(AIModelTemplate)
class AIModelTemplateAdmin(admin.ModelAdmin):
    """AI模型模板管理"""
    list_display = [
        'name', 'model_type_display', 'framework_display', 'status_display',
        'success_rate_display', 'usage_count', 'version', 'updated_at'
    ]
    list_filter = ['model_type', 'framework', 'status', 'is_default', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['usage_count', 'created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'model_type', 'framework', 'description', 'status')
        }),
        ('模型配置', {
            'fields': ('model_config', 'training_config', 'preprocessing_config')
        }),
        ('数据要求', {
            'fields': ('input_schema', 'output_schema', 'data_requirements')
        }),
        ('性能基准', {
            'fields': ('baseline_metrics', 'target_metrics')
        }),
        ('版本管理', {
            'fields': ('version', 'is_default')
        }),
        ('使用统计', {
            'fields': ('usage_count', 'success_rate'),
            'classes': ('collapse',)
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['activate_templates', 'deactivate_templates', 'set_as_default']

    def model_type_display(self, obj):
        """模型类型显示"""
        type_colors = {
            'nlp': '#1890ff',
            'classification': '#52c41a',
            'regression': '#faad14',
            'clustering': '#722ed1',
            'recommendation': '#13c2c2',
            'forecasting': '#fa8c16',
            'anomaly_detection': '#f5222d',
            'computer_vision': '#eb2f96',
            'time_series': '#8c8c8c',
            'deep_learning': '#2f54eb',
        }
        color = type_colors.get(obj.model_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_model_type_display()
        )
    model_type_display.short_description = '模型类型'

    def framework_display(self, obj):
        """框架显示"""
        framework_colors = {
            'tensorflow': '#ff6f00',
            'pytorch': '#ee4c2c',
            'scikit_learn': '#f7931e',
            'xgboost': '#1f77b4',
            'lightgbm': '#2ca02c',
            'keras': '#d62728',
            'transformers': '#9467bd',
            'spacy': '#8c564b',
            'opencv': '#e377c2',
            'custom': '#7f7f7f',
        }
        color = framework_colors.get(obj.framework, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_framework_display()
        )
    framework_display.short_description = '框架'

    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'draft': '#d9d9d9',
            'active': '#52c41a',
            'deprecated': '#faad14',
            'archived': '#8c8c8c',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'

    def success_rate_display(self, obj):
        """成功率显示"""
        rate = obj.success_rate * 100
        if rate >= 95:
            color = '#52c41a'
        elif rate >= 90:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, f"{rate:.1f}%"
        )
    success_rate_display.short_description = '成功率'

    def activate_templates(self, request, queryset):
        """激活模板"""
        updated = queryset.update(status='active')
        self.message_user(request, f"成功激活 {updated} 个模板")
    activate_templates.short_description = "激活选中的模板"

    def deactivate_templates(self, request, queryset):
        """停用模板"""
        updated = queryset.update(status='deprecated')
        self.message_user(request, f"成功停用 {updated} 个模板")
    deactivate_templates.short_description = "停用选中的模板"

    def set_as_default(self, request, queryset):
        """设为默认"""
        for template in queryset:
            # 先取消同类型的其他默认模板
            AIModelTemplate.objects.filter(
                model_type=template.model_type,
                is_default=True
            ).update(is_default=False)

            # 设置当前模板为默认
            template.is_default = True
            template.save()

        self.message_user(request, f"成功设置 {queryset.count()} 个默认模板")
    set_as_default.short_description = "设为默认模板"

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(TrainingDataset)
class TrainingDatasetAdmin(admin.ModelAdmin):
    """训练数据集管理"""
    list_display = [
        'name', 'dataset_type_display', 'status_display', 'total_samples',
        'data_quality_score_display', 'file_size_display', 'updated_at'
    ]
    list_filter = ['dataset_type', 'status', 'created_at']
    search_fields = ['name', 'description', 'data_source']
    readonly_fields = ['file_size', 'checksum', 'last_updated', 'created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'dataset_type', 'description', 'status')
        }),
        ('数据信息', {
            'fields': ('data_source', 'data_path', 'data_format')
        }),
        ('数据统计', {
            'fields': ('total_samples', 'train_samples', 'validation_samples', 'test_samples')
        }),
        ('数据质量', {
            'fields': ('data_quality_score', 'missing_rate', 'duplicate_rate')
        }),
        ('数据分布', {
            'fields': ('class_distribution', 'feature_statistics', 'data_profile'),
            'classes': ('collapse',)
        }),
        ('预处理信息', {
            'fields': ('preprocessing_steps', 'preprocessing_config'),
            'classes': ('collapse',)
        }),
        ('文件信息', {
            'fields': ('file_size', 'checksum', 'last_updated'),
            'classes': ('collapse',)
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def dataset_type_display(self, obj):
        """数据类型显示"""
        type_colors = {
            'text': '#1890ff',
            'image': '#52c41a',
            'audio': '#faad14',
            'video': '#722ed1',
            'tabular': '#13c2c2',
            'time_series': '#fa8c16',
            'graph': '#f5222d',
            'multimodal': '#eb2f96',
        }
        color = type_colors.get(obj.dataset_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_dataset_type_display()
        )
    dataset_type_display.short_description = '数据类型'

    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'preparing': '#faad14',
            'ready': '#52c41a',
            'processing': '#1890ff',
            'error': '#f5222d',
            'archived': '#8c8c8c',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'

    def data_quality_score_display(self, obj):
        """数据质量评分显示"""
        score = obj.data_quality_score
        if score >= 8:
            color = '#52c41a'
        elif score >= 6:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, f"{score:.1f}/10"
        )
    data_quality_score_display.short_description = '质量评分'

    def file_size_display(self, obj):
        """文件大小显示"""
        size = obj.file_size
        if size >= 1024**3:  # GB
            return f"{size / (1024**3):.1f} GB"
        elif size >= 1024**2:  # MB
            return f"{size / (1024**2):.1f} MB"
        elif size >= 1024:  # KB
            return f"{size / 1024:.1f} KB"
        else:
            return f"{size} B"
    file_size_display.short_description = '文件大小'

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(ModelTrainingJob)
class ModelTrainingJobAdmin(admin.ModelAdmin):
    """模型训练任务管理"""
    list_display = [
        'job_id', 'name', 'model_template_name', 'status_display', 'priority_display',
        'progress_display', 'training_duration_display', 'created_at'
    ]
    list_filter = ['status', 'priority', 'model_template__model_type', 'created_at']
    search_fields = ['job_id', 'name', 'model_template__name']
    readonly_fields = ['job_id', 'start_time', 'end_time', 'training_duration', 'created_at', 'updated_at']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('基本信息', {
            'fields': ('job_id', 'name', 'model_template', 'dataset')
        }),
        ('训练配置', {
            'fields': ('training_config', 'hyperparameters')
        }),
        ('资源配置', {
            'fields': ('compute_resources', 'estimated_duration', 'max_duration')
        }),
        ('状态信息', {
            'fields': ('status', 'priority', 'progress')
        }),
        ('时间信息', {
            'fields': ('scheduled_time', 'start_time', 'end_time', 'training_duration'),
            'classes': ('collapse',)
        }),
        ('训练结果', {
            'fields': ('final_metrics', 'best_metrics', 'training_history'),
            'classes': ('collapse',)
        }),
        ('模型输出', {
            'fields': ('model_path', 'model_size', 'model_checksum'),
            'classes': ('collapse',)
        }),
        ('错误信息', {
            'fields': ('error_message', 'error_traceback'),
            'classes': ('collapse',)
        }),
        ('资源使用', {
            'fields': ('cpu_usage', 'memory_usage', 'gpu_usage'),
            'classes': ('collapse',)
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['start_jobs', 'cancel_jobs']

    def model_template_name(self, obj):
        """模型模板名称"""
        return obj.model_template.name
    model_template_name.short_description = '模型模板'

    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'pending': '#d9d9d9',
            'preparing': '#1890ff',
            'training': '#faad14',
            'validating': '#13c2c2',
            'completed': '#52c41a',
            'failed': '#f5222d',
            'cancelled': '#8c8c8c',
            'paused': '#fa8c16',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'

    def priority_display(self, obj):
        """优先级显示"""
        priority_colors = {
            1: '#d9d9d9',
            2: '#1890ff',
            3: '#faad14',
            4: '#f5222d',
        }
        color = priority_colors.get(obj.priority, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_priority_display()
        )
    priority_display.short_description = '优先级'

    def progress_display(self, obj):
        """进度显示"""
        progress = obj.progress
        if progress >= 100:
            color = '#52c41a'
        elif progress >= 50:
            color = '#faad14'
        else:
            color = '#1890ff'

        return format_html(
            '<div style="width: 100px; background: #f0f0f0; border-radius: 3px;">'
            '<div style="width: {}%; background: {}; height: 20px; border-radius: 3px; text-align: center; color: white; font-size: 12px; line-height: 20px;">'
            '{}%</div></div>',
            progress, color, progress
        )
    progress_display.short_description = '进度'

    def training_duration_display(self, obj):
        """训练时长显示"""
        duration = obj.get_duration()
        if duration >= 1:
            return f"{duration:.1f}h"
        else:
            return f"{duration*60:.0f}m"
    training_duration_display.short_description = '训练时长'

    def start_jobs(self, request, queryset):
        """启动任务"""
        updated = 0
        for job in queryset.filter(status='pending'):
            job.status = 'training'
            job.start_time = timezone.now()
            job.save()
            updated += 1

        self.message_user(request, f"成功启动 {updated} 个训练任务")
    start_jobs.short_description = "启动选中的训练任务"

    def cancel_jobs(self, request, queryset):
        """取消任务"""
        updated = queryset.filter(status__in=['pending', 'training', 'preparing']).update(
            status='cancelled',
            end_time=timezone.now()
        )
        self.message_user(request, f"成功取消 {updated} 个训练任务")
    cancel_jobs.short_description = "取消选中的训练任务"

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
            if not obj.job_id:
                obj.job_id = f"JOB_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
        super().save_model(request, obj, form, change)


@admin.register(ModelVersion)
class ModelVersionAdmin(admin.ModelAdmin):
    """模型版本管理"""
    list_display = [
        'version_number', 'model_template_name', 'status_display', 'is_production',
        'success_rate_display', 'prediction_count', 'created_at'
    ]
    list_filter = ['status', 'is_production', 'model_template__model_type', 'created_at']
    search_fields = ['version_number', 'version_name', 'model_template__name']
    readonly_fields = ['prediction_count', 'success_count', 'error_count', 'last_prediction_time', 'created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('model_template', 'training_job', 'version_number', 'version_name', 'description')
        }),
        ('模型信息', {
            'fields': ('model_path', 'model_size', 'model_format')
        }),
        ('性能指标', {
            'fields': ('performance_metrics', 'benchmark_results')
        }),
        ('部署信息', {
            'fields': ('deployment_config', 'api_endpoint')
        }),
        ('状态管理', {
            'fields': ('status', 'is_production')
        }),
        ('使用统计', {
            'fields': ('prediction_count', 'success_count', 'error_count', 'last_prediction_time'),
            'classes': ('collapse',)
        }),
        ('监控指标', {
            'fields': ('avg_response_time', 'avg_accuracy'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['deploy_to_production', 'deprecate_versions']

    def model_template_name(self, obj):
        """模型模板名称"""
        return obj.model_template.name
    model_template_name.short_description = '模型模板'

    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'training': '#faad14',
            'ready': '#52c41a',
            'deployed': '#1890ff',
            'deprecated': '#8c8c8c',
            'archived': '#d9d9d9',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'

    def success_rate_display(self, obj):
        """成功率显示"""
        rate = obj.get_success_rate()
        if rate >= 95:
            color = '#52c41a'
        elif rate >= 90:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, f"{rate:.1f}%"
        )
    success_rate_display.short_description = '成功率'

    def deploy_to_production(self, request, queryset):
        """部署到生产环境"""
        # 先取消其他生产版本
        for version in queryset:
            ModelVersion.objects.filter(
                model_template=version.model_template,
                is_production=True
            ).update(is_production=False)

            version.is_production = True
            version.status = 'deployed'
            version.save()

        self.message_user(request, f"成功部署 {queryset.count()} 个版本到生产环境")
    deploy_to_production.short_description = "部署到生产环境"

    def deprecate_versions(self, request, queryset):
        """弃用版本"""
        updated = queryset.update(status='deprecated', is_production=False)
        self.message_user(request, f"成功弃用 {updated} 个版本")
    deprecate_versions.short_description = "弃用选中的版本"


# 注册其他模型的简化admin
@admin.register(ModelEvaluation)
class ModelEvaluationAdmin(admin.ModelAdmin):
    """模型评估管理"""
    list_display = ['model_version', 'evaluation_type', 'is_passed', 'evaluated_at']
    list_filter = ['evaluation_type', 'is_passed', 'evaluated_at']
    readonly_fields = ['evaluated_at']


@admin.register(ModelOptimization)
class ModelOptimizationAdmin(admin.ModelAdmin):
    """模型优化管理"""
    list_display = ['optimization_id', 'model_template', 'optimization_type', 'status', 'improvement_percentage', 'created_at']
    list_filter = ['optimization_type', 'status', 'created_at']
    readonly_fields = ['optimization_id', 'start_time', 'end_time', 'optimization_duration']

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
            if not obj.optimization_id:
                obj.optimization_id = f"OPT_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
        super().save_model(request, obj, form, change)


@admin.register(ModelDeployment)
class ModelDeploymentAdmin(admin.ModelAdmin):
    """模型部署管理"""
    list_display = ['deployment_id', 'model_version', 'deployment_type', 'status', 'success_rate_display', 'deployed_at']
    list_filter = ['deployment_type', 'status', 'environment', 'is_active']
    readonly_fields = ['deployment_id', 'deployed_at', 'last_health_check']

    def success_rate_display(self, obj):
        """成功率显示"""
        rate = obj.get_success_rate()
        if rate >= 95:
            color = '#52c41a'
        elif rate >= 90:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, f"{rate:.1f}%"
        )
    success_rate_display.short_description = '成功率'

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.deployed_by = request.user
            if not obj.deployment_id:
                obj.deployment_id = f"DEP_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
        super().save_model(request, obj, form, change)


@admin.register(ModelMonitoring)
class ModelMonitoringAdmin(admin.ModelAdmin):
    """模型监控管理"""
    list_display = ['monitoring_id', 'model_deployment', 'monitoring_type', 'metric_name', 'is_alert', 'alert_level', 'monitored_at']
    list_filter = ['monitoring_type', 'is_alert', 'alert_level', 'is_handled', 'monitored_at']
    readonly_fields = ['monitoring_id', 'monitored_at']

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            if not obj.monitoring_id:
                obj.monitoring_id = f"MON_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
        super().save_model(request, obj, form, change)


@admin.register(ExperimentTracking)
class ExperimentTrackingAdmin(admin.ModelAdmin):
    """实验跟踪管理"""
    list_display = ['experiment_id', 'name', 'experiment_type', 'status', 'duration', 'created_at']
    list_filter = ['experiment_type', 'status', 'created_at']
    readonly_fields = ['experiment_id', 'start_time', 'end_time', 'duration']

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
            if not obj.experiment_id:
                obj.experiment_id = f"EXP_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
        super().save_model(request, obj, form, change)


@admin.register(AIModelConfig)
class AIModelConfigAdmin(admin.ModelAdmin):
    """AI模型配置管理"""
    list_display = ['config_key', 'config_type', 'model_template', 'is_system', 'is_active', 'version', 'updated_at']
    list_filter = ['config_type', 'is_system', 'is_active']

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


# 自定义管理页面标题
admin.site.site_header = '代理记账管理系统'
admin.site.site_title = 'AI模型训练和优化'
admin.site.index_title = 'AI模型训练和优化'

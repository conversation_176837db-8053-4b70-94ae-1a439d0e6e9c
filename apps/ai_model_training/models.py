from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import json

User = get_user_model()


class AIModelTemplate(models.Model):
    """AI模型模板"""
    MODEL_TYPES = [
        ('nlp', 'NLP模型'),
        ('classification', '分类模型'),
        ('regression', '回归模型'),
        ('clustering', '聚类模型'),
        ('recommendation', '推荐模型'),
        ('forecasting', '预测模型'),
        ('anomaly_detection', '异常检测模型'),
        ('computer_vision', '计算机视觉模型'),
        ('time_series', '时间序列模型'),
        ('deep_learning', '深度学习模型'),
    ]

    FRAMEWORKS = [
        ('tensorflow', 'TensorFlow'),
        ('pytorch', 'PyTorch'),
        ('scikit_learn', 'Scikit-Learn'),
        ('xgboost', 'XGBoost'),
        ('lightgbm', 'LightGBM'),
        ('keras', 'Keras'),
        ('transformers', 'Transformers'),
        ('spacy', 'spaCy'),
        ('opencv', 'OpenCV'),
        ('custom', '自定义框架'),
    ]

    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('active', '启用'),
        ('deprecated', '已弃用'),
        ('archived', '已归档'),
    ]

    name = models.CharField('模型名称', max_length=200)
    model_type = models.CharField('模型类型', max_length=20, choices=MODEL_TYPES)
    framework = models.CharField('框架', max_length=20, choices=FRAMEWORKS)
    description = models.TextField('模型描述', blank=True)

    # 模型配置
    model_config = models.JSONField('模型配置', default=dict, help_text='模型架构和参数配置')
    training_config = models.JSONField('训练配置', default=dict, help_text='训练参数和超参数')
    preprocessing_config = models.JSONField('预处理配置', default=dict, help_text='数据预处理配置')

    # 数据要求
    input_schema = models.JSONField('输入数据结构', default=dict)
    output_schema = models.JSONField('输出数据结构', default=dict)
    data_requirements = models.JSONField('数据要求', default=dict)

    # 性能基准
    baseline_metrics = models.JSONField('基准指标', default=dict)
    target_metrics = models.JSONField('目标指标', default=dict)

    # 版本管理
    version = models.CharField('版本号', max_length=20, default='1.0')
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='draft')
    is_default = models.BooleanField('默认模板', default=False)

    # 使用统计
    usage_count = models.IntegerField('使用次数', default=0)
    success_rate = models.FloatField('成功率', default=0.0, validators=[MinValueValidator(0), MaxValueValidator(1)])

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_ai_model_templates', verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = 'AI模型模板'
        verbose_name_plural = 'AI模型模板'
        ordering = ['-created_at']

    def __str__(self):
        return self.name


class TrainingDataset(models.Model):
    """训练数据集"""
    DATASET_TYPES = [
        ('text', '文本数据'),
        ('image', '图像数据'),
        ('audio', '音频数据'),
        ('video', '视频数据'),
        ('tabular', '表格数据'),
        ('time_series', '时间序列数据'),
        ('graph', '图数据'),
        ('multimodal', '多模态数据'),
    ]

    STATUS_CHOICES = [
        ('preparing', '准备中'),
        ('ready', '就绪'),
        ('processing', '处理中'),
        ('error', '错误'),
        ('archived', '已归档'),
    ]

    name = models.CharField('数据集名称', max_length=200)
    dataset_type = models.CharField('数据类型', max_length=20, choices=DATASET_TYPES)
    description = models.TextField('数据集描述', blank=True)

    # 数据信息
    data_source = models.CharField('数据来源', max_length=200)
    data_path = models.CharField('数据路径', max_length=500)
    data_format = models.CharField('数据格式', max_length=50)

    # 数据统计
    total_samples = models.IntegerField('总样本数', default=0)
    train_samples = models.IntegerField('训练样本数', default=0)
    validation_samples = models.IntegerField('验证样本数', default=0)
    test_samples = models.IntegerField('测试样本数', default=0)

    # 数据质量
    data_quality_score = models.FloatField('数据质量评分', default=0.0, validators=[MinValueValidator(0), MaxValueValidator(10)])
    missing_rate = models.FloatField('缺失率', default=0.0, validators=[MinValueValidator(0), MaxValueValidator(1)])
    duplicate_rate = models.FloatField('重复率', default=0.0, validators=[MinValueValidator(0), MaxValueValidator(1)])

    # 数据分布
    class_distribution = models.JSONField('类别分布', default=dict)
    feature_statistics = models.JSONField('特征统计', default=dict)
    data_profile = models.JSONField('数据概况', default=dict)

    # 预处理信息
    preprocessing_steps = models.JSONField('预处理步骤', default=list)
    preprocessing_config = models.JSONField('预处理配置', default=dict)

    # 状态管理
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='preparing')
    last_updated = models.DateTimeField('最后更新时间', null=True, blank=True)

    # 文件信息
    file_size = models.BigIntegerField('文件大小(字节)', default=0)
    checksum = models.CharField('文件校验和', max_length=64, blank=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_training_datasets', verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '训练数据集'
        verbose_name_plural = '训练数据集'
        ordering = ['-created_at']

    def __str__(self):
        return self.name


class ModelTrainingJob(models.Model):
    """模型训练任务"""
    STATUS_CHOICES = [
        ('pending', '待开始'),
        ('preparing', '准备中'),
        ('training', '训练中'),
        ('validating', '验证中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
        ('paused', '已暂停'),
    ]

    PRIORITY_CHOICES = [
        (1, '低'),
        (2, '中'),
        (3, '高'),
        (4, '紧急'),
    ]

    job_id = models.CharField('任务ID', max_length=100, unique=True)
    name = models.CharField('任务名称', max_length=200)
    model_template = models.ForeignKey(AIModelTemplate, on_delete=models.CASCADE, verbose_name='模型模板')
    dataset = models.ForeignKey(TrainingDataset, on_delete=models.CASCADE, verbose_name='训练数据集')

    # 训练配置
    training_config = models.JSONField('训练配置', default=dict)
    hyperparameters = models.JSONField('超参数', default=dict)

    # 资源配置
    compute_resources = models.JSONField('计算资源', default=dict)
    estimated_duration = models.IntegerField('预估时长(分钟)', default=60)
    max_duration = models.IntegerField('最大时长(分钟)', default=1440)  # 24小时

    # 状态信息
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    priority = models.IntegerField('优先级', choices=PRIORITY_CHOICES, default=2)
    progress = models.IntegerField('进度百分比', default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])

    # 时间信息
    scheduled_time = models.DateTimeField('计划开始时间', null=True, blank=True)
    start_time = models.DateTimeField('开始时间', null=True, blank=True)
    end_time = models.DateTimeField('结束时间', null=True, blank=True)
    training_duration = models.FloatField('训练时长(小时)', null=True, blank=True)

    # 训练结果
    final_metrics = models.JSONField('最终指标', default=dict)
    best_metrics = models.JSONField('最佳指标', default=dict)
    training_history = models.JSONField('训练历史', default=dict)

    # 模型输出
    model_path = models.CharField('模型路径', max_length=500, blank=True)
    model_size = models.BigIntegerField('模型大小(字节)', default=0)
    model_checksum = models.CharField('模型校验和', max_length=64, blank=True)

    # 错误信息
    error_message = models.TextField('错误信息', blank=True)
    error_traceback = models.TextField('错误堆栈', blank=True)

    # 资源使用
    cpu_usage = models.JSONField('CPU使用情况', default=dict)
    memory_usage = models.JSONField('内存使用情况', default=dict)
    gpu_usage = models.JSONField('GPU使用情况', default=dict)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_training_jobs', verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '模型训练任务'
        verbose_name_plural = '模型训练任务'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.job_id})"

    def get_duration(self):
        """获取训练时长"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds() / 3600  # 转换为小时
        elif self.start_time:
            return (timezone.now() - self.start_time).total_seconds() / 3600
        return 0


class ModelVersion(models.Model):
    """模型版本"""
    STATUS_CHOICES = [
        ('training', '训练中'),
        ('ready', '就绪'),
        ('deployed', '已部署'),
        ('deprecated', '已弃用'),
        ('archived', '已归档'),
    ]

    model_template = models.ForeignKey(AIModelTemplate, on_delete=models.CASCADE, related_name='versions', verbose_name='模型模板')
    training_job = models.OneToOneField(ModelTrainingJob, on_delete=models.CASCADE, verbose_name='训练任务')

    version_number = models.CharField('版本号', max_length=20)
    version_name = models.CharField('版本名称', max_length=200, blank=True)
    description = models.TextField('版本描述', blank=True)

    # 模型信息
    model_path = models.CharField('模型路径', max_length=500)
    model_size = models.BigIntegerField('模型大小(字节)', default=0)
    model_format = models.CharField('模型格式', max_length=50, default='pickle')

    # 性能指标
    performance_metrics = models.JSONField('性能指标', default=dict)
    benchmark_results = models.JSONField('基准测试结果', default=dict)

    # 部署信息
    deployment_config = models.JSONField('部署配置', default=dict)
    api_endpoint = models.CharField('API端点', max_length=500, blank=True)

    # 状态管理
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='training')
    is_production = models.BooleanField('生产版本', default=False)

    # 使用统计
    prediction_count = models.IntegerField('预测次数', default=0)
    success_count = models.IntegerField('成功次数', default=0)
    error_count = models.IntegerField('错误次数', default=0)

    # 监控指标
    avg_response_time = models.FloatField('平均响应时间(ms)', null=True, blank=True)
    avg_accuracy = models.FloatField('平均准确率', null=True, blank=True)
    last_prediction_time = models.DateTimeField('最后预测时间', null=True, blank=True)

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '模型版本'
        verbose_name_plural = '模型版本'
        unique_together = ['model_template', 'version_number']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.model_template.name} v{self.version_number}"

    def get_success_rate(self):
        """获取成功率"""
        if self.prediction_count == 0:
            return 0
        return (self.success_count / self.prediction_count) * 100


class ModelEvaluation(models.Model):
    """模型评估"""
    EVALUATION_TYPES = [
        ('validation', '验证评估'),
        ('test', '测试评估'),
        ('cross_validation', '交叉验证'),
        ('benchmark', '基准测试'),
        ('a_b_test', 'A/B测试'),
        ('production', '生产评估'),
    ]

    model_version = models.ForeignKey(ModelVersion, on_delete=models.CASCADE, related_name='evaluations', verbose_name='模型版本')
    evaluation_type = models.CharField('评估类型', max_length=20, choices=EVALUATION_TYPES)

    # 评估配置
    evaluation_config = models.JSONField('评估配置', default=dict)
    test_dataset = models.ForeignKey(TrainingDataset, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='测试数据集')

    # 评估结果
    metrics = models.JSONField('评估指标', default=dict)
    confusion_matrix = models.JSONField('混淆矩阵', default=dict)
    classification_report = models.JSONField('分类报告', default=dict)

    # 详细结果
    predictions = models.JSONField('预测结果', default=list)
    feature_importance = models.JSONField('特征重要性', default=dict)
    model_interpretation = models.JSONField('模型解释', default=dict)

    # 性能分析
    inference_time = models.FloatField('推理时间(ms)', null=True, blank=True)
    memory_usage = models.FloatField('内存使用(MB)', null=True, blank=True)
    cpu_usage = models.FloatField('CPU使用率', null=True, blank=True)

    # 评估状态
    is_passed = models.BooleanField('是否通过', default=False)
    evaluation_notes = models.TextField('评估说明', blank=True)

    evaluated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='conducted_evaluations', verbose_name='评估人')
    evaluated_at = models.DateTimeField('评估时间', auto_now_add=True)

    class Meta:
        verbose_name = '模型评估'
        verbose_name_plural = '模型评估'
        ordering = ['-evaluated_at']

    def __str__(self):
        return f"{self.model_version} - {self.get_evaluation_type_display()}"


class ModelOptimization(models.Model):
    """模型优化"""
    OPTIMIZATION_TYPES = [
        ('hyperparameter_tuning', '超参数调优'),
        ('architecture_search', '架构搜索'),
        ('pruning', '模型剪枝'),
        ('quantization', '模型量化'),
        ('distillation', '知识蒸馏'),
        ('ensemble', '模型集成'),
        ('feature_selection', '特征选择'),
        ('data_augmentation', '数据增强'),
    ]

    STATUS_CHOICES = [
        ('pending', '待开始'),
        ('running', '运行中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]

    optimization_id = models.CharField('优化ID', max_length=100, unique=True)
    model_template = models.ForeignKey(AIModelTemplate, on_delete=models.CASCADE, verbose_name='模型模板')
    base_version = models.ForeignKey(ModelVersion, on_delete=models.CASCADE, verbose_name='基础版本')

    optimization_type = models.CharField('优化类型', max_length=30, choices=OPTIMIZATION_TYPES)
    description = models.TextField('优化描述', blank=True)

    # 优化配置
    optimization_config = models.JSONField('优化配置', default=dict)
    search_space = models.JSONField('搜索空间', default=dict)
    optimization_objective = models.JSONField('优化目标', default=dict)

    # 状态信息
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    progress = models.IntegerField('进度百分比', default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])

    # 时间信息
    start_time = models.DateTimeField('开始时间', null=True, blank=True)
    end_time = models.DateTimeField('结束时间', null=True, blank=True)
    optimization_duration = models.FloatField('优化时长(小时)', null=True, blank=True)

    # 优化结果
    best_config = models.JSONField('最佳配置', default=dict)
    best_metrics = models.JSONField('最佳指标', default=dict)
    optimization_history = models.JSONField('优化历史', default=list)

    # 改进效果
    baseline_metrics = models.JSONField('基线指标', default=dict)
    improvement_metrics = models.JSONField('改进指标', default=dict)
    improvement_percentage = models.FloatField('改进百分比', null=True, blank=True)

    # 资源使用
    trials_count = models.IntegerField('试验次数', default=0)
    successful_trials = models.IntegerField('成功试验数', default=0)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_optimizations', verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '模型优化'
        verbose_name_plural = '模型优化'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.model_template.name} - {self.get_optimization_type_display()}"


class ModelDeployment(models.Model):
    """模型部署"""
    DEPLOYMENT_TYPES = [
        ('api', 'API服务'),
        ('batch', '批处理'),
        ('streaming', '流处理'),
        ('edge', '边缘部署'),
        ('mobile', '移动端'),
        ('web', 'Web应用'),
        ('microservice', '微服务'),
    ]

    STATUS_CHOICES = [
        ('preparing', '准备中'),
        ('deploying', '部署中'),
        ('running', '运行中'),
        ('stopped', '已停止'),
        ('failed', '失败'),
        ('updating', '更新中'),
    ]

    deployment_id = models.CharField('部署ID', max_length=100, unique=True)
    model_version = models.ForeignKey(ModelVersion, on_delete=models.CASCADE, verbose_name='模型版本')

    deployment_type = models.CharField('部署类型', max_length=20, choices=DEPLOYMENT_TYPES)
    environment = models.CharField('部署环境', max_length=50, default='production')

    # 部署配置
    deployment_config = models.JSONField('部署配置', default=dict)
    resource_config = models.JSONField('资源配置', default=dict)
    scaling_config = models.JSONField('扩缩容配置', default=dict)

    # 服务信息
    service_name = models.CharField('服务名称', max_length=200)
    api_endpoint = models.CharField('API端点', max_length=500, blank=True)
    health_check_url = models.CharField('健康检查URL', max_length=500, blank=True)

    # 状态信息
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='preparing')
    is_active = models.BooleanField('是否激活', default=False)

    # 时间信息
    deployed_at = models.DateTimeField('部署时间', null=True, blank=True)
    last_health_check = models.DateTimeField('最后健康检查', null=True, blank=True)

    # 监控指标
    request_count = models.IntegerField('请求次数', default=0)
    success_count = models.IntegerField('成功次数', default=0)
    error_count = models.IntegerField('错误次数', default=0)
    avg_response_time = models.FloatField('平均响应时间(ms)', null=True, blank=True)

    # 资源使用
    cpu_usage = models.FloatField('CPU使用率', null=True, blank=True)
    memory_usage = models.FloatField('内存使用率', null=True, blank=True)
    disk_usage = models.FloatField('磁盘使用率', null=True, blank=True)

    deployed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='deployed_models', verbose_name='部署人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '模型部署'
        verbose_name_plural = '模型部署'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.model_version} - {self.service_name}"

    def get_success_rate(self):
        """获取成功率"""
        if self.request_count == 0:
            return 0
        return (self.success_count / self.request_count) * 100


class ModelMonitoring(models.Model):
    """模型监控"""
    MONITORING_TYPES = [
        ('performance', '性能监控'),
        ('data_drift', '数据漂移'),
        ('model_drift', '模型漂移'),
        ('accuracy', '准确率监控'),
        ('latency', '延迟监控'),
        ('resource', '资源监控'),
        ('business', '业务监控'),
    ]

    ALERT_LEVELS = [
        ('info', '信息'),
        ('warning', '警告'),
        ('error', '错误'),
        ('critical', '严重'),
    ]

    monitoring_id = models.CharField('监控ID', max_length=100, unique=True)
    model_deployment = models.ForeignKey(ModelDeployment, on_delete=models.CASCADE, related_name='monitoring_records', verbose_name='模型部署')

    monitoring_type = models.CharField('监控类型', max_length=20, choices=MONITORING_TYPES)
    metric_name = models.CharField('指标名称', max_length=100)

    # 监控数据
    metric_value = models.FloatField('指标值')
    threshold_value = models.FloatField('阈值', null=True, blank=True)
    baseline_value = models.FloatField('基线值', null=True, blank=True)

    # 监控配置
    monitoring_config = models.JSONField('监控配置', default=dict)
    alert_config = models.JSONField('告警配置', default=dict)

    # 告警信息
    is_alert = models.BooleanField('是否告警', default=False)
    alert_level = models.CharField('告警级别', max_length=20, choices=ALERT_LEVELS, blank=True)
    alert_message = models.TextField('告警消息', blank=True)

    # 时间信息
    monitored_at = models.DateTimeField('监控时间', auto_now_add=True)

    # 处理信息
    is_handled = models.BooleanField('是否已处理', default=False)
    handled_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='handled_alerts', verbose_name='处理人')
    handled_at = models.DateTimeField('处理时间', null=True, blank=True)
    handling_notes = models.TextField('处理说明', blank=True)

    class Meta:
        verbose_name = '模型监控'
        verbose_name_plural = '模型监控'
        ordering = ['-monitored_at']

    def __str__(self):
        return f"{self.model_deployment.service_name} - {self.metric_name}"


class ExperimentTracking(models.Model):
    """实验跟踪"""
    EXPERIMENT_TYPES = [
        ('training', '训练实验'),
        ('hyperparameter', '超参数实验'),
        ('architecture', '架构实验'),
        ('data', '数据实验'),
        ('feature', '特征实验'),
        ('ensemble', '集成实验'),
        ('comparison', '对比实验'),
    ]

    STATUS_CHOICES = [
        ('running', '运行中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]

    experiment_id = models.CharField('实验ID', max_length=100, unique=True)
    name = models.CharField('实验名称', max_length=200)
    experiment_type = models.CharField('实验类型', max_length=20, choices=EXPERIMENT_TYPES)
    description = models.TextField('实验描述', blank=True)

    # 实验配置
    experiment_config = models.JSONField('实验配置', default=dict)
    parameters = models.JSONField('实验参数', default=dict)

    # 关联信息
    model_template = models.ForeignKey(AIModelTemplate, on_delete=models.CASCADE, null=True, blank=True, verbose_name='模型模板')
    parent_experiment = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name='父实验')

    # 状态信息
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='running')

    # 时间信息
    start_time = models.DateTimeField('开始时间', null=True, blank=True)
    end_time = models.DateTimeField('结束时间', null=True, blank=True)
    duration = models.FloatField('持续时间(小时)', null=True, blank=True)

    # 实验结果
    metrics = models.JSONField('实验指标', default=dict)
    artifacts = models.JSONField('实验产物', default=dict)
    logs = models.JSONField('实验日志', default=list)

    # 比较结果
    baseline_metrics = models.JSONField('基线指标', default=dict)
    improvement = models.JSONField('改进情况', default=dict)

    # 标签和注释
    tags = models.JSONField('标签', default=list)
    notes = models.TextField('实验笔记', blank=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_experiments', verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '实验跟踪'
        verbose_name_plural = '实验跟踪'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.experiment_id})"


class AIModelConfig(models.Model):
    """AI模型配置"""
    CONFIG_TYPES = [
        ('training', '训练配置'),
        ('deployment', '部署配置'),
        ('monitoring', '监控配置'),
        ('optimization', '优化配置'),
        ('security', '安全配置'),
        ('performance', '性能配置'),
    ]

    config_type = models.CharField('配置类型', max_length=20, choices=CONFIG_TYPES)
    config_key = models.CharField('配置键', max_length=100)
    config_value = models.JSONField('配置值', default=dict)
    description = models.TextField('配置描述', blank=True)

    # 配置属性
    is_system = models.BooleanField('系统配置', default=False)
    is_active = models.BooleanField('是否启用', default=True)
    is_encrypted = models.BooleanField('是否加密', default=False)

    # 适用范围
    model_template = models.ForeignKey(AIModelTemplate, on_delete=models.CASCADE, null=True, blank=True, related_name='configs', verbose_name='模型模板')

    # 版本信息
    version = models.CharField('配置版本', max_length=20, default='1.0')

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_ai_model_configs', verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = 'AI模型配置'
        verbose_name_plural = 'AI模型配置'
        unique_together = ['config_type', 'config_key', 'model_template']
        ordering = ['config_type', 'config_key']

    def __str__(self):
        return f"{self.get_config_type_display()} - {self.config_key}"

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import HelpCategory, HelpArticle, FAQ, CustomerService, ServiceMessage, Feedback


@admin.register(HelpCategory)
class HelpCategoryAdmin(admin.ModelAdmin):
    """帮助分类管理"""
    list_display = ['name', 'icon', 'article_count', 'is_active', 'sort_order', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    list_editable = ['is_active', 'sort_order']
    ordering = ['sort_order', 'name']
    
    def article_count(self, obj):
        return obj.articles.count()
    article_count.short_description = '文章数量'


@admin.register(HelpArticle)
class HelpArticleAdmin(admin.ModelAdmin):
    """帮助文章管理"""
    list_display = ['title', 'category', 'author_info', 'status', 'is_featured', 
                   'view_count', 'like_count', 'created_at']
    list_filter = ['category', 'status', 'is_featured', 'created_at']
    search_fields = ['title', 'content', 'summary']
    list_editable = ['status', 'is_featured']
    readonly_fields = ['view_count', 'like_count', 'created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('category', 'title', 'summary', 'content', 'author')
        }),
        ('状态设置', {
            'fields': ('status', 'is_featured')
        }),
        ('统计信息', {
            'fields': ('view_count', 'like_count'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def author_info(self, obj):
        if obj.author:
            return format_html(
                '<a href="{}">{}</a>',
                reverse('admin:users_user_change', args=[obj.author.pk]),
                obj.author.nickname or obj.author.phone
            )
        return '-'
    author_info.short_description = '作者'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('category', 'author')


@admin.register(CustomerService)
class CustomerServiceAdmin(admin.ModelAdmin):
    """客服会话管理"""
    list_display = ['session_id', 'user_info', 'agent_info', 'status',
                   'rating', 'started_at', 'ended_at']
    list_filter = ['status', 'rating', 'started_at']
    search_fields = ['session_id', 'user__phone', 'user__nickname', 'agent__phone', 'title']
    readonly_fields = ['session_id', 'created_at', 'updated_at']
    date_hierarchy = 'started_at'

    fieldsets = (
        ('基本信息', {
            'fields': ('session_id', 'user', 'agent', 'title')
        }),
        ('会话状态', {
            'fields': ('status', 'started_at', 'ended_at', 'last_message_at')
        }),
        ('评价信息', {
            'fields': ('rating', 'feedback')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def user_info(self, obj):
        if obj.user:
            user_display = obj.user.nickname or obj.user.phone or obj.user.username
            phone_display = obj.user.phone or obj.user.username
            return format_html(
                '<a href="{}">{}</a><br/><small>{}</small>',
                reverse('admin:users_user_change', args=[obj.user.pk]),
                user_display,
                phone_display
            )
        return '-'
    user_info.short_description = '用户'

    def agent_info(self, obj):
        if obj.agent:
            agent_display = obj.agent.nickname or obj.agent.phone or obj.agent.username
            return format_html(
                '<a href="{}">{}</a>',
                reverse('admin:users_user_change', args=[obj.agent.pk]),
                agent_display
            )
        return '未分配'
    agent_info.short_description = '客服'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'agent')


@admin.register(ServiceMessage)
class ServiceMessageAdmin(admin.ModelAdmin):
    """客服消息管理"""
    list_display = ['service_info', 'sender_info', 'message_type', 'content_preview', 'is_read', 'created_at']
    list_filter = ['message_type', 'is_read', 'created_at']
    search_fields = ['content', 'session__session_id', 'sender__phone']
    readonly_fields = ['created_at', 'read_at']

    def service_info(self, obj):
        return f"{obj.session.session_id}"
    service_info.short_description = '会话'

    def sender_info(self, obj):
        if obj.sender:
            sender_display = obj.sender.nickname or obj.sender.phone or obj.sender.username
            return format_html(
                '<a href="{}">{}</a>',
                reverse('admin:users_user_change', args=[obj.sender.pk]),
                sender_display
            )
        return '-'
    sender_info.short_description = '发送人'

    def content_preview(self, obj):
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    content_preview.short_description = '内容预览'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('session', 'sender')


@admin.register(Feedback)
class FeedbackAdmin(admin.ModelAdmin):
    """意见反馈管理"""
    list_display = ['user_info', 'feedback_type', 'title', 'status', 'created_at', 'handled_at']
    list_filter = ['feedback_type', 'status', 'created_at']
    search_fields = ['title', 'content', 'user__phone', 'user__nickname']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'feedback_type', 'title', 'content')
        }),
        ('联系信息', {
            'fields': ('contact_phone', 'contact_email')
        }),
        ('处理信息', {
            'fields': ('status', 'handler', 'response', 'handled_at')
        }),
        ('附件信息', {
            'fields': ('attachments',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def user_info(self, obj):
        if obj.user:
            user_display = obj.user.nickname or obj.user.phone or obj.user.username
            phone_display = obj.user.phone or obj.user.username
            return format_html(
                '<a href="{}">{}</a><br/><small>{}</small>',
                reverse('admin:users_user_change', args=[obj.user.pk]),
                user_display,
                phone_display
            )
        return '-'
    user_info.short_description = '用户'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'handler')


@admin.register(FAQ)
class FAQAdmin(admin.ModelAdmin):
    """常见问题管理"""
    list_display = ['question', 'category', 'is_active', 'view_count', 'sort_order', 'created_at']
    list_filter = ['category', 'is_active', 'created_at']
    search_fields = ['question', 'answer']
    list_editable = ['is_active', 'sort_order']
    readonly_fields = ['view_count', 'helpful_count', 'created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('question', 'answer', 'category')
        }),
        ('设置', {
            'fields': ('is_active', 'sort_order')
        }),
        ('统计', {
            'fields': ('view_count', 'helpful_count'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('category')

import logging
from django.core.exceptions import ValidationError, PermissionDenied
from django.http import JsonResponse
from rest_framework import status

logger = logging.getLogger(__name__)

from rest_framework import viewsets, permissions
from rest_framework.views import APIView
from .models import HelpCategory, HelpArticle, FAQ, CustomerService, Feedback
from utils.response import APIResponse
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class HelpCategoryViewSet(viewsets.ModelViewSet):
    """帮助分类ViewSet"""
    queryset = HelpCategory.objects.all()
    permission_classes = [permissions.IsAuthenticated]


class HelpArticleViewSet(viewsets.ModelViewSet):
    """帮助文章ViewSet"""
    queryset = HelpArticle.objects.all()
    permission_classes = [permissions.IsAuthenticated]


class FAQViewSet(viewsets.ModelViewSet):
    """常见问题ViewSet"""
    queryset = FAQ.objects.all()
    permission_classes = [permissions.IsAuthenticated]


class CustomerServiceViewSet(viewsets.ModelViewSet):
    """客服会话ViewSet"""
    queryset = CustomerService.objects.all()
    permission_classes = [permissions.IsAuthenticated]


class FeedbackViewSet(viewsets.ModelViewSet):
    """意见反馈ViewSet"""
    queryset = Feedback.objects.all()
    permission_classes = [permissions.IsAuthenticated]


class HelpCenterView(APIView):
    """帮助中心"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        try:
            return APIResponse.success(data=[])


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class HelpSearchView(APIView):
    """帮助搜索"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        try:
            return APIResponse.success(data=[])


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class StartServiceView(APIView):
    """开始客服会话"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        try:
            return APIResponse.success(message='客服会话开始')


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class ServiceMessagesView(APIView):
    """客服消息"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, session_id):
        try:
            return APIResponse.success(data=[])


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class SendServiceMessageView(APIView):
    """发送客服消息"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, session_id):
        try:
            return APIResponse.success(message='消息发送成功')


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class CloseServiceView(APIView):
    """关闭客服会话"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, session_id):
        try:
            return APIResponse.success(message='会话已关闭')


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class SubmitFeedbackView(APIView):
    """提交反馈"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        try:
            return APIResponse.success(message='反馈提交成功')

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
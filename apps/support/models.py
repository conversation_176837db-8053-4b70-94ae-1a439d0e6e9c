from django.db import models
from django.conf import settings


class HelpCategory(models.Model):
    """帮助分类"""
    name = models.CharField('分类名称', max_length=100)
    description = models.TextField('分类描述', blank=True)
    icon = models.CharField('图标', max_length=50, blank=True)
    
    sort_order = models.PositiveIntegerField('排序', default=0)
    is_active = models.BooleanField('是否启用', default=True)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'help_categories'
        verbose_name = '帮助分类'
        verbose_name_plural = '帮助分类'
        ordering = ['sort_order', 'name']
    
    def __str__(self):
        return self.name


class HelpArticle(models.Model):
    """帮助文章"""
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('published', '已发布'),
        ('archived', '已归档'),
    ]
    
    category = models.ForeignKey(HelpCategory, on_delete=models.CASCADE, related_name='articles', verbose_name='分类')
    title = models.CharField('标题', max_length=200)
    content = models.TextField('内容')
    summary = models.TextField('摘要', blank=True)
    
    # 状态和权限
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='draft')
    is_featured = models.BooleanField('是否推荐', default=False)
    
    # 统计信息
    view_count = models.PositiveIntegerField('查看次数', default=0)
    like_count = models.PositiveIntegerField('点赞次数', default=0)
    
    # 作者信息
    author = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, 
                              blank=True, null=True, related_name='help_articles', verbose_name='作者')
    
    # 时间信息
    published_at = models.DateTimeField('发布时间', blank=True, null=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'help_articles'
        verbose_name = '帮助文章'
        verbose_name_plural = '帮助文章'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.title


class FAQ(models.Model):
    """常见问题"""
    question = models.CharField('问题', max_length=500)
    answer = models.TextField('答案')
    category = models.ForeignKey(HelpCategory, on_delete=models.CASCADE, 
                                related_name='faqs', verbose_name='分类')
    
    # 统计信息
    view_count = models.PositiveIntegerField('查看次数', default=0)
    helpful_count = models.PositiveIntegerField('有用次数', default=0)
    
    # 状态
    is_active = models.BooleanField('是否启用', default=True)
    sort_order = models.PositiveIntegerField('排序', default=0)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'faqs'
        verbose_name = '常见问题'
        verbose_name_plural = '常见问题'
        ordering = ['sort_order', '-created_at']
    
    def __str__(self):
        return self.question


class CustomerService(models.Model):
    """客服会话"""
    STATUS_CHOICES = [
        ('waiting', '等待中'),
        ('active', '进行中'),
        ('closed', '已关闭'),
    ]
    
    # 基本信息
    session_id = models.CharField('会话ID', max_length=32, unique=True)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, 
                            related_name='service_sessions', verbose_name='用户')
    agent = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, 
                             blank=True, null=True, related_name='agent_sessions', verbose_name='客服')
    
    # 会话信息
    title = models.CharField('会话标题', max_length=200, blank=True)
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='waiting')
    
    # 时间信息
    started_at = models.DateTimeField('开始时间', auto_now_add=True)
    ended_at = models.DateTimeField('结束时间', blank=True, null=True)
    last_message_at = models.DateTimeField('最后消息时间', blank=True, null=True)
    
    # 评价信息
    rating = models.PositiveIntegerField('评分', blank=True, null=True, help_text='1-5分')
    feedback = models.TextField('反馈', blank=True)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'customer_services'
        verbose_name = '客服会话'
        verbose_name_plural = '客服会话'
        ordering = ['-created_at']
    
    def __str__(self):
        user_display = self.user.phone if self.user and self.user.phone else (self.user.username if self.user else 'Unknown')
        return f"{user_display} - {self.session_id}"
    
    def save(self, *args, **kwargs):
        if not self.session_id:
            self.session_id = self.generate_session_id()
        super().save(*args, **kwargs)
    
    def generate_session_id(self):
        """生成会话ID"""
        import time
        import random
        timestamp = str(int(time.time()))
        random_num = str(random.randint(1000, 9999))
        return f"CS{timestamp}{random_num}"


class ServiceMessage(models.Model):
    """客服消息"""
    MESSAGE_TYPE_CHOICES = [
        ('text', '文本'),
        ('image', '图片'),
        ('file', '文件'),
        ('system', '系统消息'),
    ]
    
    SENDER_TYPE_CHOICES = [
        ('user', '用户'),
        ('agent', '客服'),
        ('system', '系统'),
    ]
    
    session = models.ForeignKey(CustomerService, on_delete=models.CASCADE, 
                               related_name='messages', verbose_name='会话')
    sender = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, 
                              related_name='service_messages', verbose_name='发送者')
    
    # 消息信息
    message_type = models.CharField('消息类型', max_length=20, choices=MESSAGE_TYPE_CHOICES, default='text')
    sender_type = models.CharField('发送者类型', max_length=20, choices=SENDER_TYPE_CHOICES)
    content = models.TextField('消息内容')
    
    # 附件信息
    attachment = models.FileField('附件', upload_to='service_attachments/', blank=True, null=True)
    
    # 状态
    is_read = models.BooleanField('是否已读', default=False)
    read_at = models.DateTimeField('阅读时间', blank=True, null=True)
    
    created_at = models.DateTimeField('发送时间', auto_now_add=True)
    
    class Meta:
        db_table = 'service_messages'
        verbose_name = '客服消息'
        verbose_name_plural = '客服消息'
        ordering = ['created_at']
    
    def __str__(self):
        sender_display = self.sender.phone if self.sender and self.sender.phone else (self.sender.username if self.sender else 'Unknown')
        return f"{sender_display} - {self.content[:50]}"


class Feedback(models.Model):
    """意见反馈"""
    FEEDBACK_TYPE_CHOICES = [
        ('bug', 'Bug反馈'),
        ('feature', '功能建议'),
        ('complaint', '投诉建议'),
        ('other', '其他'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('resolved', '已解决'),
        ('closed', '已关闭'),
    ]
    
    # 基本信息
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, 
                            related_name='feedbacks', verbose_name='用户')
    feedback_type = models.CharField('反馈类型', max_length=20, choices=FEEDBACK_TYPE_CHOICES)
    title = models.CharField('标题', max_length=200)
    content = models.TextField('内容')
    
    # 联系信息
    contact_phone = models.CharField('联系电话', max_length=20, blank=True)
    contact_email = models.EmailField('联系邮箱', blank=True)
    
    # 附件
    attachments = models.JSONField('附件列表', default=list, blank=True)
    
    # 处理状态
    status = models.CharField('处理状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    handler = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, 
                               blank=True, null=True, related_name='handled_feedbacks', verbose_name='处理人')
    response = models.TextField('回复内容', blank=True)
    
    # 时间信息
    handled_at = models.DateTimeField('处理时间', blank=True, null=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'feedbacks'
        verbose_name = '意见反馈'
        verbose_name_plural = '意见反馈'
        ordering = ['-created_at']
    
    def __str__(self):
        user_display = self.user.phone if self.user and self.user.phone else (self.user.username if self.user else 'Unknown')
        return f"{user_display} - {self.title}"

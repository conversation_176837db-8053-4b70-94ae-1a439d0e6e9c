from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from . import views

router = DefaultRouter()
router.register(r'help-categories', views.HelpCategoryViewSet, basename='help-category')
router.register(r'help-articles', views.HelpArticleViewSet, basename='help-article')
router.register(r'faqs', views.FAQViewSet, basename='faq')
router.register(r'customer-service', views.CustomerServiceViewSet, basename='customer-service')
router.register(r'feedbacks', views.FeedbackViewSet, basename='feedback')

urlpatterns = [
    # 帮助中心
    path('help/', views.HelpCenterView.as_view(), name='help_center'),
    path('help/search/', views.HelpSearchView.as_view(), name='help_search'),
    
    # 客服系统
    path('service/start/', views.StartServiceView.as_view(), name='start_service'),
    path('service/<str:session_id>/messages/', views.ServiceMessagesView.as_view(), name='service_messages'),
    path('service/<str:session_id>/send/', views.SendServiceMessageView.as_view(), name='send_service_message'),
    path('service/<str:session_id>/close/', views.CloseServiceView.as_view(), name='close_service'),
    
    # 意见反馈
    path('feedback/submit/', views.SubmitFeedbackView.as_view(), name='submit_feedback'),
    
    # 其他路由
    path('', include(router.urls)),
]

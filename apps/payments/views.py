from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db.models import Sum, Count, Q
from decimal import Decimal
import uuid
import json
import hashlib
import time

from .models import Payment, Refund, PaymentConfig
from .serializers import (
    PaymentSerializer, PaymentCreateSerializer, RefundSerializer, RefundCreateSerializer,
    PaymentConfigSerializer, WechatPayNotifySerializer, AlipayNotifySerializer,
    PaymentStatisticsSerializer, PaymentMethodSerializer
)
from utils.response import APIResponse
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class PaymentViewSet(viewsets.ModelViewSet):
    """支付记录ViewSet"""
    serializer_class = PaymentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """获取查询集"""
        if self.request.user.is_staff:
            return Payment.objects.all().select_related('user', 'order')
        return Payment.objects.filter(user=self.request.user).select_related('order')

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """取消支付"""
        payment = self.get_object()

        if payment.user != request.user and not request.user.is_staff:
            return APIResponse.forbidden(message='无权限执行此操作')

        if payment.status != 'pending':
            return APIResponse.error(message='只能取消待支付的订单')

        payment.status = 'cancelled'
        payment.save()

        return APIResponse.success(message='支付已取消')


class RefundViewSet(viewsets.ModelViewSet):
    """退款记录ViewSet"""
    serializer_class = RefundSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """获取查询集"""
        if self.request.user.is_staff:
            return Refund.objects.all().select_related('payment', 'processed_by')
        return Refund.objects.filter(payment__user=self.request.user).select_related('payment')

    @action(detail=True, methods=['post'])
    def process(self, request, pk=None):
        """处理退款（管理员操作）"""
        if not request.user.is_staff:
            return APIResponse.forbidden(message='无权限执行此操作')

        refund = self.get_object()
        action_type = request.data.get('action')  # 'approve' or 'reject'

        if action_type == 'approve':
            # 这里应该调用第三方退款接口
            # 暂时模拟退款成功
            refund.status = 'success'
            refund.processed_by = request.user
            refund.processed_at = timezone.now()
            refund.third_party_response = {'mock': 'refund_success'}
            refund.save()

            return APIResponse.success(message='退款处理成功')
        elif action_type == 'reject':
            refund.status = 'failed'
            refund.processed_by = request.user
            refund.processed_at = timezone.now()
            refund.save()

            return APIResponse.success(message='退款已拒绝')
        else:
            return APIResponse.error(message='无效的操作类型')


class CreatePaymentView(APIView):
    """创建支付"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """创建支付订单"""
        serializer = PaymentCreateSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            try:
                order = serializer.validated_data['order']
                payment_method = serializer.validated_data['payment_method']
                amount = serializer.validated_data['amount']

                # 创建支付记录
                payment = Payment.objects.create(
                    user=request.user,
                    order=order,
                    payment_method=payment_method,
                    amount=amount,
                    status='pending'
                )

                # 根据支付方式生成支付参数
                if payment_method == 'wechat':
                    pay_params = self._create_wechat_payment(payment)
                elif payment_method == 'alipay':
                    pay_params = self._create_alipay_payment(payment)
                else:
                    return APIResponse.error(message='不支持的支付方式')

                return APIResponse.created(data={
                    'payment_id': payment.id,
                    'payment_no': payment.payment_no,
                    'amount': float(payment.amount),
                    'pay_params': pay_params
                }, message='支付订单创建成功')

            except Exception as e:
                return APIResponse.error(message=f'支付创建失败: {str(e)}')

        return APIResponse.error(message='数据验证失败', errors=serializer.errors)

    def _create_wechat_payment(self, payment):
        """创建微信支付参数"""
        # 这里应该调用微信支付API
        # 暂时返回模拟数据
        return {
            'appId': 'wx_app_id',
            'timeStamp': str(int(time.time())),
            'nonceStr': str(uuid.uuid4()).replace('-', ''),
            'package': f'prepay_id=mock_prepay_id_{payment.id}',
            'signType': 'MD5',
            'paySign': 'mock_pay_sign'
        }

    def _create_alipay_payment(self, payment):
        """创建支付宝支付参数"""
        # 这里应该调用支付宝API
        # 暂时返回模拟数据
        return {
            'orderString': f'mock_order_string_{payment.id}',
            'return_url': 'https://example.com/return',
            'notify_url': 'https://example.com/notify'
        }


class WechatPayNotifyView(APIView):
    """微信支付回调"""
    permission_classes = []

    def post(self, request):
        """处理微信支付回调"""
        try:
            # 记录回调日志
            self._log_callback_request(request)

            # 验证回调签名
            if not self._verify_wechat_signature(request):
                return self._return_wechat_fail('签名验证失败')

            # 解析回调数据
            callback_data = self._parse_wechat_callback(request)
            if not callback_data:
                return self._return_wechat_fail('回调数据解析失败')

            # 处理支付结果
            result = self._process_payment_result(callback_data)
            if result['success']:
                return self._return_wechat_success()
            else:
                return self._return_wechat_fail(result['message'])

        except Exception as e:
            # 记录错误日志
            self._log_callback_error(request, str(e))
            return self._return_wechat_fail(f'系统错误: {str(e)}')

    def _verify_wechat_signature(self, request):
        """验证微信支付回调签名"""
        try:
            # 获取微信支付配置
            from .models import PaymentConfig
            config = PaymentConfig.objects.filter(
                payment_method='wechat',
                is_enabled=True
            ).first()

            if not config or not config.api_key:
                return False

            # 这里应该实现真实的微信签名验证逻辑
            # 暂时返回True，实际部署时需要完善
            return True

        except Exception as e:
            print(f"签名验证错误: {e}")
            return False

    def _parse_wechat_callback(self, request):
        """解析微信回调数据"""
        try:
            # 从request.data或request.body解析数据
            if hasattr(request, 'data') and request.data:
                return request.data

            # 如果是XML格式，需要解析XML
            import xml.etree.ElementTree as ET
            if request.content_type == 'application/xml':
                root = ET.fromstring(request.body)
                data = {}
                for child in root:
                    data[child.tag] = child.text
                return data

            return request.data

        except Exception as e:
            print(f"回调数据解析错误: {e}")
            return None

    def _process_payment_result(self, callback_data):
        """处理支付结果"""
        try:
            out_trade_no = callback_data.get('out_trade_no')
            transaction_id = callback_data.get('transaction_id')
            total_fee = callback_data.get('total_fee')
            result_code = callback_data.get('result_code', 'SUCCESS')

            if not out_trade_no:
                return {'success': False, 'message': '缺少订单号'}

            # 查找支付记录
            try:
                payment = Payment.objects.get(payment_no=out_trade_no)
            except Payment.DoesNotExist:
                return {'success': False, 'message': '支付记录不存在'}

            # 防止重复处理
            if payment.status == 'success':
                return {'success': True, 'message': '支付已处理'}

            # 验证金额（微信支付金额单位是分）
            if total_fee and Decimal(total_fee) / 100 != payment.amount:
                return {'success': False, 'message': '金额不匹配'}

            # 更新支付状态
            if result_code == 'SUCCESS':
                payment.status = 'success'
                payment.third_party_transaction_id = transaction_id
                payment.paid_at = timezone.now()
            else:
                payment.status = 'failed'

            payment.callback_data = callback_data
            payment.callback_time = timezone.now()
            payment.save()

            # 更新订单状态
            if payment.status == 'success' and payment.order:
                payment.order.status = 'paid'
                payment.order.paid_at = timezone.now()
                payment.order.save()

                # 触发支付成功后续处理
                self._handle_payment_success(payment)

            return {'success': True, 'message': '处理成功'}

        except Exception as e:
            return {'success': False, 'message': f'处理失败: {str(e)}'}

    def _handle_payment_success(self, payment):
        """处理支付成功后续逻辑"""
        try:
            # 发送支付成功通知
            from apps.notifications.models import Message
            Message.objects.create(
                user=payment.user,
                title='支付成功',
                content=f'您的订单 {payment.order.order_no} 支付成功，金额 ¥{payment.amount}',
                message_type='payment_success'
            )

            # 可以在这里添加其他业务逻辑
            # 比如：开通服务、发送邮件等

        except Exception as e:
            print(f"支付成功后续处理错误: {e}")

    def _return_wechat_success(self):
        """返回微信支付成功响应"""
        from django.http import HttpResponse
        return HttpResponse(
            '<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>',
            content_type='application/xml'
        )

    def _return_wechat_fail(self, message):
        """返回微信支付失败响应"""
        from django.http import HttpResponse
        return HttpResponse(
            f'<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[{message}]]></return_msg></xml>',
            content_type='application/xml'
        )

    def _log_callback_request(self, request):
        """记录回调请求日志"""
        try:
            import logging
            logger = logging.getLogger('payment')
            logger.info(f"微信支付回调: {request.data}")
        except:
            pass

    def _log_callback_error(self, request, error):
        """记录回调错误日志"""
        try:
            import logging
            logger = logging.getLogger('payment')
            logger.error(f"微信支付回调错误: {error}, 数据: {request.data}")
        except:
            pass


class AlipayNotifyView(APIView):
    """支付宝回调"""
    permission_classes = []

    def post(self, request):
        """处理支付宝回调"""
        serializer = AlipayNotifySerializer(data=request.data)
        if serializer.is_valid():
            try:
                out_trade_no = serializer.validated_data['out_trade_no']
                trade_no = serializer.validated_data['trade_no']
                total_amount = serializer.validated_data['total_amount']

                # 查找支付记录
                payment = Payment.objects.get(payment_no=out_trade_no)

                # 验证金额
                if total_amount != payment.amount:
                    return APIResponse.error(message='金额不匹配')

                # 更新支付状态
                payment.status = 'success'
                payment.transaction_id = trade_no
                payment.paid_at = timezone.now()
                payment.third_party_response = request.data
                payment.save()

                # 更新订单状态
                if payment.order:
                    payment.order.status = 'paid'
                    payment.order.paid_at = timezone.now()
                    payment.order.save()

                return APIResponse.success(message='回调处理成功')

            except Payment.DoesNotExist:
                return APIResponse.error(message='支付记录不存在')
            except Exception as e:
                return APIResponse.error(message=f'回调处理失败: {str(e)}')

        return APIResponse.error(message='回调数据验证失败', errors=serializer.errors)


class ApplyRefundView(APIView):
    """申请退款"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """申请退款"""
        serializer = RefundCreateSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            try:
                payment = serializer.validated_data['payment']
                refund_amount = serializer.validated_data['refund_amount']
                refund_reason = serializer.validated_data['refund_reason']

                # 创建退款记录
                refund = Refund.objects.create(
                    payment=payment,
                    refund_amount=refund_amount,
                    refund_reason=refund_reason,
                    status='pending'
                )

                return APIResponse.created(
                    data=RefundSerializer(refund, context={'request': request}).data,
                    message='退款申请提交成功'
                )

            except Exception as e:
                return APIResponse.error(message=f'退款申请失败: {str(e)}')

        return APIResponse.error(message='数据验证失败', errors=serializer.errors)


class PaymentMethodsView(APIView):
    """获取支付方式"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取可用的支付方式"""
        try:
            # 获取支付配置
            payment_configs = PaymentConfig.objects.filter(is_enabled=True)

            methods = []
            for config in payment_configs:
                if config.payment_method == 'wechat':
                    methods.append({
                        'method': 'wechat',
                        'name': '微信支付',
                        'is_enabled': True,
                        'icon': 'wechat-pay',
                        'description': '使用微信支付'
                    })
                elif config.payment_method == 'alipay':
                    methods.append({
                        'method': 'alipay',
                        'name': '支付宝',
                        'is_enabled': True,
                        'icon': 'alipay',
                        'description': '使用支付宝支付'
                    })

            # 如果没有配置，返回默认方式
            if not methods:
                methods = [
                    {
                        'method': 'wechat',
                        'name': '微信支付',
                        'is_enabled': True,
                        'icon': 'wechat-pay',
                        'description': '使用微信支付'
                    }
                ]

            return APIResponse.success(data=methods)

        except Exception as e:
            return APIResponse.error(message=f'获取支付方式失败: {str(e)}')


class PaymentStatusView(APIView):
    """支付状态查询"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, payment_id):
        """查询支付状态"""
        try:
            payment = get_object_or_404(
                Payment,
                id=payment_id,
                user=request.user
            )

            # 如果支付状态是处理中，尝试主动查询第三方支付状态
            if payment.status in ['pending', 'processing']:
                self._check_third_party_status(payment)
                payment.refresh_from_db()

            serializer = PaymentSerializer(payment)
            return APIResponse.success(data=serializer.data)

        except Exception as e:
            return APIResponse.error(message=f'查询支付状态失败: {str(e)}')

    def _check_third_party_status(self, payment):
        """主动查询第三方支付状态"""
        try:
            if payment.payment_method == 'wechat':
                self._check_wechat_payment_status(payment)
            elif payment.payment_method == 'alipay':
                self._check_alipay_payment_status(payment)
        except Exception as e:
            print(f"查询第三方支付状态失败: {e}")

    def _check_wechat_payment_status(self, payment):
        """查询微信支付状态"""
        # 这里应该调用微信支付查询API
        # 暂时模拟处理
        pass

    def _check_alipay_payment_status(self, payment):
        """查询支付宝支付状态"""
        # 这里应该调用支付宝查询API
        # 暂时模拟处理
        pass


class PaymentRetryView(APIView):
    """支付重试"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, payment_id):
        """重试支付"""
        try:
            payment = get_object_or_404(
                Payment,
                id=payment_id,
                user=request.user,
                status__in=['failed', 'cancelled']
            )

            # 检查订单状态
            if payment.order.status != 'pending':
                return APIResponse.error(message='订单状态不允许重试支付')

            # 创建新的支付记录
            new_payment = Payment.objects.create(
                user=payment.user,
                order=payment.order,
                payment_method=payment.payment_method,
                amount=payment.amount,
                status='pending'
            )

            # 生成支付参数
            if payment.payment_method == 'wechat':
                pay_params = self._create_wechat_payment(new_payment)
            elif payment.payment_method == 'alipay':
                pay_params = self._create_alipay_payment(new_payment)
            else:
                return APIResponse.error(message='不支持的支付方式')

            return APIResponse.success(data={
                'payment_id': new_payment.id,
                'payment_no': new_payment.payment_no,
                'pay_params': pay_params
            })

        except Exception as e:
            return APIResponse.error(message=f'重试支付失败: {str(e)}')

    def _create_wechat_payment(self, payment):
        """创建微信支付参数"""
        # 复用CreatePaymentView中的逻辑
        return {
            'appId': 'mock_app_id',
            'timeStamp': str(int(time.time())),
            'nonceStr': str(uuid.uuid4()).replace('-', ''),
            'package': f'prepay_id=mock_prepay_id_{payment.id}',
            'signType': 'MD5',
            'paySign': 'mock_pay_sign'
        }

    def _create_alipay_payment(self, payment):
        """创建支付宝支付参数"""
        return {
            'orderString': f'mock_order_string_{payment.id}',
            'return_url': 'https://example.com/return',
            'notify_url': 'https://example.com/notify'
        }


class PaymentCompensationView(APIView):
    """支付补偿处理"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """处理支付补偿"""
        try:
            # 查找超时未回调的支付记录
            from datetime import timedelta
            timeout_threshold = timezone.now() - timedelta(minutes=30)

            timeout_payments = Payment.objects.filter(
                status='processing',
                created_at__lt=timeout_threshold
            )

            compensation_results = []
            for payment in timeout_payments:
                result = self._compensate_payment(payment)
                compensation_results.append({
                    'payment_id': payment.id,
                    'payment_no': payment.payment_no,
                    'result': result
                })

            return APIResponse.success(data={
                'compensated_count': len(compensation_results),
                'results': compensation_results
            })

        except Exception as e:
            return APIResponse.error(message=f'支付补偿处理失败: {str(e)}')

    def _compensate_payment(self, payment):
        """补偿单个支付"""
        try:
            # 主动查询第三方支付状态
            if payment.payment_method == 'wechat':
                status = self._query_wechat_payment(payment)
            elif payment.payment_method == 'alipay':
                status = self._query_alipay_payment(payment)
            else:
                return 'unsupported_method'

            if status == 'success':
                # 更新为成功状态
                payment.status = 'success'
                payment.paid_at = timezone.now()
                payment.save()

                # 更新订单状态
                if payment.order:
                    payment.order.status = 'paid'
                    payment.order.paid_at = timezone.now()
                    payment.order.save()

                return 'compensated_success'
            elif status == 'failed':
                # 更新为失败状态
                payment.status = 'failed'
                payment.save()
                return 'compensated_failed'
            else:
                return 'status_unknown'

        except Exception as e:
            return f'compensation_error: {str(e)}'

    def _query_wechat_payment(self, payment):
        """查询微信支付状态"""
        # 这里应该调用微信支付查询API
        # 暂时返回模拟状态
        return 'success'

    def _query_alipay_payment(self, payment):
        """查询支付宝支付状态"""
        # 这里应该调用支付宝查询API
        # 暂时返回模拟状态
        return 'success'


class PaymentStatisticsView(APIView):
    """支付统计"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取支付统计数据"""
        if not request.user.is_staff:
            return APIResponse.forbidden(message='无权限访问')

        serializer = PaymentStatisticsSerializer(data=request.query_params)
        if serializer.is_valid():
            try:
                start_date = serializer.validated_data['start_date']
                end_date = serializer.validated_data['end_date']
                payment_method = serializer.validated_data['payment_method']

                # 构建查询条件
                queryset = Payment.objects.filter(
                    created_at__date__gte=start_date,
                    created_at__date__lte=end_date,
                    status='success'
                )

                if payment_method != 'all':
                    queryset = queryset.filter(payment_method=payment_method)

                # 统计数据
                total_amount = queryset.aggregate(total=Sum('amount'))['total'] or Decimal('0')
                total_count = queryset.count()

                # 按支付方式统计
                method_stats = queryset.values('payment_method').annotate(
                    count=Count('id'),
                    amount=Sum('amount')
                )

                # 按日期统计
                daily_stats = queryset.extra(
                    select={'date': 'DATE(created_at)'}
                ).values('date').annotate(
                    count=Count('id'),
                    amount=Sum('amount')
                ).order_by('date')

                return APIResponse.success(data={
                    'total_amount': float(total_amount),
                    'total_count': total_count,
                    'method_statistics': list(method_stats),
                    'daily_statistics': list(daily_stats)
                })

            except Exception as e:
                return APIResponse.error(message=f'统计失败: {str(e)}')

        return APIResponse.error(message='参数验证失败', errors=serializer.errors)


class PaymentConfigViewSet(viewsets.ModelViewSet):
    """支付配置管理ViewSet"""
    queryset = PaymentConfig.objects.all()
    serializer_class = PaymentConfigSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """只有管理员可以访问"""
        if self.request.user.is_staff:
            return PaymentConfig.objects.all()
        return PaymentConfig.objects.none()

    @action(detail=True, methods=['post'])
    def toggle_status(self, request, pk=None):
        """切换配置状态"""
        if not request.user.is_staff:
            return APIResponse.forbidden(message='无权限执行此操作')

        config = self.get_object()
        config.is_enabled = not config.is_enabled
        config.save()

        status_text = '启用' if config.is_enabled else '禁用'
        return APIResponse.success(message=f'支付配置已{status_text}')

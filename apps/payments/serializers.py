from rest_framework import serializers
from decimal import Decimal
from django.utils import timezone
from .models import Payment, Refund, PaymentConfig


class PaymentSerializer(serializers.ModelSerializer):
    """支付记录序列化器"""
    user_phone = serializers.CharField(source='user.phone', read_only=True)
    order_info = serializers.SerializerMethodField()
    
    class Meta:
        model = Payment
        fields = [
            'id', 'payment_no', 'user', 'user_phone', 'order', 'order_info',
            'payment_method', 'amount', 'status', 'transaction_id',
            'third_party_response', 'paid_at', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'payment_no', 'user_phone', 'order_info', 'created_at', 'updated_at']
    
    def get_order_info(self, obj):
        """获取订单信息"""
        if obj.order:
            order_data = {
                'id': obj.order.id,
                'order_no': obj.order.order_no,
                'package_name': obj.order.package_name,
                'final_price': float(obj.order.final_price),
                'status': obj.order.status,
                'status_text': self._get_status_text(obj.order.status),
                'payment_period': obj.order.payment_period,
                'months': obj.order.months,
                'service_start_date': obj.order.service_start_date,
                'service_end_date': obj.order.service_end_date
            }

            # 添加套餐信息（兼容前端期望的package_info字段）
            if obj.order.package:
                order_data['package_info'] = {
                    'id': obj.order.package.id,
                    'name': obj.order.package.name,
                    'package_type': obj.order.package.package_type,
                    'description': obj.order.package.description,
                    'features': obj.order.package.features,
                    'price': float(obj.order.final_price),
                    'original_price': float(obj.order.original_price),
                    'discount_amount': float(obj.order.discount_amount)
                }

            return order_data
        return None

    def _get_status_text(self, status):
        """获取状态文本"""
        status_map = {
            'pending': '待支付',
            'paid': '已支付',
            'active': '服务中',
            'expired': '已过期',
            'cancelled': '已取消',
            'refunded': '已退款'
        }
        return status_map.get(status, status)


class PaymentCreateSerializer(serializers.Serializer):
    """支付创建序列化器"""
    order_id = serializers.IntegerField()
    payment_method = serializers.ChoiceField(choices=[
        ('wechat', '微信支付'),
        ('alipay', '支付宝'),
    ])
    return_url = serializers.URLField(required=False, allow_blank=True)
    
    def validate_order_id(self, value):
        """验证订单ID"""
        from apps.services.models import Order
        try:
            order = Order.objects.get(
                id=value,
                user=self.context['request'].user,
                status='pending'
            )
            return order
        except Order.DoesNotExist:
            raise serializers.ValidationError('订单不存在或状态不正确')
    
    def validate(self, attrs):
        """验证支付数据"""
        order = attrs['order_id']
        
        # 检查是否已有未完成的支付
        existing_payment = Payment.objects.filter(
            order=order,
            status__in=['pending', 'processing']
        ).first()
        
        if existing_payment:
            raise serializers.ValidationError('该订单已有未完成的支付，请先处理')
        
        attrs['order'] = order
        attrs['amount'] = order.final_price
        
        return attrs


class RefundSerializer(serializers.ModelSerializer):
    """退款记录序列化器"""
    payment_info = serializers.SerializerMethodField()
    processed_by_name = serializers.CharField(source='processed_by.username', read_only=True)
    
    class Meta:
        model = Refund
        fields = [
            'id', 'refund_no', 'payment', 'payment_info', 'refund_amount',
            'refund_reason', 'status', 'processed_by', 'processed_by_name',
            'processed_at', 'third_party_response', 'created_at'
        ]
        read_only_fields = ['id', 'refund_no', 'payment_info', 'processed_by_name', 'created_at']
    
    def get_payment_info(self, obj):
        """获取支付信息"""
        if obj.payment:
            return {
                'id': obj.payment.id,
                'payment_no': obj.payment.payment_no,
                'amount': float(obj.payment.amount),
                'payment_method': obj.payment.payment_method
            }
        return None


class RefundCreateSerializer(serializers.Serializer):
    """退款创建序列化器"""
    payment_id = serializers.IntegerField()
    refund_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    refund_reason = serializers.CharField(max_length=500)
    
    def validate_payment_id(self, value):
        """验证支付ID"""
        try:
            payment = Payment.objects.get(
                id=value,
                user=self.context['request'].user,
                status='success'
            )
            return payment
        except Payment.DoesNotExist:
            raise serializers.ValidationError('支付记录不存在或状态不正确')
    
    def validate_refund_amount(self, value):
        """验证退款金额"""
        if value <= 0:
            raise serializers.ValidationError('退款金额必须大于0')
        return value
    
    def validate(self, attrs):
        """验证退款数据"""
        payment = attrs['payment_id']
        refund_amount = attrs['refund_amount']
        
        # 检查退款金额不能超过支付金额
        if refund_amount > payment.amount:
            raise serializers.ValidationError('退款金额不能超过支付金额')
        
        # 检查是否已有退款记录
        existing_refund = Refund.objects.filter(
            payment=payment,
            status__in=['pending', 'processing', 'success']
        ).first()
        
        if existing_refund:
            raise serializers.ValidationError('该支付已有退款记录')
        
        attrs['payment'] = payment
        return attrs


class PaymentConfigSerializer(serializers.ModelSerializer):
    """支付配置序列化器"""
    
    class Meta:
        model = PaymentConfig
        fields = [
            'id', 'payment_method', 'is_enabled', 'config_data',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate_config_data(self, value):
        """验证配置数据"""
        payment_method = self.initial_data.get('payment_method')
        
        if payment_method == 'wechat':
            required_fields = ['app_id', 'mch_id', 'api_key']
            for field in required_fields:
                if field not in value:
                    raise serializers.ValidationError(f'微信支付配置缺少必要字段: {field}')
        elif payment_method == 'alipay':
            required_fields = ['app_id', 'private_key', 'public_key']
            for field in required_fields:
                if field not in value:
                    raise serializers.ValidationError(f'支付宝配置缺少必要字段: {field}')
        
        return value


class WechatPayNotifySerializer(serializers.Serializer):
    """微信支付回调序列化器"""
    out_trade_no = serializers.CharField(max_length=100)
    transaction_id = serializers.CharField(max_length=100)
    total_fee = serializers.IntegerField()
    result_code = serializers.CharField(max_length=20)
    return_code = serializers.CharField(max_length=20)
    
    def validate(self, attrs):
        """验证回调数据"""
        if attrs['return_code'] != 'SUCCESS':
            raise serializers.ValidationError('微信支付回调失败')
        
        if attrs['result_code'] != 'SUCCESS':
            raise serializers.ValidationError('微信支付结果失败')
        
        return attrs


class AlipayNotifySerializer(serializers.Serializer):
    """支付宝回调序列化器"""
    out_trade_no = serializers.CharField(max_length=100)
    trade_no = serializers.CharField(max_length=100)
    total_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    trade_status = serializers.CharField(max_length=20)
    
    def validate_trade_status(self, value):
        """验证交易状态"""
        if value not in ['TRADE_SUCCESS', 'TRADE_FINISHED']:
            raise serializers.ValidationError('支付宝交易状态无效')
        return value


class PaymentStatisticsSerializer(serializers.Serializer):
    """支付统计序列化器"""
    start_date = serializers.DateField()
    end_date = serializers.DateField()
    payment_method = serializers.ChoiceField(
        choices=[('all', '全部'), ('wechat', '微信支付'), ('alipay', '支付宝')],
        default='all'
    )
    
    def validate(self, attrs):
        """验证日期范围"""
        start_date = attrs['start_date']
        end_date = attrs['end_date']
        
        if start_date >= end_date:
            raise serializers.ValidationError('开始日期必须早于结束日期')
        
        # 检查日期范围不能超过1年
        if (end_date - start_date).days > 365:
            raise serializers.ValidationError('统计日期范围不能超过1年')
        
        return attrs


class PaymentMethodSerializer(serializers.Serializer):
    """支付方式序列化器"""
    method = serializers.CharField()
    name = serializers.CharField()
    is_enabled = serializers.BooleanField()
    icon = serializers.CharField(required=False)
    description = serializers.CharField(required=False)

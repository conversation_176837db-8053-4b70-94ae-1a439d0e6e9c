from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import Payment, Refund, PaymentConfig


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    """支付记录管理"""
    list_display = ['payment_no', 'user_info', 'order_info', 'payment_method', 
                   'amount', 'status', 'paid_at', 'created_at']
    list_filter = ['payment_method', 'status', 'currency', 'paid_at', 'created_at']
    search_fields = ['payment_no', 'third_party_order_no', 'third_party_transaction_id',
                    'user__phone', 'user__nickname', 'order__order_no']
    readonly_fields = ['payment_no', 'created_at', 'updated_at', 'callback_time']
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('payment_no', 'order', 'user')
        }),
        ('支付信息', {
            'fields': ('payment_method', 'amount', 'currency', 'status', 'paid_at')
        }),
        ('第三方信息', {
            'fields': ('third_party_order_no', 'third_party_transaction_id')
        }),
        ('回调信息', {
            'fields': ('callback_data', 'callback_time'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def user_info(self, obj):
        if obj.user:
            return format_html(
                '<a href="{}">{}</a><br/><small>{}</small>',
                reverse('admin:users_user_change', args=[obj.user.pk]),
                obj.user.nickname or obj.user.phone,
                obj.user.phone
            )
        return '-'
    user_info.short_description = '用户'
    
    def order_info(self, obj):
        if obj.order:
            return format_html(
                '<a href="{}">{}</a><br/><small>{}</small>',
                reverse('admin:services_order_change', args=[obj.order.pk]),
                obj.order.order_no,
                obj.order.package_name
            )
        return '-'
    order_info.short_description = '订单'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'order')


@admin.register(Refund)
class RefundAdmin(admin.ModelAdmin):
    """退款记录管理"""
    list_display = ['refund_no', 'payment_info', 'refund_amount', 'refund_reason',
                   'status', 'refunded_at', 'created_at']
    list_filter = ['status', 'refund_type', 'refunded_at', 'created_at']
    search_fields = ['refund_no', 'payment__payment_no', 'third_party_refund_id', 'refund_reason']
    readonly_fields = ['refund_no', 'created_at', 'updated_at']
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('refund_no', 'payment', 'processed_by')
        }),
        ('退款信息', {
            'fields': ('refund_type', 'refund_amount', 'refund_reason', 'status', 'refunded_at')
        }),
        ('第三方信息', {
            'fields': ('third_party_refund_id',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def payment_info(self, obj):
        if obj.payment:
            return format_html(
                '<a href="{}">{}</a><br/><small>¥{}</small>',
                reverse('admin:payments_payment_change', args=[obj.payment.pk]),
                obj.payment.payment_no,
                obj.payment.amount
            )
        return '-'
    payment_info.short_description = '原支付'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('payment', 'processed_by')


@admin.register(PaymentConfig)
class PaymentConfigAdmin(admin.ModelAdmin):
    """支付配置管理"""
    list_display = ['payment_method', 'is_enabled', 'mch_id', 'updated_at']
    list_filter = ['payment_method', 'is_enabled']
    search_fields = ['payment_method', 'mch_id', 'app_id']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('payment_method', 'is_enabled')
        }),
        ('配置信息', {
            'fields': ('app_id', 'mch_id', 'api_key', 'app_secret')
        }),
        ('回调配置', {
            'fields': ('notify_url', 'return_url')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'payments', views.PaymentViewSet, basename='payment')
router.register(r'refunds', views.RefundViewSet, basename='refund')
router.register(r'configs', views.PaymentConfigViewSet, basename='payment-config')

urlpatterns = [
    # 支付相关
    path('create/', views.CreatePaymentView.as_view(), name='create_payment'),
    path('methods/', views.PaymentMethodsView.as_view(), name='payment_methods'),
    path('statistics/', views.PaymentStatisticsView.as_view(), name='payment_statistics'),

    # 支付状态和重试
    path('<int:payment_id>/status/', views.PaymentStatusView.as_view(), name='payment_status'),
    path('<int:payment_id>/retry/', views.PaymentRetryView.as_view(), name='payment_retry'),
    path('compensation/', views.PaymentCompensationView.as_view(), name='payment_compensation'),

    # 支付回调
    path('wechat/notify/', views.WechatPayNotifyView.as_view(), name='wechat_pay_notify'),
    path('alipay/notify/', views.AlipayNotifyView.as_view(), name='alipay_notify'),

    # 退款相关
    path('refund/apply/', views.ApplyRefundView.as_view(), name='apply_refund'),

    # 其他路由
    path('', include(router.urls)),
]

from django.db import models
from django.conf import settings
from decimal import Decimal


class Payment(models.Model):
    """支付记录"""
    PAYMENT_METHOD_CHOICES = [
        ('wechat', '微信支付'),
        ('alipay', '支付宝'),
        ('bank_transfer', '银行转账'),
        ('offline', '线下支付'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '待支付'),
        ('processing', '支付中'),
        ('success', '支付成功'),
        ('failed', '支付失败'),
        ('cancelled', '已取消'),
        ('refunded', '已退款'),
    ]
    
    # 基本信息
    payment_no = models.CharField('支付单号', max_length=32, unique=True)
    order = models.ForeignKey('services.Order', on_delete=models.CASCADE, related_name='payments', verbose_name='关联订单')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='payments', verbose_name='支付用户')
    
    # 支付信息
    payment_method = models.CharField('支付方式', max_length=20, choices=PAYMENT_METHOD_CHOICES)
    amount = models.DecimalField('支付金额', max_digits=10, decimal_places=2)
    currency = models.CharField('货币类型', max_length=3, default='CNY')
    
    # 第三方支付信息
    third_party_order_no = models.CharField('第三方订单号', max_length=100, blank=True)
    third_party_transaction_id = models.CharField('第三方交易号', max_length=100, blank=True)
    
    # 支付状态
    status = models.CharField('支付状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    paid_at = models.DateTimeField('支付时间', blank=True, null=True)
    
    # 回调信息
    callback_data = models.JSONField('回调数据', default=dict, blank=True)
    callback_time = models.DateTimeField('回调时间', blank=True, null=True)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'payments'
        verbose_name = '支付记录'
        verbose_name_plural = '支付记录'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.payment_no} - {self.amount}"
    
    def save(self, *args, **kwargs):
        if not self.payment_no:
            self.payment_no = self.generate_payment_no()
        super().save(*args, **kwargs)
    
    def generate_payment_no(self):
        """生成支付单号"""
        import time
        import random
        timestamp = str(int(time.time()))
        random_num = str(random.randint(1000, 9999))
        return f"PAY{timestamp}{random_num}"


class Refund(models.Model):
    """退款记录"""
    STATUS_CHOICES = [
        ('pending', '退款中'),
        ('success', '退款成功'),
        ('failed', '退款失败'),
    ]
    
    REFUND_TYPE_CHOICES = [
        ('full', '全额退款'),
        ('partial', '部分退款'),
    ]
    
    payment = models.ForeignKey(Payment, on_delete=models.CASCADE, related_name='refunds', verbose_name='原支付记录')
    refund_no = models.CharField('退款单号', max_length=32, unique=True)
    
    # 退款信息
    refund_type = models.CharField('退款类型', max_length=20, choices=REFUND_TYPE_CHOICES)
    refund_amount = models.DecimalField('退款金额', max_digits=10, decimal_places=2)
    refund_reason = models.TextField('退款原因')
    
    # 第三方退款信息
    third_party_refund_id = models.CharField('第三方退款号', max_length=100, blank=True)
    
    # 退款状态
    status = models.CharField('退款状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    refunded_at = models.DateTimeField('退款时间', blank=True, null=True)
    
    # 处理信息
    processed_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, 
                                   blank=True, null=True, related_name='processed_refunds', verbose_name='处理人')
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'refunds'
        verbose_name = '退款记录'
        verbose_name_plural = '退款记录'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.refund_no} - {self.refund_amount}"
    
    def save(self, *args, **kwargs):
        if not self.refund_no:
            self.refund_no = self.generate_refund_no()
        super().save(*args, **kwargs)
    
    def generate_refund_no(self):
        """生成退款单号"""
        import time
        import random
        timestamp = str(int(time.time()))
        random_num = str(random.randint(1000, 9999))
        return f"REF{timestamp}{random_num}"


class PaymentConfig(models.Model):
    """支付配置"""
    PAYMENT_METHOD_CHOICES = [
        ('wechat', '微信支付'),
        ('alipay', '支付宝'),
    ]
    
    payment_method = models.CharField('支付方式', max_length=20, choices=PAYMENT_METHOD_CHOICES, unique=True)
    is_enabled = models.BooleanField('是否启用', default=True)
    
    # 配置参数
    app_id = models.CharField('应用ID', max_length=100, blank=True)
    mch_id = models.CharField('商户号', max_length=100, blank=True)
    api_key = models.CharField('API密钥', max_length=200, blank=True)
    app_secret = models.CharField('应用密钥', max_length=200, blank=True)
    
    # 其他配置
    notify_url = models.URLField('回调地址', blank=True)
    return_url = models.URLField('返回地址', blank=True)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'payment_configs'
        verbose_name = '支付配置'
        verbose_name_plural = '支付配置'
    
    def __str__(self):
        return f"{self.get_payment_method_display()}配置"

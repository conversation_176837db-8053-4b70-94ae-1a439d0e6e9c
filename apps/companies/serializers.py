from rest_framework import serializers
from .models import Company, CompanyDocument, CompanyMember, CompanySettings


class CompanySerializer(serializers.ModelSerializer):
    """企业序列化器"""
    owner_phone = serializers.CharField(source='owner.phone', read_only=True)
    is_verified = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Company
        fields = [
            'id', 'name', 'short_name', 'unified_social_credit_code',
            'taxpayer_identification_number', 'taxpayer_type', 'industry',
            'status', 'legal_representative', 'contact_person', 'contact_phone',
            'contact_email', 'registered_address', 'business_address',
            'postal_code', 'business_scope', 'registered_capital',
            'establishment_date', 'business_term_start', 'business_term_end',
            'tax_registration_date', 'tax_authority', 'bank_name', 'bank_account',
            'owner_phone', 'is_verified', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'status', 'owner_phone', 'is_verified', 'created_at', 'updated_at']
    
    def validate_unified_social_credit_code(self, value):
        """验证统一社会信用代码"""
        if value and len(value) != 18:
            raise serializers.ValidationError('统一社会信用代码必须为18位')
        
        # 检查是否已存在（排除当前实例）
        instance = getattr(self, 'instance', None)
        if Company.objects.filter(unified_social_credit_code=value).exclude(
            id=instance.id if instance else None
        ).exists():
            raise serializers.ValidationError('该统一社会信用代码已存在')
        
        return value
    
    def validate_contact_phone(self, value):
        """验证联系电话"""
        import re
        if value and not re.match(r'^1[3-9]\d{9}$', value):
            raise serializers.ValidationError('联系电话格式不正确')
        return value


class CompanyCreateSerializer(serializers.ModelSerializer):
    """企业创建序列化器"""
    
    class Meta:
        model = Company
        fields = [
            'name', 'short_name', 'unified_social_credit_code',
            'taxpayer_identification_number', 'taxpayer_type', 'industry',
            'legal_representative', 'contact_person', 'contact_phone',
            'contact_email', 'registered_address', 'business_address',
            'postal_code', 'business_scope', 'registered_capital',
            'establishment_date', 'business_term_start', 'business_term_end',
            'tax_registration_date', 'tax_authority', 'bank_name', 'bank_account'
        ]
    
    def validate_name(self, value):
        """验证企业名称"""
        if len(value) < 2:
            raise serializers.ValidationError('企业名称至少2个字符')
        return value
    
    def create(self, validated_data):
        """创建企业"""
        user = self.context['request'].user
        validated_data['owner'] = user
        return super().create(validated_data)


class CompanyDocumentSerializer(serializers.ModelSerializer):
    """企业证件序列化器"""
    company_name = serializers.CharField(source='company.name', read_only=True)
    file_url = serializers.SerializerMethodField()
    
    class Meta:
        model = CompanyDocument
        fields = [
            'id', 'company', 'company_name', 'document_type', 'document_number',
            'document_name', 'file_path', 'file_url', 'file_size', 'is_verified',
            'verified_at', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'company_name', 'file_size', 'is_verified', 'verified_at', 'created_at', 'updated_at']
    
    def get_file_url(self, obj):
        """获取文件URL"""
        if obj.file_path:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.file_path.url)
            return obj.file_path.url
        return None
    
    def validate_file_path(self, value):
        """验证文件"""
        if value:
            # 检查文件大小（10MB限制）
            if value.size > 10 * 1024 * 1024:
                raise serializers.ValidationError('文件大小不能超过10MB')
            
            # 检查文件格式
            allowed_extensions = ['.pdf', '.jpg', '.jpeg', '.png']
            import os
            ext = os.path.splitext(value.name)[1].lower()
            if ext not in allowed_extensions:
                raise serializers.ValidationError('只支持PDF、JPG、PNG格式的文件')
        
        return value


class CompanyMemberSerializer(serializers.ModelSerializer):
    """企业成员序列化器"""
    user_phone = serializers.CharField(source='user.phone', read_only=True)
    user_nickname = serializers.CharField(source='user.nickname', read_only=True)
    company_name = serializers.CharField(source='company.name', read_only=True)
    
    class Meta:
        model = CompanyMember
        fields = [
            'id', 'company', 'company_name', 'user', 'user_phone', 'user_nickname',
            'role', 'permissions', 'is_active', 'joined_at'
        ]
        read_only_fields = ['id', 'company_name', 'user_phone', 'user_nickname', 'joined_at']


class CompanySettingsSerializer(serializers.ModelSerializer):
    """企业设置序列化器"""
    company_name = serializers.CharField(source='company.name', read_only=True)
    
    class Meta:
        model = CompanySettings
        fields = [
            'id', 'company', 'company_name', 'accounting_period', 'fiscal_year_start',
            'notification_enabled', 'email_notification', 'sms_notification',
            'wechat_notification', 'auto_backup', 'data_retention_days',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'company_name', 'created_at', 'updated_at']
    
    def validate_fiscal_year_start(self, value):
        """验证财务年度开始月份"""
        if value not in [f'{i:02d}' for i in range(1, 13)]:
            raise serializers.ValidationError('财务年度开始月份必须在01-12之间')
        return value
    
    def validate_data_retention_days(self, value):
        """验证数据保留天数"""
        if value < 30:
            raise serializers.ValidationError('数据保留天数不能少于30天')
        if value > 3650:  # 10年
            raise serializers.ValidationError('数据保留天数不能超过3650天')
        return value


class CompanyVerifySerializer(serializers.Serializer):
    """企业认证序列化器"""
    business_license = serializers.IntegerField(help_text='营业执照文件ID')
    tax_registration = serializers.IntegerField(required=False, help_text='税务登记证文件ID')
    
    def validate_business_license(self, value):
        """验证营业执照文件"""
        try:
            document = CompanyDocument.objects.get(
                id=value,
                document_type='business_license',
                company__owner=self.context['request'].user
            )
            return document
        except CompanyDocument.DoesNotExist:
            raise serializers.ValidationError('营业执照文件不存在或无权限访问')
    
    def validate_tax_registration(self, value):
        """验证税务登记证文件"""
        if value:
            try:
                document = CompanyDocument.objects.get(
                    id=value,
                    document_type='tax_registration',
                    company__owner=self.context['request'].user
                )
                return document
            except CompanyDocument.DoesNotExist:
                raise serializers.ValidationError('税务登记证文件不存在或无权限访问')
        return None


class CompanyListSerializer(serializers.ModelSerializer):
    """企业列表序列化器（简化版）"""
    is_verified = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Company
        fields = [
            'id', 'name', 'short_name', 'taxpayer_type', 'status',
            'is_verified', 'created_at'
        ]

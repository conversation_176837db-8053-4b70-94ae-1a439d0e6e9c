from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'companies', views.CompanyViewSet, basename='company')
router.register(r'documents', views.CompanyDocumentViewSet, basename='company-document')
router.register(r'members', views.CompanyMemberViewSet, basename='company-member')

urlpatterns = [
    # 企业信息
    path('info/', views.CompanyInfoView.as_view(), name='company_info'),
    path('create/', views.CreateCompanyView.as_view(), name='create_company'),
    path('verify/', views.CompanyVerifyView.as_view(), name='company_verify'),
    path('settings/', views.CompanySettingsView.as_view(), name='company_settings'),
    
    # 其他路由
    path('', include(router.urls)),
]

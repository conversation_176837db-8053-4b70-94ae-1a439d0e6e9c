from django.contrib import admin
from django.utils.html import format_html
from .models import Company, CompanyDocument, CompanyMember, CompanySettings


@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    """企业管理"""
    list_display = ['name', 'owner', 'taxpayer_type', 'status', 'is_verified', 'created_at']
    list_filter = ['taxpayer_type', 'status', 'industry', 'created_at']
    search_fields = ['name', 'short_name', 'unified_social_credit_code', 'owner__phone']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('owner', 'name', 'short_name', 'unified_social_credit_code', 
                      'taxpayer_identification_number')
        }),
        ('企业类型', {
            'fields': ('taxpayer_type', 'industry', 'status')
        }),
        ('联系信息', {
            'fields': ('legal_representative', 'contact_person', 'contact_phone', 'contact_email')
        }),
        ('地址信息', {
            'fields': ('registered_address', 'business_address', 'postal_code'),
            'classes': ('collapse',)
        }),
        ('经营信息', {
            'fields': ('business_scope', 'registered_capital', 'establishment_date', 
                      'business_term_start', 'business_term_end'),
            'classes': ('collapse',)
        }),
        ('税务信息', {
            'fields': ('tax_registration_date', 'tax_authority'),
            'classes': ('collapse',)
        }),
        ('银行信息', {
            'fields': ('bank_name', 'bank_account'),
            'classes': ('collapse',)
        }),
        ('审核信息', {
            'fields': ('verified_at', 'verified_by', 'rejection_reason')
        }),
    )
    
    def is_verified(self, obj):
        if obj.status == 'verified':
            return format_html('<span style="color: green;">✓ 已认证</span>')
        elif obj.status == 'pending':
            return format_html('<span style="color: orange;">⏳ 待审核</span>')
        elif obj.status == 'rejected':
            return format_html('<span style="color: red;">✗ 已拒绝</span>')
        else:
            return format_html('<span style="color: gray;">⏸ 已暂停</span>')
    is_verified.short_description = '认证状态'


@admin.register(CompanyDocument)
class CompanyDocumentAdmin(admin.ModelAdmin):
    """企业证件管理"""
    list_display = ['company', 'document_type', 'document_name', 'is_verified', 'created_at']
    list_filter = ['document_type', 'is_verified', 'created_at']
    search_fields = ['company__name', 'document_name', 'document_number']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('company', 'document_type', 'document_name', 'document_number')
        }),
        ('文件信息', {
            'fields': ('file_path', 'file_size')
        }),
        ('审核信息', {
            'fields': ('is_verified', 'verified_at', 'verified_by')
        }),
    )


@admin.register(CompanyMember)
class CompanyMemberAdmin(admin.ModelAdmin):
    """企业成员管理"""
    list_display = ['company', 'user', 'role', 'is_active', 'joined_at']
    list_filter = ['role', 'is_active', 'joined_at']
    search_fields = ['company__name', 'user__phone']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('company', 'user', 'role')
        }),
        ('权限配置', {
            'fields': ('permissions', 'is_active')
        }),
    )


@admin.register(CompanySettings)
class CompanySettingsAdmin(admin.ModelAdmin):
    """企业设置管理"""
    list_display = ['company', 'accounting_period', 'notification_enabled', 'auto_backup']
    list_filter = ['accounting_period', 'notification_enabled', 'auto_backup']
    search_fields = ['company__name']
    
    fieldsets = (
        ('财务设置', {
            'fields': ('company', 'accounting_period', 'fiscal_year_start')
        }),
        ('通知设置', {
            'fields': ('notification_enabled', 'email_notification', 'sms_notification', 'wechat_notification')
        }),
        ('其他设置', {
            'fields': ('auto_backup', 'data_retention_days')
        }),
    )

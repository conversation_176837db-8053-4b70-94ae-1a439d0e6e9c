from django.db import models
from django.conf import settings


class Company(models.Model):
    """企业信息模型"""
    TAXPAYER_TYPE_CHOICES = [
        ('individual', '个体户'),
        ('small_scale', '小规模纳税人'),
        ('general', '一般纳税人'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '待审核'),
        ('verified', '已认证'),
        ('rejected', '审核拒绝'),
        ('suspended', '已暂停'),
    ]
    
    INDUSTRY_CHOICES = [
        ('technology', '科技行业'),
        ('manufacturing', '制造业'),
        ('service', '服务业'),
        ('retail', '零售业'),
        ('finance', '金融业'),
        ('education', '教育行业'),
        ('healthcare', '医疗健康'),
        ('real_estate', '房地产'),
        ('construction', '建筑业'),
        ('agriculture', '农业'),
        ('other', '其他'),
    ]
    
    # 基本信息
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='companies', verbose_name='企业主')
    name = models.CharField('企业名称', max_length=200)
    short_name = models.CharField('企业简称', max_length=100, blank=True)
    unified_social_credit_code = models.CharField('统一社会信用代码', max_length=18, unique=True, blank=True, null=True)
    taxpayer_identification_number = models.CharField('纳税人识别号', max_length=20, blank=True)
    
    # 企业类型和状态
    taxpayer_type = models.CharField('纳税人类型', max_length=20, choices=TAXPAYER_TYPE_CHOICES)
    industry = models.CharField('所属行业', max_length=20, choices=INDUSTRY_CHOICES, default='other')
    status = models.CharField('认证状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # 联系信息
    legal_representative = models.CharField('法定代表人', max_length=50, blank=True)
    contact_person = models.CharField('联系人', max_length=50, blank=True)
    contact_phone = models.CharField('联系电话', max_length=20, blank=True)
    contact_email = models.EmailField('联系邮箱', blank=True)
    
    # 地址信息
    registered_address = models.TextField('注册地址', blank=True)
    business_address = models.TextField('经营地址', blank=True)
    postal_code = models.CharField('邮政编码', max_length=6, blank=True)
    
    # 经营信息
    business_scope = models.TextField('经营范围', blank=True)
    registered_capital = models.DecimalField('注册资本', max_digits=15, decimal_places=2, blank=True, null=True)
    establishment_date = models.DateField('成立日期', blank=True, null=True)
    business_term_start = models.DateField('营业期限开始', blank=True, null=True)
    business_term_end = models.DateField('营业期限结束', blank=True, null=True)
    
    # 税务信息
    tax_registration_date = models.DateField('税务登记日期', blank=True, null=True)
    tax_authority = models.CharField('主管税务机关', max_length=100, blank=True)
    
    # 银行信息
    bank_name = models.CharField('开户银行', max_length=100, blank=True)
    bank_account = models.CharField('银行账号', max_length=30, blank=True)
    
    # 审核信息
    verified_at = models.DateTimeField('认证时间', blank=True, null=True)
    verified_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, 
                                   blank=True, null=True, related_name='verified_companies', verbose_name='审核人')
    rejection_reason = models.TextField('拒绝原因', blank=True)
    
    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'companies'
        verbose_name = '企业'
        verbose_name_plural = '企业管理'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.name
    
    @property
    def is_verified(self):
        """是否已认证"""
        return self.status == 'verified'


class CompanyDocument(models.Model):
    """企业证件文档"""
    DOCUMENT_TYPE_CHOICES = [
        ('business_license', '营业执照'),
        ('tax_registration', '税务登记证'),
        ('organization_code', '组织机构代码证'),
        ('bank_permit', '开户许可证'),
        ('legal_id_front', '法人身份证正面'),
        ('legal_id_back', '法人身份证背面'),
        ('other', '其他证件'),
    ]
    
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='documents', verbose_name='所属企业')
    document_type = models.CharField('证件类型', max_length=20, choices=DOCUMENT_TYPE_CHOICES)
    document_number = models.CharField('证件号码', max_length=50, blank=True)
    document_name = models.CharField('证件名称', max_length=100, blank=True)
    file_path = models.FileField('文件路径', upload_to='company_documents/')
    file_size = models.PositiveIntegerField('文件大小', default=0)
    
    # 审核状态
    is_verified = models.BooleanField('是否已审核', default=False)
    verified_at = models.DateTimeField('审核时间', blank=True, null=True)
    verified_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, 
                                   blank=True, null=True, related_name='verified_documents', verbose_name='审核人')
    
    created_at = models.DateTimeField('上传时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'company_documents'
        verbose_name = '企业证件'
        verbose_name_plural = '企业证件'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.company.name} - {self.get_document_type_display()}"


class CompanyMember(models.Model):
    """企业成员"""
    ROLE_CHOICES = [
        ('owner', '企业主'),
        ('admin', '管理员'),
        ('accountant', '会计'),
        ('viewer', '查看者'),
    ]
    
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='members', verbose_name='所属企业')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='company_memberships', verbose_name='用户')
    role = models.CharField('角色', max_length=20, choices=ROLE_CHOICES)
    permissions = models.JSONField('权限配置', default=dict, blank=True)
    
    is_active = models.BooleanField('是否激活', default=True)
    joined_at = models.DateTimeField('加入时间', auto_now_add=True)
    
    class Meta:
        db_table = 'company_members'
        verbose_name = '企业成员'
        verbose_name_plural = '企业成员'
        unique_together = ['company', 'user']
        ordering = ['-joined_at']
    
    def __str__(self):
        return f"{self.company.name} - {self.user.phone} ({self.get_role_display()})"


class CompanySettings(models.Model):
    """企业设置"""
    company = models.OneToOneField(Company, on_delete=models.CASCADE, related_name='settings', verbose_name='所属企业')
    
    # 财务设置
    accounting_period = models.CharField('会计期间', max_length=20, choices=[
        ('monthly', '按月'),
        ('quarterly', '按季度'),
        ('yearly', '按年'),
    ], default='monthly')
    
    fiscal_year_start = models.CharField('财务年度开始月份', max_length=2, default='01')
    
    # 通知设置
    notification_enabled = models.BooleanField('启用通知', default=True)
    email_notification = models.BooleanField('邮件通知', default=True)
    sms_notification = models.BooleanField('短信通知', default=True)
    wechat_notification = models.BooleanField('微信通知', default=True)
    
    # 其他设置
    auto_backup = models.BooleanField('自动备份', default=True)
    data_retention_days = models.PositiveIntegerField('数据保留天数', default=365)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'company_settings'
        verbose_name = '企业设置'
        verbose_name_plural = '企业设置'
    
    def __str__(self):
        return f"{self.company.name}的设置"

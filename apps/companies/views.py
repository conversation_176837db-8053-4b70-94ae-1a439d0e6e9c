from rest_framework import status, viewsets, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.utils import timezone

from .models import Company, CompanyDocument, CompanyMember, CompanySettings
from .serializers import (
    CompanySerializer, CompanyCreateSerializer, CompanyDocumentSerializer,
    CompanyMemberSerializer, CompanySettingsSerializer, CompanyVerifySerializer,
    CompanyListSerializer
)
from utils.response import APIResponse
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class CompanyInfoView(APIView):
    """获取企业信息"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取当前用户的企业信息"""
        try:
            company = Company.objects.get(owner=request.user)
            serializer = CompanySerializer(company, context={'request': request})
            return APIResponse.success(data=serializer.data)
        except Company.DoesNotExist:
            return APIResponse.error(message='企业信息不存在', code=404)


class CreateCompanyView(APIView):
    """创建企业"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """创建企业信息"""
        # 检查用户是否已有企业
        if Company.objects.filter(owner=request.user).exists():
            return APIResponse.error(message='您已经创建过企业，一个用户只能创建一个企业')
        
        serializer = CompanyCreateSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            company = serializer.save()
            
            # 创建企业设置
            CompanySettings.objects.create(company=company)
            
            # 添加企业主为成员
            CompanyMember.objects.create(
                company=company,
                user=request.user,
                role='owner'
            )
            
            return APIResponse.created(
                data=CompanySerializer(company, context={'request': request}).data,
                message='企业创建成功'
            )
        
        return APIResponse.error(message='企业创建失败', errors=serializer.errors)


class CompanyVerifyView(APIView):
    """企业认证"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """提交企业认证"""
        try:
            company = Company.objects.get(owner=request.user)
        except Company.DoesNotExist:
            return APIResponse.error(message='请先创建企业信息', code=404)
        
        if company.status == 'verified':
            return APIResponse.error(message='企业已通过认证')
        
        serializer = CompanyVerifySerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            # 更新企业状态为待审核
            company.status = 'pending'
            company.save()
            
            return APIResponse.success(message='认证申请提交成功，请等待审核')
        
        return APIResponse.error(message='认证申请失败', errors=serializer.errors)


class CompanySettingsView(APIView):
    """企业设置"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取企业设置"""
        try:
            company = Company.objects.get(owner=request.user)
            settings, created = CompanySettings.objects.get_or_create(company=company)
            serializer = CompanySettingsSerializer(settings, context={'request': request})
            return APIResponse.success(data=serializer.data)
        except Company.DoesNotExist:
            return APIResponse.error(message='企业信息不存在', code=404)
    
    def put(self, request):
        """更新企业设置"""
        try:
            company = Company.objects.get(owner=request.user)
            settings, created = CompanySettings.objects.get_or_create(company=company)
            
            serializer = CompanySettingsSerializer(
                settings, 
                data=request.data, 
                partial=True,
                context={'request': request}
            )
            if serializer.is_valid():
                serializer.save()
                return APIResponse.success(data=serializer.data, message='设置更新成功')
            
            return APIResponse.error(message='设置更新失败', errors=serializer.errors)
        except Company.DoesNotExist:
            return APIResponse.error(message='企业信息不存在', code=404)


class CompanyViewSet(viewsets.ModelViewSet):
    """企业管理ViewSet"""
    serializer_class = CompanySerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """获取查询集"""
        if self.request.user.is_staff:
            return Company.objects.all()
        return Company.objects.filter(owner=self.request.user)
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return CompanyListSerializer
        elif self.action == 'create':
            return CompanyCreateSerializer
        return CompanySerializer
    
    def create(self, request, *args, **kwargs):
        """创建企业"""
        # 检查用户是否已有企业
        if Company.objects.filter(owner=request.user).exists():
            return APIResponse.error(message='您已经创建过企业，一个用户只能创建一个企业')
        
        return super().create(request, *args, **kwargs)
    
    @action(detail=True, methods=['post'])
    def verify(self, request, pk=None):
        """企业认证（管理员操作）"""
        if not request.user.is_staff:
            return APIResponse.forbidden(message='无权限执行此操作')
        
        company = self.get_object()
        action_type = request.data.get('action')  # 'approve' or 'reject'
        reason = request.data.get('reason', '')
        
        if action_type == 'approve':
            company.status = 'verified'
            company.verified_at = timezone.now()
            company.verified_by = request.user
            company.save()
            return APIResponse.success(message='企业认证通过')
        elif action_type == 'reject':
            company.status = 'rejected'
            company.rejection_reason = reason
            company.save()
            return APIResponse.success(message='企业认证已拒绝')
        else:
            return APIResponse.error(message='无效的操作类型')


class CompanyDocumentViewSet(viewsets.ModelViewSet):
    """企业证件管理ViewSet"""
    serializer_class = CompanyDocumentSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """获取查询集"""
        if self.request.user.is_staff:
            return CompanyDocument.objects.all()
        return CompanyDocument.objects.filter(company__owner=self.request.user)
    
    def perform_create(self, serializer):
        """创建证件时设置企业"""
        try:
            company = Company.objects.get(owner=self.request.user)
            serializer.save(company=company)
        except Company.DoesNotExist:
            raise serializers.ValidationError('请先创建企业信息')
    
    @action(detail=True, methods=['post'])
    def verify(self, request, pk=None):
        """证件审核（管理员操作）"""
        if not request.user.is_staff:
            return APIResponse.forbidden(message='无权限执行此操作')
        
        document = self.get_object()
        action_type = request.data.get('action')  # 'approve' or 'reject'
        
        if action_type == 'approve':
            document.is_verified = True
            document.verified_at = timezone.now()
            document.verified_by = request.user
            document.save()
            return APIResponse.success(message='证件审核通过')
        elif action_type == 'reject':
            document.is_verified = False
            document.save()
            return APIResponse.success(message='证件审核已拒绝')
        else:
            return APIResponse.error(message='无效的操作类型')


class CompanyMemberViewSet(viewsets.ModelViewSet):
    """企业成员管理ViewSet"""
    serializer_class = CompanyMemberSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """获取查询集"""
        if self.request.user.is_staff:
            return CompanyMember.objects.all()
        
        # 获取用户所属的企业
        try:
            company = Company.objects.get(owner=self.request.user)
            return CompanyMember.objects.filter(company=company)
        except Company.DoesNotExist:
            return CompanyMember.objects.none()
    
    def perform_create(self, serializer):
        """添加成员时设置企业"""
        try:
            company = Company.objects.get(owner=self.request.user)
            serializer.save(company=company)
        except Company.DoesNotExist:
            raise serializers.ValidationError('请先创建企业信息')
    
    @action(detail=True, methods=['post'])
    def change_role(self, request, pk=None):
        """修改成员角色"""
        member = self.get_object()
        
        # 检查权限（只有企业主可以修改角色）
        if member.company.owner != request.user:
            return APIResponse.forbidden(message='只有企业主可以修改成员角色')
        
        # 不能修改自己的角色
        if member.user == request.user:
            return APIResponse.error(message='不能修改自己的角色')
        
        new_role = request.data.get('role')
        if new_role not in dict(CompanyMember.ROLE_CHOICES):
            return APIResponse.error(message='无效的角色类型')
        
        member.role = new_role
        member.save()
        
        return APIResponse.success(message='角色修改成功')
    
    @action(detail=True, methods=['post'])
    def remove(self, request, pk=None):
        """移除成员"""
        member = self.get_object()
        
        # 检查权限（只有企业主可以移除成员）
        if member.company.owner != request.user:
            return APIResponse.forbidden(message='只有企业主可以移除成员')
        
        # 不能移除自己
        if member.user == request.user:
            return APIResponse.error(message='不能移除自己')
        
        member.delete()
        return APIResponse.success(message='成员移除成功')

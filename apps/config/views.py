import logging
from django.core.exceptions import ValidationError, PermissionDenied
from django.http import JsonResponse
from rest_framework import status

logger = logging.getLogger(__name__)

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from django.utils import timezone
from django.db.models import Q
from .models import (
    AppConfig, BannerConfig, ServicePackageConfig,
    CityConfig, NewsConfig, ReviewConfig
)
from .serializers import (
    AppConfigSerializer, BannerConfigSerializer, ServicePackageConfigSerializer,
    CityConfigSerializer, NewsConfigSerializer, ReviewConfigSerializer,
    AppConfigBatchSerializer
)
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class AppConfigViewSet(viewsets.ReadOnlyModelViewSet):
    """应用配置API"""
    queryset = AppConfig.objects.filter(is_active=True)
    serializer_class = AppConfigSerializer
    permission_classes = [AllowAny]
    lookup_field = 'key'
    
    @action(detail=False, methods=['post'])
    def batch_get(self, request):
        """批量获取配置"""
        try:
            serializer = AppConfigBatchSerializer(data=request.data)
            if serializer.is_valid():
                keys = serializer.validated_data['keys']
                category = serializer.validated_data.get('category')
            
                queryset = self.get_queryset().filter(key__in=keys)
                if category:
                    queryset = queryset.filter(category=category)
            
                configs = {}
                for config in queryset:
                    configs[config.key] = config.get_value()
            
                return Response({
                    'success': True,
                    'data': configs
                })
            return Response({
                'success': False,
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=False, methods=['get'])
    def by_category(self, request):
        """按分类获取配置"""
        try:
            category = request.query_params.get('category')
            if not category:
                return Response({
                    'success': False,
                    'message': '请提供配置分类'
                }, status=status.HTTP_400_BAD_REQUEST)
        
            configs = self.get_queryset().filter(category=category)
            result = {}
            for config in configs:
                result[config.key] = config.get_value()
        
            return Response({
                'success': True,
                'data': result
            })


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class BannerConfigViewSet(viewsets.ReadOnlyModelViewSet):
    """轮播图配置API"""
    serializer_class = BannerConfigSerializer
    permission_classes = [AllowAny]
    
    def get_queryset(self):
        try:
            now = timezone.now()
            return BannerConfig.objects.filter(
                is_active=True
            ).filter(
                Q(start_time__isnull=True) | Q(start_time__lte=now)
            ).filter(
                Q(end_time__isnull=True) | Q(end_time__gte=now)
            ).order_by('sort_order', '-created_at')


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class ServicePackageConfigViewSet(viewsets.ReadOnlyModelViewSet):
    """服务套餐配置API"""
    queryset = ServicePackageConfig.objects.filter(is_active=True).order_by('sort_order', 'package_type')
    serializer_class = ServicePackageConfigSerializer
    permission_classes = [AllowAny]
    
    @action(detail=False, methods=['get'])
    def by_type(self, request):
        """按类型获取套餐"""
        try:
            package_type = request.query_params.get('type')
            if package_type:
                packages = self.get_queryset().filter(package_type=package_type)
            else:
                packages = self.get_queryset()
        
            serializer = self.get_serializer(packages, many=True)
            return Response({
                'success': True,
                'data': serializer.data
            })
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=False, methods=['get'])
    def comparison_data(self, request):
        """获取套餐对比数据"""
        try:
            packages = self.get_queryset()
        
            # 构建对比数据
            comparison_data = []
        
            # 基础对比项目
            base_features = [
                {'feature': '记账凭证数量', 'key': 'voucher_limit'},
                {'feature': '财务报表', 'key': 'financial_reports'},
                {'feature': '税务申报', 'key': 'tax_filing'},
                {'feature': '专属会计师', 'key': 'dedicated_accountant'},
                {'feature': '税务筹划', 'key': 'tax_planning'},
                {'feature': '发票管理', 'key': 'invoice_management'},
                {'feature': '客服支持', 'key': 'customer_support'},
                {'feature': '数据备份', 'key': 'data_backup'},
            ]
        
            for feature_item in base_features:
                feature_data = {'feature': feature_item['feature']}
            
                for package in packages:
                    limitations = package.limitations or {}
                    feature_key = feature_item['key']
                
                    if feature_key in limitations:
                        feature_data[package.package_type] = limitations[feature_key]
                    else:
                        # 默认值
                        default_values = {
                            'individual': self._get_individual_default(feature_key),
                            'small': self._get_small_default(feature_key),
                            'general': self._get_general_default(feature_key),
                        }
                        feature_data[package.package_type] = default_values.get(package.package_type, True)
            
                comparison_data.append(feature_data)
        
            return Response({
                'success': True,
                'data': comparison_data
            })
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _get_individual_default(self, feature_key):
        try:
            defaults = {
                'voucher_limit': '50笔/月',
                'financial_reports': True,
                'tax_filing': True,
                'dedicated_accountant': False,
                'tax_planning': False,
                'invoice_management': '基础版',
                'customer_support': '工作日',
                'data_backup': '月度备份',
            }
            return defaults.get(feature_key, True)
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _get_small_default(self, feature_key):
        try:
            defaults = {
                'voucher_limit': '200笔/月',
                'financial_reports': True,
                'tax_filing': True,
                'dedicated_accountant': True,
                'tax_planning': False,
                'invoice_management': '标准版',
                'customer_support': '7×12小时',
                'data_backup': '周度备份',
            }
            return defaults.get(feature_key, True)
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _get_general_default(self, feature_key):
        try:
            defaults = {
                'voucher_limit': '500笔/月',
                'financial_reports': True,
                'tax_filing': True,
                'dedicated_accountant': True,
                'tax_planning': True,
                'invoice_management': '专业版',
                'customer_support': '7×24小时',
                'data_backup': '日度备份',
            }
            return defaults.get(feature_key, True)


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class CityConfigViewSet(viewsets.ReadOnlyModelViewSet):
    """城市配置API"""
    queryset = CityConfig.objects.filter(is_active=True).order_by('sort_order', 'name')
    serializer_class = CityConfigSerializer
    permission_classes = [AllowAny]
    
    @action(detail=False, methods=['get'])
    def hot_cities(self, request):
        """获取热门城市"""
        try:
            cities = self.get_queryset().filter(is_hot=True)
            serializer = self.get_serializer(cities, many=True)
            return Response({
                'success': True,
                'data': serializer.data
            })


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class NewsConfigViewSet(viewsets.ReadOnlyModelViewSet):
    """新闻动态API"""
    queryset = NewsConfig.objects.filter(is_active=True).order_by('-is_important', 'sort_order', '-publish_time')
    serializer_class = NewsConfigSerializer
    permission_classes = [AllowAny]
    
    @action(detail=False, methods=['get'])
    def latest(self, request):
        """获取最新动态"""
        try:
            limit = int(request.query_params.get('limit', 5))
            news = self.get_queryset()[:limit]
            serializer = self.get_serializer(news, many=True)
            return Response({
                'success': True,
                'data': serializer.data
            })


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class ReviewConfigViewSet(viewsets.ReadOnlyModelViewSet):
    """客户评价API"""
    queryset = ReviewConfig.objects.filter(is_active=True).order_by('sort_order', '-created_at')
    serializer_class = ReviewConfigSerializer
    permission_classes = [AllowAny]
    
    @action(detail=False, methods=['get'])
    def random(self, request):
        """随机获取评价"""
        try:
            count = int(request.query_params.get('count', 3))
            reviews = self.get_queryset().order_by('?')[:count]
            serializer = self.get_serializer(reviews, many=True)
            return Response({
                'success': True,
                'data': serializer.data
            })

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
from rest_framework import serializers
from .models import (
    AppConfig, BannerConfig, ServicePackageConfig,
    CityConfig, NewsConfig, ReviewConfig
)


class AppConfigSerializer(serializers.ModelSerializer):
    """应用配置序列化器"""
    value = serializers.SerializerMethodField()
    
    class Meta:
        model = AppConfig
        fields = ['key', 'name', 'value', 'config_type', 'description', 'category']
    
    def get_value(self, obj):
        return obj.get_value()


class BannerConfigSerializer(serializers.ModelSerializer):
    """轮播图配置序列化器"""
    class Meta:
        model = BannerConfig
        fields = [
            'id', 'title', 'description', 'image_url', 'link_url',
            'background_color', 'tag', 'action', 'sort_order'
        ]


class ServicePackageConfigSerializer(serializers.ModelSerializer):
    """服务套餐配置序列化器"""
    package_type_display = serializers.CharField(source='get_package_type_display', read_only=True)
    
    class Meta:
        model = ServicePackageConfig
        fields = [
            'id', 'name', 'package_type', 'package_type_display', 'description',
            'monthly_price', 'annual_price', 'first_time_price', 'features',
            'limitations', 'is_recommended', 'sort_order'
        ]


class CityConfigSerializer(serializers.ModelSerializer):
    """城市配置序列化器"""
    class Meta:
        model = CityConfig
        fields = ['id', 'name', 'code', 'province', 'is_hot', 'sort_order']


class NewsConfigSerializer(serializers.ModelSerializer):
    """新闻动态序列化器"""
    time_display = serializers.SerializerMethodField()
    
    class Meta:
        model = NewsConfig
        fields = [
            'id', 'title', 'description', 'content', 'category',
            'is_important', 'publish_time', 'time_display', 'sort_order'
        ]
    
    def get_time_display(self, obj):
        """格式化时间显示"""
        from django.utils import timezone
        from datetime import timedelta
        
        now = timezone.now()
        diff = now - obj.publish_time
        
        if diff < timedelta(hours=1):
            minutes = int(diff.total_seconds() / 60)
            return f"{minutes}分钟前"
        elif diff < timedelta(days=1):
            hours = int(diff.total_seconds() / 3600)
            return f"{hours}小时前"
        elif diff < timedelta(days=7):
            days = diff.days
            return f"{days}天前"
        else:
            return obj.publish_time.strftime('%Y-%m-%d')


class ReviewConfigSerializer(serializers.ModelSerializer):
    """客户评价序列化器"""
    class Meta:
        model = ReviewConfig
        fields = [
            'id', 'customer_name', 'avatar_url', 'rating', 'content',
            'service_type', 'sort_order'
        ]


class AppConfigBatchSerializer(serializers.Serializer):
    """批量获取配置序列化器"""
    keys = serializers.ListField(
        child=serializers.CharField(max_length=100),
        help_text="配置键列表"
    )
    category = serializers.CharField(
        max_length=50, 
        required=False,
        help_text="配置分类，可选"
    )

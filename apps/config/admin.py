from django.contrib import admin
from django.utils.html import format_html
from .models import (
    AppConfig, BannerConfig, ServicePackageConfig, 
    CityConfig, NewsConfig, ReviewConfig
)


@admin.register(AppConfig)
class AppConfigAdmin(admin.ModelAdmin):
    list_display = ['name', 'key', 'config_type', 'category', 'is_active', 'updated_at']
    list_filter = ['config_type', 'category', 'is_active']
    search_fields = ['name', 'key', 'description']
    list_editable = ['is_active']
    fieldsets = [
        ('基本信息', {
            'fields': ['key', 'name', 'category', 'config_type']
        }),
        ('配置内容', {
            'fields': ['value', 'description']
        }),
        ('状态设置', {
            'fields': ['is_active']
        })
    ]
    
    def get_readonly_fields(self, request, obj=None):
        if obj:  # 编辑时key不可修改
            return ['key']
        return []


@admin.register(BannerConfig)
class BannerConfigAdmin(admin.ModelAdmin):
    list_display = ['title', 'tag', 'preview_image', 'is_active', 'sort_order', 'updated_at']
    list_filter = ['is_active', 'tag']
    search_fields = ['title', 'description']
    list_editable = ['is_active', 'sort_order']
    ordering = ['sort_order', '-created_at']
    
    fieldsets = [
        ('基本信息', {
            'fields': ['title', 'description', 'tag']
        }),
        ('显示设置', {
            'fields': ['image_url', 'background_color', 'sort_order']
        }),
        ('链接设置', {
            'fields': ['link_url', 'action']
        }),
        ('时间设置', {
            'fields': ['start_time', 'end_time']
        }),
        ('状态设置', {
            'fields': ['is_active']
        })
    ]
    
    def preview_image(self, obj):
        if obj.image_url:
            return format_html('<img src="{}" width="50" height="30" style="object-fit: cover;" />', obj.image_url)
        return '-'
    preview_image.short_description = '预览图'


@admin.register(ServicePackageConfig)
class ServicePackageConfigAdmin(admin.ModelAdmin):
    list_display = ['name', 'package_type', 'monthly_price', 'annual_price', 'is_recommended', 'is_active', 'sort_order']
    list_filter = ['package_type', 'is_recommended', 'is_active']
    search_fields = ['name', 'description']
    list_editable = ['is_recommended', 'is_active', 'sort_order']
    ordering = ['sort_order', 'package_type']
    
    fieldsets = [
        ('基本信息', {
            'fields': ['name', 'package_type', 'description']
        }),
        ('价格设置', {
            'fields': ['monthly_price', 'annual_price', 'first_time_price']
        }),
        ('套餐内容', {
            'fields': ['features', 'limitations']
        }),
        ('显示设置', {
            'fields': ['is_recommended', 'sort_order']
        }),
        ('状态设置', {
            'fields': ['is_active']
        })
    ]


@admin.register(CityConfig)
class CityConfigAdmin(admin.ModelAdmin):
    list_display = ['name', 'province', 'code', 'is_hot', 'is_active', 'sort_order']
    list_filter = ['province', 'is_hot', 'is_active']
    search_fields = ['name', 'code', 'province']
    list_editable = ['is_hot', 'is_active', 'sort_order']
    ordering = ['sort_order', 'province', 'name']
    
    fieldsets = [
        ('基本信息', {
            'fields': ['name', 'code', 'province']
        }),
        ('显示设置', {
            'fields': ['is_hot', 'sort_order']
        }),
        ('状态设置', {
            'fields': ['is_active']
        })
    ]


@admin.register(NewsConfig)
class NewsConfigAdmin(admin.ModelAdmin):
    list_display = ['title', 'category', 'is_important', 'is_active', 'publish_time', 'sort_order']
    list_filter = ['category', 'is_important', 'is_active', 'publish_time']
    search_fields = ['title', 'description', 'content']
    list_editable = ['is_important', 'is_active', 'sort_order']
    ordering = ['-is_important', 'sort_order', '-publish_time']
    date_hierarchy = 'publish_time'
    
    fieldsets = [
        ('基本信息', {
            'fields': ['title', 'category']
        }),
        ('内容设置', {
            'fields': ['description', 'content']
        }),
        ('显示设置', {
            'fields': ['is_important', 'sort_order']
        }),
        ('时间设置', {
            'fields': ['publish_time']
        }),
        ('状态设置', {
            'fields': ['is_active']
        })
    ]


@admin.register(ReviewConfig)
class ReviewConfigAdmin(admin.ModelAdmin):
    list_display = ['customer_name', 'rating', 'service_type', 'preview_content', 'is_active', 'sort_order']
    list_filter = ['rating', 'service_type', 'is_active']
    search_fields = ['customer_name', 'content', 'service_type']
    list_editable = ['is_active', 'sort_order']
    ordering = ['sort_order', '-created_at']
    
    fieldsets = [
        ('客户信息', {
            'fields': ['customer_name', 'avatar_url']
        }),
        ('评价内容', {
            'fields': ['rating', 'content', 'service_type']
        }),
        ('显示设置', {
            'fields': ['sort_order']
        }),
        ('状态设置', {
            'fields': ['is_active']
        })
    ]
    
    def preview_content(self, obj):
        if len(obj.content) > 50:
            return obj.content[:50] + '...'
        return obj.content
    preview_content.short_description = '评价内容'

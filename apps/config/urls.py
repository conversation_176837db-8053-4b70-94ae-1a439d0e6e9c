from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    AppConfigViewSet, BannerConfigViewSet, ServicePackageConfigViewSet,
    CityConfigViewSet, NewsConfigViewSet, ReviewConfigViewSet
)

router = DefaultRouter()
router.register(r'app-config', AppConfigViewSet, basename='app-config')
router.register(r'banners', BannerConfigViewSet, basename='banners')
router.register(r'packages', ServicePackageConfigViewSet, basename='packages')
router.register(r'cities', CityConfigViewSet, basename='cities')
router.register(r'news', NewsConfigViewSet, basename='news')
router.register(r'reviews', ReviewConfigViewSet, basename='reviews')

urlpatterns = [
    path('', include(router.urls)),
]

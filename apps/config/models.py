from django.db import models
from django.core.validators import URLValidator
import json


class AppConfig(models.Model):
    """应用基础配置"""
    CONFIG_TYPES = [
        ('string', '字符串'),
        ('number', '数字'),
        ('boolean', '布尔值'),
        ('json', 'JSON对象'),
        ('url', 'URL地址'),
        ('image', '图片地址'),
    ]
    
    key = models.CharField('配置键', max_length=100, unique=True)
    name = models.CharField('配置名称', max_length=200)
    value = models.TextField('配置值')
    config_type = models.CharField('配置类型', max_length=20, choices=CONFIG_TYPES, default='string')
    description = models.TextField('配置描述', blank=True)
    category = models.CharField('配置分类', max_length=50, default='general')
    is_active = models.BooleanField('是否启用', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'app_config'
        verbose_name = '应用配置'
        verbose_name_plural = '应用配置'
        ordering = ['category', 'key']
    
    def __str__(self):
        return f"{self.name} ({self.key})"
    
    def get_value(self):
        """获取配置值，根据类型进行转换"""
        if not self.is_active:
            return None
            
        if self.config_type == 'boolean':
            return self.value.lower() in ['true', '1', 'yes', 'on']
        elif self.config_type == 'number':
            try:
                if '.' in self.value:
                    return float(self.value)
                return int(self.value)
            except ValueError:
                return 0
        elif self.config_type == 'json':
            try:
                return json.loads(self.value)
            except json.JSONDecodeError:
                return {}
        else:
            return self.value


class BannerConfig(models.Model):
    """轮播图配置"""
    title = models.CharField('标题', max_length=200)
    description = models.CharField('描述', max_length=500, blank=True)
    image_url = models.URLField('图片地址', blank=True)
    link_url = models.URLField('跳转链接', blank=True)
    background_color = models.CharField('背景色', max_length=50, blank=True)
    tag = models.CharField('标签', max_length=50, blank=True)
    action = models.CharField('动作类型', max_length=50, blank=True)
    sort_order = models.IntegerField('排序', default=0)
    is_active = models.BooleanField('是否启用', default=True)
    start_time = models.DateTimeField('开始时间', null=True, blank=True)
    end_time = models.DateTimeField('结束时间', null=True, blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'banner_config'
        verbose_name = '轮播图配置'
        verbose_name_plural = '轮播图配置'
        ordering = ['sort_order', '-created_at']
    
    def __str__(self):
        return self.title


class ServicePackageConfig(models.Model):
    """服务套餐配置"""
    PACKAGE_TYPES = [
        ('individual', '个体户'),
        ('small', '小规模纳税人'),
        ('general', '一般纳税人'),
    ]
    
    name = models.CharField('套餐名称', max_length=100)
    package_type = models.CharField('套餐类型', max_length=20, choices=PACKAGE_TYPES)
    description = models.CharField('套餐描述', max_length=500)
    monthly_price = models.DecimalField('月付价格', max_digits=10, decimal_places=2)
    annual_price = models.DecimalField('年付价格', max_digits=10, decimal_places=2, null=True, blank=True)
    first_time_price = models.DecimalField('首次价格', max_digits=10, decimal_places=2, null=True, blank=True)
    features = models.JSONField('套餐特性', default=list)
    limitations = models.JSONField('使用限制', default=dict)
    is_recommended = models.BooleanField('是否推荐', default=False)
    is_active = models.BooleanField('是否启用', default=True)
    sort_order = models.IntegerField('排序', default=0)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'service_package_config'
        verbose_name = '服务套餐配置'
        verbose_name_plural = '服务套餐配置'
        ordering = ['sort_order', 'package_type']
    
    def __str__(self):
        return f"{self.name} ({self.get_package_type_display()})"


class CityConfig(models.Model):
    """城市服务配置"""
    name = models.CharField('城市名称', max_length=50)
    code = models.CharField('城市代码', max_length=20, unique=True)
    province = models.CharField('省份', max_length=50)
    is_hot = models.BooleanField('是否热门城市', default=False)
    is_active = models.BooleanField('是否开通服务', default=True)
    sort_order = models.IntegerField('排序', default=0)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'city_config'
        verbose_name = '城市配置'
        verbose_name_plural = '城市配置'
        ordering = ['sort_order', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.province})"


class NewsConfig(models.Model):
    """新闻动态配置"""
    title = models.CharField('标题', max_length=200)
    description = models.TextField('内容描述')
    content = models.TextField('详细内容', blank=True)
    category = models.CharField('分类', max_length=50, default='news')
    is_important = models.BooleanField('是否重要', default=False)
    is_active = models.BooleanField('是否启用', default=True)
    publish_time = models.DateTimeField('发布时间', auto_now_add=True)
    sort_order = models.IntegerField('排序', default=0)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'news_config'
        verbose_name = '新闻动态'
        verbose_name_plural = '新闻动态'
        ordering = ['-is_important', 'sort_order', '-publish_time']
    
    def __str__(self):
        return self.title


class ReviewConfig(models.Model):
    """客户评价配置"""
    customer_name = models.CharField('客户姓名', max_length=50)
    avatar_url = models.URLField('头像地址', blank=True)
    rating = models.IntegerField('评分', choices=[(i, f'{i}星') for i in range(1, 6)], default=5)
    content = models.TextField('评价内容')
    service_type = models.CharField('服务类型', max_length=100, blank=True)
    is_active = models.BooleanField('是否启用', default=True)
    sort_order = models.IntegerField('排序', default=0)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'review_config'
        verbose_name = '客户评价'
        verbose_name_plural = '客户评价'
        ordering = ['sort_order', '-created_at']
    
    def __str__(self):
        return f"{self.customer_name}的评价"

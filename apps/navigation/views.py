import logging
from django.core.exceptions import ValidationError, PermissionDenied
from django.http import JsonResponse
from rest_framework import status

logger = logging.getLogger(__name__)

from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.contrib.auth.models import Permission
from utils.response import APIResponse
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class UserNavigationView(APIView):
    """用户导航菜单API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取用户导航菜单"""
        try:
            user = request.user
        
            # 基础导航结构
            navigation = {
                'main_tabs': [
                    {
                        'id': 'home',
                        'name': '首页',
                        'icon': 'home',
                        'path': '/pages/index/index',
                        'show': True
                    },
                    {
                        'id': 'finance',
                        'name': '财务',
                        'icon': 'finance',
                        'path': '/pages/finance/dashboard',
                        'show': True
                    },
                    {
                        'id': 'discover',
                        'name': '发现',
                        'icon': 'discover',
                        'path': '/pages/discover/index',
                        'show': True
                    },
                    {
                        'id': 'profile',
                        'name': '我的',
                        'icon': 'profile',
                        'path': '/pages/profile/index',
                        'show': True
                    }
                ],
                'menu_items': []
            }
        
            # 根据用户权限动态生成菜单
            menu_items = []
        
            # 财务管理菜单
            finance_menu = {
                'id': 'finance_group',
                'name': '财务管理',
                'icon': 'finance',
                'children': [
                    {
                        'id': 'finance_dashboard',
                        'name': '财务概览',
                        'path': '/pages/finance/dashboard',
                        'permission': 'finance.view'
                    },
                    {
                        'id': 'finance_records',
                        'name': '财务记录',
                        'path': '/pages/finance/records',
                        'permission': 'finance.view'
                    },
                    {
                        'id': 'finance_submit',
                        'name': '资料提交',
                        'path': '/pages/finance/submit',
                        'permission': 'finance.add'
                    }
                ]
            }
            menu_items.append(finance_menu)
        
            # 企业管理菜单
            company_menu = {
                'id': 'company_group',
                'name': '企业管理',
                'icon': 'company',
                'children': [
                    {
                        'id': 'company_info',
                        'name': '企业信息',
                        'path': '/pages/company/info',
                        'permission': 'company.view'
                    },
                    {
                        'id': 'company_create',
                        'name': '创建企业',
                        'path': '/pages/company/create',
                        'permission': 'company.add'
                    }
                ]
            }
            menu_items.append(company_menu)
        
            # 服务管理菜单
            service_menu = {
                'id': 'service_group',
                'name': '服务管理',
                'icon': 'service',
                'children': [
                    {
                        'id': 'service_packages',
                        'name': '服务套餐',
                        'path': '/pages/services/packages',
                        'permission': 'service.view'
                    },
                    {
                        'id': 'service_orders',
                        'name': '我的订单',
                        'path': '/pages/orders/index',
                        'permission': 'order.view'
                    }
                ]
            }
            menu_items.append(service_menu)
        
            # 税务管理菜单
            tax_menu = {
                'id': 'tax_group',
                'name': '税务管理',
                'icon': 'tax',
                'children': [
                    {
                        'id': 'tax_calendar',
                        'name': '税务日历',
                        'path': '/pages/tax/calendar',
                        'permission': 'tax.view'
                    },
                    {
                        'id': 'tax_records',
                        'name': '税务记录',
                        'path': '/pages/tax/records',
                        'permission': 'tax.view'
                    }
                ]
            }
            menu_items.append(tax_menu)
        
            # 文件管理菜单
            file_menu = {
                'id': 'file_group',
                'name': '文件管理',
                'icon': 'file',
                'children': [
                    {
                        'id': 'file_list',
                        'name': '文件列表',
                        'path': '/pages/files/list',
                        'permission': 'file.view'
                    },
                    {
                        'id': 'file_upload',
                        'name': '文件上传',
                        'path': '/pages/files/upload',
                        'permission': 'file.add'
                    }
                ]
            }
            menu_items.append(file_menu)
        
            # 客服支持菜单
            support_menu = {
                'id': 'support_group',
                'name': '客服支持',
                'icon': 'support',
                'children': [
                    {
                        'id': 'customer_service',
                        'name': '在线客服',
                        'path': '/pages/common/customer-service',
                        'permission': 'support.view'
                    },
                    {
                        'id': 'help_center',
                        'name': '帮助中心',
                        'path': '/pages/common/help',
                        'permission': 'support.view'
                    },
                    {
                        'id': 'feedback',
                        'name': '意见反馈',
                        'path': '/pages/common/feedback',
                        'permission': 'support.add'
                    }
                ]
            }
            menu_items.append(support_menu)
        
            navigation['menu_items'] = menu_items
        
            return APIResponse.success(data=navigation)


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class UserPermissionsView(APIView):
    """用户权限API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取用户权限列表"""
        try:
            user = request.user
        
            # 基础权限（所有用户都有）
            basic_permissions = [
                'finance.view',
                'company.view',
                'service.view',
                'order.view',
                'tax.view',
                'file.view',
                'support.view'
            ]
        
            # 根据用户类型添加额外权限
            permissions = basic_permissions.copy()
        
            # VIP用户或企业用户有更多权限
            if hasattr(user, 'userprofile') and user.userprofile.vip_level > 0:
                permissions.extend([
                    'finance.add',
                    'finance.edit',
                    'company.add',
                    'company.edit',
                    'file.add',
                    'support.add'
                ])
            else:
                # 普通用户的基础权限
                permissions.extend([
                    'finance.add',
                    'company.add',
                    'file.add',
                    'support.add'
                ])
        
            # 管理员有所有权限
            if user.is_staff:
                permissions.extend([
                    'admin.view',
                    'admin.add',
                    'admin.edit',
                    'admin.delete'
                ])
        
            return APIResponse.success(data={
                'permissions': list(set(permissions)),  # 去重
                'user_level': 'vip' if hasattr(user, 'userprofile') and user.userprofile.vip_level > 0 else 'basic',
                'vip_level': user.userprofile.vip_level if hasattr(user, 'userprofile') else 0,
                'is_staff': user.is_staff
            })


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class CheckPermissionView(APIView):
    """检查特定权限API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request, permission):
        """检查用户是否有特定权限"""
        try:
            user = request.user
        
            # 获取用户权限
            permissions_response = UserPermissionsView().get(request)
            user_permissions = permissions_response.data['data']['permissions']
        
            has_permission = permission in user_permissions
        
            return APIResponse.success(data={
                'permission': permission,
                'has_permission': has_permission
            })

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
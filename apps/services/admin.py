from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import ServicePackage, Order, OrderRenewal, Coupon, UserCoupon


@admin.register(ServicePackage)
class ServicePackageAdmin(admin.ModelAdmin):
    """服务套餐管理"""
    list_display = ['name', 'package_type', 'monthly_price', 'status', 'is_recommended', 'sort_order', 'created_at']
    list_filter = ['package_type', 'status', 'is_recommended', 'created_at']
    search_fields = ['name', 'description']
    list_editable = ['status', 'is_recommended', 'sort_order']
    ordering = ['sort_order', '-created_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'package_type', 'description', 'status', 'is_recommended', 'sort_order')
        }),
        ('价格设置', {
            'fields': ('monthly_price', 'quarterly_price', 'yearly_price', 'first_time_price')
        }),
        ('套餐限制', {
            'fields': ('max_transactions', 'max_invoices', 'max_reports')
        }),
        ('服务内容', {
            'fields': ('includes_bookkeeping', 'includes_tax_filing', 'includes_financial_reports', 
                      'includes_tax_planning', 'includes_audit', 'includes_consultation')
        }),
        ('高级设置', {
            'fields': ('features',),
            'classes': ('collapse',)
        })
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related()


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    """订单管理"""
    list_display = ['order_no', 'user_info', 'company_info', 'package_name', 'payment_period', 
                   'final_price', 'status', 'service_period', 'created_at']
    list_filter = ['status', 'payment_period', 'is_first_order', 'payment_method', 'created_at']
    search_fields = ['order_no', 'user__phone', 'user__nickname', 'company__name', 'package_name']
    readonly_fields = ['order_no', 'created_at', 'updated_at']
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('订单信息', {
            'fields': ('order_no', 'user', 'company', 'package', 'package_name')
        }),
        ('服务详情', {
            'fields': ('payment_period', 'months', 'service_start_date', 'service_end_date')
        }),
        ('价格信息', {
            'fields': ('original_price', 'discount_amount', 'final_price')
        }),
        ('订单状态', {
            'fields': ('status', 'is_first_order')
        }),
        ('支付信息', {
            'fields': ('paid_at', 'payment_method', 'transaction_id')
        }),
        ('其他信息', {
            'fields': ('remarks', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def user_info(self, obj):
        if obj.user:
            return format_html(
                '<a href="{}">{}</a><br/><small>{}</small>',
                reverse('admin:users_user_change', args=[obj.user.pk]),
                obj.user.nickname or obj.user.phone,
                obj.user.phone
            )
        return '-'
    user_info.short_description = '用户信息'
    
    def company_info(self, obj):
        if obj.company:
            return format_html(
                '<a href="{}">{}</a>',
                reverse('admin:companies_company_change', args=[obj.company.pk]),
                obj.company.name
            )
        return '-'
    company_info.short_description = '企业信息'
    
    def service_period(self, obj):
        if obj.service_start_date and obj.service_end_date:
            return f"{obj.service_start_date} 至 {obj.service_end_date}"
        return '-'
    service_period.short_description = '服务期限'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'company', 'package')


@admin.register(OrderRenewal)
class OrderRenewalAdmin(admin.ModelAdmin):
    """续费记录管理"""
    list_display = ['original_order_no', 'renewal_order_no', 'renewal_months', 'renewal_price', 'created_at']
    list_filter = ['renewal_months', 'created_at']
    search_fields = ['original_order__order_no', 'renewal_order__order_no']
    readonly_fields = ['created_at']
    
    def original_order_no(self, obj):
        return obj.original_order.order_no
    original_order_no.short_description = '原订单号'
    
    def renewal_order_no(self, obj):
        return obj.renewal_order.order_no
    renewal_order_no.short_description = '续费订单号'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('original_order', 'renewal_order')


@admin.register(Coupon)
class CouponAdmin(admin.ModelAdmin):
    """优惠券管理"""
    list_display = ['name', 'code', 'coupon_type', 'discount_info', 'usage_info', 
                   'validity_period', 'status', 'created_at']
    list_filter = ['coupon_type', 'status', 'created_at']
    search_fields = ['name', 'code']
    filter_horizontal = ['applicable_packages']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'code', 'coupon_type', 'status')
        }),
        ('优惠规则', {
            'fields': ('discount_amount', 'discount_rate', 'min_amount', 'max_discount')
        }),
        ('使用限制', {
            'fields': ('usage_limit', 'used_count', 'user_limit')
        }),
        ('适用范围', {
            'fields': ('applicable_packages', 'applicable_user_types')
        }),
        ('有效期', {
            'fields': ('start_date', 'end_date')
        })
    )
    
    def discount_info(self, obj):
        if obj.coupon_type == 'amount':
            return f"满{obj.min_amount}减{obj.discount_amount}"
        elif obj.coupon_type == 'discount':
            return f"{int(obj.discount_rate * 100)}折"
        return obj.coupon_type
    discount_info.short_description = '优惠信息'
    
    def usage_info(self, obj):
        return f"{obj.used_count}/{obj.usage_limit}"
    usage_info.short_description = '使用情况'
    
    def validity_period(self, obj):
        return f"{obj.start_date.strftime('%Y-%m-%d')} 至 {obj.end_date.strftime('%Y-%m-%d')}"
    validity_period.short_description = '有效期'


@admin.register(UserCoupon)
class UserCouponAdmin(admin.ModelAdmin):
    """用户优惠券管理"""
    list_display = ['user_info', 'coupon_info', 'status', 'obtained_at', 'used_at']
    list_filter = ['status', 'obtained_at', 'used_at']
    search_fields = ['user__phone', 'user__nickname', 'coupon__name', 'coupon__code']
    readonly_fields = ['obtained_at', 'used_at']
    
    def user_info(self, obj):
        return f"{obj.user.nickname or obj.user.phone} ({obj.user.phone})"
    user_info.short_description = '用户'
    
    def coupon_info(self, obj):
        return f"{obj.coupon.name} ({obj.coupon.code})"
    coupon_info.short_description = '优惠券'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'coupon', 'order')

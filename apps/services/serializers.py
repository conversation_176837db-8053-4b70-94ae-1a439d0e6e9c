from rest_framework import serializers
from decimal import Decimal
from django.utils import timezone
from .models import ServicePackage, Order, OrderRenewal, Coupon, UserCoupon


class ServicePackageSerializer(serializers.ModelSerializer):
    """服务套餐序列化器"""
    
    class Meta:
        model = ServicePackage
        fields = [
            'id', 'name', 'package_type', 'description', 'monthly_price',
            'quarterly_price', 'yearly_price', 'first_time_price', 'features',
            'max_transactions', 'max_invoices', 'max_reports',
            'includes_bookkeeping', 'includes_tax_filing', 'includes_financial_reports',
            'includes_tax_planning', 'includes_audit', 'includes_consultation',
            'status', 'is_recommended', 'sort_order', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']
    
    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)
        
        # 添加价格信息
        data['price_info'] = {
            'monthly': float(instance.monthly_price),
            'quarterly': float(instance.quarterly_price or instance.monthly_price * 3),
            'yearly': float(instance.yearly_price or instance.monthly_price * 12),
            'first_time': float(instance.first_time_price or instance.monthly_price)
        }
        
        # 添加套餐特性统计
        data['feature_count'] = len(instance.features) if instance.features else 0
        
        return data


class ServicePackageListSerializer(serializers.ModelSerializer):
    """服务套餐列表序列化器（简化版）"""
    price_info = serializers.SerializerMethodField()
    
    class Meta:
        model = ServicePackage
        fields = [
            'id', 'name', 'package_type', 'description', 'monthly_price',
            'first_time_price', 'is_recommended', 'price_info'
        ]
    
    def get_price_info(self, obj):
        """获取价格信息"""
        return {
            'monthly': float(obj.monthly_price),
            'first_time': float(obj.first_time_price or obj.monthly_price),
            'discount': float(obj.monthly_price - (obj.first_time_price or obj.monthly_price))
        }


class OrderSerializer(serializers.ModelSerializer):
    """订单序列化器"""
    user_phone = serializers.CharField(source='user.phone', read_only=True)
    company_name = serializers.CharField(source='company.name', read_only=True)
    package_name = serializers.CharField(read_only=True)
    package_info = serializers.SerializerMethodField()
    days_left = serializers.IntegerField(read_only=True)
    is_expired = serializers.BooleanField(read_only=True)
    status_text = serializers.SerializerMethodField()
    payment_method_text = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = [
            'id', 'order_no', 'user', 'user_phone', 'company', 'company_name',
            'package', 'package_info', 'package_name', 'payment_period', 'months',
            'original_price', 'discount_amount', 'final_price',
            'service_start_date', 'service_end_date', 'status', 'status_text',
            'is_first_order', 'paid_at', 'payment_method', 'payment_method_text', 'transaction_id',
            'remarks', 'days_left', 'is_expired', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'order_no', 'user_phone', 'company_name', 'package_name', 'package_info',
            'status_text', 'payment_method_text', 'days_left', 'is_expired', 'created_at', 'updated_at'
        ]

    def get_package_info(self, obj):
        """获取套餐详细信息"""
        if obj.package:
            return {
                'id': obj.package.id,
                'name': obj.package.name,
                'package_type': obj.package.package_type,
                'description': obj.package.description,
                'features': obj.package.features,
                'price': float(obj.final_price),
                'original_price': float(obj.original_price),
                'discount_amount': float(obj.discount_amount),
                'includes_bookkeeping': obj.package.includes_bookkeeping,
                'includes_tax_filing': obj.package.includes_tax_filing,
                'includes_financial_reports': obj.package.includes_financial_reports,
                'includes_tax_planning': obj.package.includes_tax_planning,
                'includes_audit': obj.package.includes_audit,
                'includes_consultation': obj.package.includes_consultation
            }
        return None

    def get_status_text(self, obj):
        """获取状态文本"""
        status_map = {
            'pending': '待支付',
            'paid': '已支付',
            'active': '服务中',
            'expired': '已过期',
            'cancelled': '已取消',
            'refunded': '已退款'
        }
        return status_map.get(obj.status, obj.status)

    def get_payment_method_text(self, obj):
        """获取支付方式文本"""
        method_map = {
            'wechat': '微信支付',
            'alipay': '支付宝',
            'bank_transfer': '银行转账',
            'offline': '线下支付'
        }
        return method_map.get(obj.payment_method, obj.payment_method or '未支付')


class OrderCreateSerializer(serializers.Serializer):
    """订单创建序列化器"""
    package_id = serializers.IntegerField()
    payment_period = serializers.ChoiceField(choices=Order.PAYMENT_PERIOD_CHOICES, default='monthly')
    months = serializers.IntegerField(min_value=1, max_value=36, default=1)
    coupon_code = serializers.CharField(max_length=20, required=False, allow_blank=True)
    
    def validate_package_id(self, value):
        """验证套餐ID"""
        try:
            package = ServicePackage.objects.get(id=value, status='active')
            return package
        except ServicePackage.DoesNotExist:
            raise serializers.ValidationError('套餐不存在或已下架')
    
    def validate_coupon_code(self, value):
        """验证优惠券"""
        if value:
            try:
                coupon = Coupon.objects.get(code=value, status='active')
                if not coupon.is_valid:
                    raise serializers.ValidationError('优惠券已过期或已用完')
                return coupon
            except Coupon.DoesNotExist:
                raise serializers.ValidationError('优惠券不存在')
        return None
    
    def validate(self, attrs):
        """验证订单数据"""
        user = self.context['request'].user
        package = attrs['package_id']
        payment_period = attrs['payment_period']
        months = attrs['months']
        
        # 检查用户是否有企业
        if not hasattr(user, 'companies') or not user.companies.exists():
            raise serializers.ValidationError('请先创建企业信息')
        
        company = user.companies.first()
        
        # 检查是否有未完成的订单
        pending_orders = Order.objects.filter(
            user=user,
            company=company,
            status__in=['pending', 'paid']
        )
        if pending_orders.exists():
            raise serializers.ValidationError('您有未完成的订单，请先处理')
        
        # 计算价格
        if payment_period == 'monthly':
            unit_price = package.monthly_price
        elif payment_period == 'quarterly':
            unit_price = package.quarterly_price or package.monthly_price * 3
            months = months * 3  # 季付转换为月数
        elif payment_period == 'yearly':
            unit_price = package.yearly_price or package.monthly_price * 12
            months = months * 12  # 年付转换为月数
        else:
            unit_price = package.monthly_price
        
        # 检查是否首次订单
        is_first_order = not Order.objects.filter(user=user, status__in=['paid', 'active']).exists()
        
        # 计算原价
        if is_first_order and package.first_time_price:
            original_price = package.first_time_price
        else:
            original_price = unit_price
        
        # 应用优惠券
        discount_amount = Decimal('0.00')
        coupon = attrs.get('coupon_code')
        if coupon:
            if coupon.coupon_type == 'amount':
                if original_price >= coupon.min_amount:
                    discount_amount = min(coupon.discount_amount, original_price)
            elif coupon.coupon_type == 'discount':
                discount_amount = original_price * (Decimal('1.00') - coupon.discount_rate)
                if coupon.max_discount:
                    discount_amount = min(discount_amount, coupon.max_discount)
        
        final_price = original_price - discount_amount
        
        attrs.update({
            'company': company,
            'original_price': original_price,
            'discount_amount': discount_amount,
            'final_price': final_price,
            'is_first_order': is_first_order,
            'months': months
        })
        
        return attrs
    
    def create(self, validated_data):
        """创建订单"""
        user = self.context['request'].user
        package = validated_data['package_id']
        
        order = Order.objects.create(
            user=user,
            company=validated_data['company'],
            package=package,
            package_name=package.name,
            payment_period=validated_data['payment_period'],
            months=validated_data['months'],
            original_price=validated_data['original_price'],
            discount_amount=validated_data['discount_amount'],
            final_price=validated_data['final_price'],
            is_first_order=validated_data['is_first_order'],
            remarks=validated_data.get('remarks', '')
        )
        
        # 使用优惠券
        coupon = validated_data.get('coupon_code')
        if coupon:
            user_coupon, created = UserCoupon.objects.get_or_create(
                user=user,
                coupon=coupon,
                defaults={'status': 'used', 'order': order, 'used_at': timezone.now()}
            )
            if not created:
                user_coupon.status = 'used'
                user_coupon.order = order
                user_coupon.used_at = timezone.now()
                user_coupon.save()
            
            # 更新优惠券使用次数
            coupon.used_count += 1
            coupon.save()
        
        return order


class OrderRenewalSerializer(serializers.ModelSerializer):
    """订单续费序列化器"""
    original_order_no = serializers.CharField(source='original_order.order_no', read_only=True)
    renewal_order_no = serializers.CharField(source='renewal_order.order_no', read_only=True)
    
    class Meta:
        model = OrderRenewal
        fields = [
            'id', 'original_order', 'original_order_no', 'renewal_order',
            'renewal_order_no', 'renewal_months', 'renewal_price', 'created_at'
        ]
        read_only_fields = ['id', 'original_order_no', 'renewal_order_no', 'created_at']


class CouponSerializer(serializers.ModelSerializer):
    """优惠券序列化器"""
    is_valid = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Coupon
        fields = [
            'id', 'name', 'code', 'coupon_type', 'discount_amount',
            'discount_rate', 'min_amount', 'max_discount', 'usage_limit',
            'used_count', 'user_limit', 'start_date', 'end_date',
            'status', 'is_valid', 'created_at'
        ]
        read_only_fields = ['id', 'used_count', 'is_valid', 'created_at']


class UserCouponSerializer(serializers.ModelSerializer):
    """用户优惠券序列化器"""
    coupon_name = serializers.CharField(source='coupon.name', read_only=True)
    coupon_code = serializers.CharField(source='coupon.code', read_only=True)
    coupon_type = serializers.CharField(source='coupon.coupon_type', read_only=True)
    discount_amount = serializers.DecimalField(source='coupon.discount_amount', max_digits=10, decimal_places=2, read_only=True)
    discount_rate = serializers.DecimalField(source='coupon.discount_rate', max_digits=3, decimal_places=2, read_only=True)
    min_amount = serializers.DecimalField(source='coupon.min_amount', max_digits=10, decimal_places=2, read_only=True)
    end_date = serializers.DateTimeField(source='coupon.end_date', read_only=True)
    
    class Meta:
        model = UserCoupon
        fields = [
            'id', 'coupon', 'coupon_name', 'coupon_code', 'coupon_type',
            'discount_amount', 'discount_rate', 'min_amount', 'end_date',
            'status', 'obtained_at', 'used_at'
        ]
        read_only_fields = ['id', 'coupon_name', 'coupon_code', 'coupon_type', 
                           'discount_amount', 'discount_rate', 'min_amount', 
                           'end_date', 'obtained_at', 'used_at']


class RenewOrderSerializer(serializers.Serializer):
    """续费订单序列化器"""
    original_order_id = serializers.IntegerField()
    months = serializers.IntegerField(min_value=1, max_value=36)
    coupon_code = serializers.CharField(max_length=20, required=False, allow_blank=True)
    
    def validate_original_order_id(self, value):
        """验证原订单"""
        try:
            order = Order.objects.get(
                id=value,
                user=self.context['request'].user,
                status='active'
            )
            return order
        except Order.DoesNotExist:
            raise serializers.ValidationError('订单不存在或状态不正确')
    
    def validate_coupon_code(self, value):
        """验证优惠券"""
        if value:
            try:
                coupon = Coupon.objects.get(code=value, status='active')
                if not coupon.is_valid:
                    raise serializers.ValidationError('优惠券已过期或已用完')
                return coupon
            except Coupon.DoesNotExist:
                raise serializers.ValidationError('优惠券不存在')
        return None

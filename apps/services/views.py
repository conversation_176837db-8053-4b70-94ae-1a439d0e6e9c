from rest_framework import status, viewsets, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated, AllowAny
from django.shortcuts import get_object_or_404
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from .models import ServicePackage, Order, OrderRenewal, Coupon, UserCoupon
from .serializers import (
    ServicePackageSerializer, ServicePackageListSerializer, OrderSerializer,
    OrderCreateSerializer, OrderRenewalSerializer, CouponSerializer,
    UserCouponSerializer, RenewOrderSerializer
)
from utils.response import APIResponse
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class PackageListView(APIView):
    """服务套餐列表"""
    permission_classes = [AllowAny]
    
    def get(self, request):
        """获取服务套餐列表"""
        package_type = request.query_params.get('type')
        
        queryset = ServicePackage.objects.filter(status='active').order_by('sort_order', '-created_at')
        
        if package_type:
            queryset = queryset.filter(package_type=package_type)
        
        serializer = ServicePackageListSerializer(queryset, many=True)
        return APIResponse.success(data=serializer.data)


class PackageDetailView(APIView):
    """服务套餐详情"""
    permission_classes = [AllowAny]
    
    def get(self, request, pk):
        """获取套餐详情"""
        try:
            package = ServicePackage.objects.get(id=pk, status='active')
            serializer = ServicePackageSerializer(package)
            return APIResponse.success(data=serializer.data)
        except ServicePackage.DoesNotExist:
            return APIResponse.not_found(message='套餐不存在')


class CreateOrderView(APIView):
    """创建订单"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """创建订单"""
        serializer = OrderCreateSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            order = serializer.save()
            order_serializer = OrderSerializer(order)
            return APIResponse.created(
                data=order_serializer.data,
                message='订单创建成功'
            )
        
        return APIResponse.error(message='订单创建失败', errors=serializer.errors)


class PayOrderView(APIView):
    """支付订单"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """支付订单"""
        order_id = request.data.get('order_id')
        payment_method = request.data.get('payment_method', 'wechat')
        
        try:
            order = Order.objects.get(
                id=order_id,
                user=request.user,
                status='pending'
            )
        except Order.DoesNotExist:
            return APIResponse.error(message='订单不存在或状态不正确', code=404)
        
        # 这里应该调用支付接口
        # 暂时模拟支付成功
        order.status = 'paid'
        order.paid_at = timezone.now()
        order.payment_method = payment_method
        order.transaction_id = f'mock_transaction_{order.id}'
        
        # 设置服务期限
        if not order.service_start_date:
            order.service_start_date = timezone.now().date()
            order.service_end_date = order.service_start_date + timedelta(days=order.months * 30)
        
        order.save()
        
        # 更新订单状态为服务中
        order.status = 'active'
        order.save()
        
        return APIResponse.success(
            data=OrderSerializer(order).data,
            message='支付成功'
        )


class RenewOrderView(APIView):
    """续费订单"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """创建续费订单"""
        serializer = RenewOrderSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            original_order = serializer.validated_data['original_order_id']
            months = serializer.validated_data['months']
            coupon = serializer.validated_data.get('coupon_code')
            
            # 计算续费价格
            package = original_order.package
            renewal_price = package.monthly_price * months
            
            # 应用优惠券
            discount_amount = 0
            if coupon:
                if coupon.coupon_type == 'amount':
                    if renewal_price >= coupon.min_amount:
                        discount_amount = min(coupon.discount_amount, renewal_price)
                elif coupon.coupon_type == 'discount':
                    discount_amount = renewal_price * (1 - coupon.discount_rate)
                    if coupon.max_discount:
                        discount_amount = min(discount_amount, coupon.max_discount)
            
            final_price = renewal_price - discount_amount
            
            # 创建续费订单
            renewal_order = Order.objects.create(
                user=request.user,
                company=original_order.company,
                package=package,
                package_name=package.name,
                payment_period='monthly',
                months=months,
                original_price=renewal_price,
                discount_amount=discount_amount,
                final_price=final_price,
                is_first_order=False
            )
            
            # 创建续费记录
            OrderRenewal.objects.create(
                original_order=original_order,
                renewal_order=renewal_order,
                renewal_months=months,
                renewal_price=final_price
            )
            
            return APIResponse.created(
                data=OrderSerializer(renewal_order).data,
                message='续费订单创建成功'
            )
        
        return APIResponse.error(message='续费失败', errors=serializer.errors)


class AvailableCouponsView(APIView):
    """可用优惠券"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取用户可用优惠券"""
        user_coupons = UserCoupon.objects.filter(
            user=request.user,
            status='unused',
            coupon__status='active',
            coupon__start_date__lte=timezone.now(),
            coupon__end_date__gte=timezone.now()
        ).select_related('coupon')
        
        serializer = UserCouponSerializer(user_coupons, many=True)
        return APIResponse.success(data=serializer.data)


class UseCouponView(APIView):
    """使用优惠券"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """验证优惠券是否可用"""
        coupon_code = request.data.get('coupon_code')
        order_amount = request.data.get('order_amount', 0)
        
        if not coupon_code:
            return APIResponse.error(message='请提供优惠券代码')
        
        try:
            coupon = Coupon.objects.get(code=coupon_code, status='active')
            
            if not coupon.is_valid:
                return APIResponse.error(message='优惠券已过期或已用完')
            
            # 检查用户是否已使用过此优惠券
            user_coupon = UserCoupon.objects.filter(
                user=request.user,
                coupon=coupon
            ).first()
            
            if user_coupon and user_coupon.status == 'used':
                return APIResponse.error(message='您已使用过此优惠券')
            
            # 检查最低消费金额
            if order_amount < coupon.min_amount:
                return APIResponse.error(
                    message=f'订单金额需满{coupon.min_amount}元才能使用此优惠券'
                )
            
            # 计算优惠金额
            if coupon.coupon_type == 'amount':
                discount = min(coupon.discount_amount, order_amount)
            elif coupon.coupon_type == 'discount':
                discount = order_amount * (1 - coupon.discount_rate)
                if coupon.max_discount:
                    discount = min(discount, coupon.max_discount)
            else:
                discount = 0
            
            return APIResponse.success(
                data={
                    'coupon_id': coupon.id,
                    'coupon_name': coupon.name,
                    'discount_amount': float(discount),
                    'final_amount': float(order_amount - discount)
                },
                message='优惠券可用'
            )
            
        except Coupon.DoesNotExist:
            return APIResponse.error(message='优惠券不存在')


class ServicePackageViewSet(viewsets.ModelViewSet):
    """服务套餐管理ViewSet"""
    queryset = ServicePackage.objects.all()
    serializer_class = ServicePackageSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """获取查询集"""
        if self.request.user.is_staff:
            return ServicePackage.objects.all()
        return ServicePackage.objects.filter(status='active')
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return ServicePackageListSerializer
        return ServicePackageSerializer
    
    @action(detail=True, methods=['post'])
    def toggle_status(self, request, pk=None):
        """切换套餐状态（管理员操作）"""
        if not request.user.is_staff:
            return APIResponse.forbidden(message='无权限执行此操作')
        
        package = self.get_object()
        new_status = request.data.get('status')
        
        if new_status in ['active', 'inactive']:
            package.status = new_status
            package.save()
            return APIResponse.success(message=f'套餐状态已更新为{package.get_status_display()}')
        
        return APIResponse.error(message='无效的状态值')


class OrderViewSet(viewsets.ModelViewSet):
    """订单管理ViewSet"""
    serializer_class = OrderSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """获取查询集"""
        if self.request.user.is_staff:
            return Order.objects.all().select_related('user', 'company', 'package')
        return Order.objects.filter(user=self.request.user).select_related('company', 'package')
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """取消订单"""
        order = self.get_object()
        
        if order.user != request.user and not request.user.is_staff:
            return APIResponse.forbidden(message='无权限执行此操作')
        
        if order.status != 'pending':
            return APIResponse.error(message='只能取消待支付的订单')
        
        order.status = 'cancelled'
        order.save()
        
        return APIResponse.success(message='订单已取消')
    
    @action(detail=True, methods=['post'])
    def refund(self, request, pk=None):
        """申请退款（管理员操作）"""
        if not request.user.is_staff:
            return APIResponse.forbidden(message='无权限执行此操作')
        
        order = self.get_object()
        
        if order.status not in ['paid', 'active']:
            return APIResponse.error(message='订单状态不支持退款')
        
        # 这里应该调用退款接口
        # 暂时模拟退款成功
        order.status = 'refunded'
        order.save()
        
        return APIResponse.success(message='退款处理成功')


class CouponViewSet(viewsets.ModelViewSet):
    """优惠券管理ViewSet"""
    queryset = Coupon.objects.all()
    serializer_class = CouponSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """获取查询集"""
        if self.request.user.is_staff:
            return Coupon.objects.all()
        
        # 普通用户只能看到可用的优惠券
        return Coupon.objects.filter(
            status='active',
            start_date__lte=timezone.now(),
            end_date__gte=timezone.now()
        )
    
    @action(detail=True, methods=['post'])
    def claim(self, request, pk=None):
        """领取优惠券"""
        coupon = self.get_object()
        
        if not coupon.is_valid:
            return APIResponse.error(message='优惠券已过期或已用完')
        
        # 检查用户是否已领取
        user_coupon, created = UserCoupon.objects.get_or_create(
            user=request.user,
            coupon=coupon,
            defaults={'status': 'unused'}
        )
        
        if not created:
            return APIResponse.error(message='您已领取过此优惠券')
        
        return APIResponse.success(message='优惠券领取成功')

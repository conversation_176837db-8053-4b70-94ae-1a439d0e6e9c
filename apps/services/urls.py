from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'packages', views.ServicePackageViewSet, basename='service-package')
router.register(r'orders', views.OrderViewSet, basename='order')
router.register(r'coupons', views.CouponViewSet, basename='coupon')

urlpatterns = [
    # 服务套餐
    path('packages/list/', views.PackageListView.as_view(), name='package_list'),
    path('packages/<int:pk>/detail/', views.PackageDetailView.as_view(), name='package_detail'),
    
    # 订单管理
    path('orders/create/', views.CreateOrderView.as_view(), name='create_order'),
    path('orders/pay/', views.PayOrderView.as_view(), name='pay_order'),
    path('orders/renew/', views.RenewOrderView.as_view(), name='renew_order'),
    
    # 优惠券
    path('coupons/available/', views.AvailableCouponsView.as_view(), name='available_coupons'),
    path('coupons/use/', views.UseCouponView.as_view(), name='use_coupon'),
    
    # 其他路由
    path('', include(router.urls)),
]

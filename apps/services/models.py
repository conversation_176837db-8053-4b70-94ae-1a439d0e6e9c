from django.db import models
from django.conf import settings
from django.utils import timezone
from decimal import Decimal


class ServicePackage(models.Model):
    """服务套餐"""
    PACKAGE_TYPE_CHOICES = [
        ('individual', '个体户套餐'),
        ('small_scale', '小规模纳税人套餐'),
        ('general', '一般纳税人套餐'),
    ]
    
    STATUS_CHOICES = [
        ('active', '启用'),
        ('inactive', '停用'),
        ('draft', '草稿'),
    ]
    
    name = models.CharField('套餐名称', max_length=100)
    package_type = models.CharField('套餐类型', max_length=20, choices=PACKAGE_TYPE_CHOICES)
    description = models.TextField('套餐描述', blank=True)
    
    # 价格设置
    monthly_price = models.DecimalField('月付价格', max_digits=10, decimal_places=2)
    quarterly_price = models.DecimalField('季付价格', max_digits=10, decimal_places=2, blank=True, null=True)
    yearly_price = models.DecimalField('年付价格', max_digits=10, decimal_places=2, blank=True, null=True)
    first_time_price = models.DecimalField('首次开通价格', max_digits=10, decimal_places=2, blank=True, null=True)
    
    # 套餐特性
    features = models.JSONField('套餐特性', default=list, help_text='套餐包含的服务特性列表')
    max_transactions = models.PositiveIntegerField('最大交易笔数/月', default=0, help_text='0表示无限制')
    max_invoices = models.PositiveIntegerField('最大发票张数/月', default=0, help_text='0表示无限制')
    max_reports = models.PositiveIntegerField('最大报表数量/月', default=0, help_text='0表示无限制')
    
    # 服务内容
    includes_bookkeeping = models.BooleanField('包含记账服务', default=True)
    includes_tax_filing = models.BooleanField('包含报税服务', default=True)
    includes_financial_reports = models.BooleanField('包含财务报表', default=True)
    includes_tax_planning = models.BooleanField('包含税务筹划', default=False)
    includes_audit = models.BooleanField('包含审计服务', default=False)
    includes_consultation = models.BooleanField('包含财务咨询', default=False)
    
    # 状态和排序
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='active')
    is_recommended = models.BooleanField('推荐套餐', default=False)
    sort_order = models.PositiveIntegerField('排序', default=0)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'service_packages'
        verbose_name = '服务套餐'
        verbose_name_plural = '服务套餐'
        ordering = ['sort_order', '-created_at']
    
    def __str__(self):
        return self.name
    
    def get_price_by_period(self, period='monthly'):
        """根据付费周期获取价格"""
        price_map = {
            'monthly': self.monthly_price,
            'quarterly': self.quarterly_price or self.monthly_price * 3,
            'yearly': self.yearly_price or self.monthly_price * 12,
        }
        return price_map.get(period, self.monthly_price)


class Order(models.Model):
    """订单"""
    STATUS_CHOICES = [
        ('pending', '待支付'),
        ('paid', '已支付'),
        ('active', '服务中'),
        ('expired', '已过期'),
        ('cancelled', '已取消'),
        ('refunded', '已退款'),
    ]
    
    PAYMENT_PERIOD_CHOICES = [
        ('monthly', '按月'),
        ('quarterly', '按季度'),
        ('yearly', '按年'),
    ]
    
    # 基本信息
    order_no = models.CharField('订单号', max_length=32, unique=True)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='orders', verbose_name='用户')
    company = models.ForeignKey('companies.Company', on_delete=models.CASCADE, related_name='orders', verbose_name='企业')
    package = models.ForeignKey(ServicePackage, on_delete=models.CASCADE, related_name='orders', verbose_name='服务套餐')
    
    # 订单详情
    package_name = models.CharField('套餐名称', max_length=100)  # 冗余存储，防止套餐删除后无法查看
    payment_period = models.CharField('付费周期', max_length=20, choices=PAYMENT_PERIOD_CHOICES, default='monthly')
    months = models.PositiveIntegerField('服务月数', default=1)
    
    # 价格信息
    original_price = models.DecimalField('原价', max_digits=10, decimal_places=2)
    discount_amount = models.DecimalField('优惠金额', max_digits=10, decimal_places=2, default=Decimal('0.00'))
    final_price = models.DecimalField('实付金额', max_digits=10, decimal_places=2)
    
    # 服务期限
    service_start_date = models.DateField('服务开始日期', blank=True, null=True)
    service_end_date = models.DateField('服务结束日期', blank=True, null=True)
    
    # 订单状态
    status = models.CharField('订单状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    is_first_order = models.BooleanField('是否首次订单', default=False)
    
    # 支付信息
    paid_at = models.DateTimeField('支付时间', blank=True, null=True)
    payment_method = models.CharField('支付方式', max_length=20, blank=True)
    transaction_id = models.CharField('交易流水号', max_length=100, blank=True)
    
    # 备注
    remarks = models.TextField('备注', blank=True)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'orders'
        verbose_name = '订单'
        verbose_name_plural = '订单管理'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.order_no} - {self.package_name}"
    
    def save(self, *args, **kwargs):
        if not self.order_no:
            self.order_no = self.generate_order_no()
        super().save(*args, **kwargs)
    
    def generate_order_no(self):
        """生成订单号"""
        import time
        import random
        timestamp = str(int(time.time()))
        random_num = str(random.randint(1000, 9999))
        return f"ORD{timestamp}{random_num}"
    
    @property
    def is_expired(self):
        """是否已过期"""
        if not self.service_end_date:
            return False
        return timezone.now().date() > self.service_end_date
    
    @property
    def days_left(self):
        """剩余天数"""
        if not self.service_end_date:
            return 0
        delta = self.service_end_date - timezone.now().date()
        return max(0, delta.days)


class OrderRenewal(models.Model):
    """订单续费记录"""
    original_order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='renewals', verbose_name='原订单')
    renewal_order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='renewal_from', verbose_name='续费订单')
    
    # 续费信息
    renewal_months = models.PositiveIntegerField('续费月数')
    renewal_price = models.DecimalField('续费金额', max_digits=10, decimal_places=2)
    
    created_at = models.DateTimeField('续费时间', auto_now_add=True)
    
    class Meta:
        db_table = 'order_renewals'
        verbose_name = '续费记录'
        verbose_name_plural = '续费记录'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.original_order.order_no} 续费 {self.renewal_months}个月"


class Coupon(models.Model):
    """优惠券"""
    COUPON_TYPE_CHOICES = [
        ('amount', '满减券'),
        ('discount', '折扣券'),
        ('first_order', '首单优惠'),
    ]
    
    STATUS_CHOICES = [
        ('active', '有效'),
        ('inactive', '无效'),
        ('expired', '已过期'),
    ]
    
    name = models.CharField('优惠券名称', max_length=100)
    code = models.CharField('优惠码', max_length=20, unique=True)
    coupon_type = models.CharField('优惠券类型', max_length=20, choices=COUPON_TYPE_CHOICES)
    
    # 优惠规则
    discount_amount = models.DecimalField('优惠金额', max_digits=10, decimal_places=2, blank=True, null=True)
    discount_rate = models.DecimalField('折扣率', max_digits=3, decimal_places=2, blank=True, null=True, help_text='0.8表示8折')
    min_amount = models.DecimalField('最低消费金额', max_digits=10, decimal_places=2, default=Decimal('0.00'))
    max_discount = models.DecimalField('最大优惠金额', max_digits=10, decimal_places=2, blank=True, null=True)
    
    # 使用限制
    usage_limit = models.PositiveIntegerField('使用次数限制', default=1)
    used_count = models.PositiveIntegerField('已使用次数', default=0)
    user_limit = models.PositiveIntegerField('每用户使用限制', default=1)
    
    # 适用范围
    applicable_packages = models.ManyToManyField(ServicePackage, blank=True, verbose_name='适用套餐')
    applicable_user_types = models.JSONField('适用用户类型', default=list, blank=True)
    
    # 有效期
    start_date = models.DateTimeField('开始时间')
    end_date = models.DateTimeField('结束时间')
    
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='active')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'coupons'
        verbose_name = '优惠券'
        verbose_name_plural = '优惠券管理'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.code})"
    
    @property
    def is_valid(self):
        """是否有效"""
        now = timezone.now()
        return (self.status == 'active' and 
                self.start_date <= now <= self.end_date and
                self.used_count < self.usage_limit)


class UserCoupon(models.Model):
    """用户优惠券"""
    STATUS_CHOICES = [
        ('unused', '未使用'),
        ('used', '已使用'),
        ('expired', '已过期'),
    ]
    
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='user_coupons', verbose_name='用户')
    coupon = models.ForeignKey(Coupon, on_delete=models.CASCADE, related_name='user_coupons', verbose_name='优惠券')
    order = models.ForeignKey(Order, on_delete=models.SET_NULL, blank=True, null=True, related_name='used_coupons', verbose_name='使用订单')
    
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='unused')
    obtained_at = models.DateTimeField('获得时间', auto_now_add=True)
    used_at = models.DateTimeField('使用时间', blank=True, null=True)
    
    class Meta:
        db_table = 'user_coupons'
        verbose_name = '用户优惠券'
        verbose_name_plural = '用户优惠券'
        unique_together = ['user', 'coupon']
        ordering = ['-obtained_at']
    
    def __str__(self):
        return f"{self.user.phone} - {self.coupon.name}"

from django.db import models
from django.utils import timezone
from django.contrib.auth import get_user_model

User = get_user_model()


class BannerConfig(models.Model):
    """轮播横幅配置"""
    BANNER_TYPE_CHOICES = [
        ('home', '首页轮播'),
        ('discover', '发现页轮播'),
        ('service', '服务页轮播'),
    ]

    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('published', '已发布'),
        ('expired', '已过期'),
    ]

    title = models.CharField('标题', max_length=100)
    subtitle = models.CharField('副标题', max_length=200, blank=True)
    description = models.TextField('描述', blank=True)
    image = models.ImageField('图片', upload_to='banners/', blank=True, null=True)
    background_color = models.CharField('背景色', max_length=20, default='#4A90E2')
    background_gradient = models.CharField('渐变背景', max_length=100, blank=True)

    # 链接配置
    link_type = models.CharField('链接类型', max_length=20, choices=[
        ('page', '页面跳转'),
        ('url', '外部链接'),
        ('none', '无链接'),
    ], default='none')
    link_url = models.CharField('链接地址', max_length=500, blank=True)
    link_params = models.JSONField('链接参数', default=dict, blank=True)

    # 显示配置
    banner_type = models.CharField('横幅类型', max_length=20, choices=BANNER_TYPE_CHOICES, default='home')
    sort_order = models.PositiveIntegerField('排序', default=0)
    is_active = models.BooleanField('是否启用', default=True)
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='draft')

    # 时间配置
    start_time = models.DateTimeField('开始时间', default=timezone.now)
    end_time = models.DateTimeField('结束时间', blank=True, null=True)

    # 统计数据
    view_count = models.PositiveIntegerField('浏览次数', default=0)
    click_count = models.PositiveIntegerField('点击次数', default=0)

    # 创建信息
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='创建者')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'content_banners'
        verbose_name = '轮播横幅'
        verbose_name_plural = '轮播横幅管理'
        ordering = ['banner_type', 'sort_order', '-created_at']

    def __str__(self):
        return f"{self.get_banner_type_display()} - {self.title}"

    @property
    def is_valid(self):
        """是否在有效期内"""
        now = timezone.now()
        if self.end_time:
            return self.start_time <= now <= self.end_time
        return self.start_time <= now


class ArticleCategory(models.Model):
    """文章分类"""
    name = models.CharField('分类名称', max_length=50)
    slug = models.SlugField('分类标识', max_length=50, unique=True)
    description = models.TextField('分类描述', blank=True)
    icon = models.CharField('图标', max_length=50, blank=True)
    color = models.CharField('颜色', max_length=20, default='#4A90E2')

    # 显示配置
    is_hot = models.BooleanField('热门分类', default=False)
    sort_order = models.PositiveIntegerField('排序', default=0)
    is_active = models.BooleanField('是否启用', default=True)

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'content_article_categories'
        verbose_name = '文章分类'
        verbose_name_plural = '文章分类管理'
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name


class Article(models.Model):
    """资讯文章"""
    ARTICLE_TYPE_CHOICES = [
        ('news', '新闻资讯'),
        ('policy', '政策解读'),
        ('guide', '操作指南'),
        ('case', '案例分析'),
        ('notice', '通知公告'),
    ]

    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('published', '已发布'),
        ('hidden', '已隐藏'),
    ]

    title = models.CharField('标题', max_length=200)
    subtitle = models.CharField('副标题', max_length=300, blank=True)
    summary = models.TextField('摘要', max_length=500, blank=True)
    content = models.TextField('内容')
    cover_image = models.ImageField('封面图片', upload_to='articles/', blank=True, null=True)

    # 分类和标签
    category = models.ForeignKey(ArticleCategory, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='分类')
    article_type = models.CharField('文章类型', max_length=20, choices=ARTICLE_TYPE_CHOICES, default='news')
    tags = models.CharField('标签', max_length=200, blank=True, help_text='多个标签用逗号分隔')

    # 显示配置
    is_featured = models.BooleanField('精选文章', default=False)
    is_top = models.BooleanField('置顶文章', default=False)
    sort_order = models.PositiveIntegerField('排序', default=0)
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='draft')

    # 统计数据
    view_count = models.PositiveIntegerField('浏览次数', default=0)
    like_count = models.PositiveIntegerField('点赞次数', default=0)
    share_count = models.PositiveIntegerField('分享次数', default=0)

    # 时间配置
    publish_time = models.DateTimeField('发布时间', default=timezone.now)

    # 创建信息
    author = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='作者')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'content_articles'
        verbose_name = '资讯文章'
        verbose_name_plural = '资讯文章管理'
        ordering = ['-is_top', '-sort_order', '-publish_time']

    def __str__(self):
        return self.title

    @property
    def tag_list(self):
        """标签列表"""
        if self.tags:
            return [tag.strip() for tag in self.tags.split(',') if tag.strip()]
        return []


class ToolCategory(models.Model):
    """实用工具分类"""
    name = models.CharField('分类名称', max_length=50)
    slug = models.SlugField('分类标识', max_length=50, unique=True)
    description = models.TextField('分类描述', blank=True)
    icon = models.CharField('图标', max_length=50, blank=True)
    color = models.CharField('颜色', max_length=20, default='#4A90E2')

    sort_order = models.PositiveIntegerField('排序', default=0)
    is_active = models.BooleanField('是否启用', default=True)

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'content_tool_categories'
        verbose_name = '工具分类'
        verbose_name_plural = '工具分类管理'
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name


class UtilityTool(models.Model):
    """实用工具"""
    TOOL_TYPE_CHOICES = [
        ('calculator', '计算器'),
        ('calendar', '日历'),
        ('checker', '查验工具'),
        ('analyzer', '分析工具'),
        ('query', '查询工具'),
        ('service', '服务工具'),
    ]

    LINK_TYPE_CHOICES = [
        ('page', '页面跳转'),
        ('miniprogram', '小程序页面'),
        ('webview', '网页链接'),
        ('function', '功能调用'),
        ('coming_soon', '敬请期待'),
    ]

    STATUS_CHOICES = [
        ('active', '正常'),
        ('maintenance', '维护中'),
        ('coming_soon', '即将上线'),
        ('disabled', '已禁用'),
    ]

    name = models.CharField('工具名称', max_length=50)
    description = models.CharField('工具描述', max_length=200, blank=True)
    icon = models.CharField('图标', max_length=50, default='🔧')
    icon_color = models.CharField('图标颜色', max_length=20, default='#4A90E2')
    background_color = models.CharField('背景色', max_length=20, default='#f0f8ff')

    # 分类和类型
    category = models.ForeignKey(ToolCategory, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='分类')
    tool_type = models.CharField('工具类型', max_length=20, choices=TOOL_TYPE_CHOICES, default='service')

    # 链接配置
    link_type = models.CharField('链接类型', max_length=20, choices=LINK_TYPE_CHOICES, default='page')
    link_url = models.CharField('链接地址', max_length=500, blank=True)
    link_params = models.JSONField('链接参数', default=dict, blank=True)

    # 显示配置
    is_hot = models.BooleanField('热门工具', default=False)
    is_new = models.BooleanField('新工具', default=False)
    sort_order = models.PositiveIntegerField('排序', default=0)
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='active')

    # 统计数据
    use_count = models.PositiveIntegerField('使用次数', default=0)

    # 创建信息
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='创建者')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'content_utility_tools'
        verbose_name = '实用工具'
        verbose_name_plural = '实用工具管理'
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name

    @property
    def is_available(self):
        """是否可用"""
        return self.status == 'active'


class NavigationMenu(models.Model):
    """导航菜单"""
    MENU_TYPE_CHOICES = [
        ('main', '主导航'),
        ('footer', '底部导航'),
        ('sidebar', '侧边导航'),
        ('quick', '快捷导航'),
    ]

    name = models.CharField('菜单名称', max_length=50)
    icon = models.CharField('图标', max_length=50, blank=True)
    icon_color = models.CharField('图标颜色', max_length=20, default='#4A90E2')

    # 链接配置
    link_type = models.CharField('链接类型', max_length=20, choices=[
        ('page', '页面跳转'),
        ('tab', 'Tab切换'),
        ('external', '外部链接'),
        ('function', '功能调用'),
    ], default='page')
    link_url = models.CharField('链接地址', max_length=500, blank=True)
    link_params = models.JSONField('链接参数', default=dict, blank=True)

    # 显示配置
    menu_type = models.CharField('菜单类型', max_length=20, choices=MENU_TYPE_CHOICES, default='main')
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name='父菜单')
    sort_order = models.PositiveIntegerField('排序', default=0)
    is_active = models.BooleanField('是否启用', default=True)

    # 权限配置
    require_login = models.BooleanField('需要登录', default=False)
    require_vip = models.BooleanField('需要VIP', default=False)

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'content_navigation_menus'
        verbose_name = '导航菜单'
        verbose_name_plural = '导航菜单管理'
        ordering = ['menu_type', 'sort_order', 'name']

    def __str__(self):
        if self.parent:
            return f"{self.parent.name} - {self.name}"
        return self.name

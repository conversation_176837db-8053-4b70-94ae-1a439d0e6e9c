from django.urls import path
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from . import views

app_name = 'content'

@csrf_exempt
def content_api_root(request):
    """内容API根视图"""
    return JsonResponse({
        'code': 200,
        'message': '内容API服务正常',
        'endpoints': {
            'banners': '/api/v1/content/banners/',
            'article_categories': '/api/v1/content/article-categories/',
            'articles': '/api/v1/content/articles/',
            'tool_categories': '/api/v1/content/tool-categories/',
            'tools': '/api/v1/content/tools/',
            'navigation': '/api/v1/content/navigation/',
        }
    })

urlpatterns = [
    # API根路径
    path('', content_api_root, name='content_api_root'),

    # 轮播横幅
    path('banners/', views.BannerListView.as_view(), name='banner-list'),
    path('banners/<int:banner_id>/click/', views.banner_click, name='banner-click'),

    # 文章管理
    path('article-categories/', views.ArticleCategoryListView.as_view(), name='article-category-list'),
    path('articles/', views.ArticleListView.as_view(), name='article-list'),
    path('articles/<int:id>/', views.ArticleDetailView.as_view(), name='article-detail'),

    # 实用工具
    path('tool-categories/', views.ToolCategoryListView.as_view(), name='tool-category-list'),
    path('tools/', views.UtilityToolListView.as_view(), name='tool-list'),
    path('tools/<int:tool_id>/use/', views.tool_use, name='tool-use'),

    # 导航菜单
    path('navigation/', views.NavigationMenuListView.as_view(), name='navigation-list'),
]

from rest_framework import serializers
from .models import BannerConfig, ArticleCategory, Article, ToolCategory, UtilityTool, NavigationMenu


class BannerConfigSerializer(serializers.ModelSerializer):
    """轮播横幅序列化器"""
    image_url = serializers.SerializerMethodField()
    
    class Meta:
        model = BannerConfig
        fields = [
            'id', 'title', 'subtitle', 'description', 'image_url', 
            'background_color', 'background_gradient', 'link_type', 
            'link_url', 'link_params', 'banner_type', 'sort_order'
        ]
    
    def get_image_url(self, obj):
        if obj.image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.image.url)
            return obj.image.url
        return None


class ArticleCategorySerializer(serializers.ModelSerializer):
    """文章分类序列化器"""
    
    class Meta:
        model = ArticleCategory
        fields = ['id', 'name', 'slug', 'description', 'icon', 'color', 'is_hot']


class ArticleListSerializer(serializers.ModelSerializer):
    """文章列表序列化器"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    cover_image_url = serializers.SerializerMethodField()
    tag_list = serializers.ReadOnlyField()
    
    class Meta:
        model = Article
        fields = [
            'id', 'title', 'subtitle', 'summary', 'cover_image_url',
            'category_name', 'article_type', 'tag_list', 'is_featured',
            'is_top', 'view_count', 'like_count', 'publish_time'
        ]
    
    def get_cover_image_url(self, obj):
        if obj.cover_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.cover_image.url)
            return obj.cover_image.url
        return None


class ArticleDetailSerializer(serializers.ModelSerializer):
    """文章详情序列化器"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    cover_image_url = serializers.SerializerMethodField()
    tag_list = serializers.ReadOnlyField()
    author_name = serializers.CharField(source='author.nickname', read_only=True)
    
    class Meta:
        model = Article
        fields = [
            'id', 'title', 'subtitle', 'summary', 'content', 'cover_image_url',
            'category_name', 'article_type', 'tag_list', 'is_featured',
            'is_top', 'view_count', 'like_count', 'share_count',
            'author_name', 'publish_time', 'updated_at'
        ]
    
    def get_cover_image_url(self, obj):
        if obj.cover_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.cover_image.url)
            return obj.cover_image.url
        return None


class ToolCategorySerializer(serializers.ModelSerializer):
    """工具分类序列化器"""
    
    class Meta:
        model = ToolCategory
        fields = ['id', 'name', 'slug', 'description', 'icon', 'color']


class UtilityToolSerializer(serializers.ModelSerializer):
    """实用工具序列化器"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    
    class Meta:
        model = UtilityTool
        fields = [
            'id', 'name', 'description', 'icon', 'icon_color', 
            'background_color', 'category_name', 'tool_type',
            'link_type', 'link_url', 'link_params', 'is_hot',
            'is_new', 'status', 'use_count'
        ]


class NavigationMenuSerializer(serializers.ModelSerializer):
    """导航菜单序列化器"""
    children = serializers.SerializerMethodField()
    
    class Meta:
        model = NavigationMenu
        fields = [
            'id', 'name', 'icon', 'icon_color', 'link_type',
            'link_url', 'link_params', 'menu_type', 'sort_order',
            'require_login', 'require_vip', 'children'
        ]
    
    def get_children(self, obj):
        if hasattr(obj, 'navigationmenu_set'):
            children = obj.navigationmenu_set.filter(is_active=True).order_by('sort_order')
            return NavigationMenuSerializer(children, many=True, context=self.context).data
        return []

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import BannerConfig, ArticleCategory, Article, ToolCategory, UtilityTool, NavigationMenu


@admin.register(BannerConfig)
class BannerConfigAdmin(admin.ModelAdmin):
    """轮播横幅管理"""
    list_display = [
        'title', 'banner_type', 'status', 'image_preview',
        'sort_order', 'view_count', 'click_count', 'is_active', 'created_at'
    ]
    list_filter = ['banner_type', 'status', 'is_active', 'created_at']
    search_fields = ['title', 'subtitle', 'description']
    list_editable = ['sort_order', 'is_active', 'status']
    readonly_fields = ['view_count', 'click_count', 'created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('title', 'subtitle', 'description', 'banner_type')
        }),
        ('视觉设计', {
            'fields': ('image', 'background_color', 'background_gradient')
        }),
        ('链接配置', {
            'fields': ('link_type', 'link_url', 'link_params'),
            'classes': ('collapse',)
        }),
        ('显示设置', {
            'fields': ('sort_order', 'is_active', 'status')
        }),
        ('时间设置', {
            'fields': ('start_time', 'end_time')
        }),
        ('统计信息', {
            'fields': ('view_count', 'click_count', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def image_preview(self, obj):
        """图片预览"""
        if obj.image:
            return format_html(
                '<img src="{}" style="width: 60px; height: 40px; object-fit: cover; border-radius: 4px;" />',
                obj.image.url
            )
        return '无图片'
    image_preview.short_description = '图片预览'

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时设置创建者
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(ArticleCategory)
class ArticleCategoryAdmin(admin.ModelAdmin):
    """文章分类管理"""
    list_display = ['name', 'slug', 'icon_display', 'color_display', 'is_hot', 'sort_order', 'is_active']
    list_filter = ['is_hot', 'is_active', 'created_at']
    search_fields = ['name', 'slug', 'description']
    list_editable = ['sort_order', 'is_hot', 'is_active']
    prepopulated_fields = {'slug': ('name',)}

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'slug', 'description')
        }),
        ('显示设置', {
            'fields': ('icon', 'color', 'is_hot', 'sort_order', 'is_active')
        })
    )

    def icon_display(self, obj):
        """图标显示"""
        if obj.icon:
            return format_html(
                '<span style="font-size: 20px;">{}</span>',
                obj.icon
            )
        return '-'
    icon_display.short_description = '图标'

    def color_display(self, obj):
        """颜色显示"""
        return format_html(
            '<div style="width: 20px; height: 20px; background-color: {}; border-radius: 50%; display: inline-block;"></div>',
            obj.color
        )
    color_display.short_description = '颜色'


@admin.register(Article)
class ArticleAdmin(admin.ModelAdmin):
    """资讯文章管理"""
    list_display = [
        'title', 'category', 'article_type', 'status', 'cover_preview',
        'is_featured', 'is_top', 'view_count', 'author', 'publish_time'
    ]
    list_filter = ['category', 'article_type', 'status', 'is_featured', 'is_top', 'publish_time']
    search_fields = ['title', 'subtitle', 'summary', 'content', 'tags']
    list_editable = ['status', 'is_featured', 'is_top']
    readonly_fields = ['view_count', 'like_count', 'share_count', 'created_at', 'updated_at']
    date_hierarchy = 'publish_time'

    fieldsets = (
        ('基本信息', {
            'fields': ('title', 'subtitle', 'summary', 'cover_image')
        }),
        ('内容', {
            'fields': ('content',)
        }),
        ('分类标签', {
            'fields': ('category', 'article_type', 'tags')
        }),
        ('显示设置', {
            'fields': ('is_featured', 'is_top', 'sort_order', 'status', 'publish_time')
        }),
        ('统计信息', {
            'fields': ('view_count', 'like_count', 'share_count', 'author', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def cover_preview(self, obj):
        """封面预览"""
        if obj.cover_image:
            return format_html(
                '<img src="{}" style="width: 60px; height: 40px; object-fit: cover; border-radius: 4px;" />',
                obj.cover_image.url
            )
        return '无封面'
    cover_preview.short_description = '封面预览'

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时设置作者
            obj.author = request.user
        super().save_model(request, obj, form, change)


@admin.register(ToolCategory)
class ToolCategoryAdmin(admin.ModelAdmin):
    """工具分类管理"""
    list_display = ['name', 'slug', 'icon_display', 'color_display', 'sort_order', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'slug', 'description']
    list_editable = ['sort_order', 'is_active']
    prepopulated_fields = {'slug': ('name',)}

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'slug', 'description')
        }),
        ('显示设置', {
            'fields': ('icon', 'color', 'sort_order', 'is_active')
        })
    )

    def icon_display(self, obj):
        """图标显示"""
        if obj.icon:
            return format_html(
                '<span style="font-size: 20px;">{}</span>',
                obj.icon
            )
        return '-'
    icon_display.short_description = '图标'

    def color_display(self, obj):
        """颜色显示"""
        return format_html(
            '<div style="width: 20px; height: 20px; background-color: {}; border-radius: 50%; display: inline-block;"></div>',
            obj.color
        )
    color_display.short_description = '颜色'


@admin.register(UtilityTool)
class UtilityToolAdmin(admin.ModelAdmin):
    """实用工具管理"""
    list_display = [
        'name', 'category', 'tool_type', 'status', 'icon_display',
        'is_hot', 'is_new', 'sort_order', 'use_count', 'created_at'
    ]
    list_filter = ['category', 'tool_type', 'status', 'is_hot', 'is_new', 'created_at']
    search_fields = ['name', 'description']
    list_editable = ['status', 'is_hot', 'is_new', 'sort_order']
    readonly_fields = ['use_count', 'created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'category', 'tool_type')
        }),
        ('视觉设计', {
            'fields': ('icon', 'icon_color', 'background_color')
        }),
        ('链接配置', {
            'fields': ('link_type', 'link_url', 'link_params'),
            'classes': ('collapse',)
        }),
        ('显示设置', {
            'fields': ('is_hot', 'is_new', 'sort_order', 'status')
        }),
        ('统计信息', {
            'fields': ('use_count', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def icon_display(self, obj):
        """图标显示"""
        return format_html(
            '<span style="font-size: 20px; color: {}; background-color: {}; padding: 8px; border-radius: 8px; display: inline-block;">{}</span>',
            obj.icon_color,
            obj.background_color,
            obj.icon
        )
    icon_display.short_description = '图标预览'

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时设置创建者
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(NavigationMenu)
class NavigationMenuAdmin(admin.ModelAdmin):
    """导航菜单管理"""
    list_display = [
        'name', 'menu_type', 'parent', 'icon_display',
        'require_login', 'require_vip', 'sort_order', 'is_active'
    ]
    list_filter = ['menu_type', 'require_login', 'require_vip', 'is_active', 'created_at']
    search_fields = ['name', 'link_url']
    list_editable = ['sort_order', 'is_active']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'icon', 'icon_color', 'menu_type', 'parent')
        }),
        ('链接配置', {
            'fields': ('link_type', 'link_url', 'link_params')
        }),
        ('显示设置', {
            'fields': ('sort_order', 'is_active')
        }),
        ('权限设置', {
            'fields': ('require_login', 'require_vip')
        })
    )

    def icon_display(self, obj):
        """图标显示"""
        if obj.icon:
            return format_html(
                '<span style="font-size: 20px; color: {};">{}</span>',
                obj.icon_color,
                obj.icon
            )
        return '-'
    icon_display.short_description = '图标'

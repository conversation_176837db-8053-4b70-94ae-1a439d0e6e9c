from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import F, Q
from .models import BannerConfig, ArticleCategory, Article, ToolCategory, UtilityTool, NavigationMenu
from .serializers import (
    BannerConfigSerializer, ArticleCategorySerializer, ArticleListSerializer,
    ArticleDetailSerializer, ToolCategorySerializer, UtilityToolSerializer,
    NavigationMenuSerializer
)
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class BannerListView(generics.ListAPIView):
    """轮播横幅列表"""
    serializer_class = BannerConfigSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        banner_type = self.request.query_params.get('type', 'home')
        now = timezone.now()

        queryset = BannerConfig.objects.filter(
            banner_type=banner_type,
            is_active=True,
            status='published',
            start_time__lte=now
        )

        # 过滤未过期的横幅
        queryset = queryset.filter(
            Q(end_time__isnull=True) | Q(end_time__gte=now)
        )

        return queryset.order_by('sort_order', '-created_at')


@api_view(['POST'])
@permission_classes([AllowAny])
def banner_click(request, banner_id):
    """横幅点击统计"""
    try:
        banner = BannerConfig.objects.get(id=banner_id, is_active=True)
        banner.click_count = F('click_count') + 1
        banner.save(update_fields=['click_count'])
        return Response({'message': '统计成功'})
    except BannerConfig.DoesNotExist:
        return Response({'error': '横幅不存在'}, status=status.HTTP_404_NOT_FOUND)


class ArticleCategoryListView(generics.ListAPIView):
    """文章分类列表"""
    serializer_class = ArticleCategorySerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        return ArticleCategory.objects.filter(is_active=True).order_by('sort_order', 'name')


class ArticleListView(generics.ListAPIView):
    """文章列表"""
    serializer_class = ArticleListSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        queryset = Article.objects.filter(status='published')

        # 分类筛选
        category_id = self.request.query_params.get('category')
        if category_id:
            queryset = queryset.filter(category_id=category_id)

        # 文章类型筛选
        article_type = self.request.query_params.get('type')
        if article_type:
            queryset = queryset.filter(article_type=article_type)

        # 是否精选
        is_featured = self.request.query_params.get('featured')
        if is_featured == 'true':
            queryset = queryset.filter(is_featured=True)

        return queryset.order_by('-is_top', '-sort_order', '-publish_time')


class ArticleDetailView(generics.RetrieveAPIView):
    """文章详情"""
    serializer_class = ArticleDetailSerializer
    permission_classes = [AllowAny]
    lookup_field = 'id'

    def get_queryset(self):
        return Article.objects.filter(status='published')

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        # 增加浏览次数
        instance.view_count = F('view_count') + 1
        instance.save(update_fields=['view_count'])

        serializer = self.get_serializer(instance)
        return Response(serializer.data)


class ToolCategoryListView(generics.ListAPIView):
    """工具分类列表"""
    serializer_class = ToolCategorySerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        return ToolCategory.objects.filter(is_active=True).order_by('sort_order', 'name')


class UtilityToolListView(generics.ListAPIView):
    """实用工具列表"""
    serializer_class = UtilityToolSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        queryset = UtilityTool.objects.filter(status__in=['active', 'coming_soon'])

        # 分类筛选
        category_id = self.request.query_params.get('category')
        if category_id:
            queryset = queryset.filter(category_id=category_id)

        # 工具类型筛选
        tool_type = self.request.query_params.get('type')
        if tool_type:
            queryset = queryset.filter(tool_type=tool_type)

        # 是否热门
        is_hot = self.request.query_params.get('hot')
        if is_hot == 'true':
            queryset = queryset.filter(is_hot=True)

        return queryset.order_by('sort_order', 'name')


@api_view(['POST'])
@permission_classes([AllowAny])
def tool_use(request, tool_id):
    """工具使用统计"""
    try:
        tool = UtilityTool.objects.get(id=tool_id, status='active')
        tool.use_count = F('use_count') + 1
        tool.save(update_fields=['use_count'])
        return Response({'message': '统计成功'})
    except UtilityTool.DoesNotExist:
        return Response({'error': '工具不存在'}, status=status.HTTP_404_NOT_FOUND)


class NavigationMenuListView(generics.ListAPIView):
    """导航菜单列表"""
    serializer_class = NavigationMenuSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        menu_type = self.request.query_params.get('type', 'main')
        return NavigationMenu.objects.filter(
            menu_type=menu_type,
            is_active=True,
            parent__isnull=True  # 只返回顶级菜单，子菜单通过序列化器获取
        ).order_by('sort_order', 'name')

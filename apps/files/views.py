from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.views import APIView
from rest_framework.parsers import <PERSON>PartPars<PERSON>, FormParser
from django.shortcuts import get_object_or_404
from django.http import HttpResponse, Http404
from django.utils import timezone
from django.db.models import Q, Sum, Count
import os
import uuid
import mimetypes

from .models import UploadedFile, FileCategory, FileAccessLog
from .serializers import (
    UploadedFileSerializer, FileCategorySerializer, FileUploadSerializer,
    BatchFileUploadSerializer, FileAccessLogSerializer, FileSearchSerializer,
    FileShareSerializer, FileStatisticsSerializer, FileMoveSerializer,
    FileRenameSerializer
)
from utils.response import APIResponse
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class UploadedFileViewSet(viewsets.ModelViewSet):
    """文件管理ViewSet"""
    serializer_class = UploadedFileSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """获取查询集"""
        if self.request.user.is_staff:
            return UploadedFile.objects.filter(is_deleted=False).select_related('user', 'category')
        return UploadedFile.objects.filter(
            user=self.request.user,
            is_deleted=False
        ).select_related('category')

    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """下载文件"""
        file_obj = self.get_object()

        # 记录访问日志
        FileAccessLog.objects.create(
            user=request.user,
            file=file_obj,
            action='download',
            ip_address=self.get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        try:
            # 获取文件路径
            file_path = file_obj.file_path.path

            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise Http404('文件不存在')

            # 获取文件内容
            with open(file_path, 'rb') as f:
                file_content = f.read()

            # 设置响应头
            response = HttpResponse(file_content, content_type=file_obj.mime_type)
            response['Content-Disposition'] = f'attachment; filename="{file_obj.original_filename}"'
            response['Content-Length'] = file_obj.file_size

            return response

        except Exception as e:
            return APIResponse.error(message=f'文件下载失败: {str(e)}')

    @action(detail=True, methods=['post'])
    def rename(self, request, pk=None):
        """重命名文件"""
        file_obj = self.get_object()

        if file_obj.user != request.user and not request.user.is_staff:
            return APIResponse.forbidden(message='无权限执行此操作')

        serializer = FileRenameSerializer(data=request.data)
        if serializer.is_valid():
            new_name = serializer.validated_data['new_name']

            # 保留原始扩展名
            original_ext = os.path.splitext(file_obj.original_filename)[1]
            if not new_name.endswith(original_ext):
                new_name += original_ext

            file_obj.original_filename = new_name
            file_obj.save()

            return APIResponse.success(message='文件重命名成功')

        return APIResponse.error(message='重命名失败', errors=serializer.errors)

    @action(detail=True, methods=['delete'])
    def soft_delete(self, request, pk=None):
        """软删除文件"""
        file_obj = self.get_object()

        if file_obj.user != request.user and not request.user.is_staff:
            return APIResponse.forbidden(message='无权限执行此操作')

        file_obj.is_deleted = True
        file_obj.save()

        return APIResponse.success(message='文件已删除')

    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class FileCategoryViewSet(viewsets.ModelViewSet):
    """文件分类ViewSet"""
    queryset = FileCategory.objects.filter(is_active=True)
    serializer_class = FileCategorySerializer
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=True, methods=['post'])
    def toggle_status(self, request, pk=None):
        """切换分类状态（管理员操作）"""
        if not request.user.is_staff:
            return APIResponse.forbidden(message='无权限执行此操作')

        category = self.get_object()
        category.is_active = not category.is_active
        category.save()

        status_text = '启用' if category.is_active else '禁用'
        return APIResponse.success(message=f'分类已{status_text}')


class FileUploadView(APIView):
    """文件上传"""
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request):
        """上传文件"""
        try:
            # 验证请求数据
            serializer = FileUploadSerializer(data=request.data)
            if not serializer.is_valid():
                return APIResponse.error(
                    message='请求参数验证失败',
                    errors=serializer.errors
                )

            file = serializer.validated_data['file']
            category = serializer.validated_data['category']
            description = serializer.validated_data.get('description', '')
            tags = serializer.validated_data.get('tags', [])
            is_public = serializer.validated_data.get('is_public', False)

            # 文件安全检查
            security_check = self._perform_security_check(file)
            if not security_check['safe']:
                return APIResponse.error(message=security_check['message'])

            # 生成存储文件名
            file_ext = os.path.splitext(file.name)[1].lower()
            stored_filename = f"{uuid.uuid4()}{file_ext}"

            # 获取文件类型和MIME类型
            file_type = self._get_file_type(file_ext)
            mime_type = mimetypes.guess_type(file.name)[0] or 'application/octet-stream'

            # 计算文件哈希（用于去重）
            file_hash = self._calculate_file_hash(file)

            # 检查文件是否已存在
            existing_file = UploadedFile.objects.filter(
                user=request.user,
                file_hash=file_hash,
                is_deleted=False
            ).first()

            if existing_file:
                return APIResponse.success(
                    data=UploadedFileSerializer(existing_file, context={'request': request}).data,
                    message='文件已存在，返回已有文件信息'
                )

            # 获取用户企业信息
            company = None
            try:
                company = request.user.companies.first()
            except:
                pass

            # 创建文件记录
            uploaded_file = UploadedFile.objects.create(
                user=request.user,
                company=company,
                category=category,
                original_name=file.name,
                file_name=stored_filename,
                file_path=file,
                file_size=file.size,
                file_type=file_type,
                mime_type=mime_type,
                file_extension=file_ext,
                file_hash=file_hash,
                description=description,
                tags=tags,
                is_public=is_public
            )

            # 记录上传日志
            FileAccessLog.objects.create(
                user=request.user,
                file=uploaded_file,
                action='upload',
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                details={
                    'file_size': file.size,
                    'file_type': file_type,
                    'category': category.name if category else None
                }
            )

            # 返回成功响应
            response_data = UploadedFileSerializer(uploaded_file, context={'request': request}).data

            # 添加额外信息
            response_data.update({
                'upload_success': True,
                'file_hash': file_hash,
                'duplicate': False
            })

            return APIResponse.created(
                data=response_data,
                message='文件上传成功'
            )

        except Exception as e:
            # 记录错误日志
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"文件上传失败: {str(e)}", exc_info=True)

            return APIResponse.error(message=f'文件上传失败: {str(e)}')

    def _get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

    def _perform_security_check(self, file):
        """执行文件安全检查"""
        try:
            # 检查文件扩展名
            file_ext = os.path.splitext(file.name)[1].lower()
            dangerous_exts = ['.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js']

            if file_ext in dangerous_exts:
                return {
                    'safe': False,
                    'message': f'不允许上传 {file_ext} 类型的文件'
                }

            # 检查文件内容（简单的魔数检查）
            file.seek(0)
            file_header = file.read(1024)
            file.seek(0)

            # 检查是否为可执行文件
            if file_header.startswith(b'MZ') or file_header.startswith(b'\x7fELF'):
                return {
                    'safe': False,
                    'message': '检测到可执行文件，上传被拒绝'
                }

            return {'safe': True}

        except Exception as e:
            return {
                'safe': False,
                'message': f'安全检查失败: {str(e)}'
            }

    def _calculate_file_hash(self, file):
        """计算文件哈希值"""
        import hashlib

        try:
            file.seek(0)
            hash_md5 = hashlib.md5()

            for chunk in iter(lambda: file.read(4096), b""):
                hash_md5.update(chunk)

            file.seek(0)
            return hash_md5.hexdigest()

        except Exception as e:
            print(f"计算文件哈希失败: {e}")
            return str(uuid.uuid4()).replace('-', '')

    def _get_file_type(self, file_ext):
        """根据扩展名获取文件类型"""
        image_exts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        document_exts = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt']
        video_exts = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']
        audio_exts = ['.mp3', '.wav', '.flac', '.aac', '.ogg']

        file_ext = file_ext.lower()
        if file_ext in image_exts:
            return 'image'
        elif file_ext in document_exts:
            return 'document'
        elif file_ext in video_exts:
            return 'video'
        elif file_ext in audio_exts:
            return 'audio'
        else:
            return 'other'

    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class BatchFileUploadView(APIView):
    """批量文件上传"""
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request):
        """批量上传文件"""
        serializer = BatchFileUploadSerializer(data=request.data)
        if serializer.is_valid():
            try:
                files = serializer.validated_data['files']
                category = serializer.validated_data['category_id']
                description = serializer.validated_data.get('description', '')
                is_public = serializer.validated_data.get('is_public', False)

                uploaded_files = []

                for file in files:
                    # 生成存储文件名
                    file_ext = os.path.splitext(file.name)[1]
                    stored_filename = f"{uuid.uuid4()}{file_ext}"

                    # 获取文件类型和MIME类型
                    file_type = self._get_file_type(file_ext)
                    mime_type = mimetypes.guess_type(file.name)[0] or 'application/octet-stream'

                    # 创建文件记录
                    uploaded_file = UploadedFile.objects.create(
                        user=request.user,
                        category=category,
                        original_filename=file.name,
                        stored_filename=stored_filename,
                        file_path=file,
                        file_size=file.size,
                        file_type=file_type,
                        mime_type=mime_type,
                        description=description,
                        is_public=is_public
                    )

                    uploaded_files.append(uploaded_file)

                    # 记录上传日志
                    FileAccessLog.objects.create(
                        user=request.user,
                        file=uploaded_file,
                        action='upload',
                        ip_address=self.get_client_ip(request),
                        user_agent=request.META.get('HTTP_USER_AGENT', '')
                    )

                return APIResponse.created(
                    data=UploadedFileSerializer(uploaded_files, many=True, context={'request': request}).data,
                    message=f'成功上传{len(uploaded_files)}个文件'
                )

            except Exception as e:
                return APIResponse.error(message=f'批量上传失败: {str(e)}')

        return APIResponse.error(message='上传失败', errors=serializer.errors)

    def _get_file_type(self, file_ext):
        """根据扩展名获取文件类型"""
        image_exts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        document_exts = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt']
        video_exts = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']
        audio_exts = ['.mp3', '.wav', '.flac', '.aac', '.ogg']

        file_ext = file_ext.lower()
        if file_ext in image_exts:
            return 'image'
        elif file_ext in document_exts:
            return 'document'
        elif file_ext in video_exts:
            return 'video'
        elif file_ext in audio_exts:
            return 'audio'
        else:
            return 'other'

    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class FileSearchView(APIView):
    """文件搜索"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """搜索文件"""
        serializer = FileSearchSerializer(data=request.query_params)
        if serializer.is_valid():
            try:
                # 基础查询
                queryset = UploadedFile.objects.filter(
                    user=request.user,
                    is_deleted=False
                ).select_related('category')

                # 关键词搜索
                keyword = serializer.validated_data.get('keyword')
                if keyword:
                    queryset = queryset.filter(
                        Q(original_filename__icontains=keyword) |
                        Q(description__icontains=keyword) |
                        Q(tags__icontains=keyword)
                    )

                # 分类筛选
                category_id = serializer.validated_data.get('category_id')
                if category_id:
                    queryset = queryset.filter(category_id=category_id)

                # 文件类型筛选
                file_type = serializer.validated_data.get('file_type')
                if file_type:
                    queryset = queryset.filter(file_type=file_type)

                # 日期范围筛选
                start_date = serializer.validated_data.get('start_date')
                if start_date:
                    queryset = queryset.filter(uploaded_at__date__gte=start_date)

                end_date = serializer.validated_data.get('end_date')
                if end_date:
                    queryset = queryset.filter(uploaded_at__date__lte=end_date)

                # 公开性筛选
                is_public = serializer.validated_data.get('is_public')
                if is_public is not None:
                    queryset = queryset.filter(is_public=is_public)

                # 标签筛选
                tags = serializer.validated_data.get('tags')
                if tags:
                    for tag in tags:
                        queryset = queryset.filter(tags__icontains=tag)

                # 排序
                queryset = queryset.order_by('-uploaded_at')

                # 分页
                from django.core.paginator import Paginator
                paginator = Paginator(queryset, 20)
                page_number = request.query_params.get('page', 1)
                page_obj = paginator.get_page(page_number)

                return APIResponse.success(data={
                    'results': UploadedFileSerializer(page_obj, many=True, context={'request': request}).data,
                    'count': paginator.count,
                    'num_pages': paginator.num_pages,
                    'current_page': page_obj.number,
                    'has_next': page_obj.has_next(),
                    'has_previous': page_obj.has_previous()
                })

            except Exception as e:
                return APIResponse.error(message=f'搜索失败: {str(e)}')

        return APIResponse.error(message='搜索参数错误', errors=serializer.errors)


class FileStatisticsView(APIView):
    """文件统计"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取文件统计信息"""
        try:
            # 基础统计
            user_files = UploadedFile.objects.filter(user=request.user, is_deleted=False)
            total_files = user_files.count()
            total_size = user_files.aggregate(total=Sum('file_size'))['total'] or 0

            # 按分类统计
            category_stats = user_files.values('category__name').annotate(
                count=Count('id'),
                size=Sum('file_size')
            ).order_by('-count')

            # 按文件类型统计
            file_type_stats = user_files.values('file_type').annotate(
                count=Count('id'),
                size=Sum('file_size')
            ).order_by('-count')

            # 最近上传的文件
            recent_uploads = user_files.order_by('-uploaded_at')[:10]

            # 格式化文件大小
            def format_size(size):
                if size < 1024:
                    return f"{size} B"
                elif size < 1024 * 1024:
                    return f"{size / 1024:.1f} KB"
                elif size < 1024 * 1024 * 1024:
                    return f"{size / (1024 * 1024):.1f} MB"
                else:
                    return f"{size / (1024 * 1024 * 1024):.1f} GB"

            return APIResponse.success(data={
                'total_files': total_files,
                'total_size': total_size,
                'total_size_display': format_size(total_size),
                'category_stats': list(category_stats),
                'file_type_stats': list(file_type_stats),
                'recent_uploads': UploadedFileSerializer(recent_uploads, many=True, context={'request': request}).data
            })

        except Exception as e:
            return APIResponse.error(message=f'获取统计信息失败: {str(e)}')


class FileMoveView(APIView):
    """文件移动"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """移动文件到其他分类"""
        serializer = FileMoveSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            try:
                files = serializer.validated_data['file_ids']
                target_category = serializer.validated_data['target_category_id']

                # 更新文件分类
                updated_count = 0
                for file_obj in files:
                    file_obj.category = target_category
                    file_obj.save()
                    updated_count += 1

                return APIResponse.success(message=f'成功移动{updated_count}个文件')

            except Exception as e:
                return APIResponse.error(message=f'文件移动失败: {str(e)}')

        return APIResponse.error(message='移动失败', errors=serializer.errors)

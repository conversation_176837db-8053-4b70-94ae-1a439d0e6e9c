from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views

router = DefaultRouter()
router.register(r'files', views.UploadedFileViewSet, basename='uploaded-file')
router.register(r'categories', views.FileCategoryViewSet, basename='file-category')

urlpatterns = [
    # 文件上传
    path('upload/', views.FileUploadView.as_view(), name='file_upload'),
    path('batch-upload/', views.BatchFileUploadView.as_view(), name='batch_file_upload'),

    # 文件管理
    path('search/', views.FileSearchView.as_view(), name='file_search'),
    path('statistics/', views.FileStatisticsView.as_view(), name='file_statistics'),
    path('move/', views.FileMoveView.as_view(), name='file_move'),

    # 其他路由
    path('', include(router.urls)),
]

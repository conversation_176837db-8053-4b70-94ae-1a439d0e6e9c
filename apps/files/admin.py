from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import FileCategory, UploadedFile, FileAccessLog


@admin.register(FileCategory)
class FileCategoryAdmin(admin.ModelAdmin):
    """文件分类管理"""
    list_display = ['name', 'code', 'parent', 'max_file_size', 'is_active', 'sort_order', 'created_at']
    list_filter = ['is_active', 'parent', 'created_at']
    search_fields = ['name', 'code', 'description']
    list_editable = ['is_active', 'sort_order']
    ordering = ['sort_order', 'name']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'code', 'description', 'parent')
        }),
        ('配置', {
            'fields': ('max_file_size', 'allowed_extensions')
        }),
        ('设置', {
            'fields': ('sort_order', 'is_active')
        })
    )


@admin.register(UploadedFile)
class UploadedFileAdmin(admin.ModelAdmin):
    """上传文件管理"""
    list_display = ['original_name', 'user_info', 'company_info', 'category', 
                   'file_type', 'file_size_display', 'is_public', 'created_at']
    list_filter = ['file_type', 'is_public', 'category', 'created_at']
    search_fields = ['original_name', 'file_name', 'description', 
                    'user__phone', 'user__nickname', 'company__name']
    readonly_fields = ['file_name', 'file_path', 'file_size', 'file_hash', 'created_at', 'updated_at']
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'company', 'category')
        }),
        ('文件信息', {
            'fields': ('original_name', 'file_name', 'file_path', 'file_type', 'file_size', 'file_hash')
        }),
        ('设置', {
            'fields': ('description', 'is_public', 'tags')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def user_info(self, obj):
        if obj.user:
            return format_html(
                '<a href="{}">{}</a><br/><small>{}</small>',
                reverse('admin:users_user_change', args=[obj.user.pk]),
                obj.user.nickname or obj.user.phone,
                obj.user.phone
            )
        return '-'
    user_info.short_description = '用户'
    
    def company_info(self, obj):
        if obj.company:
            return format_html(
                '<a href="{}">{}</a>',
                reverse('admin:companies_company_change', args=[obj.company.pk]),
                obj.company.name
            )
        return '-'
    company_info.short_description = '企业'
    
    def file_size_display(self, obj):
        """显示文件大小"""
        if obj.file_size:
            if obj.file_size < 1024:
                return f"{obj.file_size} B"
            elif obj.file_size < 1024 * 1024:
                return f"{obj.file_size / 1024:.1f} KB"
            else:
                return f"{obj.file_size / (1024 * 1024):.1f} MB"
        return '-'
    file_size_display.short_description = '文件大小'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'company', 'category')


@admin.register(FileAccessLog)
class FileAccessLogAdmin(admin.ModelAdmin):
    """文件访问日志管理"""
    list_display = ['file_info', 'user_info', 'action', 'ip_address', 'user_agent_short', 'accessed_at']
    list_filter = ['action', 'accessed_at']
    search_fields = ['file__original_name', 'user__phone', 'user__nickname', 'ip_address']
    readonly_fields = ['accessed_at']
    date_hierarchy = 'accessed_at'

    fieldsets = (
        ('基本信息', {
            'fields': ('file', 'user', 'action')
        }),
        ('访问信息', {
            'fields': ('ip_address', 'user_agent')
        }),
        ('时间信息', {
            'fields': ('accessed_at',),
            'classes': ('collapse',)
        })
    )

    def file_info(self, obj):
        if obj.file:
            return format_html(
                '<a href="{}">{}</a><br/><small>{}</small>',
                reverse('admin:files_uploadedfile_change', args=[obj.file.pk]),
                obj.file.original_name,
                obj.file.file_type
            )
        return '-'
    file_info.short_description = '文件'

    def user_info(self, obj):
        if obj.user:
            return format_html(
                '<a href="{}">{}</a><br/><small>{}</small>',
                reverse('admin:users_user_change', args=[obj.user.pk]),
                obj.user.nickname or obj.user.phone,
                obj.user.phone
            )
        return '-'
    user_info.short_description = '用户'

    def user_agent_short(self, obj):
        """显示简短的用户代理"""
        if obj.user_agent:
            return obj.user_agent[:50] + '...' if len(obj.user_agent) > 50 else obj.user_agent
        return '-'
    user_agent_short.short_description = '用户代理'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('file', 'user')

from django.db import models
from django.conf import settings
import os


class FileCategory(models.Model):
    """文件分类"""
    name = models.CharField('分类名称', max_length=50)
    code = models.CharField('分类代码', max_length=20, unique=True)
    description = models.TextField('描述', blank=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, blank=True, null=True, related_name='children', verbose_name='父分类')
    
    # 配置
    max_file_size = models.PositiveIntegerField('最大文件大小(MB)', default=10)
    allowed_extensions = models.JSONField('允许的文件扩展名', default=list)
    
    sort_order = models.PositiveIntegerField('排序', default=0)
    is_active = models.BooleanField('是否启用', default=True)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'file_categories'
        verbose_name = '文件分类'
        verbose_name_plural = '文件分类'
        ordering = ['sort_order', 'name']
    
    def __str__(self):
        return self.name


class UploadedFile(models.Model):
    """上传文件"""
    FILE_TYPE_CHOICES = [
        ('image', '图片'),
        ('document', '文档'),
        ('video', '视频'),
        ('audio', '音频'),
        ('other', '其他'),
    ]
    
    # 基本信息
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='uploaded_files', verbose_name='上传用户')
    company = models.ForeignKey('companies.Company', on_delete=models.CASCADE, blank=True, null=True, related_name='files', verbose_name='所属企业')
    category = models.ForeignKey(FileCategory, on_delete=models.SET_NULL, blank=True, null=True, related_name='files', verbose_name='文件分类')
    
    # 文件信息
    original_name = models.CharField('原始文件名', max_length=255)
    file_name = models.CharField('存储文件名', max_length=255)
    file_path = models.FileField('文件路径', upload_to='uploads/')
    file_size = models.PositiveIntegerField('文件大小', default=0)
    file_type = models.CharField('文件类型', max_length=20, choices=FILE_TYPE_CHOICES)
    mime_type = models.CharField('MIME类型', max_length=100, blank=True)
    file_extension = models.CharField('文件扩展名', max_length=10, blank=True)
    
    # 文件哈希值（用于去重）
    file_hash = models.CharField('文件哈希', max_length=64, blank=True, db_index=True)
    
    # 访问控制
    is_public = models.BooleanField('是否公开', default=False)
    access_permissions = models.JSONField('访问权限', default=dict, blank=True)
    
    # 统计信息
    download_count = models.PositiveIntegerField('下载次数', default=0)
    view_count = models.PositiveIntegerField('查看次数', default=0)
    
    # 状态
    is_active = models.BooleanField('是否有效', default=True)
    is_deleted = models.BooleanField('是否已删除', default=False)
    deleted_at = models.DateTimeField('删除时间', blank=True, null=True)
    
    # 描述和标签
    description = models.TextField('文件描述', blank=True)
    tags = models.JSONField('标签', default=list, blank=True)
    
    created_at = models.DateTimeField('上传时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'uploaded_files'
        verbose_name = '上传文件'
        verbose_name_plural = '文件管理'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.original_name
    
    def save(self, *args, **kwargs):
        if self.file_path:
            # 设置文件大小
            if not self.file_size:
                self.file_size = self.file_path.size
            
            # 设置文件扩展名
            if not self.file_extension:
                self.file_extension = os.path.splitext(self.original_name)[1].lower()
            
            # 设置文件类型
            if not self.file_type:
                self.file_type = self.get_file_type_by_extension()
        
        super().save(*args, **kwargs)
    
    def get_file_type_by_extension(self):
        """根据扩展名判断文件类型"""
        image_exts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        document_exts = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt']
        video_exts = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']
        audio_exts = ['.mp3', '.wav', '.flac', '.aac', '.ogg']
        
        ext = self.file_extension.lower()
        if ext in image_exts:
            return 'image'
        elif ext in document_exts:
            return 'document'
        elif ext in video_exts:
            return 'video'
        elif ext in audio_exts:
            return 'audio'
        else:
            return 'other'
    
    @property
    def file_size_mb(self):
        """文件大小(MB)"""
        return round(self.file_size / 1024 / 1024, 2)
    
    @property
    def file_url(self):
        """文件访问URL"""
        if self.file_path:
            return self.file_path.url
        return None


class FileAccessLog(models.Model):
    """文件访问日志"""
    ACTION_CHOICES = [
        ('view', '查看'),
        ('download', '下载'),
        ('share', '分享'),
    ]
    
    file = models.ForeignKey(UploadedFile, on_delete=models.CASCADE, related_name='access_logs', verbose_name='文件')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='file_access_logs', verbose_name='访问用户')
    action = models.CharField('操作类型', max_length=20, choices=ACTION_CHOICES)
    
    # 访问信息
    ip_address = models.GenericIPAddressField('IP地址', blank=True, null=True)
    user_agent = models.TextField('用户代理', blank=True)
    
    accessed_at = models.DateTimeField('访问时间', auto_now_add=True)
    
    class Meta:
        db_table = 'file_access_logs'
        verbose_name = '文件访问日志'
        verbose_name_plural = '文件访问日志'
        ordering = ['-accessed_at']
    
    def __str__(self):
        return f"{self.user.phone} {self.get_action_display()} {self.file.original_name}"

from rest_framework import serializers
from django.core.files.uploadedfile import UploadedFile as DjangoUploadedFile
import os
import mimetypes
from .models import UploadedFile, FileCategory, FileAccessLog


class FileCategorySerializer(serializers.ModelSerializer):
    """文件分类序列化器"""
    file_count = serializers.SerializerMethodField()
    
    class Meta:
        model = FileCategory
        fields = [
            'id', 'name', 'code', 'description', 'allowed_extensions',
            'max_file_size', 'is_active', 'file_count', 'created_at'
        ]
        read_only_fields = ['id', 'file_count', 'created_at']
    
    def get_file_count(self, obj):
        """获取该分类下的文件数量"""
        return obj.uploadedfile_set.filter(is_deleted=False).count()


class UploadedFileSerializer(serializers.ModelSerializer):
    """上传文件序列化器"""
    user_phone = serializers.CharField(source='user.phone', read_only=True)
    category_name = serializers.CharField(source='category.name', read_only=True)
    file_url = serializers.SerializerMethodField()
    file_size_display = serializers.SerializerMethodField()
    
    class Meta:
        model = UploadedFile
        fields = [
            'id', 'user', 'user_phone', 'category', 'category_name',
            'original_filename', 'stored_filename', 'file_path', 'file_url',
            'file_size', 'file_size_display', 'file_type', 'mime_type',
            'description', 'tags', 'is_public', 'is_deleted',
            'uploaded_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'user_phone', 'category_name', 'stored_filename', 'file_url',
            'file_size', 'file_size_display', 'file_type', 'mime_type',
            'uploaded_at', 'updated_at'
        ]
    
    def get_file_url(self, obj):
        """获取文件URL"""
        if obj.file_path:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.file_path.url)
            return obj.file_path.url
        return None
    
    def get_file_size_display(self, obj):
        """获取文件大小显示"""
        size = obj.file_size
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.1f} GB"


class FileUploadSerializer(serializers.Serializer):
    """文件上传序列化器"""
    file = serializers.FileField()
    category_id = serializers.IntegerField()
    description = serializers.CharField(max_length=500, required=False, allow_blank=True)
    tags = serializers.ListField(
        child=serializers.CharField(max_length=50),
        required=False,
        allow_empty=True
    )
    is_public = serializers.BooleanField(default=False)
    
    def validate_file(self, value):
        """验证文件"""
        if not isinstance(value, DjangoUploadedFile):
            raise serializers.ValidationError('无效的文件')
        
        # 检查文件大小（默认10MB限制）
        max_size = 10 * 1024 * 1024
        if value.size > max_size:
            raise serializers.ValidationError(f'文件大小不能超过{max_size / (1024 * 1024):.0f}MB')
        
        return value
    
    def validate_category_id(self, value):
        """验证文件分类"""
        try:
            category = FileCategory.objects.get(id=value, is_active=True)
            return category
        except FileCategory.DoesNotExist:
            raise serializers.ValidationError('文件分类不存在或已禁用')
    
    def validate(self, attrs):
        """验证文件和分类的匹配"""
        file = attrs['file']
        category = attrs['category_id']
        
        # 检查文件扩展名
        file_ext = os.path.splitext(file.name)[1].lower()
        if category.allowed_extensions and file_ext not in category.allowed_extensions:
            raise serializers.ValidationError(
                f'该分类不支持 {file_ext} 格式的文件，支持的格式: {", ".join(category.allowed_extensions)}'
            )
        
        # 检查文件大小限制
        if category.max_file_size and file.size > category.max_file_size:
            max_size_mb = category.max_file_size / (1024 * 1024)
            raise serializers.ValidationError(f'文件大小不能超过{max_size_mb:.0f}MB')
        
        attrs['category'] = category
        return attrs


class BatchFileUploadSerializer(serializers.Serializer):
    """批量文件上传序列化器"""
    files = serializers.ListField(
        child=serializers.FileField(),
        min_length=1,
        max_length=10
    )
    category_id = serializers.IntegerField()
    description = serializers.CharField(max_length=500, required=False, allow_blank=True)
    is_public = serializers.BooleanField(default=False)
    
    def validate_category_id(self, value):
        """验证文件分类"""
        try:
            category = FileCategory.objects.get(id=value, is_active=True)
            return category
        except FileCategory.DoesNotExist:
            raise serializers.ValidationError('文件分类不存在或已禁用')
    
    def validate_files(self, value):
        """验证文件列表"""
        if len(value) > 10:
            raise serializers.ValidationError('一次最多只能上传10个文件')
        
        total_size = sum(file.size for file in value)
        max_total_size = 50 * 1024 * 1024  # 50MB
        if total_size > max_total_size:
            raise serializers.ValidationError('批量上传文件总大小不能超过50MB')
        
        return value


class FileAccessLogSerializer(serializers.ModelSerializer):
    """文件访问日志序列化器"""
    user_phone = serializers.CharField(source='user.phone', read_only=True)
    file_name = serializers.CharField(source='file.original_filename', read_only=True)
    
    class Meta:
        model = FileAccessLog
        fields = [
            'id', 'user', 'user_phone', 'file', 'file_name',
            'action', 'ip_address', 'user_agent', 'accessed_at'
        ]
        read_only_fields = ['id', 'user_phone', 'file_name', 'accessed_at']


class FileSearchSerializer(serializers.Serializer):
    """文件搜索序列化器"""
    keyword = serializers.CharField(max_length=100, required=False, allow_blank=True)
    category_id = serializers.IntegerField(required=False)
    file_type = serializers.CharField(max_length=50, required=False, allow_blank=True)
    start_date = serializers.DateField(required=False)
    end_date = serializers.DateField(required=False)
    is_public = serializers.BooleanField(required=False)
    tags = serializers.ListField(
        child=serializers.CharField(max_length=50),
        required=False,
        allow_empty=True
    )
    
    def validate(self, attrs):
        """验证搜索参数"""
        start_date = attrs.get('start_date')
        end_date = attrs.get('end_date')
        
        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError('开始日期不能晚于结束日期')
        
        return attrs


class FileShareSerializer(serializers.Serializer):
    """文件分享序列化器"""
    file_id = serializers.IntegerField()
    expire_hours = serializers.IntegerField(min_value=1, max_value=168, default=24)  # 1小时到7天
    password = serializers.CharField(max_length=20, required=False, allow_blank=True)
    
    def validate_file_id(self, value):
        """验证文件ID"""
        try:
            file = UploadedFile.objects.get(
                id=value,
                user=self.context['request'].user,
                is_deleted=False
            )
            return file
        except UploadedFile.DoesNotExist:
            raise serializers.ValidationError('文件不存在或无权限访问')


class FileStatisticsSerializer(serializers.Serializer):
    """文件统计序列化器"""
    total_files = serializers.IntegerField()
    total_size = serializers.IntegerField()
    total_size_display = serializers.CharField()
    category_stats = serializers.ListField()
    file_type_stats = serializers.ListField()
    recent_uploads = serializers.ListField()


class FileMoveSerializer(serializers.Serializer):
    """文件移动序列化器"""
    file_ids = serializers.ListField(
        child=serializers.IntegerField(),
        min_length=1
    )
    target_category_id = serializers.IntegerField()
    
    def validate_target_category_id(self, value):
        """验证目标分类"""
        try:
            category = FileCategory.objects.get(id=value, is_active=True)
            return category
        except FileCategory.DoesNotExist:
            raise serializers.ValidationError('目标分类不存在或已禁用')
    
    def validate_file_ids(self, value):
        """验证文件ID列表"""
        user = self.context['request'].user
        files = UploadedFile.objects.filter(
            id__in=value,
            user=user,
            is_deleted=False
        )
        
        if len(files) != len(value):
            raise serializers.ValidationError('部分文件不存在或无权限访问')
        
        return files


class FileRenameSerializer(serializers.Serializer):
    """文件重命名序列化器"""
    new_name = serializers.CharField(max_length=255)
    
    def validate_new_name(self, value):
        """验证新文件名"""
        # 检查文件名是否包含非法字符
        invalid_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
        for char in invalid_chars:
            if char in value:
                raise serializers.ValidationError(f'文件名不能包含字符: {char}')
        
        return value

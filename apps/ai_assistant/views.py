from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q, Count, Avg, Sum
from django.shortcuts import get_object_or_404
from datetime import datetime, timedelta
import uuid
import json
import re
import random
from .models import (
    KnowledgeBase, ConversationSession, ConversationMessage, AIModel,
    IntentPattern, AIAssistantConfig, AIAssistantLog
)
from .serializers import (
    KnowledgeBaseSerializer, ConversationSessionSerializer, ConversationMessageSerializer,
    AIModelSerializer, IntentPatternSerializer, AIAssistantConfigSerializer,
    AIAssistantLogSerializer, KnowledgeBaseListSerializer, ConversationSessionListSerializer,
    ChatMessageSerializer, KnowledgeSearchSerializer, FeedbackSerializer,
    SessionFeedbackSerializer, ModelTrainingSerializer, IntentRecognitionSerializer
)
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class KnowledgeBaseViewSet(viewsets.ModelViewSet):
    """知识库ViewSet"""
    queryset = KnowledgeBase.objects.all()
    serializer_class = KnowledgeBaseSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按知识类型过滤
        knowledge_type = self.request.query_params.get('knowledge_type')
        if knowledge_type:
            queryset = queryset.filter(knowledge_type=knowledge_type)

        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # 搜索
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) |
                Q(content__icontains=search) |
                Q(keywords__icontains=search)
            )

        return queryset.order_by('-priority', '-updated_at')

    def get_serializer_class(self):
        if self.action == 'list':
            return KnowledgeBaseListSerializer
        return KnowledgeBaseSerializer

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user, updated_by=self.request.user)

    def perform_update(self, serializer):
        serializer.save(updated_by=self.request.user)

    @action(detail=True, methods=['post'])
    def mark_helpful(self, request, pk=None):
        """标记为有用"""
        knowledge = self.get_object()
        knowledge.helpful_count += 1
        knowledge.save()

        return Response({'message': '标记成功', 'helpful_count': knowledge.helpful_count})

    @action(detail=True, methods=['post'])
    def mark_unhelpful(self, request, pk=None):
        """标记为无用"""
        knowledge = self.get_object()
        knowledge.unhelpful_count += 1
        knowledge.save()

        return Response({'message': '标记成功', 'unhelpful_count': knowledge.unhelpful_count})

    @action(detail=False, methods=['post'])
    def search(self, request):
        """搜索知识"""
        serializer = KnowledgeSearchSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data
        query = data['query']
        knowledge_type = data.get('knowledge_type')
        limit = data.get('limit', 10)

        # 构建搜索查询
        queryset = KnowledgeBase.objects.filter(status='published')

        if knowledge_type:
            queryset = queryset.filter(knowledge_type=knowledge_type)

        # 多字段搜索
        search_query = (
            Q(title__icontains=query) |
            Q(content__icontains=query) |
            Q(keywords__icontains=query) |
            Q(questions__icontains=query)
        )

        queryset = queryset.filter(search_query)

        # 按相关性排序（简单实现）
        queryset = queryset.order_by('-priority', '-use_count')[:limit]

        # 更新查看次数
        for knowledge in queryset:
            knowledge.view_count += 1
            knowledge.save(update_fields=['view_count'])

        serializer = KnowledgeBaseListSerializer(queryset, many=True)
        return Response({
            'results': serializer.data,
            'total': queryset.count(),
            'query': query
        })


class ConversationSessionViewSet(viewsets.ModelViewSet):
    """对话会话ViewSet"""
    queryset = ConversationSession.objects.all()
    serializer_class = ConversationSessionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user

        # 根据用户权限过滤
        if not user.is_staff:
            queryset = queryset.filter(user=user)

        # 按会话类型过滤
        session_type = self.request.query_params.get('session_type')
        if session_type:
            queryset = queryset.filter(session_type=session_type)

        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        return queryset.order_by('-start_time')

    def get_serializer_class(self):
        if self.action == 'list':
            return ConversationSessionListSerializer
        return ConversationSessionSerializer

    @action(detail=True, methods=['get'])
    def messages(self, request, pk=None):
        """获取会话消息"""
        session = self.get_object()
        messages = session.messages.all().order_by('timestamp')

        serializer = ConversationMessageSerializer(messages, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def end_session(self, request, pk=None):
        """结束会话"""
        session = self.get_object()

        if session.status == 'active':
            session.status = 'completed'
            session.end_time = timezone.now()
            session.save()

            return Response({'message': '会话已结束'})
        else:
            return Response(
                {'error': '会话状态不允许结束'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def feedback(self, request, pk=None):
        """会话反馈"""
        session = self.get_object()
        serializer = SessionFeedbackSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data
        session.satisfaction_score = data['satisfaction_score']
        session.save()

        # 记录日志
        AIAssistantLog.objects.create(
            session=session,
            level='INFO',
            action_type='system_operation',
            message=f'用户提交会话反馈，满意度: {data["satisfaction_score"]}',
            details={
                'satisfaction_score': data['satisfaction_score'],
                'feedback_comment': data.get('feedback_comment', '')
            }
        )

        return Response({'message': '反馈提交成功'})


class ConversationMessageViewSet(viewsets.ReadOnlyModelViewSet):
    """对话消息ViewSet"""
    queryset = ConversationMessage.objects.all()
    serializer_class = ConversationMessageSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按会话过滤
        session_id = self.request.query_params.get('session')
        if session_id:
            queryset = queryset.filter(session_id=session_id)

        # 按消息类型过滤
        message_type = self.request.query_params.get('message_type')
        if message_type:
            queryset = queryset.filter(message_type=message_type)

        # 按意图过滤
        intent = self.request.query_params.get('intent')
        if intent:
            queryset = queryset.filter(intent=intent)

        return queryset.order_by('timestamp')

    @action(detail=True, methods=['post'])
    def feedback(self, request, pk=None):
        """消息反馈"""
        message = self.get_object()
        serializer = FeedbackSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data
        message.is_helpful = data['is_helpful']
        message.feedback_score = data.get('feedback_score')
        message.feedback_comment = data.get('feedback_comment', '')
        message.save()

        # 更新知识库统计
        if message.knowledge_used:
            if data['is_helpful']:
                message.knowledge_used.helpful_count += 1
            else:
                message.knowledge_used.unhelpful_count += 1
            message.knowledge_used.save()

        return Response({'message': '反馈提交成功'})


class AIModelViewSet(viewsets.ModelViewSet):
    """AI模型ViewSet"""
    queryset = AIModel.objects.all()
    serializer_class = AIModelSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按模型类型过滤
        model_type = self.request.query_params.get('model_type')
        if model_type:
            queryset = queryset.filter(model_type=model_type)

        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        return queryset.order_by('-created_at')

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """激活模型"""
        model = self.get_object()
        model.status = 'active'
        model.save()

        return Response({'message': '模型已激活'})

    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """停用模型"""
        model = self.get_object()
        model.status = 'inactive'
        model.save()

        return Response({'message': '模型已停用'})

    @action(detail=True, methods=['post'])
    def set_default(self, request, pk=None):
        """设为默认模型"""
        model = self.get_object()

        # 清除同类型的其他默认模型
        AIModel.objects.filter(model_type=model.model_type).update(is_default=False)

        # 设置当前模型为默认
        model.is_default = True
        model.save()

        return Response({'message': '已设为默认模型'})

    @action(detail=True, methods=['post'])
    def train(self, request, pk=None):
        """训练模型"""
        model = self.get_object()
        serializer = ModelTrainingSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data

        # 更新模型状态
        model.status = 'training'
        model.training_start_time = timezone.now()
        model.training_data_size = len(data.get('training_data', []))
        model.save()

        # 记录日志
        AIAssistantLog.objects.create(
            level='INFO',
            action_type='model_inference',
            message=f'开始训练模型: {model.name}',
            details={
                'model_id': model.id,
                'training_data_size': model.training_data_size,
                'training_params': data.get('training_params', {})
            }
        )

        # 这里可以启动异步训练任务
        # 模拟训练完成
        model.status = 'active'
        model.training_end_time = timezone.now()
        model.accuracy = random.uniform(0.85, 0.98)
        model.precision = random.uniform(0.80, 0.95)
        model.recall = random.uniform(0.75, 0.92)
        model.f1_score = 2 * (model.precision * model.recall) / (model.precision + model.recall)
        model.save()

        return Response({
            'message': '模型训练已启动',
            'model_id': model.id,
            'training_data_size': model.training_data_size
        })


class IntentPatternViewSet(viewsets.ModelViewSet):
    """意图模式ViewSet"""
    queryset = IntentPattern.objects.all()
    serializer_class = IntentPatternSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按意图分类过滤
        intent_category = self.request.query_params.get('intent_category')
        if intent_category:
            queryset = queryset.filter(intent_category=intent_category)

        # 只显示启用的
        if self.request.query_params.get('active_only') == 'true':
            queryset = queryset.filter(is_active=True)

        return queryset.order_by('-priority', 'intent_name')

    @action(detail=True, methods=['post'])
    def test_pattern(self, request, pk=None):
        """测试意图模式"""
        pattern = self.get_object()
        text = request.data.get('text', '')

        if not text:
            return Response(
                {'error': '请提供测试文本'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 简单的模式匹配测试
        matched = False
        confidence = 0.0

        # 检查关键词匹配
        for keyword in pattern.keywords:
            if keyword.lower() in text.lower():
                matched = True
                confidence += 0.3

        # 检查模式匹配
        for pattern_text in pattern.patterns:
            if re.search(pattern_text, text, re.IGNORECASE):
                matched = True
                confidence += 0.5

        confidence = min(confidence, 1.0)

        # 更新统计
        pattern.match_count += 1
        if matched and confidence >= pattern.confidence_threshold:
            pattern.success_count += 1
        pattern.save()

        return Response({
            'matched': matched,
            'confidence': confidence,
            'threshold': pattern.confidence_threshold,
            'success': matched and confidence >= pattern.confidence_threshold,
            'response_templates': pattern.response_templates if matched else []
        })


class AIAssistantConfigViewSet(viewsets.ModelViewSet):
    """AI助手配置ViewSet"""
    queryset = AIAssistantConfig.objects.all()
    serializer_class = AIAssistantConfigSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按配置类型过滤
        config_type = self.request.query_params.get('config_type')
        if config_type:
            queryset = queryset.filter(config_type=config_type)

        # 只显示启用的
        if self.request.query_params.get('active_only') == 'true':
            queryset = queryset.filter(is_active=True)

        return queryset.order_by('config_type', 'config_key')

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class AIAssistantLogViewSet(viewsets.ReadOnlyModelViewSet):
    """AI助手日志ViewSet"""
    queryset = AIAssistantLog.objects.all()
    serializer_class = AIAssistantLogSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按会话过滤
        session_id = self.request.query_params.get('session')
        if session_id:
            queryset = queryset.filter(session_id=session_id)

        # 按日志级别过滤
        level = self.request.query_params.get('level')
        if level:
            queryset = queryset.filter(level=level)

        # 按操作类型过滤
        action_type = self.request.query_params.get('action_type')
        if action_type:
            queryset = queryset.filter(action_type=action_type)

        return queryset.order_by('-timestamp')


# 业务操作视图
class AIAssistantViewSet(viewsets.ViewSet):
    """AI助手业务操作ViewSet"""
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['post'])
    def chat(self, request):
        """智能聊天"""
        serializer = ChatMessageSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data
        user_message = data['message']
        session_id = data.get('session_id')
        context = data.get('context', {})

        # 获取或创建会话
        if session_id:
            try:
                session = ConversationSession.objects.get(session_id=session_id)
            except ConversationSession.DoesNotExist:
                session = self._create_session(request.user)
        else:
            session = self._create_session(request.user)

        # 记录用户消息
        user_msg = ConversationMessage.objects.create(
            session=session,
            message_type='user',
            content=user_message,
            raw_content=user_message
        )

        # 意图识别和回复生成
        ai_response = self._generate_ai_response(user_message, session, context)

        # 记录AI回复
        ai_msg = ConversationMessage.objects.create(
            session=session,
            message_type='ai',
            content=ai_response['content'],
            intent=ai_response.get('intent'),
            confidence=ai_response.get('confidence'),
            entities=ai_response.get('entities', []),
            keywords=ai_response.get('keywords', []),
            knowledge_used_id=ai_response.get('knowledge_id'),
            response_time=ai_response.get('response_time', 0)
        )

        # 更新会话统计
        session.message_count += 2
        session.ai_response_count += 1
        session.last_activity = timezone.now()
        session.save()

        return Response({
            'session_id': session.session_id,
            'user_message': {
                'id': user_msg.id,
                'content': user_msg.content,
                'timestamp': user_msg.timestamp
            },
            'ai_response': {
                'id': ai_msg.id,
                'content': ai_msg.content,
                'intent': ai_msg.intent,
                'confidence': ai_msg.confidence,
                'timestamp': ai_msg.timestamp
            }
        })

    @action(detail=False, methods=['post'])
    def recognize_intent(self, request):
        """意图识别"""
        serializer = IntentRecognitionSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data
        text = data['text']
        confidence_threshold = data.get('confidence_threshold', 0.7)

        # 获取活跃的意图模式
        patterns = IntentPattern.objects.filter(is_active=True).order_by('-priority')

        best_match = None
        best_confidence = 0.0

        for pattern in patterns:
            confidence = self._calculate_intent_confidence(text, pattern)
            if confidence > best_confidence and confidence >= confidence_threshold:
                best_match = pattern
                best_confidence = confidence

        if best_match:
            # 更新统计
            best_match.match_count += 1
            best_match.success_count += 1
            best_match.save()

            return Response({
                'intent': best_match.intent_name,
                'category': best_match.intent_category,
                'confidence': best_confidence,
                'response_templates': best_match.response_templates,
                'follow_up_questions': best_match.follow_up_questions
            })
        else:
            return Response({
                'intent': 'unknown',
                'category': 'other',
                'confidence': 0.0,
                'response_templates': ['抱歉，我没有理解您的意思，请您换个方式表达。'],
                'follow_up_questions': []
            })

    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """AI助手仪表板"""
        # 会话统计
        total_sessions = ConversationSession.objects.count()
        active_sessions = ConversationSession.objects.filter(status='active').count()

        # 消息统计
        total_messages = ConversationMessage.objects.count()
        ai_messages = ConversationMessage.objects.filter(message_type='ai').count()

        # 满意度统计
        satisfaction_avg = ConversationSession.objects.filter(
            satisfaction_score__isnull=False
        ).aggregate(avg=Avg('satisfaction_score'))['avg'] or 0

        # 知识库统计
        knowledge_stats = KnowledgeBase.objects.values('knowledge_type').annotate(
            count=Count('id'),
            published=Count('id', filter=Q(status='published'))
        )

        # 意图识别统计
        intent_stats = IntentPattern.objects.values('intent_category').annotate(
            count=Count('id'),
            total_matches=Sum('match_count'),
            total_success=Sum('success_count')
        )

        # 模型性能统计
        model_stats = AIModel.objects.filter(status='active').aggregate(
            total_models=Count('id'),
            avg_accuracy=Avg('accuracy'),
            avg_response_time=Avg('avg_response_time')
        )

        # 最近7天的会话趋势
        seven_days_ago = timezone.now() - timedelta(days=7)
        daily_sessions = []
        for i in range(7):
            date = (timezone.now() - timedelta(days=i)).date()
            day_sessions = ConversationSession.objects.filter(start_time__date=date)

            daily_sessions.insert(0, {
                'date': date.strftime('%Y-%m-%d'),
                'total': day_sessions.count(),
                'completed': day_sessions.filter(status='completed').count(),
                'avg_satisfaction': day_sessions.filter(
                    satisfaction_score__isnull=False
                ).aggregate(avg=Avg('satisfaction_score'))['avg'] or 0
            })

        return Response({
            'session_stats': {
                'total': total_sessions,
                'active': active_sessions,
                'satisfaction_avg': satisfaction_avg
            },
            'message_stats': {
                'total': total_messages,
                'ai_messages': ai_messages,
                'user_messages': total_messages - ai_messages
            },
            'knowledge_stats': list(knowledge_stats),
            'intent_stats': list(intent_stats),
            'model_stats': model_stats,
            'daily_sessions': daily_sessions
        })

    def _create_session(self, user):
        """创建新会话"""
        session_id = f"CHAT_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"

        return ConversationSession.objects.create(
            session_id=session_id,
            session_type='chat',
            user=user,
            status='active'
        )

    def _generate_ai_response(self, message, session, context):
        """生成AI回复"""
        start_time = timezone.now()

        # 意图识别
        intent_result = self._recognize_intent(message)

        # 知识库搜索
        knowledge_result = self._search_knowledge(message, intent_result.get('intent'))

        # 生成回复
        if knowledge_result:
            response_content = self._generate_response_from_knowledge(knowledge_result, message)
            knowledge_id = knowledge_result.id

            # 更新知识库使用统计
            knowledge_result.use_count += 1
            knowledge_result.save()
        else:
            response_content = self._generate_default_response(intent_result)
            knowledge_id = None

        end_time = timezone.now()
        response_time = (end_time - start_time).total_seconds()

        # 记录日志
        AIAssistantLog.objects.create(
            session=session,
            level='INFO',
            action_type='response',
            message=f'生成AI回复: {response_content[:50]}...',
            details={
                'user_message': message,
                'intent': intent_result.get('intent'),
                'confidence': intent_result.get('confidence'),
                'knowledge_used': knowledge_id,
                'response_time': response_time
            },
            execution_time=response_time
        )

        return {
            'content': response_content,
            'intent': intent_result.get('intent'),
            'confidence': intent_result.get('confidence'),
            'entities': intent_result.get('entities', []),
            'keywords': intent_result.get('keywords', []),
            'knowledge_id': knowledge_id,
            'response_time': response_time
        }

    def _recognize_intent(self, text):
        """识别意图"""
        patterns = IntentPattern.objects.filter(is_active=True).order_by('-priority')

        best_match = None
        best_confidence = 0.0

        for pattern in patterns:
            confidence = self._calculate_intent_confidence(text, pattern)
            if confidence > best_confidence:
                best_match = pattern
                best_confidence = confidence

        if best_match and best_confidence >= best_match.confidence_threshold:
            return {
                'intent': best_match.intent_name,
                'category': best_match.intent_category,
                'confidence': best_confidence,
                'entities': self._extract_entities(text, best_match.entities),
                'keywords': self._extract_keywords(text, best_match.keywords)
            }
        else:
            return {
                'intent': 'unknown',
                'category': 'other',
                'confidence': 0.0,
                'entities': [],
                'keywords': []
            }

    def _calculate_intent_confidence(self, text, pattern):
        """计算意图置信度"""
        confidence = 0.0
        text_lower = text.lower()

        # 关键词匹配
        keyword_matches = 0
        for keyword in pattern.keywords:
            if keyword.lower() in text_lower:
                keyword_matches += 1

        if pattern.keywords:
            confidence += (keyword_matches / len(pattern.keywords)) * 0.6

        # 模式匹配
        pattern_matches = 0
        for pattern_text in pattern.patterns:
            if re.search(pattern_text, text, re.IGNORECASE):
                pattern_matches += 1

        if pattern.patterns:
            confidence += (pattern_matches / len(pattern.patterns)) * 0.4

        return min(confidence, 1.0)

    def _search_knowledge(self, query, intent=None):
        """搜索知识库"""
        queryset = KnowledgeBase.objects.filter(status='published')

        # 根据意图过滤知识类型
        if intent:
            intent_type_mapping = {
                'tax_question': 'tax',
                'finance_question': 'finance',
                'process_question': 'process',
                'system_help': 'system'
            }
            knowledge_type = intent_type_mapping.get(intent)
            if knowledge_type:
                queryset = queryset.filter(knowledge_type=knowledge_type)

        # 搜索匹配
        search_query = (
            Q(title__icontains=query) |
            Q(content__icontains=query) |
            Q(keywords__icontains=query) |
            Q(questions__icontains=query)
        )

        results = queryset.filter(search_query).order_by('-priority', '-use_count')
        return results.first() if results.exists() else None

    def _generate_response_from_knowledge(self, knowledge, user_message):
        """从知识库生成回复"""
        if knowledge.answers:
            # 随机选择一个答案模板
            answer_template = random.choice(knowledge.answers)
            return answer_template
        else:
            return knowledge.content[:500] + ('...' if len(knowledge.content) > 500 else '')

    def _generate_default_response(self, intent_result):
        """生成默认回复"""
        intent = intent_result.get('intent', 'unknown')

        default_responses = {
            'greeting': ['您好！我是AI助手，很高兴为您服务。', '您好！有什么可以帮助您的吗？'],
            'goodbye': ['再见！祝您工作愉快！', '感谢您的使用，再见！'],
            'unknown': ['抱歉，我没有理解您的意思。您可以尝试换个方式表达，或者查看帮助文档。']
        }

        responses = default_responses.get(intent, default_responses['unknown'])
        return random.choice(responses)

    def _extract_entities(self, text, entity_types):
        """提取实体"""
        entities = []
        # 这里可以实现具体的实体识别逻辑
        # 简单示例：提取数字、日期等

        if 'number' in entity_types:
            numbers = re.findall(r'\d+(?:\.\d+)?', text)
            for num in numbers:
                entities.append({'type': 'number', 'value': num})

        if 'date' in entity_types:
            # 简单的日期匹配
            dates = re.findall(r'\d{4}[-/]\d{1,2}[-/]\d{1,2}', text)
            for date in dates:
                entities.append({'type': 'date', 'value': date})

        return entities

    def _extract_keywords(self, text, pattern_keywords):
        """提取关键词"""
        keywords = []
        text_lower = text.lower()

        for keyword in pattern_keywords:
            if keyword.lower() in text_lower:
                keywords.append(keyword)

        return keywords

from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    KnowledgeBase, ConversationSession, ConversationMessage, AIModel,
    IntentPattern, AIAssistantConfig, AIAssistantLog
)

User = get_user_model()


class KnowledgeBaseSerializer(serializers.ModelSerializer):
    """知识库序列化器"""
    knowledge_type_display = serializers.CharField(source='get_knowledge_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    updated_by_name = serializers.CharField(source='updated_by.get_full_name', read_only=True)
    helpfulness_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = KnowledgeBase
        fields = [
            'id', 'title', 'content', 'knowledge_type', 'knowledge_type_display',
            'keywords', 'tags', 'questions', 'answers', 'examples', 'status',
            'status_display', 'priority', 'view_count', 'use_count',
            'helpful_count', 'unhelpful_count', 'helpfulness_rate',
            'source_url', 'created_by', 'created_by_name', 'updated_by',
            'updated_by_name', 'created_at', 'updated_at'
        ]
    
    def get_helpfulness_rate(self, obj):
        return obj.get_helpfulness_rate()


class ConversationSessionSerializer(serializers.ModelSerializer):
    """对话会话序列化器"""
    session_type_display = serializers.CharField(source='get_session_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    duration = serializers.SerializerMethodField()
    
    class Meta:
        model = ConversationSession
        fields = [
            'id', 'session_id', 'session_type', 'session_type_display',
            'user', 'user_name', 'user_ip', 'user_agent', 'status',
            'status_display', 'start_time', 'end_time', 'last_activity',
            'message_count', 'ai_response_count', 'satisfaction_score',
            'context_data', 'duration'
        ]
    
    def get_duration(self, obj):
        return obj.get_duration()


class ConversationMessageSerializer(serializers.ModelSerializer):
    """对话消息序列化器"""
    message_type_display = serializers.CharField(source='get_message_type_display', read_only=True)
    intent_display = serializers.CharField(source='get_intent_display', read_only=True)
    session_id = serializers.CharField(source='session.session_id', read_only=True)
    knowledge_title = serializers.CharField(source='knowledge_used.title', read_only=True)
    
    class Meta:
        model = ConversationMessage
        fields = [
            'id', 'session', 'session_id', 'message_type', 'message_type_display',
            'content', 'raw_content', 'intent', 'intent_display', 'confidence',
            'entities', 'keywords', 'knowledge_used', 'knowledge_title',
            'response_template', 'response_time', 'is_helpful', 'feedback_score',
            'feedback_comment', 'timestamp'
        ]


class AIModelSerializer(serializers.ModelSerializer):
    """AI模型序列化器"""
    model_type_display = serializers.CharField(source='get_model_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    success_rate = serializers.SerializerMethodField()
    training_duration = serializers.SerializerMethodField()
    
    class Meta:
        model = AIModel
        fields = [
            'id', 'name', 'model_type', 'model_type_display', 'version',
            'description', 'config_params', 'model_path', 'accuracy',
            'precision', 'recall', 'f1_score', 'total_requests',
            'success_requests', 'success_rate', 'avg_response_time',
            'status', 'status_display', 'is_default', 'training_data_size',
            'training_start_time', 'training_end_time', 'training_duration',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
    
    def get_success_rate(self, obj):
        return obj.get_success_rate()
    
    def get_training_duration(self, obj):
        if obj.training_start_time and obj.training_end_time:
            return (obj.training_end_time - obj.training_start_time).total_seconds()
        return None


class IntentPatternSerializer(serializers.ModelSerializer):
    """意图模式序列化器"""
    intent_category_display = serializers.CharField(source='get_intent_category_display', read_only=True)
    success_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = IntentPattern
        fields = [
            'id', 'intent_name', 'intent_category', 'intent_category_display',
            'patterns', 'keywords', 'entities', 'response_templates',
            'follow_up_questions', 'priority', 'confidence_threshold',
            'match_count', 'success_count', 'success_rate', 'is_active',
            'created_at', 'updated_at'
        ]
    
    def get_success_rate(self, obj):
        return obj.get_success_rate()


class AIAssistantConfigSerializer(serializers.ModelSerializer):
    """AI助手配置序列化器"""
    config_type_display = serializers.CharField(source='get_config_type_display', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = AIAssistantConfig
        fields = [
            'id', 'config_type', 'config_type_display', 'config_key',
            'config_value', 'description', 'is_system', 'is_active',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]


class AIAssistantLogSerializer(serializers.ModelSerializer):
    """AI助手日志序列化器"""
    action_type_display = serializers.CharField(source='get_action_type_display', read_only=True)
    session_id = serializers.CharField(source='session.session_id', read_only=True)
    
    class Meta:
        model = AIAssistantLog
        fields = [
            'id', 'session', 'session_id', 'level', 'action_type',
            'action_type_display', 'message', 'details', 'execution_time',
            'memory_usage', 'error_code', 'stack_trace', 'timestamp'
        ]


# 简化版序列化器用于列表显示
class KnowledgeBaseListSerializer(serializers.ModelSerializer):
    """知识库列表序列化器"""
    knowledge_type_display = serializers.CharField(source='get_knowledge_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    helpfulness_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = KnowledgeBase
        fields = [
            'id', 'title', 'knowledge_type_display', 'status_display',
            'priority', 'use_count', 'helpfulness_rate', 'updated_at'
        ]
    
    def get_helpfulness_rate(self, obj):
        return obj.get_helpfulness_rate()


class ConversationSessionListSerializer(serializers.ModelSerializer):
    """对话会话列表序列化器"""
    session_type_display = serializers.CharField(source='get_session_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    
    class Meta:
        model = ConversationSession
        fields = [
            'id', 'session_id', 'session_type_display', 'user_name',
            'status_display', 'message_count', 'satisfaction_score', 'start_time'
        ]


# 业务操作序列化器
class ChatMessageSerializer(serializers.Serializer):
    """聊天消息序列化器"""
    session_id = serializers.CharField(max_length=100, required=False, help_text='会话ID，不提供则创建新会话')
    message = serializers.CharField(help_text='用户消息')
    message_type = serializers.ChoiceField(
        choices=[('text', '文本'), ('voice', '语音'), ('image', '图片')],
        default='text',
        help_text='消息类型'
    )
    context = serializers.JSONField(required=False, help_text='上下文信息')


class KnowledgeSearchSerializer(serializers.Serializer):
    """知识搜索序列化器"""
    query = serializers.CharField(help_text='搜索关键词')
    knowledge_type = serializers.ChoiceField(
        choices=KnowledgeBase.KNOWLEDGE_TYPES,
        required=False,
        help_text='知识类型过滤'
    )
    limit = serializers.IntegerField(
        min_value=1,
        max_value=50,
        default=10,
        help_text='返回结果数量'
    )


class FeedbackSerializer(serializers.Serializer):
    """反馈序列化器"""
    message_id = serializers.IntegerField(help_text='消息ID')
    is_helpful = serializers.BooleanField(help_text='是否有用')
    feedback_score = serializers.IntegerField(
        min_value=1,
        max_value=5,
        required=False,
        help_text='评分(1-5)'
    )
    feedback_comment = serializers.CharField(
        required=False,
        help_text='反馈意见'
    )


class SessionFeedbackSerializer(serializers.Serializer):
    """会话反馈序列化器"""
    session_id = serializers.CharField(max_length=100, help_text='会话ID')
    satisfaction_score = serializers.FloatField(
        min_value=1,
        max_value=5,
        help_text='满意度评分(1-5)'
    )
    feedback_comment = serializers.CharField(
        required=False,
        help_text='反馈意见'
    )


class ModelTrainingSerializer(serializers.Serializer):
    """模型训练序列化器"""
    model_id = serializers.IntegerField(help_text='模型ID')
    training_data = serializers.JSONField(help_text='训练数据')
    training_params = serializers.JSONField(
        required=False,
        help_text='训练参数'
    )
    validation_split = serializers.FloatField(
        min_value=0.1,
        max_value=0.5,
        default=0.2,
        help_text='验证集比例'
    )


class IntentRecognitionSerializer(serializers.Serializer):
    """意图识别序列化器"""
    text = serializers.CharField(help_text='待识别文本')
    context = serializers.JSONField(
        required=False,
        help_text='上下文信息'
    )
    confidence_threshold = serializers.FloatField(
        min_value=0.0,
        max_value=1.0,
        default=0.7,
        help_text='置信度阈值'
    )

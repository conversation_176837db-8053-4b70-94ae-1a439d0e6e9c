from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.db.models import Count, Avg
from .models import (
    KnowledgeBase, ConversationSession, ConversationMessage, AIModel,
    IntentPattern, AIAssistantConfig, AIAssistantLog
)


@admin.register(KnowledgeBase)
class KnowledgeBaseAdmin(admin.ModelAdmin):
    """知识库管理"""
    list_display = [
        'title', 'knowledge_type_display', 'status_display', 'priority',
        'helpfulness_rate_display', 'use_count', 'updated_at'
    ]
    list_filter = ['knowledge_type', 'status', 'priority', 'created_at']
    search_fields = ['title', 'content', 'keywords']
    readonly_fields = ['view_count', 'use_count', 'helpful_count', 'unhelpful_count', 'created_at', 'updated_at']
    filter_horizontal = ['related_knowledge']

    fieldsets = (
        ('基本信息', {
            'fields': ('title', 'knowledge_type', 'status', 'priority')
        }),
        ('内容信息', {
            'fields': ('content', 'keywords', 'tags', 'source_url')
        }),
        ('结构化内容', {
            'fields': ('questions', 'answers', 'examples'),
            'classes': ('collapse',)
        }),
        ('关联信息', {
            'fields': ('related_knowledge',),
            'classes': ('collapse',)
        }),
        ('使用统计', {
            'fields': ('view_count', 'use_count', 'helpful_count', 'unhelpful_count'),
            'classes': ('collapse',)
        }),
        ('创建信息', {
            'fields': ('created_by', 'updated_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['publish_knowledge', 'archive_knowledge']

    def knowledge_type_display(self, obj):
        """知识类型显示"""
        type_colors = {
            'faq': '#1890ff',
            'policy': '#52c41a',
            'process': '#faad14',
            'tutorial': '#722ed1',
            'tax': '#fa8c16',
            'finance': '#13c2c2',
            'legal': '#eb2f96',
            'system': '#f5222d',
        }
        color = type_colors.get(obj.knowledge_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_knowledge_type_display()
        )
    knowledge_type_display.short_description = '知识类型'

    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'draft': '#d9d9d9',
            'published': '#52c41a',
            'archived': '#faad14',
            'reviewing': '#1890ff',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'

    def helpfulness_rate_display(self, obj):
        """有用率显示"""
        rate = obj.get_helpfulness_rate()
        if rate >= 80:
            color = '#52c41a'
        elif rate >= 60:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, f"{rate:.1f}%"
        )
    helpfulness_rate_display.short_description = '有用率'

    def publish_knowledge(self, request, queryset):
        """发布知识"""
        updated = queryset.update(status='published')
        self.message_user(request, f"成功发布 {updated} 条知识")
    publish_knowledge.short_description = "发布选中的知识"

    def archive_knowledge(self, request, queryset):
        """归档知识"""
        updated = queryset.update(status='archived')
        self.message_user(request, f"成功归档 {updated} 条知识")
    archive_knowledge.short_description = "归档选中的知识"

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(ConversationSession)
class ConversationSessionAdmin(admin.ModelAdmin):
    """对话会话管理"""
    list_display = [
        'session_id', 'user_info', 'session_type_display', 'status_display',
        'message_count', 'satisfaction_display', 'duration_display', 'start_time'
    ]
    list_filter = ['session_type', 'status', 'start_time']
    search_fields = ['session_id', 'user__username', 'user_ip']
    readonly_fields = ['session_id', 'start_time', 'end_time', 'last_activity', 'message_count', 'ai_response_count']
    date_hierarchy = 'start_time'

    fieldsets = (
        ('会话信息', {
            'fields': ('session_id', 'session_type', 'status')
        }),
        ('用户信息', {
            'fields': ('user', 'user_ip', 'user_agent')
        }),
        ('时间信息', {
            'fields': ('start_time', 'end_time', 'last_activity')
        }),
        ('统计信息', {
            'fields': ('message_count', 'ai_response_count', 'satisfaction_score')
        }),
        ('上下文数据', {
            'fields': ('context_data',),
            'classes': ('collapse',)
        }),
    )

    def user_info(self, obj):
        """用户信息"""
        if obj.user:
            return format_html(
                '<a href="{}">{}</a>',
                reverse('admin:users_user_change', args=[obj.user.pk]),
                obj.user.get_full_name() or obj.user.username
            )
        return f"匿名用户 ({obj.user_ip})"
    user_info.short_description = '用户'

    def session_type_display(self, obj):
        """会话类型显示"""
        type_colors = {
            'chat': '#1890ff',
            'voice': '#52c41a',
            'api': '#faad14',
            'system': '#722ed1',
        }
        color = type_colors.get(obj.session_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_session_type_display()
        )
    session_type_display.short_description = '会话类型'

    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'active': '#52c41a',
            'completed': '#1890ff',
            'timeout': '#faad14',
            'error': '#f5222d',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'

    def satisfaction_display(self, obj):
        """满意度显示"""
        if obj.satisfaction_score:
            score = obj.satisfaction_score
            if score >= 4:
                color = '#52c41a'
            elif score >= 3:
                color = '#faad14'
            else:
                color = '#f5222d'

            return format_html(
                '<span style="color: {}; font-weight: bold;">{:.1f}★</span>',
                color, score
            )
        return '-'
    satisfaction_display.short_description = '满意度'

    def duration_display(self, obj):
        """时长显示"""
        duration = obj.get_duration()
        if duration < 60:
            return f"{duration:.0f}秒"
        elif duration < 3600:
            return f"{duration/60:.1f}分钟"
        else:
            return f"{duration/3600:.1f}小时"
    duration_display.short_description = '会话时长'


@admin.register(ConversationMessage)
class ConversationMessageAdmin(admin.ModelAdmin):
    """对话消息管理"""
    list_display = [
        'session_info', 'message_type_display', 'content_short',
        'intent_display', 'confidence_display', 'is_helpful', 'timestamp'
    ]
    list_filter = ['message_type', 'intent', 'is_helpful', 'timestamp']
    search_fields = ['content', 'raw_content']
    readonly_fields = ['timestamp', 'response_time']
    date_hierarchy = 'timestamp'

    fieldsets = (
        ('基本信息', {
            'fields': ('session', 'message_type', 'content', 'raw_content')
        }),
        ('AI分析', {
            'fields': ('intent', 'confidence', 'entities', 'keywords')
        }),
        ('回复生成', {
            'fields': ('knowledge_used', 'response_template', 'response_time'),
            'classes': ('collapse',)
        }),
        ('质量评估', {
            'fields': ('is_helpful', 'feedback_score', 'feedback_comment'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('timestamp',),
            'classes': ('collapse',)
        }),
    )

    def session_info(self, obj):
        """会话信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:ai_assistant_conversationsession_change', args=[obj.session.pk]),
            obj.session.session_id
        )
    session_info.short_description = '会话'

    def message_type_display(self, obj):
        """消息类型显示"""
        type_colors = {
            'user': '#1890ff',
            'ai': '#52c41a',
            'system': '#faad14',
            'notification': '#722ed1',
        }
        color = type_colors.get(obj.message_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_message_type_display()
        )
    message_type_display.short_description = '消息类型'

    def content_short(self, obj):
        """内容简短显示"""
        if len(obj.content) > 50:
            return obj.content[:47] + '...'
        return obj.content
    content_short.short_description = '消息内容'

    def intent_display(self, obj):
        """意图显示"""
        if obj.intent:
            intent_colors = {
                'question': '#1890ff',
                'complaint': '#f5222d',
                'request': '#52c41a',
                'greeting': '#faad14',
                'goodbye': '#722ed1',
                'unknown': '#d9d9d9',
            }
            color = intent_colors.get(obj.intent, '#d9d9d9')
            return format_html(
                '<span style="color: {}; font-weight: bold;">{}</span>',
                color, obj.get_intent_display()
            )
        return '-'
    intent_display.short_description = '意图'

    def confidence_display(self, obj):
        """置信度显示"""
        if obj.confidence:
            confidence = obj.confidence
            if confidence >= 0.8:
                color = '#52c41a'
            elif confidence >= 0.6:
                color = '#faad14'
            else:
                color = '#f5222d'

            return format_html(
                '<span style="color: {}; font-weight: bold;">{}</span>',
                color, f"{confidence:.2f}"
            )
        return '-'
    confidence_display.short_description = '置信度'


@admin.register(AIModel)
class AIModelAdmin(admin.ModelAdmin):
    """AI模型管理"""
    list_display = [
        'name', 'model_type_display', 'version', 'status_display',
        'accuracy_display', 'success_rate_display', 'is_default', 'updated_at'
    ]
    list_filter = ['model_type', 'status', 'is_default', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['total_requests', 'success_requests', 'avg_response_time', 'created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'model_type', 'version', 'description')
        }),
        ('模型配置', {
            'fields': ('config_params', 'model_path')
        }),
        ('性能指标', {
            'fields': ('accuracy', 'precision', 'recall', 'f1_score')
        }),
        ('使用统计', {
            'fields': ('total_requests', 'success_requests', 'avg_response_time'),
            'classes': ('collapse',)
        }),
        ('状态管理', {
            'fields': ('status', 'is_default')
        }),
        ('训练信息', {
            'fields': ('training_data_size', 'training_start_time', 'training_end_time'),
            'classes': ('collapse',)
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['activate_model', 'deactivate_model', 'set_as_default']

    def model_type_display(self, obj):
        """模型类型显示"""
        type_colors = {
            'nlp': '#1890ff',
            'classification': '#52c41a',
            'prediction': '#faad14',
            'recommendation': '#722ed1',
            'anomaly': '#fa8c16',
            'sentiment': '#13c2c2',
        }
        color = type_colors.get(obj.model_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_model_type_display()
        )
    model_type_display.short_description = '模型类型'

    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'training': '#faad14',
            'active': '#52c41a',
            'inactive': '#d9d9d9',
            'deprecated': '#f5222d',
            'error': '#f5222d',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'

    def accuracy_display(self, obj):
        """准确率显示"""
        if obj.accuracy:
            accuracy = obj.accuracy * 100
            if accuracy >= 90:
                color = '#52c41a'
            elif accuracy >= 80:
                color = '#faad14'
            else:
                color = '#f5222d'

            return format_html(
                '<span style="color: {}; font-weight: bold;">{}</span>',
                color, f"{accuracy:.1f}%"
            )
        return '-'
    accuracy_display.short_description = '准确率'

    def success_rate_display(self, obj):
        """成功率显示"""
        rate = obj.get_success_rate()
        if rate >= 95:
            color = '#52c41a'
        elif rate >= 90:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, f"{rate:.1f}%"
        )
    success_rate_display.short_description = '成功率'

    def activate_model(self, request, queryset):
        """激活模型"""
        updated = queryset.update(status='active')
        self.message_user(request, f"成功激活 {updated} 个模型")
    activate_model.short_description = "激活选中的模型"

    def deactivate_model(self, request, queryset):
        """停用模型"""
        updated = queryset.update(status='inactive')
        self.message_user(request, f"成功停用 {updated} 个模型")
    deactivate_model.short_description = "停用选中的模型"

    def set_as_default(self, request, queryset):
        """设为默认"""
        if queryset.count() != 1:
            self.message_user(request, "只能选择一个模型设为默认", level='error')
            return

        # 先清除所有默认标记
        AIModel.objects.filter(model_type=queryset.first().model_type).update(is_default=False)
        # 设置选中的为默认
        queryset.update(is_default=True)
        self.message_user(request, "成功设置默认模型")
    set_as_default.short_description = "设为默认模型"

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(IntentPattern)
class IntentPatternAdmin(admin.ModelAdmin):
    """意图模式管理"""
    list_display = [
        'intent_name', 'intent_category_display', 'priority',
        'success_rate_display', 'match_count', 'is_active', 'updated_at'
    ]
    list_filter = ['intent_category', 'priority', 'is_active', 'created_at']
    search_fields = ['intent_name', 'keywords']

    fieldsets = (
        ('基本信息', {
            'fields': ('intent_name', 'intent_category', 'priority', 'is_active')
        }),
        ('模式定义', {
            'fields': ('patterns', 'keywords', 'entities')
        }),
        ('回复配置', {
            'fields': ('response_templates', 'follow_up_questions')
        }),
        ('配置参数', {
            'fields': ('confidence_threshold',)
        }),
        ('使用统计', {
            'fields': ('match_count', 'success_count'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['activate_patterns', 'deactivate_patterns']

    def intent_category_display(self, obj):
        """意图分类显示"""
        category_colors = {
            'greeting': '#52c41a',
            'question': '#1890ff',
            'request': '#faad14',
            'complaint': '#f5222d',
            'praise': '#722ed1',
            'goodbye': '#13c2c2',
            'other': '#d9d9d9',
        }
        color = category_colors.get(obj.intent_category, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_intent_category_display()
        )
    intent_category_display.short_description = '意图分类'

    def success_rate_display(self, obj):
        """成功率显示"""
        rate = obj.get_success_rate()
        if rate >= 80:
            color = '#52c41a'
        elif rate >= 60:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, f"{rate:.1f}%"
        )
    success_rate_display.short_description = '成功率'

    def activate_patterns(self, request, queryset):
        """激活模式"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f"成功激活 {updated} 个意图模式")
    activate_patterns.short_description = "激活选中的模式"

    def deactivate_patterns(self, request, queryset):
        """停用模式"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f"成功停用 {updated} 个意图模式")
    deactivate_patterns.short_description = "停用选中的模式"


@admin.register(AIAssistantConfig)
class AIAssistantConfigAdmin(admin.ModelAdmin):
    """AI助手配置管理"""
    list_display = [
        'config_key', 'config_type_display', 'is_system', 'is_active', 'updated_at'
    ]
    list_filter = ['config_type', 'is_system', 'is_active']
    search_fields = ['config_key', 'description']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('config_type', 'config_key', 'description')
        }),
        ('配置值', {
            'fields': ('config_value',)
        }),
        ('配置属性', {
            'fields': ('is_system', 'is_active')
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def config_type_display(self, obj):
        """配置类型显示"""
        type_colors = {
            'general': '#1890ff',
            'nlp': '#52c41a',
            'response': '#faad14',
            'learning': '#722ed1',
            'security': '#f5222d',
        }
        color = type_colors.get(obj.config_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_config_type_display()
        )
    config_type_display.short_description = '配置类型'

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(AIAssistantLog)
class AIAssistantLogAdmin(admin.ModelAdmin):
    """AI助手日志管理"""
    list_display = [
        'timestamp', 'session_info', 'level_display', 'action_type_display',
        'message_short', 'execution_time_display'
    ]
    list_filter = ['level', 'action_type', 'timestamp']
    search_fields = ['message', 'error_code']
    readonly_fields = ['timestamp']
    date_hierarchy = 'timestamp'

    fieldsets = (
        ('基本信息', {
            'fields': ('session', 'level', 'action_type', 'message')
        }),
        ('详细信息', {
            'fields': ('details',),
            'classes': ('collapse',)
        }),
        ('性能信息', {
            'fields': ('execution_time', 'memory_usage'),
            'classes': ('collapse',)
        }),
        ('错误信息', {
            'fields': ('error_code', 'stack_trace'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('timestamp',),
            'classes': ('collapse',)
        }),
    )

    def session_info(self, obj):
        """会话信息"""
        if obj.session:
            return format_html(
                '<a href="{}">{}</a>',
                reverse('admin:ai_assistant_conversationsession_change', args=[obj.session.pk]),
                obj.session.session_id
            )
        return '-'
    session_info.short_description = '会话'

    def level_display(self, obj):
        """日志级别显示"""
        level_colors = {
            'DEBUG': '#d9d9d9',
            'INFO': '#1890ff',
            'WARNING': '#faad14',
            'ERROR': '#f5222d',
            'CRITICAL': '#722ed1',
        }
        color = level_colors.get(obj.level, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.level
        )
    level_display.short_description = '日志级别'

    def action_type_display(self, obj):
        """操作类型显示"""
        action_colors = {
            'query': '#1890ff',
            'response': '#52c41a',
            'learning': '#faad14',
            'model_inference': '#722ed1',
            'error_handling': '#f5222d',
            'system_operation': '#13c2c2',
        }
        color = action_colors.get(obj.action_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_action_type_display()
        )
    action_type_display.short_description = '操作类型'

    def message_short(self, obj):
        """消息简短显示"""
        if len(obj.message) > 100:
            return obj.message[:97] + '...'
        return obj.message
    message_short.short_description = '日志消息'

    def execution_time_display(self, obj):
        """执行时间显示"""
        if obj.execution_time:
            time = obj.execution_time
            if time < 0.1:
                return f"{time*1000:.0f}ms"
            else:
                return f"{time:.2f}s"
        return '-'
    execution_time_display.short_description = '执行时间'


# 自定义管理页面标题
admin.site.site_header = '代理记账管理系统'
admin.site.site_title = 'AI智能助手'
admin.site.index_title = 'AI智能助手'

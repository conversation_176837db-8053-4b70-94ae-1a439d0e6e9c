from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
import json

User = get_user_model()


class KnowledgeBase(models.Model):
    """知识库"""
    KNOWLEDGE_TYPES = [
        ('faq', '常见问题'),
        ('policy', '政策法规'),
        ('process', '业务流程'),
        ('tutorial', '操作教程'),
        ('tax', '税务知识'),
        ('finance', '财务知识'),
        ('legal', '法律法规'),
        ('system', '系统帮助'),
    ]

    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('published', '已发布'),
        ('archived', '已归档'),
        ('reviewing', '审核中'),
    ]

    title = models.CharField('标题', max_length=200)
    content = models.TextField('内容')
    knowledge_type = models.CharField('知识类型', max_length=20, choices=KNOWLEDGE_TYPES)
    keywords = models.TextField('关键词', help_text='用逗号分隔多个关键词')
    tags = models.JSONField('标签', default=list, help_text='知识标签列表')

    # 内容结构化
    questions = models.JSONField('相关问题', default=list, help_text='可能的问题表述')
    answers = models.JSONField('标准答案', default=list, help_text='标准回答模板')
    examples = models.JSONField('示例', default=list, help_text='使用示例')

    # 状态管理
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='draft')
    priority = models.IntegerField('优先级', default=1, validators=[MinValueValidator(1), MaxValueValidator(10)])

    # 使用统计
    view_count = models.IntegerField('查看次数', default=0)
    use_count = models.IntegerField('使用次数', default=0)
    helpful_count = models.IntegerField('有用评价', default=0)
    unhelpful_count = models.IntegerField('无用评价', default=0)

    # 关联信息
    related_knowledge = models.ManyToManyField('self', blank=True, verbose_name='相关知识')
    source_url = models.URLField('来源链接', blank=True)

    # 时间信息
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_knowledge', verbose_name='创建人')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='updated_knowledge', verbose_name='更新人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '知识库'
        verbose_name_plural = '知识库'
        ordering = ['-priority', '-updated_at']

    def __str__(self):
        return self.title

    def get_helpfulness_rate(self):
        """获取有用率"""
        total = self.helpful_count + self.unhelpful_count
        if total == 0:
            return 0
        return (self.helpful_count / total) * 100


class ConversationSession(models.Model):
    """对话会话"""
    SESSION_TYPES = [
        ('chat', '在线聊天'),
        ('voice', '语音对话'),
        ('api', 'API调用'),
        ('system', '系统自动'),
    ]

    STATUS_CHOICES = [
        ('active', '进行中'),
        ('completed', '已完成'),
        ('timeout', '超时'),
        ('error', '错误'),
    ]

    session_id = models.CharField('会话ID', max_length=100, unique=True)
    session_type = models.CharField('会话类型', max_length=20, choices=SESSION_TYPES, default='chat')

    # 用户信息
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, verbose_name='用户')
    user_ip = models.GenericIPAddressField('用户IP', null=True, blank=True)
    user_agent = models.TextField('用户代理', blank=True)

    # 会话状态
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='active')
    start_time = models.DateTimeField('开始时间', auto_now_add=True)
    end_time = models.DateTimeField('结束时间', null=True, blank=True)
    last_activity = models.DateTimeField('最后活动', auto_now=True)

    # 会话统计
    message_count = models.IntegerField('消息数量', default=0)
    ai_response_count = models.IntegerField('AI回复数量', default=0)
    satisfaction_score = models.FloatField('满意度评分', null=True, blank=True, validators=[MinValueValidator(1), MaxValueValidator(5)])

    # 会话上下文
    context_data = models.JSONField('上下文数据', default=dict, help_text='会话上下文信息')

    class Meta:
        verbose_name = '对话会话'
        verbose_name_plural = '对话会话'
        ordering = ['-start_time']

    def __str__(self):
        return f"会话 {self.session_id}"

    def get_duration(self):
        """获取会话时长"""
        if self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return (timezone.now() - self.start_time).total_seconds()


class ConversationMessage(models.Model):
    """对话消息"""
    MESSAGE_TYPES = [
        ('user', '用户消息'),
        ('ai', 'AI回复'),
        ('system', '系统消息'),
        ('notification', '通知消息'),
    ]

    INTENT_TYPES = [
        ('question', '问题咨询'),
        ('complaint', '投诉建议'),
        ('request', '服务请求'),
        ('greeting', '问候'),
        ('goodbye', '告别'),
        ('unknown', '未知意图'),
    ]

    session = models.ForeignKey(ConversationSession, on_delete=models.CASCADE, related_name='messages', verbose_name='会话')
    message_type = models.CharField('消息类型', max_length=20, choices=MESSAGE_TYPES)

    # 消息内容
    content = models.TextField('消息内容')
    raw_content = models.TextField('原始内容', blank=True, help_text='用户原始输入')

    # AI分析结果
    intent = models.CharField('意图识别', max_length=20, choices=INTENT_TYPES, blank=True)
    confidence = models.FloatField('置信度', null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(1)])
    entities = models.JSONField('实体识别', default=list, help_text='识别出的实体信息')
    keywords = models.JSONField('关键词', default=list, help_text='提取的关键词')

    # 回复生成
    knowledge_used = models.ForeignKey(KnowledgeBase, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='使用的知识')
    response_template = models.TextField('回复模板', blank=True)
    response_time = models.FloatField('响应时间(秒)', null=True, blank=True)

    # 质量评估
    is_helpful = models.BooleanField('是否有用', null=True, blank=True)
    feedback_score = models.IntegerField('反馈评分', null=True, blank=True, validators=[MinValueValidator(1), MaxValueValidator(5)])
    feedback_comment = models.TextField('反馈意见', blank=True)

    # 时间信息
    timestamp = models.DateTimeField('时间戳', auto_now_add=True)

    class Meta:
        verbose_name = '对话消息'
        verbose_name_plural = '对话消息'
        ordering = ['timestamp']

    def __str__(self):
        return f"{self.get_message_type_display()}: {self.content[:50]}"


class AIModel(models.Model):
    """AI模型"""
    MODEL_TYPES = [
        ('nlp', '自然语言处理'),
        ('classification', '分类模型'),
        ('prediction', '预测模型'),
        ('recommendation', '推荐模型'),
        ('anomaly', '异常检测'),
        ('sentiment', '情感分析'),
    ]

    STATUS_CHOICES = [
        ('training', '训练中'),
        ('active', '活跃'),
        ('inactive', '停用'),
        ('deprecated', '已弃用'),
        ('error', '错误'),
    ]

    name = models.CharField('模型名称', max_length=100)
    model_type = models.CharField('模型类型', max_length=20, choices=MODEL_TYPES)
    version = models.CharField('版本', max_length=20)
    description = models.TextField('描述', blank=True)

    # 模型配置
    config_params = models.JSONField('配置参数', default=dict)
    model_path = models.CharField('模型路径', max_length=500, blank=True)

    # 性能指标
    accuracy = models.FloatField('准确率', null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(1)])
    precision = models.FloatField('精确率', null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(1)])
    recall = models.FloatField('召回率', null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(1)])
    f1_score = models.FloatField('F1分数', null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(1)])

    # 使用统计
    total_requests = models.IntegerField('总请求数', default=0)
    success_requests = models.IntegerField('成功请求数', default=0)
    avg_response_time = models.FloatField('平均响应时间(秒)', default=0)

    # 状态管理
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='training')
    is_default = models.BooleanField('是否默认', default=False)

    # 训练信息
    training_data_size = models.IntegerField('训练数据量', default=0)
    training_start_time = models.DateTimeField('训练开始时间', null=True, blank=True)
    training_end_time = models.DateTimeField('训练结束时间', null=True, blank=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = 'AI模型'
        verbose_name_plural = 'AI模型'
        unique_together = ['name', 'version']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} v{self.version}"

    def get_success_rate(self):
        """获取成功率"""
        if self.total_requests == 0:
            return 0
        return (self.success_requests / self.total_requests) * 100


class IntentPattern(models.Model):
    """意图模式"""
    INTENT_CATEGORIES = [
        ('greeting', '问候类'),
        ('question', '问题类'),
        ('request', '请求类'),
        ('complaint', '投诉类'),
        ('praise', '表扬类'),
        ('goodbye', '告别类'),
        ('other', '其他'),
    ]

    intent_name = models.CharField('意图名称', max_length=100)
    intent_category = models.CharField('意图分类', max_length=20, choices=INTENT_CATEGORIES)

    # 模式定义
    patterns = models.JSONField('匹配模式', default=list, help_text='用于匹配的文本模式')
    keywords = models.JSONField('关键词', default=list, help_text='意图关键词')
    entities = models.JSONField('实体类型', default=list, help_text='可能包含的实体类型')

    # 回复配置
    response_templates = models.JSONField('回复模板', default=list, help_text='回复模板列表')
    follow_up_questions = models.JSONField('追问问题', default=list, help_text='可能的追问')

    # 优先级和权重
    priority = models.IntegerField('优先级', default=1, validators=[MinValueValidator(1), MaxValueValidator(10)])
    confidence_threshold = models.FloatField('置信度阈值', default=0.7, validators=[MinValueValidator(0), MaxValueValidator(1)])

    # 使用统计
    match_count = models.IntegerField('匹配次数', default=0)
    success_count = models.IntegerField('成功次数', default=0)

    is_active = models.BooleanField('是否启用', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '意图模式'
        verbose_name_plural = '意图模式'
        ordering = ['-priority', 'intent_name']

    def __str__(self):
        return f"{self.intent_name} ({self.get_intent_category_display()})"

    def get_success_rate(self):
        """获取成功率"""
        if self.match_count == 0:
            return 0
        return (self.success_count / self.match_count) * 100


class AIAssistantConfig(models.Model):
    """AI助手配置"""
    CONFIG_TYPES = [
        ('general', '通用配置'),
        ('nlp', 'NLP配置'),
        ('response', '回复配置'),
        ('learning', '学习配置'),
        ('security', '安全配置'),
    ]

    config_type = models.CharField('配置类型', max_length=20, choices=CONFIG_TYPES)
    config_key = models.CharField('配置键', max_length=100)
    config_value = models.JSONField('配置值', default=dict)
    description = models.TextField('配置描述', blank=True)

    # 配置属性
    is_system = models.BooleanField('系统配置', default=False)
    is_active = models.BooleanField('是否启用', default=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = 'AI助手配置'
        verbose_name_plural = 'AI助手配置'
        unique_together = ['config_type', 'config_key']
        ordering = ['config_type', 'config_key']

    def __str__(self):
        return f"{self.get_config_type_display()} - {self.config_key}"


class AIAssistantLog(models.Model):
    """AI助手日志"""
    LOG_LEVELS = [
        ('DEBUG', 'DEBUG'),
        ('INFO', 'INFO'),
        ('WARNING', 'WARNING'),
        ('ERROR', 'ERROR'),
        ('CRITICAL', 'CRITICAL'),
    ]

    ACTION_TYPES = [
        ('query', '查询处理'),
        ('response', '回复生成'),
        ('learning', '学习更新'),
        ('model_inference', '模型推理'),
        ('error_handling', '错误处理'),
        ('system_operation', '系统操作'),
    ]

    session = models.ForeignKey(ConversationSession, on_delete=models.CASCADE, null=True, blank=True, verbose_name='会话')

    # 日志信息
    level = models.CharField('日志级别', max_length=10, choices=LOG_LEVELS)
    action_type = models.CharField('操作类型', max_length=20, choices=ACTION_TYPES)
    message = models.TextField('日志消息')
    details = models.JSONField('详细信息', default=dict)

    # 性能信息
    execution_time = models.FloatField('执行时间(秒)', null=True, blank=True)
    memory_usage = models.FloatField('内存使用(MB)', null=True, blank=True)

    # 错误信息
    error_code = models.CharField('错误代码', max_length=50, blank=True)
    stack_trace = models.TextField('堆栈跟踪', blank=True)

    timestamp = models.DateTimeField('时间戳', auto_now_add=True)

    class Meta:
        verbose_name = 'AI助手日志'
        verbose_name_plural = 'AI助手日志'
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.level} - {self.message[:50]}"

from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from . import views

router = DefaultRouter()
router.register(r'knowledge', views.KnowledgeBaseViewSet, basename='knowledge')
router.register(r'sessions', views.ConversationSessionViewSet, basename='session')
router.register(r'messages', views.ConversationMessageViewSet, basename='message')
router.register(r'models', views.AIModelViewSet, basename='model')
router.register(r'patterns', views.IntentPatternViewSet, basename='pattern')
router.register(r'configs', views.AIAssistantConfigViewSet, basename='config')
router.register(r'logs', views.AIAssistantLogViewSet, basename='log')
router.register(r'assistant', views.AIAssistantViewSet, basename='assistant')

app_name = 'ai_assistant'

urlpatterns = [
    path('', include(router.urls)),
]

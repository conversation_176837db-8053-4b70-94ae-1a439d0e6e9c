from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    SmartDataSource, AnalysisModel, AnalysisTask, DataInsight,
    PredictionResult, AnomalyDetection, SmartReport, SmartAnalyticsConfig
)

User = get_user_model()


class SmartDataSourceSerializer(serializers.ModelSerializer):
    """数据源序列化器"""
    source_type_display = serializers.CharField(source='get_source_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)

    class Meta:
        model = SmartDataSource
        fields = [
            'id', 'name', 'source_type', 'source_type_display', 'description',
            'connection_config', 'refresh_interval', 'data_schema', 'query_config',
            'status', 'status_display', 'last_sync_time', 'last_sync_status',
            'error_message', 'total_records', 'sync_count', 'created_by',
            'created_by_name', 'created_at', 'updated_at'
        ]


class AnalysisModelSerializer(serializers.ModelSerializer):
    """分析模型序列化器"""
    model_type_display = serializers.CharField(source='get_model_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    success_rate = serializers.SerializerMethodField()
    data_source_names = serializers.SerializerMethodField()
    
    class Meta:
        model = AnalysisModel
        fields = [
            'id', 'name', 'model_type', 'model_type_display', 'description',
            'algorithm', 'target_fields', 'feature_fields', 'parameters',
            'training_config', 'accuracy', 'precision', 'recall', 'f1_score',
            'mse', 'mae', 'status', 'status_display', 'is_auto_retrain',
            'retrain_interval', 'training_data_size', 'last_training_time',
            'training_duration', 'prediction_count', 'success_count',
            'success_rate', 'data_source_names', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]
    
    def get_success_rate(self, obj):
        return obj.get_success_rate()
    
    def get_data_source_names(self, obj):
        return [ds.name for ds in obj.data_sources.all()]


class AnalysisTaskSerializer(serializers.ModelSerializer):
    """分析任务序列化器"""
    task_type_display = serializers.CharField(source='get_task_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    model_name = serializers.CharField(source='model.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    duration = serializers.SerializerMethodField()
    
    class Meta:
        model = AnalysisTask
        fields = [
            'id', 'task_id', 'name', 'task_type', 'task_type_display',
            'description', 'model', 'model_name', 'input_data', 'analysis_config',
            'priority', 'priority_display', 'scheduled_time', 'timeout',
            'status', 'status_display', 'progress', 'start_time', 'end_time',
            'execution_time', 'duration', 'result_data', 'result_files',
            'insights', 'error_code', 'error_message', 'parent_task',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
    
    def get_duration(self, obj):
        return obj.get_duration()


class DataInsightSerializer(serializers.ModelSerializer):
    """数据洞察序列化器"""
    insight_type_display = serializers.CharField(source='get_insight_type_display', read_only=True)
    severity_display = serializers.CharField(source='get_severity_display', read_only=True)
    task_name = serializers.CharField(source='task.name', read_only=True)
    model_name = serializers.CharField(source='model.name', read_only=True)
    acknowledged_by_name = serializers.CharField(source='acknowledged_by.get_full_name', read_only=True)
    is_valid_now = serializers.SerializerMethodField()
    data_source_names = serializers.SerializerMethodField()
    
    class Meta:
        model = DataInsight
        fields = [
            'id', 'title', 'insight_type', 'insight_type_display', 'description',
            'task', 'task_name', 'model', 'model_name', 'key_findings',
            'metrics', 'visualizations', 'severity', 'severity_display',
            'confidence', 'impact_score', 'recommendations', 'action_items',
            'is_acknowledged', 'acknowledged_by', 'acknowledged_by_name',
            'acknowledged_at', 'valid_from', 'valid_until', 'is_valid_now',
            'data_source_names', 'created_at', 'updated_at'
        ]
    
    def get_is_valid_now(self, obj):
        return obj.is_valid()
    
    def get_data_source_names(self, obj):
        return [ds.name for ds in obj.data_sources.all()]


class PredictionResultSerializer(serializers.ModelSerializer):
    """预测结果序列化器"""
    prediction_type_display = serializers.CharField(source='get_prediction_type_display', read_only=True)
    task_name = serializers.CharField(source='task.name', read_only=True)
    model_name = serializers.CharField(source='model.name', read_only=True)
    used_by_name = serializers.CharField(source='used_by.get_full_name', read_only=True)
    
    class Meta:
        model = PredictionResult
        fields = [
            'id', 'prediction_id', 'prediction_type', 'prediction_type_display',
            'task', 'task_name', 'model', 'model_name', 'input_features',
            'input_timestamp', 'predicted_value', 'confidence_interval',
            'probability_distribution', 'confidence_score', 'uncertainty',
            'prediction_horizon', 'target_date', 'actual_value', 'is_validated',
            'validation_date', 'accuracy_score', 'is_used', 'used_by',
            'used_by_name', 'used_at', 'created_at', 'updated_at'
        ]


class AnomalyDetectionSerializer(serializers.ModelSerializer):
    """异常检测序列化器"""
    anomaly_type_display = serializers.CharField(source='get_anomaly_type_display', read_only=True)
    severity_display = serializers.CharField(source='get_severity_display', read_only=True)
    task_name = serializers.CharField(source='task.name', read_only=True)
    model_name = serializers.CharField(source='model.name', read_only=True)
    data_source_name = serializers.CharField(source='data_source.name', read_only=True)
    resolved_by_name = serializers.CharField(source='resolved_by.get_full_name', read_only=True)
    
    class Meta:
        model = AnomalyDetection
        fields = [
            'id', 'anomaly_id', 'anomaly_type', 'anomaly_type_display',
            'task', 'task_name', 'model', 'model_name', 'data_source',
            'data_source_name', 'affected_fields', 'anomaly_score',
            'severity', 'severity_display', 'anomalous_data', 'expected_range',
            'deviation', 'detection_time', 'anomaly_start_time', 'anomaly_end_time',
            'is_resolved', 'resolution_notes', 'resolved_by', 'resolved_by_name',
            'resolved_at', 'root_cause', 'contributing_factors', 'business_impact',
            'financial_impact', 'created_at', 'updated_at'
        ]


class SmartReportSerializer(serializers.ModelSerializer):
    """智能报告序列化器"""
    report_type_display = serializers.CharField(source='get_report_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    data_source_names = serializers.SerializerMethodField()
    model_names = serializers.SerializerMethodField()
    
    class Meta:
        model = SmartReport
        fields = [
            'id', 'report_id', 'title', 'report_type', 'report_type_display',
            'description', 'executive_summary', 'key_insights', 'recommendations',
            'metrics_summary', 'trend_analysis', 'comparative_analysis',
            'charts_config', 'tables_data', 'report_period_start',
            'report_period_end', 'status', 'status_display', 'generation_progress',
            'generation_start_time', 'generation_end_time', 'generation_duration',
            'report_file_path', 'file_size', 'is_public', 'view_count',
            'download_count', 'data_source_names', 'model_names',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
    
    def get_data_source_names(self, obj):
        return [ds.name for ds in obj.data_sources.all()]
    
    def get_model_names(self, obj):
        return [model.name for model in obj.analysis_models.all()]


class SmartAnalyticsConfigSerializer(serializers.ModelSerializer):
    """智能分析配置序列化器"""
    config_type_display = serializers.CharField(source='get_config_type_display', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = SmartAnalyticsConfig
        fields = [
            'id', 'config_type', 'config_type_display', 'config_key',
            'config_value', 'description', 'is_system', 'is_active',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]


# 简化版序列化器用于列表显示
class SmartDataSourceListSerializer(serializers.ModelSerializer):
    """数据源列表序列化器"""
    source_type_display = serializers.CharField(source='get_source_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)

    class Meta:
        model = SmartDataSource
        fields = [
            'id', 'name', 'source_type_display', 'status_display',
            'total_records', 'last_sync_status', 'updated_at'
        ]


class AnalysisModelListSerializer(serializers.ModelSerializer):
    """分析模型列表序列化器"""
    model_type_display = serializers.CharField(source='get_model_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    success_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = AnalysisModel
        fields = [
            'id', 'name', 'model_type_display', 'status_display',
            'accuracy', 'success_rate', 'prediction_count', 'updated_at'
        ]
    
    def get_success_rate(self, obj):
        return obj.get_success_rate()


class AnalysisTaskListSerializer(serializers.ModelSerializer):
    """分析任务列表序列化器"""
    task_type_display = serializers.CharField(source='get_task_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    model_name = serializers.CharField(source='model.name', read_only=True)
    
    class Meta:
        model = AnalysisTask
        fields = [
            'id', 'task_id', 'name', 'task_type_display', 'model_name',
            'status_display', 'progress', 'created_at'
        ]


# 业务操作序列化器
class DataSyncSerializer(serializers.Serializer):
    """数据同步序列化器"""
    data_source_id = serializers.IntegerField(help_text='数据源ID')
    force_sync = serializers.BooleanField(default=False, help_text='强制同步')


class ModelTrainingSerializer(serializers.Serializer):
    """模型训练序列化器"""
    model_id = serializers.IntegerField(help_text='模型ID')
    training_data = serializers.JSONField(required=False, help_text='训练数据')
    validation_split = serializers.FloatField(
        min_value=0.1,
        max_value=0.5,
        default=0.2,
        help_text='验证集比例'
    )
    epochs = serializers.IntegerField(
        min_value=1,
        max_value=1000,
        default=100,
        help_text='训练轮数'
    )


class PredictionRequestSerializer(serializers.Serializer):
    """预测请求序列化器"""
    model_id = serializers.IntegerField(help_text='模型ID')
    input_data = serializers.JSONField(help_text='输入数据')
    prediction_horizon = serializers.IntegerField(
        min_value=1,
        max_value=365,
        default=1,
        help_text='预测时间范围(天)'
    )
    confidence_level = serializers.FloatField(
        min_value=0.5,
        max_value=0.99,
        default=0.95,
        help_text='置信水平'
    )


class AnomalyDetectionRequestSerializer(serializers.Serializer):
    """异常检测请求序列化器"""
    model_id = serializers.IntegerField(help_text='模型ID')
    data_source_id = serializers.IntegerField(help_text='数据源ID')
    detection_config = serializers.JSONField(
        required=False,
        help_text='检测配置'
    )
    sensitivity = serializers.FloatField(
        min_value=0.1,
        max_value=1.0,
        default=0.5,
        help_text='检测敏感度'
    )


class ReportGenerationSerializer(serializers.Serializer):
    """报告生成序列化器"""
    report_type = serializers.ChoiceField(
        choices=SmartReport.REPORT_TYPES,
        help_text='报告类型'
    )
    title = serializers.CharField(max_length=200, help_text='报告标题')
    data_source_ids = serializers.ListField(
        child=serializers.IntegerField(),
        help_text='数据源ID列表'
    )
    model_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        help_text='分析模型ID列表'
    )
    period_start = serializers.DateTimeField(help_text='报告期间开始')
    period_end = serializers.DateTimeField(help_text='报告期间结束')
    include_insights = serializers.BooleanField(
        default=True,
        help_text='包含洞察分析'
    )
    include_predictions = serializers.BooleanField(
        default=True,
        help_text='包含预测分析'
    )

from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views

router = DefaultRouter()
router.register(r'data-sources', views.SmartDataSourceViewSet, basename='data-source')
router.register(r'models', views.AnalysisModelViewSet, basename='analysis-model')
router.register(r'tasks', views.AnalysisTaskViewSet, basename='analysis-task')
router.register(r'insights', views.DataInsightViewSet, basename='data-insight')
router.register(r'predictions', views.PredictionResultViewSet, basename='prediction-result')
router.register(r'anomalies', views.AnomalyDetectionViewSet, basename='anomaly-detection')
router.register(r'reports', views.SmartReportViewSet, basename='smart-report')
router.register(r'configs', views.SmartAnalyticsConfigViewSet, basename='smart-analytics-config')
router.register(r'analytics', views.SmartAnalyticsViewSet, basename='smart-analytics')

app_name = 'smart_analytics'

urlpatterns = [
    path('', include(router.urls)),
]

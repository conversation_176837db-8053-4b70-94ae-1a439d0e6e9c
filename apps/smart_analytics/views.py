from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q, Count, Avg, Sum, Max, Min
from django.shortcuts import get_object_or_404
from datetime import datetime, timedelta
import uuid
import json
import random
import numpy as np
from .models import (
    SmartDataSource, AnalysisModel, AnalysisTask, DataInsight,
    PredictionResult, AnomalyDetection, SmartReport, SmartAnalyticsConfig
)
from .serializers import (
    SmartDataSourceSerializer, AnalysisModelSerializer, AnalysisTaskSerializer,
    DataInsightSerializer, PredictionResultSerializer, AnomalyDetectionSerializer,
    SmartReportSerializer, SmartAnalyticsConfigSerializer,
    SmartDataSourceListSerializer, AnalysisModelListSerializer, AnalysisTaskListSerializer,
    DataSyncSerializer, ModelTrainingSerializer, PredictionRequestSerializer,
    AnomalyDetectionRequestSerializer, ReportGenerationSerializer
)
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class SmartDataSourceViewSet(viewsets.ModelViewSet):
    """数据源ViewSet"""
    queryset = SmartDataSource.objects.all()
    serializer_class = SmartDataSourceSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按数据源类型过滤
        source_type = self.request.query_params.get('source_type')
        if source_type:
            queryset = queryset.filter(source_type=source_type)

        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # 搜索
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )

        return queryset.order_by('-created_at')

    def get_serializer_class(self):
        if self.action == 'list':
            return SmartDataSourceListSerializer
        return SmartDataSourceSerializer

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def sync_data(self, request, pk=None):
        """同步数据"""
        data_source = self.get_object()
        serializer = DataSyncSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        force_sync = serializer.validated_data.get('force_sync', False)

        # 检查是否需要同步
        if not force_sync and data_source.last_sync_time:
            time_diff = timezone.now() - data_source.last_sync_time
            if time_diff.total_seconds() < data_source.refresh_interval * 60:
                return Response({
                    'message': '数据源最近已同步，无需重复同步',
                    'last_sync_time': data_source.last_sync_time
                })

        # 执行同步逻辑
        try:
            # 这里可以实现具体的数据同步逻辑
            # 模拟同步过程
            data_source.last_sync_time = timezone.now()
            data_source.last_sync_status = True
            data_source.sync_count += 1
            data_source.total_records = random.randint(1000, 10000)
            data_source.error_message = ''
            data_source.save()

            return Response({
                'message': '数据同步成功',
                'sync_time': data_source.last_sync_time,
                'total_records': data_source.total_records
            })

        except Exception as e:
            data_source.last_sync_status = False
            data_source.error_message = str(e)
            data_source.save()

            return Response(
                {'error': f'数据同步失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def test_connection(self, request, pk=None):
        """测试连接"""
        data_source = self.get_object()

        try:
            # 这里可以实现具体的连接测试逻辑
            # 模拟连接测试
            connection_time = random.uniform(0.1, 2.0)

            return Response({
                'status': 'success',
                'message': '连接测试成功',
                'connection_time': connection_time,
                'test_time': timezone.now()
            })

        except Exception as e:
            return Response({
                'status': 'failed',
                'message': f'连接测试失败: {str(e)}',
                'test_time': timezone.now()
            })

    @action(detail=True, methods=['get'])
    def statistics(self, request, pk=None):
        """获取数据源统计信息"""
        data_source = self.get_object()

        # 获取相关的分析模型
        related_models = AnalysisModel.objects.filter(data_sources=data_source)

        # 获取相关的分析任务
        related_tasks = AnalysisTask.objects.filter(model__in=related_models)

        # 统计信息
        stats = {
            'data_source': {
                'name': data_source.name,
                'type': data_source.get_source_type_display(),
                'status': data_source.get_status_display(),
                'total_records': data_source.total_records,
                'sync_count': data_source.sync_count,
                'last_sync_time': data_source.last_sync_time
            },
            'related_models': {
                'total': related_models.count(),
                'active': related_models.filter(status='active').count(),
                'models': [
                    {
                        'name': model.name,
                        'type': model.get_model_type_display(),
                        'accuracy': model.accuracy
                    }
                    for model in related_models[:5]
                ]
            },
            'related_tasks': {
                'total': related_tasks.count(),
                'completed': related_tasks.filter(status='completed').count(),
                'failed': related_tasks.filter(status='failed').count(),
                'recent_tasks': [
                    {
                        'name': task.name,
                        'status': task.get_status_display(),
                        'created_at': task.created_at
                    }
                    for task in related_tasks.order_by('-created_at')[:5]
                ]
            }
        }

        return Response(stats)


class AnalysisModelViewSet(viewsets.ModelViewSet):
    """分析模型ViewSet"""
    queryset = AnalysisModel.objects.all()
    serializer_class = AnalysisModelSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按模型类型过滤
        model_type = self.request.query_params.get('model_type')
        if model_type:
            queryset = queryset.filter(model_type=model_type)

        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # 搜索
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search) |
                Q(algorithm__icontains=search)
            )

        return queryset.order_by('-created_at')

    def get_serializer_class(self):
        if self.action == 'list':
            return AnalysisModelListSerializer
        return AnalysisModelSerializer

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def train(self, request, pk=None):
        """训练模型"""
        model = self.get_object()
        serializer = ModelTrainingSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data

        # 检查模型状态
        if model.status == 'training':
            return Response(
                {'error': '模型正在训练中，请稍后再试'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 开始训练
        model.status = 'training'
        model.last_training_time = timezone.now()
        model.training_data_size = len(data.get('training_data', []))
        model.save()

        try:
            # 这里可以实现具体的模型训练逻辑
            # 模拟训练过程
            training_duration = random.uniform(10, 60)  # 模拟训练时间

            # 模拟训练结果
            model.status = 'active'
            model.training_duration = training_duration
            model.accuracy = random.uniform(0.8, 0.98)
            model.precision = random.uniform(0.75, 0.95)
            model.recall = random.uniform(0.70, 0.92)
            model.f1_score = 2 * (model.precision * model.recall) / (model.precision + model.recall)

            if model.model_type in ['forecast', 'regression']:
                model.mse = random.uniform(0.01, 0.1)
                model.mae = random.uniform(0.05, 0.2)

            model.save()

            return Response({
                'message': '模型训练完成',
                'training_duration': training_duration,
                'accuracy': model.accuracy,
                'precision': model.precision,
                'recall': model.recall,
                'f1_score': model.f1_score
            })

        except Exception as e:
            model.status = 'error'
            model.save()

            return Response(
                {'error': f'模型训练失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def predict(self, request, pk=None):
        """模型预测"""
        model = self.get_object()
        serializer = PredictionRequestSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data

        # 检查模型状态
        if model.status != 'active':
            return Response(
                {'error': '模型未激活，无法进行预测'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 创建预测任务
        task = AnalysisTask.objects.create(
            task_id=f"PRED_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}",
            name=f"预测任务 - {model.name}",
            task_type='manual',
            model=model,
            input_data=data['input_data'],
            analysis_config={
                'prediction_horizon': data['prediction_horizon'],
                'confidence_level': data['confidence_level']
            },
            created_by=request.user
        )

        # 执行预测
        try:
            task.status = 'running'
            task.start_time = timezone.now()
            task.save()

            # 这里可以实现具体的预测逻辑
            # 模拟预测过程
            prediction_result = self._generate_prediction(model, data)

            # 保存预测结果
            prediction = PredictionResult.objects.create(
                prediction_id=f"PRED_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}",
                prediction_type=self._get_prediction_type(model.model_type),
                task=task,
                model=model,
                input_features=data['input_data'],
                input_timestamp=timezone.now(),
                predicted_value=prediction_result['predicted_value'],
                confidence_interval=prediction_result['confidence_interval'],
                probability_distribution=prediction_result.get('probability_distribution', {}),
                confidence_score=prediction_result['confidence_score'],
                uncertainty=prediction_result['uncertainty'],
                prediction_horizon=data['prediction_horizon'],
                target_date=timezone.now() + timedelta(days=data['prediction_horizon'])
            )

            # 更新任务状态
            task.status = 'completed'
            task.end_time = timezone.now()
            task.execution_time = (task.end_time - task.start_time).total_seconds()
            task.result_data = prediction_result
            task.save()

            # 更新模型统计
            model.prediction_count += 1
            model.success_count += 1
            model.save()

            return Response({
                'message': '预测完成',
                'task_id': task.task_id,
                'prediction_id': prediction.prediction_id,
                'predicted_value': prediction_result['predicted_value'],
                'confidence_score': prediction_result['confidence_score']
            })

        except Exception as e:
            task.status = 'failed'
            task.error_message = str(e)
            task.end_time = timezone.now()
            task.save()

            model.prediction_count += 1
            model.save()

            return Response(
                {'error': f'预测失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _generate_prediction(self, model, data):
        """生成预测结果"""
        # 这里是模拟的预测逻辑
        if model.model_type == 'forecast':
            # 时间序列预测
            base_value = random.uniform(1000, 10000)
            trend = random.uniform(-0.1, 0.1)
            predicted_value = base_value * (1 + trend)

            confidence_score = random.uniform(0.7, 0.95)
            uncertainty = 1 - confidence_score

            # 置信区间
            margin = predicted_value * uncertainty * 0.5
            confidence_interval = {
                'lower': predicted_value - margin,
                'upper': predicted_value + margin
            }

        elif model.model_type == 'classification':
            # 分类预测
            classes = ['A', 'B', 'C', 'D']
            probabilities = np.random.dirichlet(np.ones(len(classes)))

            predicted_class = classes[np.argmax(probabilities)]
            confidence_score = float(np.max(probabilities))
            uncertainty = 1 - confidence_score

            predicted_value = predicted_class
            confidence_interval = {}

        else:
            # 其他类型的预测
            predicted_value = random.uniform(0, 100)
            confidence_score = random.uniform(0.6, 0.9)
            uncertainty = 1 - confidence_score

            margin = predicted_value * uncertainty * 0.3
            confidence_interval = {
                'lower': predicted_value - margin,
                'upper': predicted_value + margin
            }

        return {
            'predicted_value': predicted_value,
            'confidence_score': confidence_score,
            'uncertainty': uncertainty,
            'confidence_interval': confidence_interval
        }

    def _get_prediction_type(self, model_type):
        """根据模型类型获取预测类型"""
        type_mapping = {
            'forecast': 'trend',
            'classification': 'classification',
            'regression': 'trend',
            'anomaly': 'risk'
        }
        return type_mapping.get(model_type, 'trend')


class AnalysisTaskViewSet(viewsets.ModelViewSet):
    """分析任务ViewSet"""
    queryset = AnalysisTask.objects.all()
    serializer_class = AnalysisTaskSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按任务类型过滤
        task_type = self.request.query_params.get('task_type')
        if task_type:
            queryset = queryset.filter(task_type=task_type)

        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # 按模型过滤
        model_id = self.request.query_params.get('model')
        if model_id:
            queryset = queryset.filter(model_id=model_id)

        return queryset.order_by('-created_at')

    def get_serializer_class(self):
        if self.action == 'list':
            return AnalysisTaskListSerializer
        return AnalysisTaskSerializer

    def perform_create(self, serializer):
        task_id = f"TASK_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
        serializer.save(created_by=self.request.user, task_id=task_id)

    @action(detail=True, methods=['post'])
    def execute(self, request, pk=None):
        """执行任务"""
        task = self.get_object()

        if task.status != 'pending':
            return Response(
                {'error': '任务状态不允许执行'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 开始执行任务
        task.status = 'running'
        task.start_time = timezone.now()
        task.progress = 10
        task.save()

        try:
            # 这里可以实现具体的任务执行逻辑
            # 模拟任务执行过程
            execution_result = self._execute_analysis_task(task)

            # 更新任务状态
            task.status = 'completed'
            task.end_time = timezone.now()
            task.execution_time = (task.end_time - task.start_time).total_seconds()
            task.progress = 100
            task.result_data = execution_result
            task.save()

            return Response({
                'message': '任务执行完成',
                'execution_time': task.execution_time,
                'result': execution_result
            })

        except Exception as e:
            task.status = 'failed'
            task.error_message = str(e)
            task.end_time = timezone.now()
            task.save()

            return Response(
                {'error': f'任务执行失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """取消任务"""
        task = self.get_object()

        if task.status not in ['pending', 'running']:
            return Response(
                {'error': '任务状态不允许取消'},
                status=status.HTTP_400_BAD_REQUEST
            )

        task.status = 'cancelled'
        task.end_time = timezone.now()
        task.save()

        return Response({'message': '任务已取消'})

    def _execute_analysis_task(self, task):
        """执行分析任务"""
        model = task.model

        if model.model_type == 'trend':
            return self._execute_trend_analysis(task)
        elif model.model_type == 'anomaly':
            return self._execute_anomaly_detection(task)
        elif model.model_type == 'forecast':
            return self._execute_forecast_analysis(task)
        else:
            return {'message': '分析完成', 'type': model.model_type}

    def _execute_trend_analysis(self, task):
        """执行趋势分析"""
        # 模拟趋势分析结果
        trends = []
        for i in range(5):
            trends.append({
                'metric': f'指标{i+1}',
                'trend': random.choice(['上升', '下降', '平稳']),
                'change_rate': random.uniform(-0.3, 0.3),
                'significance': random.uniform(0.6, 0.95)
            })

        return {
            'analysis_type': 'trend',
            'trends': trends,
            'summary': '整体趋势向好，部分指标需要关注'
        }

    def _execute_anomaly_detection(self, task):
        """执行异常检测"""
        # 模拟异常检测结果
        anomalies = []
        for i in range(random.randint(0, 3)):
            anomaly_id = f"ANOM_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"

            # 创建异常记录
            anomaly = AnomalyDetection.objects.create(
                anomaly_id=anomaly_id,
                anomaly_type=random.choice(['statistical', 'pattern', 'trend', 'outlier']),
                task=task,
                model=task.model,
                data_source=task.model.data_sources.first(),
                affected_fields=[f'field_{i+1}'],
                anomaly_score=random.uniform(0.7, 0.95),
                severity=random.choice(['low', 'medium', 'high']),
                anomalous_data={'value': random.uniform(100, 1000)},
                expected_range={'min': 50, 'max': 500},
                deviation={'absolute': random.uniform(50, 200)},
                detection_time=timezone.now(),
                anomaly_start_time=timezone.now() - timedelta(hours=random.randint(1, 24))
            )

            anomalies.append({
                'anomaly_id': anomaly_id,
                'type': anomaly.get_anomaly_type_display(),
                'score': anomaly.anomaly_score,
                'severity': anomaly.get_severity_display()
            })

        return {
            'analysis_type': 'anomaly_detection',
            'anomalies_found': len(anomalies),
            'anomalies': anomalies,
            'summary': f'检测到 {len(anomalies)} 个异常'
        }

    def _execute_forecast_analysis(self, task):
        """执行预测分析"""
        # 模拟预测分析结果
        forecasts = []
        base_date = timezone.now().date()

        for i in range(7):  # 预测未来7天
            forecast_date = base_date + timedelta(days=i+1)
            forecasts.append({
                'date': forecast_date.strftime('%Y-%m-%d'),
                'predicted_value': random.uniform(1000, 5000),
                'confidence': random.uniform(0.7, 0.9),
                'trend': random.choice(['上升', '下降', '平稳'])
            })

        return {
            'analysis_type': 'forecast',
            'forecast_period': '7天',
            'forecasts': forecasts,
            'summary': '未来一周预测趋势整体稳定'
        }


class DataInsightViewSet(viewsets.ReadOnlyModelViewSet):
    """数据洞察ViewSet"""
    queryset = DataInsight.objects.all()
    serializer_class = DataInsightSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按洞察类型过滤
        insight_type = self.request.query_params.get('insight_type')
        if insight_type:
            queryset = queryset.filter(insight_type=insight_type)

        # 按严重程度过滤
        severity = self.request.query_params.get('severity')
        if severity:
            queryset = queryset.filter(severity=severity)

        # 只显示有效的洞察
        if self.request.query_params.get('valid_only') == 'true':
            now = timezone.now()
            queryset = queryset.filter(
                Q(valid_from__lte=now) &
                (Q(valid_until__isnull=True) | Q(valid_until__gte=now))
            )

        return queryset.order_by('-impact_score', '-created_at')

    @action(detail=True, methods=['post'])
    def acknowledge(self, request, pk=None):
        """确认洞察"""
        insight = self.get_object()

        insight.is_acknowledged = True
        insight.acknowledged_by = request.user
        insight.acknowledged_at = timezone.now()
        insight.save()

        return Response({'message': '洞察已确认'})


class PredictionResultViewSet(viewsets.ReadOnlyModelViewSet):
    """预测结果ViewSet"""
    queryset = PredictionResult.objects.all()
    serializer_class = PredictionResultSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按预测类型过滤
        prediction_type = self.request.query_params.get('prediction_type')
        if prediction_type:
            queryset = queryset.filter(prediction_type=prediction_type)

        # 按模型过滤
        model_id = self.request.query_params.get('model')
        if model_id:
            queryset = queryset.filter(model_id=model_id)

        # 按验证状态过滤
        is_validated = self.request.query_params.get('is_validated')
        if is_validated:
            queryset = queryset.filter(is_validated=is_validated.lower() == 'true')

        return queryset.order_by('-created_at')

    @action(detail=True, methods=['post'])
    def validate_prediction(self, request, pk=None):
        """验证预测结果"""
        prediction = self.get_object()
        actual_value = request.data.get('actual_value')

        if not actual_value:
            return Response(
                {'error': '请提供实际值'},
                status=status.HTTP_400_BAD_REQUEST
            )

        prediction.actual_value = actual_value
        prediction.is_validated = True
        prediction.validation_date = timezone.now()

        # 计算准确度
        if isinstance(prediction.predicted_value, (int, float)) and isinstance(actual_value, (int, float)):
            error = abs(prediction.predicted_value - actual_value)
            accuracy = max(0, 1 - error / max(abs(actual_value), 1))
            prediction.accuracy_score = accuracy

        prediction.save()

        return Response({
            'message': '预测结果已验证',
            'accuracy_score': prediction.accuracy_score
        })


class AnomalyDetectionViewSet(viewsets.ReadOnlyModelViewSet):
    """异常检测ViewSet"""
    queryset = AnomalyDetection.objects.all()
    serializer_class = AnomalyDetectionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按异常类型过滤
        anomaly_type = self.request.query_params.get('anomaly_type')
        if anomaly_type:
            queryset = queryset.filter(anomaly_type=anomaly_type)

        # 按严重程度过滤
        severity = self.request.query_params.get('severity')
        if severity:
            queryset = queryset.filter(severity=severity)

        # 按解决状态过滤
        is_resolved = self.request.query_params.get('is_resolved')
        if is_resolved:
            queryset = queryset.filter(is_resolved=is_resolved.lower() == 'true')

        return queryset.order_by('-anomaly_score', '-detection_time')

    @action(detail=True, methods=['post'])
    def resolve(self, request, pk=None):
        """解决异常"""
        anomaly = self.get_object()
        resolution_notes = request.data.get('resolution_notes', '')

        anomaly.is_resolved = True
        anomaly.resolution_notes = resolution_notes
        anomaly.resolved_by = request.user
        anomaly.resolved_at = timezone.now()
        anomaly.save()

        return Response({'message': '异常已解决'})


class SmartReportViewSet(viewsets.ModelViewSet):
    """智能报告ViewSet"""
    queryset = SmartReport.objects.all()
    serializer_class = SmartReportSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user

        # 根据权限过滤
        if not user.is_staff:
            queryset = queryset.filter(
                Q(is_public=True) |
                Q(created_by=user) |
                Q(allowed_users=user)
            ).distinct()

        # 按报告类型过滤
        report_type = self.request.query_params.get('report_type')
        if report_type:
            queryset = queryset.filter(report_type=report_type)

        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        return queryset.order_by('-created_at')

    def perform_create(self, serializer):
        report_id = f"RPT_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
        serializer.save(created_by=self.request.user, report_id=report_id)

    @action(detail=True, methods=['post'])
    def generate(self, request, pk=None):
        """生成报告"""
        report = self.get_object()

        if report.status != 'draft':
            return Response(
                {'error': '报告状态不允许生成'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 开始生成报告
        report.status = 'generating'
        report.generation_start_time = timezone.now()
        report.generation_progress = 10
        report.save()

        try:
            # 这里可以实现具体的报告生成逻辑
            # 模拟报告生成过程
            report_content = self._generate_report_content(report)

            # 更新报告状态
            report.status = 'completed'
            report.generation_end_time = timezone.now()
            report.generation_duration = (report.generation_end_time - report.generation_start_time).total_seconds()
            report.generation_progress = 100
            report.executive_summary = report_content['executive_summary']
            report.key_insights = report_content['key_insights']
            report.recommendations = report_content['recommendations']
            report.metrics_summary = report_content['metrics_summary']
            report.save()

            return Response({
                'message': '报告生成完成',
                'generation_duration': report.generation_duration,
                'file_path': report.report_file_path
            })

        except Exception as e:
            report.status = 'failed'
            report.generation_end_time = timezone.now()
            report.save()

            return Response(
                {'error': f'报告生成失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _generate_report_content(self, report):
        """生成报告内容"""
        # 模拟报告内容生成
        return {
            'executive_summary': f'本报告分析了{report.report_period_start.strftime("%Y-%m-%d")}至{report.report_period_end.strftime("%Y-%m-%d")}期间的数据，发现了多个重要趋势和洞察。',
            'key_insights': [
                '数据质量整体良好，准确率达到95%以上',
                '业务指标呈现稳定增长趋势',
                '发现3个需要关注的异常模式',
                '预测模型准确率达到90%以上'
            ],
            'recommendations': [
                '建议加强数据质量监控',
                '优化异常检测算法',
                '增加预测模型的训练频率',
                '建立自动化报告生成机制'
            ],
            'metrics_summary': {
                'total_records': random.randint(10000, 100000),
                'data_quality_score': random.uniform(0.9, 0.99),
                'anomalies_detected': random.randint(0, 10),
                'predictions_made': random.randint(50, 200)
            }
        }


class SmartAnalyticsConfigViewSet(viewsets.ModelViewSet):
    """智能分析配置ViewSet"""
    queryset = SmartAnalyticsConfig.objects.all()
    serializer_class = SmartAnalyticsConfigSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按配置类型过滤
        config_type = self.request.query_params.get('config_type')
        if config_type:
            queryset = queryset.filter(config_type=config_type)

        # 只显示启用的
        if self.request.query_params.get('active_only') == 'true':
            queryset = queryset.filter(is_active=True)

        return queryset.order_by('config_type', 'config_key')

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


# 业务操作视图
class SmartAnalyticsViewSet(viewsets.ViewSet):
    """智能分析业务操作ViewSet"""
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """智能分析仪表板"""
        # 数据源统计
        data_source_stats = SmartDataSource.objects.aggregate(
            total=Count('id'),
            active=Count('id', filter=Q(status='active')),
            synced_today=Count('id', filter=Q(last_sync_time__date=timezone.now().date()))
        )

        # 模型统计
        model_stats = AnalysisModel.objects.aggregate(
            total=Count('id'),
            active=Count('id', filter=Q(status='active')),
            avg_accuracy=Avg('accuracy')
        )

        # 任务统计
        task_stats = AnalysisTask.objects.aggregate(
            total=Count('id'),
            completed=Count('id', filter=Q(status='completed')),
            running=Count('id', filter=Q(status='running')),
            failed=Count('id', filter=Q(status='failed'))
        )

        # 洞察统计
        insight_stats = DataInsight.objects.aggregate(
            total=Count('id'),
            high_impact=Count('id', filter=Q(impact_score__gte=8)),
            acknowledged=Count('id', filter=Q(is_acknowledged=True))
        )

        # 异常统计
        anomaly_stats = AnomalyDetection.objects.aggregate(
            total=Count('id'),
            unresolved=Count('id', filter=Q(is_resolved=False)),
            critical=Count('id', filter=Q(severity='critical'))
        )

        # 预测统计
        prediction_stats = PredictionResult.objects.aggregate(
            total=Count('id'),
            validated=Count('id', filter=Q(is_validated=True)),
            avg_confidence=Avg('confidence_score')
        )

        # 最近7天的任务趋势
        seven_days_ago = timezone.now() - timedelta(days=7)
        daily_tasks = []
        for i in range(7):
            date = (timezone.now() - timedelta(days=i)).date()
            day_tasks = AnalysisTask.objects.filter(created_at__date=date)

            daily_tasks.insert(0, {
                'date': date.strftime('%Y-%m-%d'),
                'total': day_tasks.count(),
                'completed': day_tasks.filter(status='completed').count(),
                'failed': day_tasks.filter(status='failed').count()
            })

        # 模型性能排行
        top_models = AnalysisModel.objects.filter(
            status='active',
            accuracy__isnull=False
        ).order_by('-accuracy')[:5]

        model_performance = [
            {
                'name': model.name,
                'type': model.get_model_type_display(),
                'accuracy': model.accuracy,
                'prediction_count': model.prediction_count,
                'success_rate': model.get_success_rate()
            }
            for model in top_models
        ]

        return Response({
            'data_source_stats': data_source_stats,
            'model_stats': model_stats,
            'task_stats': task_stats,
            'insight_stats': insight_stats,
            'anomaly_stats': anomaly_stats,
            'prediction_stats': prediction_stats,
            'daily_tasks': daily_tasks,
            'model_performance': model_performance
        })

    @action(detail=False, methods=['post'])
    def batch_analysis(self, request):
        """批量分析"""
        serializer = ReportGenerationSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data

        # 创建批量分析任务
        batch_task = AnalysisTask.objects.create(
            task_id=f"BATCH_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}",
            name=f"批量分析 - {data['title']}",
            task_type='batch',
            model=AnalysisModel.objects.filter(status='active').first(),  # 使用第一个活跃模型
            input_data={
                'data_source_ids': data['data_source_ids'],
                'model_ids': data.get('model_ids', []),
                'period_start': data['period_start'].isoformat(),
                'period_end': data['period_end'].isoformat()
            },
            analysis_config={
                'include_insights': data.get('include_insights', True),
                'include_predictions': data.get('include_predictions', True)
            },
            created_by=request.user
        )

        # 创建智能报告
        report = SmartReport.objects.create(
            report_id=f"RPT_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}",
            title=data['title'],
            report_type=data['report_type'],
            report_period_start=data['period_start'],
            report_period_end=data['period_end'],
            created_by=request.user
        )

        # 关联数据源和模型
        report.data_sources.set(data['data_source_ids'])
        if data.get('model_ids'):
            report.analysis_models.set(data['model_ids'])

        return Response({
            'message': '批量分析任务已创建',
            'batch_task_id': batch_task.task_id,
            'report_id': report.report_id
        })

    @action(detail=False, methods=['post'])
    def anomaly_scan(self, request):
        """异常扫描"""
        serializer = AnomalyDetectionRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data
        model = get_object_or_404(AnalysisModel, id=data['model_id'])
        data_source = get_object_or_404(SmartDataSource, id=data['data_source_id'])

        # 创建异常检测任务
        task = AnalysisTask.objects.create(
            task_id=f"ANOM_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}",
            name=f"异常扫描 - {data_source.name}",
            task_type='manual',
            model=model,
            input_data={
                'data_source_id': data['data_source_id'],
                'sensitivity': data['sensitivity']
            },
            analysis_config=data.get('detection_config', {}),
            created_by=request.user
        )

        # 执行异常检测
        try:
            task.status = 'running'
            task.start_time = timezone.now()
            task.save()

            # 模拟异常检测
            anomalies_found = random.randint(0, 5)
            anomalies = []

            for i in range(anomalies_found):
                anomaly = AnomalyDetection.objects.create(
                    anomaly_id=f"ANOM_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}",
                    anomaly_type=random.choice(['statistical', 'pattern', 'trend', 'outlier']),
                    task=task,
                    model=model,
                    data_source=data_source,
                    affected_fields=[f'field_{i+1}'],
                    anomaly_score=random.uniform(0.7, 0.95),
                    severity=random.choice(['low', 'medium', 'high']),
                    anomalous_data={'value': random.uniform(100, 1000)},
                    expected_range={'min': 50, 'max': 500},
                    deviation={'absolute': random.uniform(50, 200)},
                    detection_time=timezone.now(),
                    anomaly_start_time=timezone.now() - timedelta(hours=random.randint(1, 24))
                )

                anomalies.append({
                    'anomaly_id': anomaly.anomaly_id,
                    'type': anomaly.get_anomaly_type_display(),
                    'score': anomaly.anomaly_score,
                    'severity': anomaly.get_severity_display()
                })

            # 更新任务状态
            task.status = 'completed'
            task.end_time = timezone.now()
            task.execution_time = (task.end_time - task.start_time).total_seconds()
            task.result_data = {
                'anomalies_found': anomalies_found,
                'anomalies': anomalies
            }
            task.save()

            return Response({
                'message': '异常扫描完成',
                'task_id': task.task_id,
                'anomalies_found': anomalies_found,
                'anomalies': anomalies
            })

        except Exception as e:
            task.status = 'failed'
            task.error_message = str(e)
            task.end_time = timezone.now()
            task.save()

            return Response(
                {'error': f'异常扫描失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

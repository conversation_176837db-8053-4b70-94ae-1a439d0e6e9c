from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.db.models import Count, Avg
import uuid
from .models import (
    SmartDataSource, AnalysisModel, AnalysisTask, DataInsight,
    PredictionResult, AnomalyDetection, SmartReport, SmartAnalyticsConfig
)


@admin.register(SmartDataSource)
class SmartDataSourceAdmin(admin.ModelAdmin):
    """数据源管理"""
    list_display = [
        'name', 'source_type_display', 'status_display', 'total_records',
        'sync_count', 'last_sync_status_display', 'updated_at'
    ]
    list_filter = ['source_type', 'status', 'last_sync_status', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['last_sync_time', 'total_records', 'sync_count', 'created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'source_type', 'description', 'status')
        }),
        ('连接配置', {
            'fields': ('connection_config', 'refresh_interval')
        }),
        ('数据配置', {
            'fields': ('data_schema', 'query_config')
        }),
        ('同步状态', {
            'fields': ('last_sync_time', 'last_sync_status', 'error_message')
        }),
        ('统计信息', {
            'fields': ('total_records', 'sync_count'),
            'classes': ('collapse',)
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['sync_data_sources', 'activate_sources', 'deactivate_sources']

    def source_type_display(self, obj):
        """数据源类型显示"""
        type_colors = {
            'database': '#1890ff',
            'api': '#52c41a',
            'file': '#faad14',
            'external': '#722ed1',
            'manual': '#13c2c2',
        }
        color = type_colors.get(obj.source_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_source_type_display()
        )
    source_type_display.short_description = '数据源类型'

    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'active': '#52c41a',
            'inactive': '#d9d9d9',
            'error': '#f5222d',
            'maintenance': '#faad14',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'

    def last_sync_status_display(self, obj):
        """最后同步状态显示"""
        if obj.last_sync_status:
            return format_html('<span style="color: #52c41a;">✓ 成功</span>')
        else:
            return format_html('<span style="color: #f5222d;">✗ 失败</span>')
    last_sync_status_display.short_description = '同步状态'

    def sync_data_sources(self, request, queryset):
        """同步数据源"""
        updated = 0
        for source in queryset:
            # 这里可以实现具体的同步逻辑
            source.last_sync_time = timezone.now()
            source.last_sync_status = True
            source.sync_count += 1
            source.save()
            updated += 1

        self.message_user(request, f"成功同步 {updated} 个数据源")
    sync_data_sources.short_description = "同步选中的数据源"

    def activate_sources(self, request, queryset):
        """激活数据源"""
        updated = queryset.update(status='active')
        self.message_user(request, f"成功激活 {updated} 个数据源")
    activate_sources.short_description = "激活选中的数据源"

    def deactivate_sources(self, request, queryset):
        """停用数据源"""
        updated = queryset.update(status='inactive')
        self.message_user(request, f"成功停用 {updated} 个数据源")
    deactivate_sources.short_description = "停用选中的数据源"

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(AnalysisModel)
class AnalysisModelAdmin(admin.ModelAdmin):
    """分析模型管理"""
    list_display = [
        'name', 'model_type_display', 'algorithm', 'status_display',
        'accuracy_display', 'success_rate_display', 'prediction_count', 'updated_at'
    ]
    list_filter = ['model_type', 'status', 'is_auto_retrain', 'created_at']
    search_fields = ['name', 'description', 'algorithm']
    readonly_fields = ['prediction_count', 'success_count', 'last_training_time', 'training_duration', 'created_at', 'updated_at']
    filter_horizontal = ['data_sources']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'model_type', 'description', 'algorithm')
        }),
        ('数据配置', {
            'fields': ('data_sources', 'target_fields', 'feature_fields')
        }),
        ('模型配置', {
            'fields': ('parameters', 'training_config')
        }),
        ('性能指标', {
            'fields': ('accuracy', 'precision', 'recall', 'f1_score', 'mse', 'mae')
        }),
        ('状态管理', {
            'fields': ('status', 'is_auto_retrain', 'retrain_interval')
        }),
        ('训练信息', {
            'fields': ('training_data_size', 'last_training_time', 'training_duration'),
            'classes': ('collapse',)
        }),
        ('使用统计', {
            'fields': ('prediction_count', 'success_count'),
            'classes': ('collapse',)
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['train_models', 'activate_models', 'deactivate_models']

    def model_type_display(self, obj):
        """模型类型显示"""
        type_colors = {
            'trend': '#1890ff',
            'forecast': '#52c41a',
            'anomaly': '#f5222d',
            'classification': '#faad14',
            'clustering': '#722ed1',
            'correlation': '#13c2c2',
            'regression': '#fa8c16',
            'time_series': '#eb2f96',
        }
        color = type_colors.get(obj.model_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_model_type_display()
        )
    model_type_display.short_description = '模型类型'

    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'draft': '#d9d9d9',
            'training': '#faad14',
            'active': '#52c41a',
            'inactive': '#8c8c8c',
            'error': '#f5222d',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'

    def accuracy_display(self, obj):
        """准确率显示"""
        if obj.accuracy:
            accuracy = obj.accuracy * 100
            if accuracy >= 90:
                color = '#52c41a'
            elif accuracy >= 80:
                color = '#faad14'
            else:
                color = '#f5222d'

            return format_html(
                '<span style="color: {}; font-weight: bold;">{}</span>',
                color, f"{accuracy:.1f}%"
            )
        return '-'
    accuracy_display.short_description = '准确率'

    def success_rate_display(self, obj):
        """成功率显示"""
        rate = obj.get_success_rate()
        if rate >= 95:
            color = '#52c41a'
        elif rate >= 90:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, f"{rate:.1f}%"
        )
    success_rate_display.short_description = '成功率'

    def train_models(self, request, queryset):
        """训练模型"""
        updated = 0
        for model in queryset:
            model.status = 'training'
            model.last_training_time = timezone.now()
            model.save()
            updated += 1

        self.message_user(request, f"开始训练 {updated} 个模型")
    train_models.short_description = "训练选中的模型"

    def activate_models(self, request, queryset):
        """激活模型"""
        updated = queryset.update(status='active')
        self.message_user(request, f"成功激活 {updated} 个模型")
    activate_models.short_description = "激活选中的模型"

    def deactivate_models(self, request, queryset):
        """停用模型"""
        updated = queryset.update(status='inactive')
        self.message_user(request, f"成功停用 {updated} 个模型")
    deactivate_models.short_description = "停用选中的模型"

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(AnalysisTask)
class AnalysisTaskAdmin(admin.ModelAdmin):
    """分析任务管理"""
    list_display = [
        'task_id', 'name', 'task_type_display', 'model_name', 'status_display',
        'priority_display', 'progress_display', 'created_at'
    ]
    list_filter = ['task_type', 'status', 'priority', 'created_at']
    search_fields = ['task_id', 'name', 'description']
    readonly_fields = ['task_id', 'start_time', 'end_time', 'execution_time', 'created_at', 'updated_at']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('基本信息', {
            'fields': ('task_id', 'name', 'task_type', 'description')
        }),
        ('分析配置', {
            'fields': ('model', 'input_data', 'analysis_config')
        }),
        ('执行配置', {
            'fields': ('priority', 'scheduled_time', 'timeout')
        }),
        ('状态信息', {
            'fields': ('status', 'progress')
        }),
        ('执行信息', {
            'fields': ('start_time', 'end_time', 'execution_time'),
            'classes': ('collapse',)
        }),
        ('结果信息', {
            'fields': ('result_data', 'result_files', 'insights'),
            'classes': ('collapse',)
        }),
        ('错误信息', {
            'fields': ('error_code', 'error_message'),
            'classes': ('collapse',)
        }),
        ('关联信息', {
            'fields': ('parent_task',),
            'classes': ('collapse',)
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['execute_tasks', 'cancel_tasks']

    def model_name(self, obj):
        """模型名称"""
        return obj.model.name
    model_name.short_description = '分析模型'

    def task_type_display(self, obj):
        """任务类型显示"""
        type_colors = {
            'scheduled': '#1890ff',
            'manual': '#52c41a',
            'triggered': '#faad14',
            'batch': '#722ed1',
        }
        color = type_colors.get(obj.task_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_task_type_display()
        )
    task_type_display.short_description = '任务类型'

    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'pending': '#d9d9d9',
            'running': '#faad14',
            'completed': '#52c41a',
            'failed': '#f5222d',
            'cancelled': '#8c8c8c',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'

    def priority_display(self, obj):
        """优先级显示"""
        priority_colors = {
            1: '#d9d9d9',
            2: '#1890ff',
            3: '#faad14',
            4: '#f5222d',
        }
        color = priority_colors.get(obj.priority, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_priority_display()
        )
    priority_display.short_description = '优先级'

    def progress_display(self, obj):
        """进度显示"""
        progress = obj.progress
        if progress >= 100:
            color = '#52c41a'
        elif progress >= 50:
            color = '#faad14'
        else:
            color = '#1890ff'

        return format_html(
            '<div style="width: 100px; background: #f0f0f0; border-radius: 3px;">'
            '<div style="width: {}%; background: {}; height: 20px; border-radius: 3px; text-align: center; color: white; font-size: 12px; line-height: 20px;">'
            '{}%</div></div>',
            progress, color, progress
        )
    progress_display.short_description = '进度'

    def execute_tasks(self, request, queryset):
        """执行任务"""
        updated = 0
        for task in queryset.filter(status='pending'):
            task.status = 'running'
            task.start_time = timezone.now()
            task.save()
            updated += 1

        self.message_user(request, f"开始执行 {updated} 个任务")
    execute_tasks.short_description = "执行选中的任务"

    def cancel_tasks(self, request, queryset):
        """取消任务"""
        updated = queryset.filter(status__in=['pending', 'running']).update(
            status='cancelled',
            end_time=timezone.now()
        )
        self.message_user(request, f"成功取消 {updated} 个任务")
    cancel_tasks.short_description = "取消选中的任务"

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
            if not obj.task_id:
                obj.task_id = f"TASK_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
        super().save_model(request, obj, form, change)


@admin.register(DataInsight)
class DataInsightAdmin(admin.ModelAdmin):
    """数据洞察管理"""
    list_display = [
        'title', 'insight_type_display', 'severity_display', 'confidence_display',
        'impact_score', 'is_acknowledged', 'created_at'
    ]
    list_filter = ['insight_type', 'severity', 'is_acknowledged', 'created_at']
    search_fields = ['title', 'description']
    readonly_fields = ['created_at', 'updated_at']
    filter_horizontal = ['data_sources']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('基本信息', {
            'fields': ('title', 'insight_type', 'description')
        }),
        ('关联信息', {
            'fields': ('task', 'model', 'data_sources')
        }),
        ('洞察内容', {
            'fields': ('key_findings', 'metrics', 'visualizations')
        }),
        ('重要性评估', {
            'fields': ('severity', 'confidence', 'impact_score')
        }),
        ('建议行动', {
            'fields': ('recommendations', 'action_items')
        }),
        ('状态管理', {
            'fields': ('is_acknowledged', 'acknowledged_by', 'acknowledged_at')
        }),
        ('有效期', {
            'fields': ('valid_from', 'valid_until')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['acknowledge_insights']

    def insight_type_display(self, obj):
        """洞察类型显示"""
        type_colors = {
            'trend': '#1890ff',
            'anomaly': '#f5222d',
            'pattern': '#52c41a',
            'correlation': '#faad14',
            'forecast': '#722ed1',
            'recommendation': '#13c2c2',
        }
        color = type_colors.get(obj.insight_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_insight_type_display()
        )
    insight_type_display.short_description = '洞察类型'

    def severity_display(self, obj):
        """严重程度显示"""
        severity_colors = {
            'low': '#52c41a',
            'medium': '#faad14',
            'high': '#fa8c16',
            'critical': '#f5222d',
        }
        color = severity_colors.get(obj.severity, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_severity_display()
        )
    severity_display.short_description = '严重程度'

    def confidence_display(self, obj):
        """置信度显示"""
        confidence = obj.confidence * 100
        if confidence >= 90:
            color = '#52c41a'
        elif confidence >= 70:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, f"{confidence:.1f}%"
        )
    confidence_display.short_description = '置信度'

    def acknowledge_insights(self, request, queryset):
        """确认洞察"""
        updated = queryset.update(
            is_acknowledged=True,
            acknowledged_by=request.user,
            acknowledged_at=timezone.now()
        )
        self.message_user(request, f"成功确认 {updated} 个洞察")
    acknowledge_insights.short_description = "确认选中的洞察"


@admin.register(PredictionResult)
class PredictionResultAdmin(admin.ModelAdmin):
    """预测结果管理"""
    list_display = [
        'prediction_id', 'prediction_type_display', 'model_name', 'confidence_score_display',
        'target_date', 'is_validated', 'accuracy_score_display', 'created_at'
    ]
    list_filter = ['prediction_type', 'is_validated', 'is_used', 'created_at']
    search_fields = ['prediction_id']
    readonly_fields = ['prediction_id', 'created_at', 'updated_at']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('基本信息', {
            'fields': ('prediction_id', 'prediction_type', 'task', 'model')
        }),
        ('输入数据', {
            'fields': ('input_features', 'input_timestamp')
        }),
        ('预测结果', {
            'fields': ('predicted_value', 'confidence_interval', 'probability_distribution')
        }),
        ('质量指标', {
            'fields': ('confidence_score', 'uncertainty')
        }),
        ('预测时间', {
            'fields': ('prediction_horizon', 'target_date')
        }),
        ('验证信息', {
            'fields': ('actual_value', 'is_validated', 'validation_date', 'accuracy_score'),
            'classes': ('collapse',)
        }),
        ('使用情况', {
            'fields': ('is_used', 'used_by', 'used_at'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def model_name(self, obj):
        """模型名称"""
        return obj.model.name
    model_name.short_description = '预测模型'

    def prediction_type_display(self, obj):
        """预测类型显示"""
        type_colors = {
            'revenue': '#52c41a',
            'customer': '#1890ff',
            'demand': '#faad14',
            'risk': '#f5222d',
            'trend': '#722ed1',
            'classification': '#13c2c2',
        }
        color = type_colors.get(obj.prediction_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_prediction_type_display()
        )
    prediction_type_display.short_description = '预测类型'

    def confidence_score_display(self, obj):
        """置信度分数显示"""
        score = obj.confidence_score * 100
        if score >= 90:
            color = '#52c41a'
        elif score >= 70:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, f"{score:.1f}%"
        )
    confidence_score_display.short_description = '置信度'

    def accuracy_score_display(self, obj):
        """准确度分数显示"""
        if obj.accuracy_score:
            score = obj.accuracy_score * 100
            if score >= 90:
                color = '#52c41a'
            elif score >= 70:
                color = '#faad14'
            else:
                color = '#f5222d'

            return format_html(
                '<span style="color: {}; font-weight: bold;">{}</span>',
                color, f"{score:.1f}%"
            )
        return '-'
    accuracy_score_display.short_description = '准确度'


@admin.register(AnomalyDetection)
class AnomalyDetectionAdmin(admin.ModelAdmin):
    """异常检测管理"""
    list_display = [
        'anomaly_id', 'anomaly_type_display', 'severity_display', 'anomaly_score_display',
        'detection_time', 'is_resolved', 'resolved_by'
    ]
    list_filter = ['anomaly_type', 'severity', 'is_resolved', 'detection_time']
    search_fields = ['anomaly_id', 'root_cause', 'business_impact']
    readonly_fields = ['anomaly_id', 'detection_time', 'created_at', 'updated_at']
    date_hierarchy = 'detection_time'

    fieldsets = (
        ('基本信息', {
            'fields': ('anomaly_id', 'anomaly_type', 'task', 'model', 'data_source')
        }),
        ('异常详情', {
            'fields': ('affected_fields', 'anomaly_score', 'severity')
        }),
        ('异常数据', {
            'fields': ('anomalous_data', 'expected_range', 'deviation')
        }),
        ('时间信息', {
            'fields': ('detection_time', 'anomaly_start_time', 'anomaly_end_time')
        }),
        ('处理状态', {
            'fields': ('is_resolved', 'resolution_notes', 'resolved_by', 'resolved_at')
        }),
        ('根因分析', {
            'fields': ('root_cause', 'contributing_factors'),
            'classes': ('collapse',)
        }),
        ('影响评估', {
            'fields': ('business_impact', 'financial_impact'),
            'classes': ('collapse',)
        }),
        ('创建信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['resolve_anomalies']

    def anomaly_type_display(self, obj):
        """异常类型显示"""
        type_colors = {
            'statistical': '#1890ff',
            'pattern': '#52c41a',
            'trend': '#faad14',
            'seasonal': '#722ed1',
            'outlier': '#f5222d',
            'drift': '#fa8c16',
        }
        color = type_colors.get(obj.anomaly_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_anomaly_type_display()
        )
    anomaly_type_display.short_description = '异常类型'

    def severity_display(self, obj):
        """严重程度显示"""
        severity_colors = {
            'low': '#52c41a',
            'medium': '#faad14',
            'high': '#fa8c16',
            'critical': '#f5222d',
        }
        color = severity_colors.get(obj.severity, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_severity_display()
        )
    severity_display.short_description = '严重程度'

    def anomaly_score_display(self, obj):
        """异常分数显示"""
        score = obj.anomaly_score * 100
        if score >= 80:
            color = '#f5222d'
        elif score >= 60:
            color = '#fa8c16'
        elif score >= 40:
            color = '#faad14'
        else:
            color = '#52c41a'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, f"{score:.1f}%"
        )
    anomaly_score_display.short_description = '异常分数'

    def resolve_anomalies(self, request, queryset):
        """解决异常"""
        updated = queryset.update(
            is_resolved=True,
            resolved_by=request.user,
            resolved_at=timezone.now()
        )
        self.message_user(request, f"成功解决 {updated} 个异常")
    resolve_anomalies.short_description = "解决选中的异常"


@admin.register(SmartReport)
class SmartReportAdmin(admin.ModelAdmin):
    """智能报告管理"""
    list_display = [
        'title', 'report_type_display', 'status_display', 'generation_progress_display',
        'view_count', 'download_count', 'created_at'
    ]
    list_filter = ['report_type', 'status', 'is_public', 'created_at']
    search_fields = ['title', 'description', 'report_id']
    readonly_fields = ['report_id', 'generation_start_time', 'generation_end_time', 'generation_duration', 'file_size', 'view_count', 'download_count', 'created_at', 'updated_at']
    filter_horizontal = ['data_sources', 'analysis_models', 'allowed_users']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('基本信息', {
            'fields': ('report_id', 'title', 'report_type', 'description')
        }),
        ('报告配置', {
            'fields': ('data_sources', 'analysis_models', 'report_config')
        }),
        ('报告内容', {
            'fields': ('executive_summary', 'key_insights', 'recommendations'),
            'classes': ('collapse',)
        }),
        ('数据分析', {
            'fields': ('metrics_summary', 'trend_analysis', 'comparative_analysis'),
            'classes': ('collapse',)
        }),
        ('可视化', {
            'fields': ('charts_config', 'tables_data'),
            'classes': ('collapse',)
        }),
        ('报告期间', {
            'fields': ('report_period_start', 'report_period_end')
        }),
        ('状态管理', {
            'fields': ('status', 'generation_progress')
        }),
        ('生成信息', {
            'fields': ('generation_start_time', 'generation_end_time', 'generation_duration'),
            'classes': ('collapse',)
        }),
        ('文件信息', {
            'fields': ('report_file_path', 'file_size'),
            'classes': ('collapse',)
        }),
        ('访问控制', {
            'fields': ('is_public', 'allowed_users')
        }),
        ('使用统计', {
            'fields': ('view_count', 'download_count'),
            'classes': ('collapse',)
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['generate_reports']

    def report_type_display(self, obj):
        """报告类型显示"""
        type_colors = {
            'daily': '#1890ff',
            'weekly': '#52c41a',
            'monthly': '#faad14',
            'quarterly': '#722ed1',
            'annual': '#fa8c16',
            'custom': '#13c2c2',
        }
        color = type_colors.get(obj.report_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_report_type_display()
        )
    report_type_display.short_description = '报告类型'

    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'draft': '#d9d9d9',
            'generating': '#faad14',
            'completed': '#52c41a',
            'failed': '#f5222d',
            'archived': '#8c8c8c',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'

    def generation_progress_display(self, obj):
        """生成进度显示"""
        progress = obj.generation_progress
        if progress >= 100:
            color = '#52c41a'
        elif progress >= 50:
            color = '#faad14'
        else:
            color = '#1890ff'

        return format_html(
            '<div style="width: 100px; background: #f0f0f0; border-radius: 3px;">'
            '<div style="width: {}%; background: {}; height: 20px; border-radius: 3px; text-align: center; color: white; font-size: 12px; line-height: 20px;">'
            '{}%</div></div>',
            progress, color, progress
        )
    generation_progress_display.short_description = '生成进度'

    def generate_reports(self, request, queryset):
        """生成报告"""
        updated = 0
        for report in queryset.filter(status='draft'):
            report.status = 'generating'
            report.generation_start_time = timezone.now()
            report.save()
            updated += 1

        self.message_user(request, f"开始生成 {updated} 个报告")
    generate_reports.short_description = "生成选中的报告"

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
            if not obj.report_id:
                obj.report_id = f"RPT_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
        super().save_model(request, obj, form, change)


@admin.register(SmartAnalyticsConfig)
class SmartAnalyticsConfigAdmin(admin.ModelAdmin):
    """智能分析配置管理"""
    list_display = [
        'config_key', 'config_type_display', 'is_system', 'is_active', 'updated_at'
    ]
    list_filter = ['config_type', 'is_system', 'is_active']
    search_fields = ['config_key', 'description']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('config_type', 'config_key', 'description')
        }),
        ('配置值', {
            'fields': ('config_value',)
        }),
        ('配置属性', {
            'fields': ('is_system', 'is_active')
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def config_type_display(self, obj):
        """配置类型显示"""
        type_colors = {
            'general': '#1890ff',
            'model': '#52c41a',
            'alert': '#f5222d',
            'report': '#faad14',
            'performance': '#722ed1',
        }
        color = type_colors.get(obj.config_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_config_type_display()
        )
    config_type_display.short_description = '配置类型'

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


# 自定义管理页面标题
admin.site.site_header = '代理记账管理系统'
admin.site.site_title = '智能数据分析助手'
admin.site.index_title = '智能数据分析助手'

from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import json

User = get_user_model()


class SmartDataSource(models.Model):
    """数据源"""
    SOURCE_TYPES = [
        ('database', '数据库'),
        ('api', 'API接口'),
        ('file', '文件'),
        ('external', '外部系统'),
        ('manual', '手动输入'),
    ]

    STATUS_CHOICES = [
        ('active', '活跃'),
        ('inactive', '停用'),
        ('error', '错误'),
        ('maintenance', '维护中'),
    ]

    name = models.CharField('数据源名称', max_length=100)
    source_type = models.CharField('数据源类型', max_length=20, choices=SOURCE_TYPES)
    description = models.TextField('描述', blank=True)

    # 连接配置
    connection_config = models.JSONField('连接配置', default=dict, help_text='数据源连接参数')
    refresh_interval = models.IntegerField('刷新间隔(分钟)', default=60)

    # 数据配置
    data_schema = models.JSONField('数据结构', default=dict, help_text='数据字段定义')
    query_config = models.JSONField('查询配置', default=dict, help_text='数据查询参数')

    # 状态信息
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='active')
    last_sync_time = models.DateTimeField('最后同步时间', null=True, blank=True)
    last_sync_status = models.BooleanField('最后同步状态', default=True)
    error_message = models.TextField('错误信息', blank=True)

    # 统计信息
    total_records = models.IntegerField('总记录数', default=0)
    sync_count = models.IntegerField('同步次数', default=0)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='smart_data_sources', verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '数据源'
        verbose_name_plural = '数据源'
        ordering = ['-created_at']

    def __str__(self):
        return self.name


class AnalysisModel(models.Model):
    """分析模型"""
    MODEL_TYPES = [
        ('trend', '趋势分析'),
        ('forecast', '预测分析'),
        ('anomaly', '异常检测'),
        ('classification', '分类分析'),
        ('clustering', '聚类分析'),
        ('correlation', '相关性分析'),
        ('regression', '回归分析'),
        ('time_series', '时间序列'),
    ]

    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('training', '训练中'),
        ('active', '活跃'),
        ('inactive', '停用'),
        ('error', '错误'),
    ]

    name = models.CharField('模型名称', max_length=100)
    model_type = models.CharField('模型类型', max_length=20, choices=MODEL_TYPES)
    description = models.TextField('模型描述', blank=True)

    # 数据配置
    data_sources = models.ManyToManyField(SmartDataSource, verbose_name='数据源')
    target_fields = models.JSONField('目标字段', default=list, help_text='分析目标字段')
    feature_fields = models.JSONField('特征字段', default=list, help_text='特征字段')

    # 模型配置
    algorithm = models.CharField('算法', max_length=50, blank=True)
    parameters = models.JSONField('模型参数', default=dict)
    training_config = models.JSONField('训练配置', default=dict)

    # 性能指标
    accuracy = models.FloatField('准确率', null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(1)])
    precision = models.FloatField('精确率', null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(1)])
    recall = models.FloatField('召回率', null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(1)])
    f1_score = models.FloatField('F1分数', null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(1)])
    mse = models.FloatField('均方误差', null=True, blank=True)
    mae = models.FloatField('平均绝对误差', null=True, blank=True)

    # 状态管理
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='draft')
    is_auto_retrain = models.BooleanField('自动重训练', default=False)
    retrain_interval = models.IntegerField('重训练间隔(天)', default=30)

    # 训练信息
    training_data_size = models.IntegerField('训练数据量', default=0)
    last_training_time = models.DateTimeField('最后训练时间', null=True, blank=True)
    training_duration = models.FloatField('训练时长(秒)', null=True, blank=True)

    # 使用统计
    prediction_count = models.IntegerField('预测次数', default=0)
    success_count = models.IntegerField('成功次数', default=0)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '分析模型'
        verbose_name_plural = '分析模型'
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def get_success_rate(self):
        """获取成功率"""
        if self.prediction_count == 0:
            return 0
        return (self.success_count / self.prediction_count) * 100


class AnalysisTask(models.Model):
    """分析任务"""
    TASK_TYPES = [
        ('scheduled', '定时任务'),
        ('manual', '手动执行'),
        ('triggered', '触发执行'),
        ('batch', '批量处理'),
    ]

    STATUS_CHOICES = [
        ('pending', '待执行'),
        ('running', '执行中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]

    PRIORITY_CHOICES = [
        (1, '低'),
        (2, '中'),
        (3, '高'),
        (4, '紧急'),
    ]

    task_id = models.CharField('任务ID', max_length=100, unique=True)
    name = models.CharField('任务名称', max_length=200)
    task_type = models.CharField('任务类型', max_length=20, choices=TASK_TYPES)
    description = models.TextField('任务描述', blank=True)

    # 分析配置
    model = models.ForeignKey(AnalysisModel, on_delete=models.CASCADE, verbose_name='分析模型')
    input_data = models.JSONField('输入数据', default=dict)
    analysis_config = models.JSONField('分析配置', default=dict)

    # 执行配置
    priority = models.IntegerField('优先级', choices=PRIORITY_CHOICES, default=2)
    scheduled_time = models.DateTimeField('计划执行时间', null=True, blank=True)
    timeout = models.IntegerField('超时时间(秒)', default=3600)

    # 状态信息
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    progress = models.IntegerField('进度百分比', default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])

    # 执行信息
    start_time = models.DateTimeField('开始时间', null=True, blank=True)
    end_time = models.DateTimeField('结束时间', null=True, blank=True)
    execution_time = models.FloatField('执行时长(秒)', null=True, blank=True)

    # 结果信息
    result_data = models.JSONField('结果数据', default=dict)
    result_files = models.JSONField('结果文件', default=list)
    insights = models.JSONField('分析洞察', default=list)

    # 错误信息
    error_code = models.CharField('错误代码', max_length=50, blank=True)
    error_message = models.TextField('错误信息', blank=True)

    # 关联信息
    parent_task = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name='父任务')

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '分析任务'
        verbose_name_plural = '分析任务'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.task_id})"

    def get_duration(self):
        """获取执行时长"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        elif self.start_time:
            return (timezone.now() - self.start_time).total_seconds()
        return 0


class DataInsight(models.Model):
    """数据洞察"""
    INSIGHT_TYPES = [
        ('trend', '趋势洞察'),
        ('anomaly', '异常洞察'),
        ('pattern', '模式洞察'),
        ('correlation', '关联洞察'),
        ('forecast', '预测洞察'),
        ('recommendation', '建议洞察'),
    ]

    SEVERITY_LEVELS = [
        ('low', '低'),
        ('medium', '中'),
        ('high', '高'),
        ('critical', '严重'),
    ]

    title = models.CharField('洞察标题', max_length=200)
    insight_type = models.CharField('洞察类型', max_length=20, choices=INSIGHT_TYPES)
    description = models.TextField('洞察描述')

    # 关联信息
    task = models.ForeignKey(AnalysisTask, on_delete=models.CASCADE, related_name='insights_detail', verbose_name='分析任务')
    model = models.ForeignKey(AnalysisModel, on_delete=models.CASCADE, verbose_name='分析模型')
    data_sources = models.ManyToManyField(SmartDataSource, verbose_name='相关数据源')

    # 洞察内容
    key_findings = models.JSONField('关键发现', default=list)
    metrics = models.JSONField('相关指标', default=dict)
    visualizations = models.JSONField('可视化配置', default=list)

    # 重要性评估
    severity = models.CharField('严重程度', max_length=20, choices=SEVERITY_LEVELS, default='medium')
    confidence = models.FloatField('置信度', validators=[MinValueValidator(0), MaxValueValidator(1)])
    impact_score = models.FloatField('影响分数', validators=[MinValueValidator(0), MaxValueValidator(10)])

    # 建议行动
    recommendations = models.JSONField('建议行动', default=list)
    action_items = models.JSONField('行动项', default=list)

    # 状态管理
    is_acknowledged = models.BooleanField('已确认', default=False)
    acknowledged_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='acknowledged_insights', verbose_name='确认人')
    acknowledged_at = models.DateTimeField('确认时间', null=True, blank=True)

    # 有效期
    valid_from = models.DateTimeField('有效开始时间')
    valid_until = models.DateTimeField('有效结束时间', null=True, blank=True)

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '数据洞察'
        verbose_name_plural = '数据洞察'
        ordering = ['-impact_score', '-created_at']

    def __str__(self):
        return self.title

    def is_valid(self):
        """检查洞察是否有效"""
        now = timezone.now()
        if self.valid_until:
            return self.valid_from <= now <= self.valid_until
        return self.valid_from <= now


class PredictionResult(models.Model):
    """预测结果"""
    PREDICTION_TYPES = [
        ('revenue', '收入预测'),
        ('customer', '客户预测'),
        ('demand', '需求预测'),
        ('risk', '风险预测'),
        ('trend', '趋势预测'),
        ('classification', '分类预测'),
    ]

    prediction_id = models.CharField('预测ID', max_length=100, unique=True)
    prediction_type = models.CharField('预测类型', max_length=20, choices=PREDICTION_TYPES)

    # 关联信息
    task = models.ForeignKey(AnalysisTask, on_delete=models.CASCADE, related_name='predictions', verbose_name='分析任务')
    model = models.ForeignKey(AnalysisModel, on_delete=models.CASCADE, verbose_name='预测模型')

    # 输入数据
    input_features = models.JSONField('输入特征', default=dict)
    input_timestamp = models.DateTimeField('输入时间戳')

    # 预测结果
    predicted_value = models.JSONField('预测值', default=dict)
    confidence_interval = models.JSONField('置信区间', default=dict)
    probability_distribution = models.JSONField('概率分布', default=dict)

    # 质量指标
    confidence_score = models.FloatField('置信度分数', validators=[MinValueValidator(0), MaxValueValidator(1)])
    uncertainty = models.FloatField('不确定性', validators=[MinValueValidator(0), MaxValueValidator(1)])

    # 预测时间范围
    prediction_horizon = models.IntegerField('预测时间范围(天)', default=1)
    target_date = models.DateTimeField('目标日期')

    # 验证信息
    actual_value = models.JSONField('实际值', default=dict, blank=True)
    is_validated = models.BooleanField('已验证', default=False)
    validation_date = models.DateTimeField('验证日期', null=True, blank=True)
    accuracy_score = models.FloatField('准确度分数', null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(1)])

    # 使用情况
    is_used = models.BooleanField('已使用', default=False)
    used_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='used_predictions', verbose_name='使用人')
    used_at = models.DateTimeField('使用时间', null=True, blank=True)

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '预测结果'
        verbose_name_plural = '预测结果'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_prediction_type_display()} - {self.prediction_id}"


class AnomalyDetection(models.Model):
    """异常检测"""
    ANOMALY_TYPES = [
        ('statistical', '统计异常'),
        ('pattern', '模式异常'),
        ('trend', '趋势异常'),
        ('seasonal', '季节性异常'),
        ('outlier', '离群值'),
        ('drift', '数据漂移'),
    ]

    SEVERITY_LEVELS = [
        ('low', '低'),
        ('medium', '中'),
        ('high', '高'),
        ('critical', '严重'),
    ]

    anomaly_id = models.CharField('异常ID', max_length=100, unique=True)
    anomaly_type = models.CharField('异常类型', max_length=20, choices=ANOMALY_TYPES)

    # 关联信息
    task = models.ForeignKey(AnalysisTask, on_delete=models.CASCADE, related_name='anomalies', verbose_name='分析任务')
    model = models.ForeignKey(AnalysisModel, on_delete=models.CASCADE, verbose_name='检测模型')
    data_source = models.ForeignKey(SmartDataSource, on_delete=models.CASCADE, verbose_name='数据源')

    # 异常详情
    affected_fields = models.JSONField('受影响字段', default=list)
    anomaly_score = models.FloatField('异常分数', validators=[MinValueValidator(0), MaxValueValidator(1)])
    severity = models.CharField('严重程度', max_length=20, choices=SEVERITY_LEVELS)

    # 异常数据
    anomalous_data = models.JSONField('异常数据', default=dict)
    expected_range = models.JSONField('期望范围', default=dict)
    deviation = models.JSONField('偏差信息', default=dict)

    # 时间信息
    detection_time = models.DateTimeField('检测时间')
    anomaly_start_time = models.DateTimeField('异常开始时间')
    anomaly_end_time = models.DateTimeField('异常结束时间', null=True, blank=True)

    # 处理状态
    is_resolved = models.BooleanField('已解决', default=False)
    resolution_notes = models.TextField('解决说明', blank=True)
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_anomalies', verbose_name='解决人')
    resolved_at = models.DateTimeField('解决时间', null=True, blank=True)

    # 根因分析
    root_cause = models.TextField('根本原因', blank=True)
    contributing_factors = models.JSONField('影响因素', default=list)

    # 影响评估
    business_impact = models.TextField('业务影响', blank=True)
    financial_impact = models.DecimalField('财务影响', max_digits=15, decimal_places=2, null=True, blank=True)

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '异常检测'
        verbose_name_plural = '异常检测'
        ordering = ['-anomaly_score', '-detection_time']

    def __str__(self):
        return f"{self.get_anomaly_type_display()} - {self.anomaly_id}"


class SmartReport(models.Model):
    """智能报告"""
    REPORT_TYPES = [
        ('daily', '日报'),
        ('weekly', '周报'),
        ('monthly', '月报'),
        ('quarterly', '季报'),
        ('annual', '年报'),
        ('custom', '自定义'),
    ]

    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('generating', '生成中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('archived', '已归档'),
    ]

    report_id = models.CharField('报告ID', max_length=100, unique=True)
    title = models.CharField('报告标题', max_length=200)
    report_type = models.CharField('报告类型', max_length=20, choices=REPORT_TYPES)
    description = models.TextField('报告描述', blank=True)

    # 报告配置
    data_sources = models.ManyToManyField(SmartDataSource, verbose_name='数据源')
    analysis_models = models.ManyToManyField(AnalysisModel, verbose_name='分析模型')
    report_config = models.JSONField('报告配置', default=dict)

    # 报告内容
    executive_summary = models.TextField('执行摘要', blank=True)
    key_insights = models.JSONField('关键洞察', default=list)
    recommendations = models.JSONField('建议', default=list)

    # 数据分析
    metrics_summary = models.JSONField('指标摘要', default=dict)
    trend_analysis = models.JSONField('趋势分析', default=dict)
    comparative_analysis = models.JSONField('对比分析', default=dict)

    # 可视化
    charts_config = models.JSONField('图表配置', default=list)
    tables_data = models.JSONField('表格数据', default=list)

    # 报告期间
    report_period_start = models.DateTimeField('报告期间开始')
    report_period_end = models.DateTimeField('报告期间结束')

    # 状态管理
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='draft')
    generation_progress = models.IntegerField('生成进度', default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])

    # 生成信息
    generation_start_time = models.DateTimeField('生成开始时间', null=True, blank=True)
    generation_end_time = models.DateTimeField('生成结束时间', null=True, blank=True)
    generation_duration = models.FloatField('生成时长(秒)', null=True, blank=True)

    # 文件信息
    report_file_path = models.CharField('报告文件路径', max_length=500, blank=True)
    file_size = models.IntegerField('文件大小(字节)', default=0)

    # 访问控制
    is_public = models.BooleanField('公开报告', default=False)
    allowed_users = models.ManyToManyField(User, blank=True, related_name='accessible_smart_reports', verbose_name='授权用户')

    # 使用统计
    view_count = models.IntegerField('查看次数', default=0)
    download_count = models.IntegerField('下载次数', default=0)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '智能报告'
        verbose_name_plural = '智能报告'
        ordering = ['-created_at']

    def __str__(self):
        return self.title


class SmartAnalyticsConfig(models.Model):
    """智能分析配置"""
    CONFIG_TYPES = [
        ('general', '通用配置'),
        ('model', '模型配置'),
        ('alert', '告警配置'),
        ('report', '报告配置'),
        ('performance', '性能配置'),
    ]

    config_type = models.CharField('配置类型', max_length=20, choices=CONFIG_TYPES)
    config_key = models.CharField('配置键', max_length=100)
    config_value = models.JSONField('配置值', default=dict)
    description = models.TextField('配置描述', blank=True)

    # 配置属性
    is_system = models.BooleanField('系统配置', default=False)
    is_active = models.BooleanField('是否启用', default=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '智能分析配置'
        verbose_name_plural = '智能分析配置'
        unique_together = ['config_type', 'config_key']
        ordering = ['config_type', 'config_key']

    def __str__(self):
        return f"{self.get_config_type_display()} - {self.config_key}"

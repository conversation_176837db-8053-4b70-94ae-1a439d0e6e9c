from django.db import models
from django.conf import settings


class Message(models.Model):
    """系统消息"""
    MESSAGE_TYPE_CHOICES = [
        ('system', '系统消息'),
        ('order', '订单消息'),
        ('finance', '财务消息'),
        ('service', '服务消息'),
        ('promotion', '推广消息'),
    ]
    
    PRIORITY_CHOICES = [
        ('low', '低'),
        ('normal', '普通'),
        ('high', '高'),
        ('urgent', '紧急'),
    ]
    
    # 基本信息
    title = models.CharField('消息标题', max_length=200)
    content = models.TextField('消息内容')
    message_type = models.CharField('消息类型', max_length=20, choices=MESSAGE_TYPE_CHOICES, default='system')
    priority = models.CharField('优先级', max_length=20, choices=PRIORITY_CHOICES, default='normal')
    
    # 发送对象
    sender = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, 
                              blank=True, null=True, related_name='sent_messages', verbose_name='发送者')
    recipients = models.ManyToManyField(settings.AUTH_USER_MODEL, through='MessageRecipient', 
                                       related_name='received_messages', verbose_name='接收者')
    
    # 发送范围
    is_broadcast = models.BooleanField('是否群发', default=False)
    target_user_types = models.JSONField('目标用户类型', default=list, blank=True)
    
    # 附加信息
    extra_data = models.JSONField('附加数据', default=dict, blank=True)
    action_url = models.URLField('操作链接', blank=True)
    
    # 状态
    is_active = models.BooleanField('是否有效', default=True)
    scheduled_time = models.DateTimeField('定时发送时间', blank=True, null=True)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'messages'
        verbose_name = '系统消息'
        verbose_name_plural = '消息管理'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.title


class MessageRecipient(models.Model):
    """消息接收者"""
    STATUS_CHOICES = [
        ('unread', '未读'),
        ('read', '已读'),
        ('deleted', '已删除'),
    ]
    
    message = models.ForeignKey(Message, on_delete=models.CASCADE, verbose_name='消息')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name='接收用户')
    
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='unread')
    read_at = models.DateTimeField('阅读时间', blank=True, null=True)
    deleted_at = models.DateTimeField('删除时间', blank=True, null=True)
    
    created_at = models.DateTimeField('接收时间', auto_now_add=True)
    
    class Meta:
        db_table = 'message_recipients'
        verbose_name = '消息接收者'
        verbose_name_plural = '消息接收者'
        unique_together = ['message', 'user']
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.user.phone} - {self.message.title}"


class Notification(models.Model):
    """推送通知"""
    NOTIFICATION_TYPE_CHOICES = [
        ('wechat_template', '微信模板消息'),
        ('sms', '短信通知'),
        ('email', '邮件通知'),
        ('push', '推送通知'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '待发送'),
        ('sending', '发送中'),
        ('sent', '已发送'),
        ('failed', '发送失败'),
    ]
    
    # 基本信息
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, 
                            related_name='notifications', verbose_name='接收用户')
    notification_type = models.CharField('通知类型', max_length=20, choices=NOTIFICATION_TYPE_CHOICES)
    
    # 通知内容
    title = models.CharField('标题', max_length=200, blank=True)
    content = models.TextField('内容')
    template_id = models.CharField('模板ID', max_length=100, blank=True)
    template_data = models.JSONField('模板数据', default=dict, blank=True)
    
    # 发送信息
    recipient = models.CharField('接收方', max_length=200)  # 手机号、邮箱、openid等
    status = models.CharField('发送状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # 发送结果
    sent_at = models.DateTimeField('发送时间', blank=True, null=True)
    response_data = models.JSONField('响应数据', default=dict, blank=True)
    error_message = models.TextField('错误信息', blank=True)
    
    # 重试信息
    retry_count = models.PositiveIntegerField('重试次数', default=0)
    max_retry = models.PositiveIntegerField('最大重试次数', default=3)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'notifications'
        verbose_name = '推送通知'
        verbose_name_plural = '推送通知'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.get_notification_type_display()} - {self.user.phone}"

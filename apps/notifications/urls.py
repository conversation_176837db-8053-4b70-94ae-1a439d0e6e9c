from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'messages', views.MessageViewSet, basename='message')
router.register(r'notifications', views.NotificationViewSet, basename='notification')

urlpatterns = [
    # 消息管理
    path('list/', views.MessageListView.as_view(), name='message_list'),
    path('<int:pk>/read/', views.MarkMessageReadView.as_view(), name='mark_message_read'),
    path('unread-count/', views.UnreadCountView.as_view(), name='unread_count'),

    # 通知管理
    path('notifications/send/', views.SendNotificationView.as_view(), name='send_notification'),
    path('list/', views.get_notification_list, name='notification_list'),
    path('mark-read/', views.mark_notification_read, name='mark_notification_read'),

    # 其他路由
    path('', include(router.urls)),
]

from rest_framework import serializers
from .models import Message, MessageRecipient, Notification


class MessageSerializer(serializers.ModelSerializer):
    """消息序列化器"""
    
    class Meta:
        model = Message
        fields = ['id', 'title', 'content', 'message_type', 'is_read', 'created_at']
        read_only_fields = ['id', 'created_at']


class MessageRecipientSerializer(serializers.ModelSerializer):
    """消息接收者序列化器"""
    message = MessageSerializer(read_only=True)
    
    class Meta:
        model = MessageRecipient
        fields = ['id', 'message', 'user', 'is_read', 'read_at', 'created_at']
        read_only_fields = ['id', 'created_at']


class NotificationSerializer(serializers.ModelSerializer):
    """通知序列化器"""
    
    class Meta:
        model = Notification
        fields = ['id', 'user', 'title', 'content', 'notification_type', 
                 'is_read', 'read_at', 'created_at']
        read_only_fields = ['id', 'created_at']

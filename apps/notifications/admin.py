from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import Message, MessageRecipient, Notification


@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    """系统消息管理"""
    list_display = ['title', 'message_type', 'priority', 'sender_info', 'recipient_count', 
                   'is_broadcast', 'is_active', 'scheduled_time', 'created_at']
    list_filter = ['message_type', 'priority', 'is_broadcast', 'is_active', 'created_at']
    search_fields = ['title', 'content', 'sender__phone', 'sender__nickname']
    # filter_horizontal = ['recipients']  # 由于使用through模型，不能使用filter_horizontal
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('title', 'content', 'message_type', 'priority', 'sender')
        }),
        ('发送设置', {
            'fields': ('is_broadcast', 'target_user_types')
        }),
        ('附加信息', {
            'fields': ('extra_data', 'action_url')
        }),
        ('状态设置', {
            'fields': ('is_active', 'scheduled_time')
        })
    )
    
    def sender_info(self, obj):
        if obj.sender:
            return format_html(
                '<a href="{}">{}</a>',
                reverse('admin:users_user_change', args=[obj.sender.pk]),
                obj.sender.nickname or obj.sender.phone
            )
        return '系统'
    sender_info.short_description = '发送者'
    
    def recipient_count(self, obj):
        return obj.recipients.count()
    recipient_count.short_description = '接收人数'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('sender').prefetch_related('recipients')


@admin.register(MessageRecipient)
class MessageRecipientAdmin(admin.ModelAdmin):
    """消息接收记录管理"""
    list_display = ['message_title', 'user_info', 'status', 'read_at', 'created_at']
    list_filter = ['status', 'read_at', 'created_at']
    search_fields = ['message__title', 'user__phone', 'user__nickname']
    readonly_fields = ['created_at']
    
    def message_title(self, obj):
        return obj.message.title
    message_title.short_description = '消息标题'
    
    def user_info(self, obj):
        if obj.user:
            return format_html(
                '<a href="{}">{}</a><br/><small>{}</small>',
                reverse('admin:users_user_change', args=[obj.user.pk]),
                obj.user.nickname or obj.user.phone,
                obj.user.phone
            )
        return '-'
    user_info.short_description = '用户'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('message', 'user')


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    """推送通知管理"""
    list_display = ['user_info', 'notification_type', 'title', 'recipient', 'status', 'sent_at', 'created_at']
    list_filter = ['notification_type', 'status', 'sent_at', 'created_at']
    search_fields = ['title', 'content', 'recipient', 'user__phone', 'user__nickname']
    readonly_fields = ['sent_at', 'response_data', 'created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'notification_type', 'recipient')
        }),
        ('通知内容', {
            'fields': ('title', 'content', 'template_id', 'template_data')
        }),
        ('发送状态', {
            'fields': ('status', 'sent_at', 'error_message')
        }),
        ('重试设置', {
            'fields': ('retry_count', 'max_retry')
        }),
        ('响应数据', {
            'fields': ('response_data',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def user_info(self, obj):
        if obj.user:
            return format_html(
                '<a href="{}">{}</a><br/><small>{}</small>',
                reverse('admin:users_user_change', args=[obj.user.pk]),
                obj.user.nickname or obj.user.phone,
                obj.user.phone
            )
        return '-'
    user_info.short_description = '用户'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')

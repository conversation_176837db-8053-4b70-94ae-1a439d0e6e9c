from rest_framework import viewsets, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from .models import Message, MessageRecipient, Notification
from .serializers import MessageSerializer, MessageRecipientSerializer, NotificationSerializer
from utils.response import APIResponse
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class MessageViewSet(viewsets.ModelViewSet):
    """消息管理ViewSet"""
    queryset = Message.objects.all()
    serializer_class = MessageSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        if self.request.user.is_staff:
            return Message.objects.all()
        return Message.objects.filter(recipients=self.request.user)


class NotificationViewSet(viewsets.ModelViewSet):
    """通知管理ViewSet"""
    queryset = Notification.objects.all()
    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        if self.request.user.is_staff:
            return Notification.objects.all()
        return Notification.objects.filter(user=self.request.user)


class MessageListView(APIView):
    """消息列表"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        return APIResponse.success(data=[])


class MarkMessageReadView(APIView):
    """标记消息已读"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, pk):
        return APIResponse.success(message='消息已标记为已读')


class UnreadCountView(APIView):
    """未读消息数量"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        return APIResponse.success(data={'count': 0})


class SendNotificationView(APIView):
    """发送通知"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        try:
            user = request.user
            title = request.data.get('title', '')
            message = request.data.get('message', '')
            notification_type = request.data.get('type', 'info')

            if not title or not message:
                return APIResponse.error(message='标题和消息内容不能为空')

            # 创建通知记录
            notification = Notification.objects.create(
                user=user,
                title=title,
                message=message,
                notification_type=notification_type,
                is_read=False
            )

            # 通过WebSocket发送实时通知
            try:
                from channels.layers import get_channel_layer
                from asgiref.sync import async_to_sync

                channel_layer = get_channel_layer()
                if channel_layer:
                    async_to_sync(channel_layer.group_send)(
                        f"notifications_{user.id}",
                        {
                            'type': 'notification_message',
                            'title': title,
                            'message': message,
                            'notification_type': notification_type,
                            'timestamp': notification.created_at.isoformat()
                        }
                    )
            except Exception as ws_error:
                # WebSocket发送失败不影响通知创建
                print(f"WebSocket通知发送失败: {ws_error}")

            return APIResponse.success(
                message='通知发送成功',
                data={
                    'id': notification.id,
                    'title': notification.title,
                    'message': notification.message,
                    'type': notification.notification_type,
                    'created_at': notification.created_at.isoformat()
                }
            )

        except Exception as e:
            return APIResponse.error(message=f'发送通知失败: {str(e)}')


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_notification_list(request):
    """
    获取用户通知列表
    """
    try:
        user = request.user
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))

        # 获取通知列表
        notifications = Notification.objects.filter(
            user=user
        ).order_by('-created_at')

        # 分页
        start = (page - 1) * page_size
        end = start + page_size
        notification_list = notifications[start:end]

        # 序列化数据
        data = []
        for notification in notification_list:
            data.append({
                'id': notification.id,
                'title': notification.title,
                'message': notification.message,
                'type': notification.notification_type,
                'is_read': notification.is_read,
                'created_at': notification.created_at.isoformat()
            })

        return APIResponse.success(data={
            'notifications': data,
            'total': notifications.count(),
            'page': page,
            'page_size': page_size,
            'has_more': end < notifications.count()
        })

    except Exception as e:
        return APIResponse.error(message=f'获取通知列表失败: {str(e)}')


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_notification_read(request):
    """
    标记通知为已读
    """
    try:
        user = request.user
        notification_id = request.data.get('notification_id')

        if notification_id:
            # 标记单个通知为已读
            Notification.objects.filter(
                id=notification_id,
                user=user
            ).update(is_read=True)
        else:
            # 标记所有通知为已读
            Notification.objects.filter(
                user=user,
                is_read=False
            ).update(is_read=True)

        return APIResponse.success(message='标记成功')

    except Exception as e:
        return APIResponse.error(message=f'标记失败: {str(e)}')

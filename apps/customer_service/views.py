from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q, Count, Avg, Sum
from django.shortcuts import get_object_or_404
from datetime import datetime, timedelta, date
from .models import (
    ServiceCategory, ServiceTicket, TicketMessage, KnowledgeCategory,
    KnowledgeArticle, QuickReply, ServiceAgent, ServiceMetrics, CustomerFeedback
)
from .serializers import (
    ServiceCategorySerializer, ServiceTicketSerializer, TicketMessageSerializer,
    KnowledgeCategorySerializer, KnowledgeArticleSerializer, QuickReplySerializer,
    ServiceAgentSerializer, ServiceMetricsSerializer, CustomerFeedbackSerializer,
    ServiceTicketListSerializer, KnowledgeArticleListSerializer,
    ServiceTicketCreateSerializer, TicketMessageCreateSerializer
)
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class ServiceCategoryViewSet(viewsets.ModelViewSet):
    """客服分类ViewSet"""
    queryset = ServiceCategory.objects.all()
    serializer_class = ServiceCategorySerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset.filter(is_active=True).order_by('sort_order', 'name')


class ServiceTicketViewSet(viewsets.ModelViewSet):
    """客服工单ViewSet"""
    queryset = ServiceTicket.objects.all()
    serializer_class = ServiceTicketSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user
        
        # 根据用户权限过滤
        if not user.is_staff:
            # 普通用户只能看到自己的工单
            queryset = queryset.filter(customer=user)
        else:
            # 客服可以看到分配给自己的工单
            assigned_to_me = self.request.query_params.get('assigned_to_me')
            if assigned_to_me == 'true':
                queryset = queryset.filter(assigned_to=user)
        
        # 状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # 优先级过滤
        priority = self.request.query_params.get('priority')
        if priority:
            queryset = queryset.filter(priority=priority)
        
        # 分类过滤
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category_id=category)
        
        # 日期范围过滤
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')
        if date_from:
            queryset = queryset.filter(created_at__gte=date_from)
        if date_to:
            queryset = queryset.filter(created_at__lte=date_to)
        
        # 超时工单过滤
        overdue = self.request.query_params.get('overdue')
        if overdue == 'true':
            # 这里需要在数据库层面实现超时逻辑
            pass
        
        return queryset.order_by('-created_at')
    
    def get_serializer_class(self):
        if self.action == 'list':
            return ServiceTicketListSerializer
        elif self.action == 'create':
            return ServiceTicketCreateSerializer
        return ServiceTicketSerializer
    
    @action(detail=True, methods=['post'])
    def assign(self, request, pk=None):
        """分配工单"""
        ticket = self.get_object()
        assigned_to_id = request.data.get('assigned_to')
        
        if assigned_to_id:
            try:
                from django.contrib.auth import get_user_model
                User = get_user_model()
                assigned_to = User.objects.get(id=assigned_to_id)
                
                ticket.assigned_to = assigned_to
                ticket.assigned_at = timezone.now()
                ticket.status = 'assigned'
                ticket.save()
                
                return Response({'message': '工单分配成功'})
            except User.DoesNotExist:
                return Response(
                    {'error': '指定的用户不存在'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:
            return Response(
                {'error': '请指定分配的用户'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        """更新工单状态"""
        ticket = self.get_object()
        new_status = request.data.get('status')
        
        if new_status not in dict(ServiceTicket.STATUS_CHOICES):
            return Response(
                {'error': '无效的状态'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        old_status = ticket.status
        ticket.status = new_status
        
        # 更新相关时间字段
        if new_status == 'resolved' and old_status != 'resolved':
            ticket.resolved_at = timezone.now()
        elif new_status == 'closed' and old_status != 'closed':
            ticket.closed_at = timezone.now()
        
        ticket.save()
        
        return Response({'message': '状态更新成功'})
    
    @action(detail=True, methods=['post'])
    def add_message(self, request, pk=None):
        """添加工单消息"""
        ticket = self.get_object()
        
        message_data = {
            'ticket': ticket.id,
            'sender': request.user.id,
            **request.data
        }
        
        serializer = TicketMessageCreateSerializer(data=message_data)
        if serializer.is_valid():
            message = serializer.save()
            
            # 更新首次响应时间
            if not ticket.first_response_at and message.message_type == 'agent':
                ticket.first_response_at = timezone.now()
                ticket.save()
            
            return Response(TicketMessageSerializer(message).data)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """工单仪表板"""
        user = request.user
        
        # 基础统计
        if user.is_staff:
            # 客服看到的统计
            total_tickets = ServiceTicket.objects.count()
            my_tickets = ServiceTicket.objects.filter(assigned_to=user).count()
            pending_tickets = ServiceTicket.objects.filter(
                assigned_to=user,
                status__in=['assigned', 'in_progress']
            ).count()
        else:
            # 客户看到的统计
            total_tickets = ServiceTicket.objects.filter(customer=user).count()
            my_tickets = total_tickets
            pending_tickets = ServiceTicket.objects.filter(
                customer=user,
                status__in=['open', 'assigned', 'in_progress']
            ).count()
        
        # 今日统计
        today = timezone.now().date()
        today_tickets = ServiceTicket.objects.filter(created_at__date=today).count()
        
        # 超时工单
        overdue_tickets = 0
        for ticket in ServiceTicket.objects.filter(status__in=['open', 'assigned', 'in_progress']):
            if ticket.is_overdue():
                overdue_tickets += 1
        
        return Response({
            'total_tickets': total_tickets,
            'my_tickets': my_tickets,
            'pending_tickets': pending_tickets,
            'today_tickets': today_tickets,
            'overdue_tickets': overdue_tickets,
        })
    
    @action(detail=False, methods=['post'])
    def auto_assign(self, request):
        """自动分配工单"""
        # 获取未分配的工单
        unassigned_tickets = ServiceTicket.objects.filter(
            status='open',
            assigned_to__isnull=True
        )
        
        # 获取可用的客服
        available_agents = ServiceAgent.objects.filter(
            status='online',
            auto_assign=True
        )
        
        assigned_count = 0
        
        for ticket in unassigned_tickets:
            # 找到工作负载最少的客服
            best_agent = None
            min_workload = float('inf')
            
            for agent in available_agents:
                if agent.is_available():
                    workload = agent.get_current_workload()
                    if workload < min_workload:
                        min_workload = workload
                        best_agent = agent
            
            if best_agent:
                ticket.assigned_to = best_agent.user
                ticket.assigned_at = timezone.now()
                ticket.status = 'assigned'
                ticket.save()
                assigned_count += 1
        
        return Response({
            'message': f'成功自动分配 {assigned_count} 个工单'
        })


class TicketMessageViewSet(viewsets.ModelViewSet):
    """工单消息ViewSet"""
    queryset = TicketMessage.objects.all()
    serializer_class = TicketMessageSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user
        
        # 只能看到自己相关的消息
        if not user.is_staff:
            queryset = queryset.filter(ticket__customer=user)
        
        ticket_id = self.request.query_params.get('ticket')
        if ticket_id:
            queryset = queryset.filter(ticket_id=ticket_id)
        
        return queryset.order_by('created_at')
    
    @action(detail=True, methods=['post'])
    def mark_read(self, request, pk=None):
        """标记消息为已读"""
        message = self.get_object()
        message.is_read = True
        message.read_at = timezone.now()
        message.save()
        
        return Response({'message': '消息已标记为已读'})


class KnowledgeCategoryViewSet(viewsets.ModelViewSet):
    """知识库分类ViewSet"""
    queryset = KnowledgeCategory.objects.all()
    serializer_class = KnowledgeCategorySerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset.filter(is_active=True).order_by('sort_order', 'name')


class KnowledgeArticleViewSet(viewsets.ModelViewSet):
    """知识库文章ViewSet"""
    queryset = KnowledgeArticle.objects.all()
    serializer_class = KnowledgeArticleSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user
        
        # 普通用户只能看到已发布的非内部文档
        if not user.is_staff:
            queryset = queryset.filter(is_published=True, is_internal=False)
        
        # 分类过滤
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category_id=category)
        
        # 文章类型过滤
        article_type = self.request.query_params.get('article_type')
        if article_type:
            queryset = queryset.filter(article_type=article_type)
        
        # 搜索
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) |
                Q(content__icontains=search) |
                Q(summary__icontains=search) |
                Q(keywords__icontains=search)
            )
        
        return queryset.order_by('-updated_at')
    
    def get_serializer_class(self):
        if self.action == 'list':
            return KnowledgeArticleListSerializer
        return KnowledgeArticleSerializer
    
    def perform_create(self, serializer):
        serializer.save(author=self.request.user)
    
    @action(detail=True, methods=['post'])
    def increment_view(self, request, pk=None):
        """增加查看次数"""
        article = self.get_object()
        article.view_count += 1
        article.save(update_fields=['view_count'])
        
        return Response({'message': '查看次数已更新'})
    
    @action(detail=True, methods=['post'])
    def rate_helpful(self, request, pk=None):
        """标记为有用"""
        article = self.get_object()
        is_helpful = request.data.get('helpful', True)
        
        if is_helpful:
            article.helpful_count += 1
        else:
            article.unhelpful_count += 1
        
        article.save()
        
        return Response({'message': '评价已记录'})


class QuickReplyViewSet(viewsets.ModelViewSet):
    """快捷回复ViewSet"""
    queryset = QuickReply.objects.all()
    serializer_class = QuickReplySerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user
        
        # 只显示激活的回复
        queryset = queryset.filter(is_active=True)
        
        # 普通用户只能看到公共回复
        if not user.is_staff:
            queryset = queryset.filter(is_public=True)
        
        # 回复类型过滤
        reply_type = self.request.query_params.get('reply_type')
        if reply_type:
            queryset = queryset.filter(reply_type=reply_type)
        
        # 分类过滤
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category_id=category)
        
        # 关键词搜索
        keyword = self.request.query_params.get('keyword')
        if keyword:
            queryset = queryset.filter(
                Q(title__icontains=keyword) |
                Q(content__icontains=keyword) |
                Q(keywords__icontains=keyword)
            )
        
        return queryset.order_by('-use_count', 'title')
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def use_reply(self, request, pk=None):
        """使用快捷回复"""
        reply = self.get_object()
        reply.use_count += 1
        reply.save(update_fields=['use_count'])
        
        return Response({
            'message': '快捷回复使用次数已更新',
            'content': reply.content
        })


class ServiceAgentViewSet(viewsets.ModelViewSet):
    """客服代表ViewSet"""
    queryset = ServiceAgent.objects.all()
    serializer_class = ServiceAgentSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # 状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # 可用性过滤
        available = self.request.query_params.get('available')
        if available == 'true':
            # 这里需要在应用层过滤
            available_agents = []
            for agent in queryset:
                if agent.is_available():
                    available_agents.append(agent.id)
            queryset = queryset.filter(id__in=available_agents)
        
        return queryset.order_by('display_name')
    
    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        """更新客服状态"""
        agent = self.get_object()
        new_status = request.data.get('status')
        
        if new_status not in dict(ServiceAgent.STATUS_CHOICES):
            return Response(
                {'error': '无效的状态'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        agent.status = new_status
        agent.last_active = timezone.now()
        agent.save()
        
        return Response({'message': '状态更新成功'})
    
    @action(detail=False, methods=['get'])
    def workload_stats(self, request):
        """工作负载统计"""
        agents = ServiceAgent.objects.all()
        stats = []
        
        for agent in agents:
            current_workload = agent.get_current_workload()
            stats.append({
                'agent_id': agent.id,
                'display_name': agent.display_name,
                'status': agent.status,
                'current_workload': current_workload,
                'max_workload': agent.max_concurrent_tickets,
                'workload_percentage': (current_workload / agent.max_concurrent_tickets * 100) if agent.max_concurrent_tickets > 0 else 0,
                'is_available': agent.is_available()
            })
        
        return Response(stats)


class ServiceMetricsViewSet(viewsets.ReadOnlyModelViewSet):
    """客服指标ViewSet"""
    queryset = ServiceMetrics.objects.all()
    serializer_class = ServiceMetricsSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # 日期范围过滤
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')
        if date_from:
            queryset = queryset.filter(date__gte=date_from)
        if date_to:
            queryset = queryset.filter(date__lte=date_to)
        
        # 客服过滤
        agent = self.request.query_params.get('agent')
        if agent:
            queryset = queryset.filter(agent_id=agent)
        
        return queryset.order_by('-date')
    
    @action(detail=False, methods=['get'])
    def summary(self, request):
        """指标汇总"""
        queryset = self.get_queryset()
        
        if not queryset.exists():
            return Response({
                'total_tickets': 0,
                'avg_response_time': 0,
                'avg_satisfaction': 0,
                'resolution_rate': 0
            })
        
        summary = queryset.aggregate(
            total_tickets=Sum('tickets_handled'),
            total_resolved=Sum('tickets_resolved'),
            avg_response_time=Avg('avg_response_time'),
            avg_satisfaction=Avg('avg_satisfaction')
        )
        
        resolution_rate = 0
        if summary['total_tickets'] and summary['total_tickets'] > 0:
            resolution_rate = (summary['total_resolved'] / summary['total_tickets']) * 100
        
        return Response({
            'total_tickets': summary['total_tickets'] or 0,
            'avg_response_time': summary['avg_response_time'] or 0,
            'avg_satisfaction': summary['avg_satisfaction'] or 0,
            'resolution_rate': resolution_rate
        })


class CustomerFeedbackViewSet(viewsets.ModelViewSet):
    """客户反馈ViewSet"""
    queryset = CustomerFeedback.objects.all()
    serializer_class = CustomerFeedbackSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user
        
        # 普通用户只能看到自己的反馈
        if not user.is_staff:
            queryset = queryset.filter(customer=user)
        
        # 反馈类型过滤
        feedback_type = self.request.query_params.get('feedback_type')
        if feedback_type:
            queryset = queryset.filter(feedback_type=feedback_type)
        
        # 处理状态过滤
        is_processed = self.request.query_params.get('is_processed')
        if is_processed == 'true':
            queryset = queryset.filter(is_processed=True)
        elif is_processed == 'false':
            queryset = queryset.filter(is_processed=False)
        
        return queryset.order_by('-created_at')
    
    def perform_create(self, serializer):
        serializer.save(customer=self.request.user)
    
    @action(detail=True, methods=['post'])
    def process_feedback(self, request, pk=None):
        """处理反馈"""
        feedback = self.get_object()
        response_text = request.data.get('response', '')
        
        feedback.is_processed = True
        feedback.processed_by = request.user
        feedback.processed_at = timezone.now()
        feedback.response = response_text
        feedback.save()
        
        return Response({'message': '反馈处理完成'})

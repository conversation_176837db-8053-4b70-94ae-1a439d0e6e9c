from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    ServiceCategory, ServiceTicket, TicketMessage, KnowledgeCategory,
    KnowledgeArticle, QuickReply, ServiceAgent, ServiceMetrics, CustomerFeedback
)

User = get_user_model()


class ServiceCategorySerializer(serializers.ModelSerializer):
    """客服分类序列化器"""
    parent_name = serializers.CharField(source='parent.name', read_only=True)
    
    class Meta:
        model = ServiceCategory
        fields = [
            'id', 'name', 'description', 'parent', 'parent_name',
            'sort_order', 'is_active', 'created_at', 'updated_at'
        ]


class TicketMessageSerializer(serializers.ModelSerializer):
    """工单消息序列化器"""
    sender_name = serializers.CharField(source='sender.get_full_name', read_only=True)
    message_type_display = serializers.CharField(source='get_message_type_display', read_only=True)
    
    class Meta:
        model = TicketMessage
        fields = [
            'id', 'ticket', 'message_type', 'message_type_display',
            'sender', 'sender_name', 'content', 'attachments',
            'is_read', 'read_at', 'created_at'
        ]


class ServiceTicketSerializer(serializers.ModelSerializer):
    """客服工单序列化器"""
    customer_name = serializers.CharField(source='customer.get_full_name', read_only=True)
    company_name = serializers.CharField(source='company.name', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.get_full_name', read_only=True)
    category_name = serializers.CharField(source='category.name', read_only=True)
    
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    source_display = serializers.CharField(source='get_source_display', read_only=True)
    
    # 计算字段
    response_time = serializers.SerializerMethodField()
    resolution_time = serializers.SerializerMethodField()
    is_overdue = serializers.SerializerMethodField()
    
    # 消息列表
    messages = TicketMessageSerializer(many=True, read_only=True)
    
    class Meta:
        model = ServiceTicket
        fields = [
            'id', 'ticket_number', 'title', 'description',
            'category', 'category_name', 'priority', 'priority_display',
            'status', 'status_display', 'source', 'source_display',
            'customer', 'customer_name', 'company', 'company_name',
            'assigned_to', 'assigned_to_name',
            'created_at', 'assigned_at', 'first_response_at',
            'resolved_at', 'closed_at',
            'customer_satisfaction', 'customer_feedback',
            'internal_notes', 'tags',
            'response_time', 'resolution_time', 'is_overdue',
            'messages'
        ]
    
    def get_response_time(self, obj):
        return obj.get_response_time()
    
    def get_resolution_time(self, obj):
        return obj.get_resolution_time()
    
    def get_is_overdue(self, obj):
        return obj.is_overdue()


class KnowledgeCategorySerializer(serializers.ModelSerializer):
    """知识库分类序列化器"""
    parent_name = serializers.CharField(source='parent.name', read_only=True)
    article_count = serializers.SerializerMethodField()
    
    class Meta:
        model = KnowledgeCategory
        fields = [
            'id', 'name', 'description', 'parent', 'parent_name',
            'icon', 'sort_order', 'is_active', 'article_count',
            'created_at', 'updated_at'
        ]
    
    def get_article_count(self, obj):
        return obj.knowledgearticle_set.filter(is_published=True).count()


class KnowledgeArticleSerializer(serializers.ModelSerializer):
    """知识库文章序列化器"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    author_name = serializers.CharField(source='author.get_full_name', read_only=True)
    article_type_display = serializers.CharField(source='get_article_type_display', read_only=True)
    helpfulness_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = KnowledgeArticle
        fields = [
            'id', 'title', 'content', 'summary',
            'category', 'category_name', 'article_type', 'article_type_display',
            'keywords', 'tags', 'is_published', 'is_internal',
            'view_count', 'helpful_count', 'unhelpful_count', 'helpfulness_rate',
            'author', 'author_name', 'created_at', 'updated_at'
        ]
    
    def get_helpfulness_rate(self, obj):
        return obj.get_helpfulness_rate()


class QuickReplySerializer(serializers.ModelSerializer):
    """快捷回复序列化器"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    reply_type_display = serializers.CharField(source='get_reply_type_display', read_only=True)
    
    class Meta:
        model = QuickReply
        fields = [
            'id', 'title', 'content', 'reply_type', 'reply_type_display',
            'category', 'category_name', 'keywords', 'use_count',
            'is_active', 'is_public', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]


class ServiceAgentSerializer(serializers.ModelSerializer):
    """客服代表序列化器"""
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    current_workload = serializers.SerializerMethodField()
    is_available = serializers.SerializerMethodField()
    categories_names = serializers.SerializerMethodField()
    
    class Meta:
        model = ServiceAgent
        fields = [
            'id', 'user', 'user_name', 'display_name', 'avatar',
            'status', 'status_display', 'last_active',
            'categories', 'categories_names', 'skills', 'languages',
            'max_concurrent_tickets', 'auto_assign',
            'phone', 'email', 'wechat',
            'current_workload', 'is_available',
            'created_at', 'updated_at'
        ]
    
    def get_current_workload(self, obj):
        return obj.get_current_workload()
    
    def get_is_available(self, obj):
        return obj.is_available()
    
    def get_categories_names(self, obj):
        return [cat.name for cat in obj.categories.all()]


class ServiceMetricsSerializer(serializers.ModelSerializer):
    """客服指标序列化器"""
    agent_name = serializers.CharField(source='agent.get_full_name', read_only=True)
    resolution_rate = serializers.SerializerMethodField()
    efficiency_score = serializers.SerializerMethodField()
    
    class Meta:
        model = ServiceMetrics
        fields = [
            'id', 'agent', 'agent_name', 'date',
            'tickets_handled', 'tickets_resolved', 'tickets_closed',
            'avg_response_time', 'avg_resolution_time', 'total_work_time',
            'avg_satisfaction', 'first_contact_resolution',
            'messages_sent', 'knowledge_articles_used', 'quick_replies_used',
            'resolution_rate', 'efficiency_score', 'created_at'
        ]
    
    def get_resolution_rate(self, obj):
        if obj.tickets_handled > 0:
            return (obj.tickets_resolved / obj.tickets_handled) * 100
        return 0
    
    def get_efficiency_score(self, obj):
        # 综合效率评分算法
        score = 0
        
        # 解决率权重 40%
        if obj.tickets_handled > 0:
            resolution_rate = (obj.tickets_resolved / obj.tickets_handled) * 100
            score += (resolution_rate / 100) * 40
        
        # 满意度权重 30%
        score += (float(obj.avg_satisfaction) / 5) * 30
        
        # 响应时间权重 20% (越快越好，最大120分钟)
        response_score = max(0, (120 - obj.avg_response_time) / 120)
        score += response_score * 20
        
        # 首次解决率权重 10%
        score += float(obj.first_contact_resolution) * 10 / 100
        
        return min(100, score)


class CustomerFeedbackSerializer(serializers.ModelSerializer):
    """客户反馈序列化器"""
    customer_name = serializers.CharField(source='customer.get_full_name', read_only=True)
    agent_name = serializers.CharField(source='agent.get_full_name', read_only=True)
    processed_by_name = serializers.CharField(source='processed_by.get_full_name', read_only=True)
    feedback_type_display = serializers.CharField(source='get_feedback_type_display', read_only=True)
    overall_rating = serializers.SerializerMethodField()
    
    class Meta:
        model = CustomerFeedback
        fields = [
            'id', 'ticket', 'customer', 'customer_name',
            'agent', 'agent_name', 'feedback_type', 'feedback_type_display',
            'title', 'content', 'service_rating', 'response_rating', 'resolution_rating',
            'overall_rating', 'is_processed', 'processed_by', 'processed_by_name',
            'processed_at', 'response', 'created_at'
        ]
    
    def get_overall_rating(self, obj):
        return obj.get_overall_rating()


# 简化版序列化器用于列表显示
class ServiceTicketListSerializer(serializers.ModelSerializer):
    """客服工单列表序列化器"""
    customer_name = serializers.CharField(source='customer.get_full_name', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.get_full_name', read_only=True)
    category_name = serializers.CharField(source='category.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    is_overdue = serializers.SerializerMethodField()
    
    class Meta:
        model = ServiceTicket
        fields = [
            'id', 'ticket_number', 'title', 'customer_name',
            'category_name', 'priority', 'priority_display',
            'status', 'status_display', 'assigned_to_name',
            'is_overdue', 'created_at'
        ]
    
    def get_is_overdue(self, obj):
        return obj.is_overdue()


class KnowledgeArticleListSerializer(serializers.ModelSerializer):
    """知识库文章列表序列化器"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    article_type_display = serializers.CharField(source='get_article_type_display', read_only=True)
    helpfulness_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = KnowledgeArticle
        fields = [
            'id', 'title', 'summary', 'category_name',
            'article_type_display', 'view_count',
            'helpfulness_rate', 'updated_at'
        ]
    
    def get_helpfulness_rate(self, obj):
        return obj.get_helpfulness_rate()


class ServiceTicketCreateSerializer(serializers.ModelSerializer):
    """客服工单创建序列化器"""
    class Meta:
        model = ServiceTicket
        fields = [
            'title', 'description', 'category', 'priority',
            'source', 'customer', 'company', 'tags'
        ]
    
    def create(self, validated_data):
        # 自动生成工单号
        import uuid
        from datetime import datetime
        
        ticket_number = f"TK{datetime.now().strftime('%Y%m%d')}{str(uuid.uuid4())[:8].upper()}"
        validated_data['ticket_number'] = ticket_number
        
        return super().create(validated_data)


class TicketMessageCreateSerializer(serializers.ModelSerializer):
    """工单消息创建序列化器"""
    class Meta:
        model = TicketMessage
        fields = [
            'ticket', 'message_type', 'sender', 'content', 'attachments'
        ]

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.db.models import Count, Avg
from .models import (
    ServiceCategory, ServiceTicket, TicketMessage, KnowledgeCategory,
    KnowledgeArticle, QuickReply, ServiceAgent, ServiceMetrics, CustomerFeedback
)


@admin.register(ServiceCategory)
class ServiceCategoryAdmin(admin.ModelAdmin):
    """客服分类管理"""
    list_display = ['name', 'parent', 'sort_order', 'is_active', 'created_at']
    list_filter = ['is_active', 'parent', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'parent')
        }),
        ('显示设置', {
            'fields': ('sort_order', 'is_active')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


class TicketMessageInline(admin.TabularInline):
    """工单消息内联"""
    model = TicketMessage
    extra = 0
    readonly_fields = ['created_at']
    fields = ['message_type', 'sender', 'content', 'is_read', 'created_at']


@admin.register(ServiceTicket)
class ServiceTicketAdmin(admin.ModelAdmin):
    """客服工单管理"""
    list_display = [
        'ticket_number', 'title', 'customer_info', 'category', 'priority_display',
        'status_display', 'assigned_to', 'response_time_display', 'created_at'
    ]
    list_filter = ['status', 'priority', 'source', 'category', 'created_at', 'assigned_to']
    search_fields = ['ticket_number', 'title', 'description', 'customer__username']
    readonly_fields = ['ticket_number', 'created_at', 'assigned_at', 'first_response_at', 
                      'resolved_at', 'closed_at', 'response_time_display', 'resolution_time_display']
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('ticket_number', 'title', 'description', 'category')
        }),
        ('状态和优先级', {
            'fields': ('status', 'priority', 'source')
        }),
        ('关联信息', {
            'fields': ('customer', 'company', 'assigned_to')
        }),
        ('时间信息', {
            'fields': ('created_at', 'assigned_at', 'first_response_at', 'resolved_at', 'closed_at'),
            'classes': ('collapse',)
        }),
        ('服务质量', {
            'fields': ('customer_satisfaction', 'customer_feedback'),
            'classes': ('collapse',)
        }),
        ('内部信息', {
            'fields': ('internal_notes', 'tags'),
            'classes': ('collapse',)
        }),
        ('统计信息', {
            'fields': ('response_time_display', 'resolution_time_display'),
            'classes': ('collapse',)
        }),
    )
    
    inlines = [TicketMessageInline]
    actions = ['assign_to_me', 'mark_resolved', 'mark_closed']
    
    def customer_info(self, obj):
        """客户信息"""
        try:
            user_url = reverse('admin:users_user_change', args=[obj.customer.pk])
        except:
            user_url = '#'

        return format_html(
            '<a href="{}">{}</a><br/><small>{}</small>',
            user_url,
            obj.customer.get_full_name() or obj.customer.username,
            obj.company.name if obj.company else '无企业'
        )
    customer_info.short_description = '客户信息'
    
    def priority_display(self, obj):
        """优先级显示"""
        priority_colors = {
            'urgent': '#f5222d',
            'high': '#fa8c16',
            'normal': '#1890ff',
            'low': '#52c41a',
        }
        color = priority_colors.get(obj.priority, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_priority_display()
        )
    priority_display.short_description = '优先级'
    
    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'open': '#faad14',
            'assigned': '#1890ff',
            'in_progress': '#722ed1',
            'pending_customer': '#13c2c2',
            'resolved': '#52c41a',
            'closed': '#d9d9d9',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        
        # 检查是否超时
        overdue_text = ''
        if obj.is_overdue():
            overdue_text = ' (超时)'
            color = '#f5222d'
        
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}{}',
            color, obj.get_status_display(), overdue_text
        )
    status_display.short_description = '状态'
    
    def response_time_display(self, obj):
        """响应时间显示"""
        response_time = obj.get_response_time()
        if response_time is not None:
            if response_time < 60:
                return f"{response_time}分钟"
            else:
                hours = response_time // 60
                minutes = response_time % 60
                return f"{hours}小时{minutes}分钟"
        return '-'
    response_time_display.short_description = '响应时间'
    
    def resolution_time_display(self, obj):
        """解决时间显示"""
        resolution_time = obj.get_resolution_time()
        if resolution_time is not None:
            if resolution_time < 60:
                return f"{resolution_time}分钟"
            else:
                hours = resolution_time // 60
                minutes = resolution_time % 60
                return f"{hours}小时{minutes}分钟"
        return '-'
    resolution_time_display.short_description = '解决时间'
    
    def assign_to_me(self, request, queryset):
        """分配给我"""
        updated = queryset.filter(assigned_to__isnull=True).update(
            assigned_to=request.user,
            assigned_at=timezone.now(),
            status='assigned'
        )
        self.message_user(request, f"成功分配 {updated} 个工单给您")
    assign_to_me.short_description = "分配给我"
    
    def mark_resolved(self, request, queryset):
        """标记为已解决"""
        updated = queryset.filter(status__in=['assigned', 'in_progress']).update(
            status='resolved',
            resolved_at=timezone.now()
        )
        self.message_user(request, f"成功标记 {updated} 个工单为已解决")
    mark_resolved.short_description = "标记为已解决"
    
    def mark_closed(self, request, queryset):
        """标记为已关闭"""
        updated = queryset.filter(status='resolved').update(
            status='closed',
            closed_at=timezone.now()
        )
        self.message_user(request, f"成功关闭 {updated} 个工单")
    mark_closed.short_description = "标记为已关闭"


@admin.register(TicketMessage)
class TicketMessageAdmin(admin.ModelAdmin):
    """工单消息管理"""
    list_display = ['ticket_info', 'message_type_display', 'sender', 'content_preview', 'is_read', 'created_at']
    list_filter = ['message_type', 'is_read', 'created_at']
    search_fields = ['ticket__ticket_number', 'content', 'sender__username']
    readonly_fields = ['created_at']
    
    def ticket_info(self, obj):
        """工单信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:customer_service_serviceticket_change', args=[obj.ticket.pk]),
            obj.ticket.ticket_number
        )
    ticket_info.short_description = '工单'
    
    def message_type_display(self, obj):
        """消息类型显示"""
        type_colors = {
            'customer': '#1890ff',
            'agent': '#52c41a',
            'system': '#faad14',
            'internal': '#722ed1',
        }
        color = type_colors.get(obj.message_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_message_type_display()
        )
    message_type_display.short_description = '消息类型'
    
    def content_preview(self, obj):
        """内容预览"""
        if len(obj.content) > 50:
            return obj.content[:50] + '...'
        return obj.content
    content_preview.short_description = '内容预览'


@admin.register(KnowledgeCategory)
class KnowledgeCategoryAdmin(admin.ModelAdmin):
    """知识库分类管理"""
    list_display = ['name', 'parent', 'icon', 'sort_order', 'is_active', 'created_at']
    list_filter = ['is_active', 'parent', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(KnowledgeArticle)
class KnowledgeArticleAdmin(admin.ModelAdmin):
    """知识库文章管理"""
    list_display = [
        'title', 'category', 'article_type_display', 'author',
        'view_count', 'helpfulness_display', 'is_published', 'updated_at'
    ]
    list_filter = ['article_type', 'category', 'is_published', 'is_internal', 'created_at']
    search_fields = ['title', 'content', 'summary', 'keywords']
    readonly_fields = ['view_count', 'helpful_count', 'unhelpful_count', 'created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('title', 'summary', 'content')
        }),
        ('分类和类型', {
            'fields': ('category', 'article_type')
        }),
        ('关键词和标签', {
            'fields': ('keywords', 'tags')
        }),
        ('状态和权限', {
            'fields': ('is_published', 'is_internal')
        }),
        ('统计信息', {
            'fields': ('view_count', 'helpful_count', 'unhelpful_count'),
            'classes': ('collapse',)
        }),
        ('作者和时间', {
            'fields': ('author', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def article_type_display(self, obj):
        """文章类型显示"""
        type_colors = {
            'faq': '#1890ff',
            'guide': '#52c41a',
            'policy': '#faad14',
            'troubleshooting': '#f5222d',
            'tutorial': '#722ed1',
        }
        color = type_colors.get(obj.article_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_article_type_display()
        )
    article_type_display.short_description = '文章类型'
    
    def helpfulness_display(self, obj):
        """有用率显示"""
        rate = obj.get_helpfulness_rate()
        if rate > 80:
            color = '#52c41a'
        elif rate > 60:
            color = '#faad14'
        else:
            color = '#f5222d'
        
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}%</span><br/>'
            '<small>有用: {} | 无用: {}</small>',
            color, rate, obj.helpful_count, obj.unhelpful_count
        )
    helpfulness_display.short_description = '有用率'
    
    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.author = request.user
        super().save_model(request, obj, form, change)


@admin.register(QuickReply)
class QuickReplyAdmin(admin.ModelAdmin):
    """快捷回复管理"""
    list_display = ['title', 'reply_type_display', 'category', 'use_count', 'is_active', 'created_by']
    list_filter = ['reply_type', 'category', 'is_active', 'is_public', 'created_at']
    search_fields = ['title', 'content', 'keywords']
    readonly_fields = ['use_count', 'created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('title', 'content', 'reply_type')
        }),
        ('分类和关键词', {
            'fields': ('category', 'keywords')
        }),
        ('权限和状态', {
            'fields': ('is_active', 'is_public')
        }),
        ('统计信息', {
            'fields': ('use_count',),
            'classes': ('collapse',)
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def reply_type_display(self, obj):
        """回复类型显示"""
        type_colors = {
            'greeting': '#52c41a',
            'closing': '#1890ff',
            'common': '#faad14',
            'template': '#722ed1',
        }
        color = type_colors.get(obj.reply_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_reply_type_display()
        )
    reply_type_display.short_description = '回复类型'
    
    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(ServiceAgent)
class ServiceAgentAdmin(admin.ModelAdmin):
    """客服代表管理"""
    list_display = [
        'display_name', 'user', 'status_display', 'workload_display',
        'auto_assign', 'last_active'
    ]
    list_filter = ['status', 'auto_assign', 'categories']
    search_fields = ['display_name', 'user__username', 'user__first_name']
    readonly_fields = ['last_active', 'created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'display_name', 'avatar')
        }),
        ('状态信息', {
            'fields': ('status', 'last_active')
        }),
        ('技能和分类', {
            'fields': ('categories', 'skills', 'languages')
        }),
        ('工作配置', {
            'fields': ('max_concurrent_tickets', 'auto_assign')
        }),
        ('联系方式', {
            'fields': ('phone', 'email', 'wechat'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    filter_horizontal = ['categories']
    
    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'online': '#52c41a',
            'busy': '#faad14',
            'away': '#1890ff',
            'offline': '#d9d9d9',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'
    
    def workload_display(self, obj):
        """工作负载显示"""
        current = obj.get_current_workload()
        max_load = obj.max_concurrent_tickets
        percentage = (current / max_load * 100) if max_load > 0 else 0
        
        if percentage >= 90:
            color = '#f5222d'
        elif percentage >= 70:
            color = '#faad14'
        else:
            color = '#52c41a'
        
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}/{}</span><br/>'
            '<small>({:.1f}%)</small>',
            color, current, max_load, percentage
        )
    workload_display.short_description = '工作负载'


@admin.register(ServiceMetrics)
class ServiceMetricsAdmin(admin.ModelAdmin):
    """客服指标管理"""
    list_display = [
        'agent', 'date', 'tickets_handled', 'tickets_resolved',
        'avg_response_time_display', 'avg_satisfaction_display', 'efficiency_display'
    ]
    list_filter = ['date', 'agent']
    search_fields = ['agent__username', 'agent__first_name']
    readonly_fields = ['created_at']
    date_hierarchy = 'date'

    fieldsets = (
        ('基本信息', {
            'fields': ('agent', 'date')
        }),
        ('工单统计', {
            'fields': ('tickets_handled', 'tickets_resolved', 'tickets_closed')
        }),
        ('时间统计', {
            'fields': ('avg_response_time', 'avg_resolution_time', 'total_work_time')
        }),
        ('质量统计', {
            'fields': ('avg_satisfaction', 'first_contact_resolution')
        }),
        ('效率统计', {
            'fields': ('messages_sent', 'knowledge_articles_used', 'quick_replies_used')
        }),
        ('创建时间', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

    def avg_response_time_display(self, obj):
        """平均响应时间显示"""
        if obj.avg_response_time < 60:
            return f"{obj.avg_response_time}分钟"
        else:
            hours = obj.avg_response_time // 60
            minutes = obj.avg_response_time % 60
            return f"{hours}小时{minutes}分钟"
    avg_response_time_display.short_description = '平均响应时间'

    def avg_satisfaction_display(self, obj):
        """平均满意度显示"""
        if obj.avg_satisfaction >= 4.5:
            color = '#52c41a'
        elif obj.avg_satisfaction >= 3.5:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.2f}</span>',
            color, float(obj.avg_satisfaction)
        )
    avg_satisfaction_display.short_description = '平均满意度'

    def efficiency_display(self, obj):
        """效率显示"""
        if obj.tickets_handled > 0:
            resolution_rate = (obj.tickets_resolved / obj.tickets_handled) * 100
        else:
            resolution_rate = 0

        if resolution_rate >= 90:
            color = '#52c41a'
        elif resolution_rate >= 70:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}%</span><br/>'
            '<small>解决率</small>',
            color, resolution_rate
        )
    efficiency_display.short_description = '解决效率'


@admin.register(CustomerFeedback)
class CustomerFeedbackAdmin(admin.ModelAdmin):
    """客户反馈管理"""
    list_display = [
        'title', 'customer', 'agent', 'feedback_type_display',
        'overall_rating_display', 'is_processed', 'created_at'
    ]
    list_filter = ['feedback_type', 'is_processed', 'service_rating', 'created_at']
    search_fields = ['title', 'content', 'customer__username', 'agent__username']
    readonly_fields = ['created_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('ticket', 'customer', 'agent', 'feedback_type')
        }),
        ('反馈内容', {
            'fields': ('title', 'content')
        }),
        ('评分信息', {
            'fields': ('service_rating', 'response_rating', 'resolution_rating')
        }),
        ('处理信息', {
            'fields': ('is_processed', 'processed_by', 'processed_at', 'response'),
            'classes': ('collapse',)
        }),
        ('创建时间', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_processed']

    def feedback_type_display(self, obj):
        """反馈类型显示"""
        type_colors = {
            'complaint': '#f5222d',
            'suggestion': '#1890ff',
            'praise': '#52c41a',
            'inquiry': '#faad14',
        }
        color = type_colors.get(obj.feedback_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_feedback_type_display()
        )
    feedback_type_display.short_description = '反馈类型'

    def overall_rating_display(self, obj):
        """综合评分显示"""
        rating = obj.get_overall_rating()
        if rating is None:
            return '-'

        if rating >= 4.5:
            color = '#52c41a'
        elif rating >= 3.5:
            color = '#faad14'
        else:
            color = '#f5222d'

        # 生成星级显示
        stars = '★' * int(rating) + '☆' * (5 - int(rating))

        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}</span><br/>'
            '<span style="color: {};">{}</span>',
            color, rating, color, stars
        )
    overall_rating_display.short_description = '综合评分'

    def mark_processed(self, request, queryset):
        """标记为已处理"""
        updated = queryset.filter(is_processed=False).update(
            is_processed=True,
            processed_by=request.user,
            processed_at=timezone.now()
        )
        self.message_user(request, f"成功标记 {updated} 个反馈为已处理")
    mark_processed.short_description = "标记为已处理"


# 自定义管理页面标题
admin.site.site_header = '代理记账管理系统'
admin.site.site_title = '智能客服管理'
admin.site.index_title = '智能客服管理'

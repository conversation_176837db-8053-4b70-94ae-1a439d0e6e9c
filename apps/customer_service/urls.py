from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

router = DefaultRouter()
router.register(r'categories', views.ServiceCategoryViewSet, basename='service-category')
router.register(r'tickets', views.ServiceTicketViewSet, basename='service-ticket')
router.register(r'messages', views.TicketMessageViewSet, basename='ticket-message')
router.register(r'knowledge-categories', views.KnowledgeCategoryViewSet, basename='knowledge-category')
router.register(r'knowledge-articles', views.KnowledgeArticleViewSet, basename='knowledge-article')
router.register(r'quick-replies', views.QuickReplyViewSet, basename='quick-reply')
router.register(r'agents', views.ServiceAgentViewSet, basename='service-agent')
router.register(r'metrics', views.ServiceMetricsViewSet, basename='service-metrics')
router.register(r'feedback', views.CustomerFeedbackViewSet, basename='customer-feedback')

app_name = 'customer_service'

urlpatterns = [
    path('', include(router.urls)),
]

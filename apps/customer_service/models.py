from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from apps.companies.models import Company

User = get_user_model()


class ServiceCategory(models.Model):
    """客服分类"""
    name = models.CharField('分类名称', max_length=100)
    description = models.TextField('分类描述', blank=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name='父分类')
    sort_order = models.IntegerField('排序', default=0)
    is_active = models.BooleanField('是否启用', default=True)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '客服分类'
        verbose_name_plural = '客服分类'
        ordering = ['sort_order', 'name']
    
    def __str__(self):
        return self.name


class ServiceTicket(models.Model):
    """客服工单"""
    PRIORITY_CHOICES = [
        ('low', '低'),
        ('normal', '普通'),
        ('high', '高'),
        ('urgent', '紧急'),
    ]
    
    STATUS_CHOICES = [
        ('open', '待处理'),
        ('assigned', '已分配'),
        ('in_progress', '处理中'),
        ('pending_customer', '等待客户'),
        ('resolved', '已解决'),
        ('closed', '已关闭'),
    ]
    
    SOURCE_CHOICES = [
        ('phone', '电话'),
        ('email', '邮件'),
        ('wechat', '微信'),
        ('web', '网站'),
        ('app', '小程序'),
        ('internal', '内部'),
    ]
    
    # 基本信息
    ticket_number = models.CharField('工单号', max_length=32, unique=True)
    title = models.CharField('工单标题', max_length=200)
    description = models.TextField('问题描述')
    category = models.ForeignKey(ServiceCategory, on_delete=models.SET_NULL, null=True, verbose_name='问题分类')
    
    # 优先级和状态
    priority = models.CharField('优先级', max_length=10, choices=PRIORITY_CHOICES, default='normal')
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='open')
    source = models.CharField('来源', max_length=20, choices=SOURCE_CHOICES, default='web')
    
    # 关联信息
    customer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='customer_tickets', verbose_name='客户')
    company = models.ForeignKey(Company, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='关联企业')
    assigned_to = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='assigned_tickets', verbose_name='处理人'
    )
    
    # 时间信息
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    assigned_at = models.DateTimeField('分配时间', null=True, blank=True)
    first_response_at = models.DateTimeField('首次响应时间', null=True, blank=True)
    resolved_at = models.DateTimeField('解决时间', null=True, blank=True)
    closed_at = models.DateTimeField('关闭时间', null=True, blank=True)
    
    # 服务质量
    customer_satisfaction = models.IntegerField('客户满意度', null=True, blank=True, help_text='1-5分')
    customer_feedback = models.TextField('客户反馈', blank=True)
    
    # 内部信息
    internal_notes = models.TextField('内部备注', blank=True)
    tags = models.JSONField('标签', default=list, help_text='工单标签列表')
    
    class Meta:
        verbose_name = '客服工单'
        verbose_name_plural = '客服工单'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['assigned_to', 'status']),
            models.Index(fields=['customer', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.ticket_number} - {self.title}"
    
    def get_response_time(self):
        """获取响应时间（分钟）"""
        if self.first_response_at:
            delta = self.first_response_at - self.created_at
            return int(delta.total_seconds() / 60)
        return None
    
    def get_resolution_time(self):
        """获取解决时间（分钟）"""
        if self.resolved_at:
            delta = self.resolved_at - self.created_at
            return int(delta.total_seconds() / 60)
        return None
    
    def is_overdue(self):
        """检查是否超时"""
        if self.status in ['resolved', 'closed']:
            return False
        
        # 根据优先级设置超时时间
        timeout_hours = {
            'urgent': 2,
            'high': 8,
            'normal': 24,
            'low': 72,
        }
        
        timeout = timeout_hours.get(self.priority, 24)
        deadline = self.created_at + timezone.timedelta(hours=timeout)
        return timezone.now() > deadline


class TicketMessage(models.Model):
    """工单消息"""
    MESSAGE_TYPES = [
        ('customer', '客户消息'),
        ('agent', '客服回复'),
        ('system', '系统消息'),
        ('internal', '内部备注'),
    ]
    
    ticket = models.ForeignKey(ServiceTicket, on_delete=models.CASCADE, related_name='messages', verbose_name='工单')
    message_type = models.CharField('消息类型', max_length=20, choices=MESSAGE_TYPES)
    sender = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='发送人')
    
    content = models.TextField('消息内容')
    attachments = models.JSONField('附件', default=list, help_text='附件文件路径列表')
    
    is_read = models.BooleanField('是否已读', default=False)
    read_at = models.DateTimeField('阅读时间', null=True, blank=True)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '工单消息'
        verbose_name_plural = '工单消息'
        ordering = ['created_at']
    
    def __str__(self):
        return f"{self.ticket.ticket_number} - {self.get_message_type_display()}"


class KnowledgeCategory(models.Model):
    """知识库分类"""
    name = models.CharField('分类名称', max_length=100)
    description = models.TextField('分类描述', blank=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name='父分类')
    icon = models.CharField('图标', max_length=50, blank=True)
    sort_order = models.IntegerField('排序', default=0)
    is_active = models.BooleanField('是否启用', default=True)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '知识库分类'
        verbose_name_plural = '知识库分类'
        ordering = ['sort_order', 'name']
    
    def __str__(self):
        return self.name


class KnowledgeArticle(models.Model):
    """知识库文章"""
    ARTICLE_TYPES = [
        ('faq', '常见问题'),
        ('guide', '操作指南'),
        ('policy', '政策解读'),
        ('troubleshooting', '故障排除'),
        ('tutorial', '教程'),
    ]
    
    title = models.CharField('文章标题', max_length=200)
    content = models.TextField('文章内容')
    summary = models.TextField('文章摘要', blank=True)
    
    category = models.ForeignKey(KnowledgeCategory, on_delete=models.SET_NULL, null=True, verbose_name='分类')
    article_type = models.CharField('文章类型', max_length=20, choices=ARTICLE_TYPES, default='faq')
    
    # 关键词和标签
    keywords = models.JSONField('关键词', default=list, help_text='搜索关键词列表')
    tags = models.JSONField('标签', default=list, help_text='文章标签列表')
    
    # 状态和权限
    is_published = models.BooleanField('是否发布', default=True)
    is_internal = models.BooleanField('是否内部文档', default=False)
    
    # 统计信息
    view_count = models.IntegerField('查看次数', default=0)
    helpful_count = models.IntegerField('有用次数', default=0)
    unhelpful_count = models.IntegerField('无用次数', default=0)
    
    # 作者和时间
    author = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='作者')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '知识库文章'
        verbose_name_plural = '知识库文章'
        ordering = ['-updated_at']
    
    def __str__(self):
        return self.title
    
    def get_helpfulness_rate(self):
        """获取有用率"""
        total = self.helpful_count + self.unhelpful_count
        if total == 0:
            return 0
        return (self.helpful_count / total) * 100


class QuickReply(models.Model):
    """快捷回复"""
    REPLY_TYPES = [
        ('greeting', '问候语'),
        ('closing', '结束语'),
        ('common', '常用回复'),
        ('template', '模板回复'),
    ]
    
    title = models.CharField('回复标题', max_length=100)
    content = models.TextField('回复内容')
    reply_type = models.CharField('回复类型', max_length=20, choices=REPLY_TYPES, default='common')
    
    category = models.ForeignKey(ServiceCategory, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='适用分类')
    keywords = models.JSONField('触发关键词', default=list, help_text='自动触发的关键词')
    
    # 使用统计
    use_count = models.IntegerField('使用次数', default=0)
    
    # 权限和状态
    is_active = models.BooleanField('是否启用', default=True)
    is_public = models.BooleanField('是否公共', default=True, help_text='是否对所有客服可见')
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='创建人')
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '快捷回复'
        verbose_name_plural = '快捷回复'
        ordering = ['-use_count', 'title']
    
    def __str__(self):
        return self.title


class ServiceAgent(models.Model):
    """客服代表"""
    STATUS_CHOICES = [
        ('online', '在线'),
        ('busy', '忙碌'),
        ('away', '离开'),
        ('offline', '离线'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户')
    display_name = models.CharField('显示名称', max_length=50)
    avatar = models.CharField('头像', max_length=200, blank=True)
    
    # 状态信息
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='offline')
    last_active = models.DateTimeField('最后活跃时间', auto_now=True)
    
    # 技能和分类
    categories = models.ManyToManyField(ServiceCategory, blank=True, verbose_name='服务分类')
    skills = models.JSONField('技能标签', default=list, help_text='客服技能列表')
    languages = models.JSONField('语言能力', default=list, help_text='支持的语言列表')
    
    # 工作配置
    max_concurrent_tickets = models.IntegerField('最大并发工单数', default=10)
    auto_assign = models.BooleanField('自动分配', default=True)
    
    # 联系方式
    phone = models.CharField('电话', max_length=20, blank=True)
    email = models.EmailField('邮箱', blank=True)
    wechat = models.CharField('微信号', max_length=50, blank=True)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '客服代表'
        verbose_name_plural = '客服代表'
        ordering = ['display_name']
    
    def __str__(self):
        return self.display_name
    
    def get_current_workload(self):
        """获取当前工作负载"""
        return ServiceTicket.objects.filter(
            assigned_to=self.user,
            status__in=['assigned', 'in_progress', 'pending_customer']
        ).count()
    
    def is_available(self):
        """检查是否可用"""
        if self.status not in ['online', 'busy']:
            return False
        return self.get_current_workload() < self.max_concurrent_tickets


class ServiceMetrics(models.Model):
    """客服指标"""
    agent = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='客服代表')
    date = models.DateField('日期')
    
    # 工单统计
    tickets_handled = models.IntegerField('处理工单数', default=0)
    tickets_resolved = models.IntegerField('解决工单数', default=0)
    tickets_closed = models.IntegerField('关闭工单数', default=0)
    
    # 时间统计
    avg_response_time = models.IntegerField('平均响应时间(分钟)', default=0)
    avg_resolution_time = models.IntegerField('平均解决时间(分钟)', default=0)
    total_work_time = models.IntegerField('总工作时间(分钟)', default=0)
    
    # 质量统计
    avg_satisfaction = models.DecimalField('平均满意度', max_digits=3, decimal_places=2, default=0)
    first_contact_resolution = models.DecimalField('首次解决率', max_digits=5, decimal_places=2, default=0)
    
    # 效率统计
    messages_sent = models.IntegerField('发送消息数', default=0)
    knowledge_articles_used = models.IntegerField('使用知识库次数', default=0)
    quick_replies_used = models.IntegerField('使用快捷回复次数', default=0)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '客服指标'
        verbose_name_plural = '客服指标'
        unique_together = ['agent', 'date']
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.agent.get_full_name()} - {self.date}"


class CustomerFeedback(models.Model):
    """客户反馈"""
    FEEDBACK_TYPES = [
        ('complaint', '投诉'),
        ('suggestion', '建议'),
        ('praise', '表扬'),
        ('inquiry', '咨询'),
    ]
    
    ticket = models.ForeignKey(ServiceTicket, on_delete=models.CASCADE, null=True, blank=True, verbose_name='关联工单')
    customer = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='客户')
    agent = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, 
                            related_name='received_feedback', verbose_name='客服代表')
    
    feedback_type = models.CharField('反馈类型', max_length=20, choices=FEEDBACK_TYPES)
    title = models.CharField('反馈标题', max_length=200)
    content = models.TextField('反馈内容')
    
    # 评分
    service_rating = models.IntegerField('服务评分', null=True, blank=True, help_text='1-5分')
    response_rating = models.IntegerField('响应评分', null=True, blank=True, help_text='1-5分')
    resolution_rating = models.IntegerField('解决评分', null=True, blank=True, help_text='1-5分')
    
    # 处理状态
    is_processed = models.BooleanField('是否已处理', default=False)
    processed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='processed_feedback', verbose_name='处理人')
    processed_at = models.DateTimeField('处理时间', null=True, blank=True)
    response = models.TextField('处理回复', blank=True)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '客户反馈'
        verbose_name_plural = '客户反馈'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.get_feedback_type_display()} - {self.title}"
    
    def get_overall_rating(self):
        """获取综合评分"""
        ratings = [r for r in [self.service_rating, self.response_rating, self.resolution_rating] if r is not None]
        if ratings:
            return sum(ratings) / len(ratings)
        return None

from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal
from apps.companies.models import Company

User = get_user_model()


class InvoiceType(models.Model):
    """发票类型"""
    TYPE_CHOICES = [
        ('vat_special', '增值税专用发票'),
        ('vat_ordinary', '增值税普通发票'),
        ('vat_electronic', '增值税电子发票'),
        ('receipt', '收据'),
        ('other', '其他'),
    ]
    
    name = models.CharField('类型名称', max_length=100)
    code = models.CharField('类型代码', max_length=20, unique=True)
    type_category = models.CharField('发票分类', max_length=20, choices=TYPE_CHOICES)
    description = models.TextField('类型描述', blank=True)
    
    # 配置信息
    can_deduct = models.BooleanField('可抵扣', default=False, help_text='是否可以抵扣进项税')
    requires_authentication = models.BooleanField('需要认证', default=False, help_text='是否需要认证')
    authentication_deadline = models.IntegerField('认证期限(天)', default=360, help_text='认证期限天数')
    
    # 税率配置
    default_tax_rates = models.JSONField('默认税率', default=list, help_text='适用的税率列表')
    
    is_active = models.BooleanField('是否启用', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '发票类型'
        verbose_name_plural = '发票类型'
        ordering = ['name']
    
    def __str__(self):
        return self.name


class Invoice(models.Model):
    """发票主表"""
    DIRECTION_CHOICES = [
        ('inbound', '进项发票'),
        ('outbound', '销项发票'),
    ]
    
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('issued', '已开具'),
        ('received', '已接收'),
        ('authenticated', '已认证'),
        ('deducted', '已抵扣'),
        ('invalid', '已作废'),
        ('red_flushed', '已红冲'),
    ]
    
    # 基本信息
    invoice_number = models.CharField('发票号码', max_length=50, unique=True)
    invoice_code = models.CharField('发票代码', max_length=20)
    invoice_type = models.ForeignKey(InvoiceType, on_delete=models.PROTECT, verbose_name='发票类型')
    direction = models.CharField('发票方向', max_length=10, choices=DIRECTION_CHOICES)
    
    # 开票信息
    issue_date = models.DateField('开票日期')
    seller_name = models.CharField('销售方名称', max_length=200)
    seller_tax_number = models.CharField('销售方税号', max_length=30)
    seller_address = models.CharField('销售方地址', max_length=300, blank=True)
    seller_phone = models.CharField('销售方电话', max_length=50, blank=True)
    seller_bank = models.CharField('销售方开户行', max_length=200, blank=True)
    seller_account = models.CharField('销售方账号', max_length=50, blank=True)
    
    # 购买方信息
    buyer_name = models.CharField('购买方名称', max_length=200)
    buyer_tax_number = models.CharField('购买方税号', max_length=30)
    buyer_address = models.CharField('购买方地址', max_length=300, blank=True)
    buyer_phone = models.CharField('购买方电话', max_length=50, blank=True)
    buyer_bank = models.CharField('购买方开户行', max_length=200, blank=True)
    buyer_account = models.CharField('购买方账号', max_length=50, blank=True)
    
    # 金额信息
    total_amount = models.DecimalField('价税合计', max_digits=15, decimal_places=2)
    tax_amount = models.DecimalField('税额', max_digits=15, decimal_places=2)
    amount_without_tax = models.DecimalField('不含税金额', max_digits=15, decimal_places=2)
    
    # 状态和关联
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='draft')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, verbose_name='关联企业')
    
    # 认证信息
    authentication_date = models.DateField('认证日期', null=True, blank=True)
    authentication_deadline = models.DateField('认证期限', null=True, blank=True)
    authenticated_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='authenticated_invoices', verbose_name='认证人'
    )
    
    # 抵扣信息
    deduction_date = models.DateField('抵扣日期', null=True, blank=True)
    deduction_period = models.CharField('抵扣期间', max_length=20, blank=True, help_text='格式：YYYY-MM')
    deducted_amount = models.DecimalField('抵扣金额', max_digits=15, decimal_places=2, default=0)
    
    # 附加信息
    remarks = models.TextField('备注', blank=True)
    attachments = models.JSONField('附件', default=list, help_text='发票图片或PDF文件路径')
    
    # 审核信息
    reviewed_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='reviewed_invoices', verbose_name='审核人'
    )
    reviewed_at = models.DateTimeField('审核时间', null=True, blank=True)
    review_notes = models.TextField('审核意见', blank=True)
    
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '发票'
        verbose_name_plural = '发票'
        ordering = ['-issue_date', '-created_at']
        indexes = [
            models.Index(fields=['invoice_number']),
            models.Index(fields=['company', 'issue_date']),
            models.Index(fields=['status', 'direction']),
            models.Index(fields=['authentication_deadline']),
        ]
    
    def __str__(self):
        return f"{self.invoice_number} - {self.seller_name}"
    
    def is_authentication_overdue(self):
        """检查是否认证逾期"""
        if self.status in ['authenticated', 'deducted', 'invalid', 'red_flushed']:
            return False
        if not self.authentication_deadline:
            return False
        return timezone.now().date() > self.authentication_deadline
    
    def days_until_authentication_deadline(self):
        """距离认证期限的天数"""
        if not self.authentication_deadline:
            return None
        delta = self.authentication_deadline - timezone.now().date()
        return delta.days
    
    def can_authenticate(self):
        """是否可以认证"""
        return (
            self.direction == 'inbound' and
            self.invoice_type.requires_authentication and
            self.status in ['received'] and
            not self.is_authentication_overdue()
        )
    
    def can_deduct(self):
        """是否可以抵扣"""
        return (
            self.direction == 'inbound' and
            self.invoice_type.can_deduct and
            self.status == 'authenticated'
        )


class InvoiceItem(models.Model):
    """发票明细"""
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='items', verbose_name='发票')
    
    # 商品信息
    item_name = models.CharField('商品名称', max_length=200)
    specification = models.CharField('规格型号', max_length=100, blank=True)
    unit = models.CharField('单位', max_length=20, blank=True)
    quantity = models.DecimalField('数量', max_digits=12, decimal_places=4, default=1)
    unit_price = models.DecimalField('单价', max_digits=12, decimal_places=4)
    
    # 金额信息
    amount = models.DecimalField('金额', max_digits=15, decimal_places=2)
    tax_rate = models.DecimalField('税率', max_digits=5, decimal_places=4, help_text='例如：0.13表示13%')
    tax_amount = models.DecimalField('税额', max_digits=15, decimal_places=2)
    
    # 分类信息
    category_code = models.CharField('商品编码', max_length=50, blank=True)
    category_name = models.CharField('商品分类', max_length=100, blank=True)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '发票明细'
        verbose_name_plural = '发票明细'
        ordering = ['id']
    
    def __str__(self):
        return f"{self.invoice.invoice_number} - {self.item_name}"


class InvoiceAuthentication(models.Model):
    """发票认证记录"""
    AUTHENTICATION_METHODS = [
        ('online', '网上认证'),
        ('manual', '手工认证'),
        ('scan', '扫描认证'),
        ('batch', '批量认证'),
    ]
    
    AUTHENTICATION_RESULTS = [
        ('success', '认证成功'),
        ('failed', '认证失败'),
        ('pending', '认证中'),
        ('timeout', '认证超时'),
    ]
    
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='authentications', verbose_name='发票')
    
    # 认证信息
    authentication_method = models.CharField('认证方式', max_length=20, choices=AUTHENTICATION_METHODS)
    authentication_result = models.CharField('认证结果', max_length=20, choices=AUTHENTICATION_RESULTS)
    authentication_time = models.DateTimeField('认证时间')
    
    # 认证详情
    authentication_code = models.CharField('认证码', max_length=50, blank=True)
    error_message = models.TextField('错误信息', blank=True)
    retry_count = models.IntegerField('重试次数', default=0)
    
    # 操作人员
    authenticated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='认证人')
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '发票认证记录'
        verbose_name_plural = '发票认证记录'
        ordering = ['-authentication_time']
    
    def __str__(self):
        return f"{self.invoice.invoice_number} - {self.get_authentication_result_display()}"


class InvoiceDeduction(models.Model):
    """发票抵扣记录"""
    DEDUCTION_TYPES = [
        ('input_tax', '进项税抵扣'),
        ('output_tax', '销项税'),
        ('transfer', '转出'),
        ('adjustment', '调整'),
    ]
    
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='deductions', verbose_name='发票')
    
    # 抵扣信息
    deduction_type = models.CharField('抵扣类型', max_length=20, choices=DEDUCTION_TYPES)
    deduction_period = models.CharField('抵扣期间', max_length=20, help_text='格式：YYYY-MM')
    deduction_amount = models.DecimalField('抵扣金额', max_digits=15, decimal_places=2)
    deduction_date = models.DateField('抵扣日期')
    
    # 申报信息
    declaration_form = models.CharField('申报表', max_length=100, blank=True)
    declaration_line = models.CharField('申报表行次', max_length=20, blank=True)
    
    # 备注信息
    notes = models.TextField('备注', blank=True)
    
    # 操作人员
    processed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='处理人')
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '发票抵扣记录'
        verbose_name_plural = '发票抵扣记录'
        ordering = ['-deduction_date']
    
    def __str__(self):
        return f"{self.invoice.invoice_number} - {self.deduction_period}"


class InvoiceTemplate(models.Model):
    """发票模板"""
    name = models.CharField('模板名称', max_length=100)
    invoice_type = models.ForeignKey(InvoiceType, on_delete=models.CASCADE, verbose_name='发票类型')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, verbose_name='适用企业')
    
    # 默认信息
    default_seller_info = models.JSONField('默认销售方信息', default=dict)
    default_buyer_info = models.JSONField('默认购买方信息', default=dict)
    default_items = models.JSONField('默认商品明细', default=list)
    
    # 配置信息
    auto_calculate_tax = models.BooleanField('自动计算税额', default=True)
    default_tax_rate = models.DecimalField('默认税率', max_digits=5, decimal_places=4, default=Decimal('0.13'))
    
    is_active = models.BooleanField('是否启用', default=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '发票模板'
        verbose_name_plural = '发票模板'
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} - {self.company.name}"


class InvoiceValidation(models.Model):
    """发票验证记录"""
    VALIDATION_TYPES = [
        ('format', '格式验证'),
        ('tax_bureau', '税局验证'),
        ('duplicate', '重复验证'),
        ('amount', '金额验证'),
    ]
    
    VALIDATION_RESULTS = [
        ('pass', '验证通过'),
        ('warning', '验证警告'),
        ('error', '验证错误'),
    ]
    
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='validations', verbose_name='发票')
    
    # 验证信息
    validation_type = models.CharField('验证类型', max_length=20, choices=VALIDATION_TYPES)
    validation_result = models.CharField('验证结果', max_length=20, choices=VALIDATION_RESULTS)
    validation_message = models.TextField('验证信息')
    
    # 验证详情
    validation_data = models.JSONField('验证数据', default=dict, help_text='验证过程中的详细数据')
    
    validated_at = models.DateTimeField('验证时间', auto_now_add=True)
    validated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='验证人')
    
    class Meta:
        verbose_name = '发票验证记录'
        verbose_name_plural = '发票验证记录'
        ordering = ['-validated_at']
    
    def __str__(self):
        return f"{self.invoice.invoice_number} - {self.get_validation_type_display()}"


class InvoiceStatistics(models.Model):
    """发票统计"""
    company = models.ForeignKey(Company, on_delete=models.CASCADE, verbose_name='企业')
    period = models.CharField('统计期间', max_length=20, help_text='格式：YYYY-MM')
    
    # 发票数量统计
    total_invoices = models.IntegerField('发票总数', default=0)
    inbound_invoices = models.IntegerField('进项发票数', default=0)
    outbound_invoices = models.IntegerField('销项发票数', default=0)
    authenticated_invoices = models.IntegerField('已认证发票数', default=0)
    deducted_invoices = models.IntegerField('已抵扣发票数', default=0)
    
    # 金额统计
    total_amount = models.DecimalField('总金额', max_digits=20, decimal_places=2, default=0)
    inbound_amount = models.DecimalField('进项金额', max_digits=20, decimal_places=2, default=0)
    outbound_amount = models.DecimalField('销项金额', max_digits=20, decimal_places=2, default=0)
    tax_amount = models.DecimalField('税额', max_digits=20, decimal_places=2, default=0)
    deducted_tax_amount = models.DecimalField('抵扣税额', max_digits=20, decimal_places=2, default=0)
    
    # 认证统计
    authentication_rate = models.DecimalField('认证率', max_digits=5, decimal_places=2, default=0)
    overdue_authentication_count = models.IntegerField('逾期认证数', default=0)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '发票统计'
        verbose_name_plural = '发票统计'
        unique_together = ['company', 'period']
        ordering = ['-period']
    
    def __str__(self):
        return f"{self.company.name} - {self.period}"


class InvoiceReminder(models.Model):
    """发票提醒"""
    REMINDER_TYPES = [
        ('authentication_deadline', '认证期限提醒'),
        ('deduction_deadline', '抵扣期限提醒'),
        ('overdue_authentication', '逾期认证提醒'),
        ('monthly_summary', '月度汇总提醒'),
    ]
    
    REMINDER_STATUS = [
        ('pending', '待发送'),
        ('sent', '已发送'),
        ('failed', '发送失败'),
    ]
    
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, null=True, blank=True, verbose_name='关联发票')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, verbose_name='企业')
    
    # 提醒信息
    reminder_type = models.CharField('提醒类型', max_length=30, choices=REMINDER_TYPES)
    title = models.CharField('提醒标题', max_length=200)
    content = models.TextField('提醒内容')
    
    # 发送配置
    recipients = models.JSONField('接收人', default=list, help_text='接收人用户ID列表')
    send_methods = models.JSONField('发送方式', default=list, help_text='发送方式列表')
    
    # 发送状态
    status = models.CharField('状态', max_length=20, choices=REMINDER_STATUS, default='pending')
    scheduled_time = models.DateTimeField('计划发送时间')
    sent_time = models.DateTimeField('实际发送时间', null=True, blank=True)
    error_message = models.TextField('错误信息', blank=True)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '发票提醒'
        verbose_name_plural = '发票提醒'
        ordering = ['-scheduled_time']
    
    def __str__(self):
        return f"{self.title} - {self.company.name}"

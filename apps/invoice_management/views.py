import logging
from django.core.exceptions import ValidationError, PermissionDenied
from django.http import JsonResponse
from rest_framework import status

logger = logging.getLogger(__name__)

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q, Sum, Count, Avg
from django.shortcuts import get_object_or_404
from datetime import datetime, timedelta, date
from decimal import Decimal
from .models import (
    InvoiceType, Invoice, InvoiceItem, InvoiceAuthentication,
    InvoiceDeduction, InvoiceTemplate, InvoiceValidation,
    InvoiceStatistics, InvoiceReminder
)
from .serializers import (
    InvoiceTypeSerializer, InvoiceSerializer, InvoiceItemSerializer,
    InvoiceAuthenticationSerializer, InvoiceDeductionSerializer,
    InvoiceTemplateSerializer, InvoiceValidationSerializer,
    InvoiceStatisticsSerializer, InvoiceReminderSerializer,
    InvoiceListSerializer, InvoiceCreateSerializer,
    InvoiceBatchOperationSerializer, InvoiceSearchSerializer
)
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class InvoiceTypeViewSet(viewsets.ModelViewSet):
    """发票类型ViewSet"""
    queryset = InvoiceType.objects.all()
    serializer_class = InvoiceTypeSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        try:
            queryset = super().get_queryset()
            return queryset.filter(is_active=True).order_by('name')


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class InvoiceViewSet(viewsets.ModelViewSet):
    """发票ViewSet"""
    queryset = Invoice.objects.all()
    serializer_class = InvoiceSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        try:
            queryset = super().get_queryset()
            user = self.request.user
        
            # 根据用户权限过滤
            if not user.is_staff:
                # 普通用户只能看到自己企业的发票
                queryset = queryset.filter(company__owner=user)
            else:
                # 管理员可以看到所有发票
                company_id = self.request.query_params.get('company')
                if company_id:
                    queryset = queryset.filter(company_id=company_id)
        
            # 应用搜索过滤器
            search_serializer = InvoiceSearchSerializer(data=self.request.query_params)
            if search_serializer.is_valid():
                filters = search_serializer.validated_data
            
                if filters.get('invoice_number'):
                    queryset = queryset.filter(invoice_number__icontains=filters['invoice_number'])
            
                if filters.get('invoice_code'):
                    queryset = queryset.filter(invoice_code__icontains=filters['invoice_code'])
            
                if filters.get('seller_name'):
                    queryset = queryset.filter(seller_name__icontains=filters['seller_name'])
            
                if filters.get('buyer_name'):
                    queryset = queryset.filter(buyer_name__icontains=filters['buyer_name'])
            
                if filters.get('tax_number'):
                    queryset = queryset.filter(
                        Q(seller_tax_number__icontains=filters['tax_number']) |
                        Q(buyer_tax_number__icontains=filters['tax_number'])
                    )
            
                if filters.get('direction'):
                    queryset = queryset.filter(direction=filters['direction'])
            
                if filters.get('status'):
                    queryset = queryset.filter(status=filters['status'])
            
                if filters.get('invoice_type'):
                    queryset = queryset.filter(invoice_type_id=filters['invoice_type'])
            
                if filters.get('issue_date_from'):
                    queryset = queryset.filter(issue_date__gte=filters['issue_date_from'])
            
                if filters.get('issue_date_to'):
                    queryset = queryset.filter(issue_date__lte=filters['issue_date_to'])
            
                if filters.get('amount_from'):
                    queryset = queryset.filter(total_amount__gte=filters['amount_from'])
            
                if filters.get('amount_to'):
                    queryset = queryset.filter(total_amount__lte=filters['amount_to'])
            
                if filters.get('authentication_overdue'):
                    # 这里需要在应用层过滤
                    overdue_invoices = []
                    for invoice in queryset:
                        if invoice.is_authentication_overdue():
                            overdue_invoices.append(invoice.id)
                    queryset = queryset.filter(id__in=overdue_invoices)
        
            return queryset.order_by('-issue_date', '-created_at')
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def get_serializer_class(self):
        try:
            if self.action == 'list':
                return InvoiceListSerializer
            elif self.action == 'create':
                return InvoiceCreateSerializer
            return InvoiceSerializer
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def perform_create(self, serializer):
        try:
            serializer.save(created_by=self.request.user)
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def authenticate(self, request, pk=None):
        """认证发票"""
        try:
            invoice = self.get_object()
        
            if not invoice.can_authenticate():
                return Response(
                    {'error': '发票不能认证'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
            # 创建认证记录
            authentication = InvoiceAuthentication.objects.create(
                invoice=invoice,
                authentication_method='manual',
                authentication_result='success',
                authentication_time=timezone.now(),
                authenticated_by=request.user
            )
        
            # 更新发票状态
            invoice.status = 'authenticated'
            invoice.authentication_date = timezone.now().date()
            invoice.authenticated_by = request.user
            invoice.save()
        
            return Response({'message': '发票认证成功'})
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def deduct(self, request, pk=None):
        """抵扣发票"""
        try:
            invoice = self.get_object()
        
            if not invoice.can_deduct():
                return Response(
                    {'error': '发票不能抵扣'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
            deduction_period = request.data.get('deduction_period')
            if not deduction_period:
                # 默认使用当前月份
                deduction_period = timezone.now().strftime('%Y-%m')
        
            # 创建抵扣记录
            deduction = InvoiceDeduction.objects.create(
                invoice=invoice,
                deduction_type='input_tax',
                deduction_period=deduction_period,
                deduction_amount=invoice.tax_amount,
                deduction_date=timezone.now().date(),
                processed_by=request.user
            )
        
            # 更新发票状态
            invoice.status = 'deducted'
            invoice.deduction_date = timezone.now().date()
            invoice.deduction_period = deduction_period
            invoice.deducted_amount = invoice.tax_amount
            invoice.save()
        
            return Response({'message': '发票抵扣成功'})
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def validate_invoice(self, request, pk=None):
        """验证发票"""
        try:
            invoice = self.get_object()
        
            validation_results = []
        
            # 格式验证
            format_result = self._validate_format(invoice)
            validation_results.append(format_result)
        
            # 金额验证
            amount_result = self._validate_amount(invoice)
            validation_results.append(amount_result)
        
            # 重复验证
            duplicate_result = self._validate_duplicate(invoice)
            validation_results.append(duplicate_result)
        
            # 保存验证记录
            for result in validation_results:
                InvoiceValidation.objects.create(
                    invoice=invoice,
                    validation_type=result['type'],
                    validation_result=result['result'],
                    validation_message=result['message'],
                    validation_data=result.get('data', {}),
                    validated_by=request.user
                )
        
            return Response({
                'message': '发票验证完成',
                'results': validation_results
            })
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _validate_format(self, invoice):
        """格式验证"""
        try:
            errors = []
        
            if not invoice.invoice_number or len(invoice.invoice_number) != 8:
                errors.append('发票号码格式不正确')
        
            if not invoice.invoice_code or len(invoice.invoice_code) != 10:
                errors.append('发票代码格式不正确')
        
            if not invoice.seller_tax_number or len(invoice.seller_tax_number) < 15:
                errors.append('销售方税号格式不正确')
        
            if not invoice.buyer_tax_number or len(invoice.buyer_tax_number) < 15:
                errors.append('购买方税号格式不正确')
        
            if errors:
                return {
                    'type': 'format',
                    'result': 'error',
                    'message': '; '.join(errors)
                }
            else:
                return {
                    'type': 'format',
                    'result': 'pass',
                    'message': '格式验证通过'
                }
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _validate_amount(self, invoice):
        """金额验证"""
        try:
            calculated_total = invoice.amount_without_tax + invoice.tax_amount
        
            if abs(calculated_total - invoice.total_amount) > Decimal('0.01'):
                return {
                    'type': 'amount',
                    'result': 'error',
                    'message': f'金额计算错误：不含税金额+税额={calculated_total}，但价税合计={invoice.total_amount}',
                    'data': {
                        'calculated_total': str(calculated_total),
                        'declared_total': str(invoice.total_amount)
                    }
                }
            else:
                return {
                    'type': 'amount',
                    'result': 'pass',
                    'message': '金额验证通过'
                }
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _validate_duplicate(self, invoice):
        """重复验证"""
        try:
            duplicate_count = Invoice.objects.filter(
                invoice_number=invoice.invoice_number,
                invoice_code=invoice.invoice_code
            ).exclude(id=invoice.id).count()
        
            if duplicate_count > 0:
                return {
                    'type': 'duplicate',
                    'result': 'error',
                    'message': f'发现 {duplicate_count} 张重复发票',
                    'data': {'duplicate_count': duplicate_count}
                }
            else:
                return {
                    'type': 'duplicate',
                    'result': 'pass',
                    'message': '重复验证通过'
                }
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=False, methods=['post'])
    def batch_operation(self, request):
        """批量操作"""
        try:
            serializer = InvoiceBatchOperationSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
            invoice_ids = serializer.validated_data['invoice_ids']
            operation = serializer.validated_data['operation']
            parameters = serializer.validated_data.get('parameters', {})
        
            invoices = Invoice.objects.filter(id__in=invoice_ids)
        
            if operation == 'authenticate':
                return self._batch_authenticate(invoices, request.user)
            elif operation == 'deduct':
                return self._batch_deduct(invoices, request.user, parameters)
            elif operation == 'validate':
                return self._batch_validate(invoices, request.user)
            elif operation == 'export':
                return self._batch_export(invoices, parameters)
            else:
                return Response(
                    {'error': '不支持的操作类型'},
                    status=status.HTTP_400_BAD_REQUEST
                )
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _batch_authenticate(self, invoices, user):
        """批量认证"""
        try:
            authenticated_count = 0
            errors = []
        
            for invoice in invoices:
                if invoice.can_authenticate():
                    InvoiceAuthentication.objects.create(
                        invoice=invoice,
                        authentication_method='batch',
                        authentication_result='success',
                        authentication_time=timezone.now(),
                        authenticated_by=user
                    )
                
                    invoice.status = 'authenticated'
                    invoice.authentication_date = timezone.now().date()
                    invoice.authenticated_by = user
                    invoice.save()
                
                    authenticated_count += 1
                else:
                    errors.append(f'发票 {invoice.invoice_number} 不能认证')
        
            return Response({
                'message': f'成功认证 {authenticated_count} 张发票',
                'authenticated_count': authenticated_count,
                'errors': errors
            })
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _batch_deduct(self, invoices, user, parameters):
        """批量抵扣"""
        try:
            deduction_period = parameters.get('deduction_period', timezone.now().strftime('%Y-%m'))
            deducted_count = 0
            errors = []
        
            for invoice in invoices:
                if invoice.can_deduct():
                    InvoiceDeduction.objects.create(
                        invoice=invoice,
                        deduction_type='input_tax',
                        deduction_period=deduction_period,
                        deduction_amount=invoice.tax_amount,
                        deduction_date=timezone.now().date(),
                        processed_by=user
                    )
                
                    invoice.status = 'deducted'
                    invoice.deduction_date = timezone.now().date()
                    invoice.deduction_period = deduction_period
                    invoice.deducted_amount = invoice.tax_amount
                    invoice.save()
                
                    deducted_count += 1
                else:
                    errors.append(f'发票 {invoice.invoice_number} 不能抵扣')
        
            return Response({
                'message': f'成功抵扣 {deducted_count} 张发票',
                'deducted_count': deducted_count,
                'errors': errors
            })
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _batch_validate(self, invoices, user):
        """批量验证"""
        try:
            validated_count = 0
        
            for invoice in invoices:
                # 执行验证逻辑（简化版）
                InvoiceValidation.objects.create(
                    invoice=invoice,
                    validation_type='format',
                    validation_result='pass',
                    validation_message='批量验证通过',
                    validated_by=user
                )
                validated_count += 1
        
            return Response({
                'message': f'成功验证 {validated_count} 张发票',
                'validated_count': validated_count
            })
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _batch_export(self, invoices, parameters):
        """批量导出"""
        try:
            # 这里可以实现导出逻辑
            export_format = parameters.get('format', 'excel')
        
            return Response({
                'message': f'成功导出 {invoices.count()} 张发票',
                'export_count': invoices.count(),
                'format': export_format
            })
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """发票仪表板"""
        try:
            user = request.user
        
            # 基础统计
            if user.is_staff:
                queryset = Invoice.objects.all()
            else:
                queryset = Invoice.objects.filter(company__owner=user)
        
            total_invoices = queryset.count()
            inbound_invoices = queryset.filter(direction='inbound').count()
            outbound_invoices = queryset.filter(direction='outbound').count()
        
            # 状态统计
            status_stats = {}
            for status_code, status_name in Invoice.STATUS_CHOICES:
                status_stats[status_code] = queryset.filter(status=status_code).count()
        
            # 金额统计
            amount_stats = queryset.aggregate(
                total_amount=Sum('total_amount'),
                total_tax=Sum('tax_amount'),
                deducted_tax=Sum('deducted_amount')
            )
        
            # 认证统计
            authentication_required = queryset.filter(
                direction='inbound',
                invoice_type__requires_authentication=True
            )
            authenticated = authentication_required.filter(status='authenticated')
            authentication_rate = 0
            if authentication_required.count() > 0:
                authentication_rate = (authenticated.count() / authentication_required.count()) * 100
        
            # 逾期统计
            overdue_count = 0
            for invoice in authentication_required.filter(status__in=['received']):
                if invoice.is_authentication_overdue():
                    overdue_count += 1
        
            # 本月统计
            today = timezone.now().date()
            month_start = today.replace(day=1)
            month_invoices = queryset.filter(issue_date__gte=month_start).count()
        
            return Response({
                'total_invoices': total_invoices,
                'inbound_invoices': inbound_invoices,
                'outbound_invoices': outbound_invoices,
                'status_stats': status_stats,
                'amount_stats': {
                    'total_amount': amount_stats['total_amount'] or 0,
                    'total_tax': amount_stats['total_tax'] or 0,
                    'deducted_tax': amount_stats['deducted_tax'] or 0,
                },
                'authentication_rate': round(authentication_rate, 2),
                'overdue_count': overdue_count,
                'month_invoices': month_invoices,
            })
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=False, methods=['get'])
    def authentication_alerts(self, request):
        """认证预警"""
        try:
            user = request.user
        
            if user.is_staff:
                queryset = Invoice.objects.all()
            else:
                queryset = Invoice.objects.filter(company__owner=user)
        
            # 获取需要认证的发票
            authentication_required = queryset.filter(
                direction='inbound',
                invoice_type__requires_authentication=True,
                status='received'
            )
        
            alerts = []
            for invoice in authentication_required:
                days_left = invoice.days_until_authentication_deadline()
                if days_left is not None:
                    if days_left < 0:
                        alert_level = 'danger'
                        message = f'已逾期 {-days_left} 天'
                    elif days_left <= 7:
                        alert_level = 'warning'
                        message = f'{days_left} 天后到期'
                    elif days_left <= 30:
                        alert_level = 'info'
                        message = f'{days_left} 天后到期'
                    else:
                        continue
                
                    alerts.append({
                        'invoice_id': invoice.id,
                        'invoice_number': invoice.invoice_number,
                        'seller_name': invoice.seller_name,
                        'total_amount': invoice.total_amount,
                        'authentication_deadline': invoice.authentication_deadline,
                        'days_left': days_left,
                        'alert_level': alert_level,
                        'message': message
                    })
        
            # 按紧急程度排序
            alerts.sort(key=lambda x: x['days_left'])
        
            return Response({
                'alerts': alerts,
                'total_count': len(alerts),
                'danger_count': len([a for a in alerts if a['alert_level'] == 'danger']),
                'warning_count': len([a for a in alerts if a['alert_level'] == 'warning']),
            })


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class InvoiceItemViewSet(viewsets.ModelViewSet):
    """发票明细ViewSet"""
    queryset = InvoiceItem.objects.all()
    serializer_class = InvoiceItemSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        try:
            queryset = super().get_queryset()
            user = self.request.user

            # 根据用户权限过滤
            if not user.is_staff:
                queryset = queryset.filter(invoice__company__owner=user)

            # 按发票过滤
            invoice_id = self.request.query_params.get('invoice')
            if invoice_id:
                queryset = queryset.filter(invoice_id=invoice_id)

            return queryset.order_by('id')


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class InvoiceAuthenticationViewSet(viewsets.ReadOnlyModelViewSet):
    """发票认证记录ViewSet"""
    queryset = InvoiceAuthentication.objects.all()
    serializer_class = InvoiceAuthenticationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        try:
            queryset = super().get_queryset()
            user = self.request.user

            # 根据用户权限过滤
            if not user.is_staff:
                queryset = queryset.filter(invoice__company__owner=user)

            # 按发票过滤
            invoice_id = self.request.query_params.get('invoice')
            if invoice_id:
                queryset = queryset.filter(invoice_id=invoice_id)

            # 按认证结果过滤
            result = self.request.query_params.get('result')
            if result:
                queryset = queryset.filter(authentication_result=result)

            return queryset.order_by('-authentication_time')


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class InvoiceDeductionViewSet(viewsets.ReadOnlyModelViewSet):
    """发票抵扣记录ViewSet"""
    queryset = InvoiceDeduction.objects.all()
    serializer_class = InvoiceDeductionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        try:
            queryset = super().get_queryset()
            user = self.request.user

            # 根据用户权限过滤
            if not user.is_staff:
                queryset = queryset.filter(invoice__company__owner=user)

            # 按发票过滤
            invoice_id = self.request.query_params.get('invoice')
            if invoice_id:
                queryset = queryset.filter(invoice_id=invoice_id)

            # 按抵扣期间过滤
            period = self.request.query_params.get('period')
            if period:
                queryset = queryset.filter(deduction_period=period)

            return queryset.order_by('-deduction_date')


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class InvoiceTemplateViewSet(viewsets.ModelViewSet):
    """发票模板ViewSet"""
    queryset = InvoiceTemplate.objects.all()
    serializer_class = InvoiceTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        try:
            queryset = super().get_queryset()
            user = self.request.user

            # 根据用户权限过滤
            if not user.is_staff:
                queryset = queryset.filter(company__owner=user)

            # 按企业过滤
            company_id = self.request.query_params.get('company')
            if company_id:
                queryset = queryset.filter(company_id=company_id)

            # 按发票类型过滤
            invoice_type = self.request.query_params.get('invoice_type')
            if invoice_type:
                queryset = queryset.filter(invoice_type_id=invoice_type)

            return queryset.filter(is_active=True).order_by('name')

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def perform_create(self, serializer):
        try:
            serializer.save(created_by=self.request.user)


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class InvoiceValidationViewSet(viewsets.ReadOnlyModelViewSet):
    """发票验证记录ViewSet"""
    queryset = InvoiceValidation.objects.all()
    serializer_class = InvoiceValidationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        try:
            queryset = super().get_queryset()
            user = self.request.user

            # 根据用户权限过滤
            if not user.is_staff:
                queryset = queryset.filter(invoice__company__owner=user)

            # 按发票过滤
            invoice_id = self.request.query_params.get('invoice')
            if invoice_id:
                queryset = queryset.filter(invoice_id=invoice_id)

            # 按验证结果过滤
            result = self.request.query_params.get('result')
            if result:
                queryset = queryset.filter(validation_result=result)

            return queryset.order_by('-validated_at')


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class InvoiceStatisticsViewSet(viewsets.ReadOnlyModelViewSet):
    """发票统计ViewSet"""
    queryset = InvoiceStatistics.objects.all()
    serializer_class = InvoiceStatisticsSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        try:
            queryset = super().get_queryset()
            user = self.request.user

            # 根据用户权限过滤
            if not user.is_staff:
                queryset = queryset.filter(company__owner=user)

            # 按企业过滤
            company_id = self.request.query_params.get('company')
            if company_id:
                queryset = queryset.filter(company_id=company_id)

            # 按期间过滤
            period = self.request.query_params.get('period')
            if period:
                queryset = queryset.filter(period=period)

            return queryset.order_by('-period')


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class InvoiceReminderViewSet(viewsets.ModelViewSet):
    """发票提醒ViewSet"""
    queryset = InvoiceReminder.objects.all()
    serializer_class = InvoiceReminderSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        try:
            queryset = super().get_queryset()
            user = self.request.user

            # 根据用户权限过滤
            if not user.is_staff:
                queryset = queryset.filter(company__owner=user)

            # 按企业过滤
            company_id = self.request.query_params.get('company')
            if company_id:
                queryset = queryset.filter(company_id=company_id)

            # 按提醒类型过滤
            reminder_type = self.request.query_params.get('reminder_type')
            if reminder_type:
                queryset = queryset.filter(reminder_type=reminder_type)

            # 按状态过滤
            status_filter = self.request.query_params.get('status')
            if status_filter:
                queryset = queryset.filter(status=status_filter)

            return queryset.order_by('-scheduled_time')

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    InvoiceType, Invoice, InvoiceItem, InvoiceAuthentication,
    InvoiceDeduction, InvoiceTemplate, InvoiceValidation,
    InvoiceStatistics, InvoiceReminder
)

User = get_user_model()


class InvoiceTypeSerializer(serializers.ModelSerializer):
    """发票类型序列化器"""
    type_category_display = serializers.CharField(source='get_type_category_display', read_only=True)
    
    class Meta:
        model = InvoiceType
        fields = [
            'id', 'name', 'code', 'type_category', 'type_category_display',
            'description', 'can_deduct', 'requires_authentication',
            'authentication_deadline', 'default_tax_rates', 'is_active',
            'created_at', 'updated_at'
        ]


class InvoiceItemSerializer(serializers.ModelSerializer):
    """发票明细序列化器"""
    tax_rate_percentage = serializers.SerializerMethodField()
    
    class Meta:
        model = InvoiceItem
        fields = [
            'id', 'item_name', 'specification', 'unit', 'quantity',
            'unit_price', 'amount', 'tax_rate', 'tax_rate_percentage',
            'tax_amount', 'category_code', 'category_name', 'created_at'
        ]
    
    def get_tax_rate_percentage(self, obj):
        return float(obj.tax_rate) * 100


class InvoiceSerializer(serializers.ModelSerializer):
    """发票序列化器"""
    invoice_type_name = serializers.CharField(source='invoice_type.name', read_only=True)
    company_name = serializers.CharField(source='company.name', read_only=True)
    direction_display = serializers.CharField(source='get_direction_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    authenticated_by_name = serializers.CharField(source='authenticated_by.get_full_name', read_only=True)
    reviewed_by_name = serializers.CharField(source='reviewed_by.get_full_name', read_only=True)
    
    # 计算字段
    is_authentication_overdue = serializers.SerializerMethodField()
    days_until_authentication_deadline = serializers.SerializerMethodField()
    can_authenticate = serializers.SerializerMethodField()
    can_deduct = serializers.SerializerMethodField()
    
    # 发票明细
    items = InvoiceItemSerializer(many=True, read_only=True)
    
    class Meta:
        model = Invoice
        fields = [
            'id', 'invoice_number', 'invoice_code', 'invoice_type',
            'invoice_type_name', 'direction', 'direction_display',
            'issue_date', 'seller_name', 'seller_tax_number',
            'seller_address', 'seller_phone', 'seller_bank', 'seller_account',
            'buyer_name', 'buyer_tax_number', 'buyer_address',
            'buyer_phone', 'buyer_bank', 'buyer_account',
            'total_amount', 'tax_amount', 'amount_without_tax',
            'status', 'status_display', 'company', 'company_name',
            'authentication_date', 'authentication_deadline', 'authenticated_by',
            'authenticated_by_name', 'deduction_date', 'deduction_period',
            'deducted_amount', 'remarks', 'attachments',
            'reviewed_by', 'reviewed_by_name', 'reviewed_at', 'review_notes',
            'created_by', 'created_by_name', 'created_at', 'updated_at',
            'is_authentication_overdue', 'days_until_authentication_deadline',
            'can_authenticate', 'can_deduct', 'items'
        ]
    
    def get_is_authentication_overdue(self, obj):
        return obj.is_authentication_overdue()
    
    def get_days_until_authentication_deadline(self, obj):
        return obj.days_until_authentication_deadline()
    
    def get_can_authenticate(self, obj):
        return obj.can_authenticate()
    
    def get_can_deduct(self, obj):
        return obj.can_deduct()


class InvoiceAuthenticationSerializer(serializers.ModelSerializer):
    """发票认证记录序列化器"""
    invoice_number = serializers.CharField(source='invoice.invoice_number', read_only=True)
    authenticated_by_name = serializers.CharField(source='authenticated_by.get_full_name', read_only=True)
    authentication_method_display = serializers.CharField(source='get_authentication_method_display', read_only=True)
    authentication_result_display = serializers.CharField(source='get_authentication_result_display', read_only=True)
    
    class Meta:
        model = InvoiceAuthentication
        fields = [
            'id', 'invoice', 'invoice_number', 'authentication_method',
            'authentication_method_display', 'authentication_result',
            'authentication_result_display', 'authentication_time',
            'authentication_code', 'error_message', 'retry_count',
            'authenticated_by', 'authenticated_by_name', 'created_at'
        ]


class InvoiceDeductionSerializer(serializers.ModelSerializer):
    """发票抵扣记录序列化器"""
    invoice_number = serializers.CharField(source='invoice.invoice_number', read_only=True)
    processed_by_name = serializers.CharField(source='processed_by.get_full_name', read_only=True)
    deduction_type_display = serializers.CharField(source='get_deduction_type_display', read_only=True)
    
    class Meta:
        model = InvoiceDeduction
        fields = [
            'id', 'invoice', 'invoice_number', 'deduction_type',
            'deduction_type_display', 'deduction_period', 'deduction_amount',
            'deduction_date', 'declaration_form', 'declaration_line',
            'notes', 'processed_by', 'processed_by_name', 'created_at'
        ]


class InvoiceTemplateSerializer(serializers.ModelSerializer):
    """发票模板序列化器"""
    invoice_type_name = serializers.CharField(source='invoice_type.name', read_only=True)
    company_name = serializers.CharField(source='company.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    default_tax_rate_percentage = serializers.SerializerMethodField()
    
    class Meta:
        model = InvoiceTemplate
        fields = [
            'id', 'name', 'invoice_type', 'invoice_type_name',
            'company', 'company_name', 'default_seller_info',
            'default_buyer_info', 'default_items', 'auto_calculate_tax',
            'default_tax_rate', 'default_tax_rate_percentage', 'is_active',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
    
    def get_default_tax_rate_percentage(self, obj):
        return float(obj.default_tax_rate) * 100


class InvoiceValidationSerializer(serializers.ModelSerializer):
    """发票验证记录序列化器"""
    invoice_number = serializers.CharField(source='invoice.invoice_number', read_only=True)
    validated_by_name = serializers.CharField(source='validated_by.get_full_name', read_only=True)
    validation_type_display = serializers.CharField(source='get_validation_type_display', read_only=True)
    validation_result_display = serializers.CharField(source='get_validation_result_display', read_only=True)
    
    class Meta:
        model = InvoiceValidation
        fields = [
            'id', 'invoice', 'invoice_number', 'validation_type',
            'validation_type_display', 'validation_result',
            'validation_result_display', 'validation_message',
            'validation_data', 'validated_at', 'validated_by',
            'validated_by_name'
        ]


class InvoiceStatisticsSerializer(serializers.ModelSerializer):
    """发票统计序列化器"""
    company_name = serializers.CharField(source='company.name', read_only=True)
    
    class Meta:
        model = InvoiceStatistics
        fields = [
            'id', 'company', 'company_name', 'period',
            'total_invoices', 'inbound_invoices', 'outbound_invoices',
            'authenticated_invoices', 'deducted_invoices',
            'total_amount', 'inbound_amount', 'outbound_amount',
            'tax_amount', 'deducted_tax_amount', 'authentication_rate',
            'overdue_authentication_count', 'created_at', 'updated_at'
        ]


class InvoiceReminderSerializer(serializers.ModelSerializer):
    """发票提醒序列化器"""
    invoice_number = serializers.CharField(source='invoice.invoice_number', read_only=True)
    company_name = serializers.CharField(source='company.name', read_only=True)
    reminder_type_display = serializers.CharField(source='get_reminder_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = InvoiceReminder
        fields = [
            'id', 'invoice', 'invoice_number', 'company', 'company_name',
            'reminder_type', 'reminder_type_display', 'title', 'content',
            'recipients', 'send_methods', 'status', 'status_display',
            'scheduled_time', 'sent_time', 'error_message', 'created_at'
        ]


# 简化版序列化器用于列表显示
class InvoiceListSerializer(serializers.ModelSerializer):
    """发票列表序列化器"""
    invoice_type_name = serializers.CharField(source='invoice_type.name', read_only=True)
    company_name = serializers.CharField(source='company.name', read_only=True)
    direction_display = serializers.CharField(source='get_direction_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    authentication_status = serializers.SerializerMethodField()
    
    class Meta:
        model = Invoice
        fields = [
            'id', 'invoice_number', 'invoice_code', 'invoice_type_name',
            'direction', 'direction_display', 'seller_name', 'buyer_name',
            'total_amount', 'tax_amount', 'status', 'status_display',
            'company_name', 'issue_date', 'authentication_status'
        ]
    
    def get_authentication_status(self, obj):
        if obj.direction == 'outbound':
            return None
        
        if not obj.invoice_type.requires_authentication:
            return {'status': 'not_required', 'message': '无需认证'}
        
        if obj.status == 'authenticated':
            return {'status': 'authenticated', 'message': '已认证'}
        elif obj.is_authentication_overdue():
            return {'status': 'overdue', 'message': '已逾期'}
        else:
            days_left = obj.days_until_authentication_deadline()
            if days_left is not None:
                return {
                    'status': 'pending',
                    'message': f'{days_left}天后到期',
                    'days_left': days_left
                }
            return {'status': 'pending', 'message': '待认证'}


class InvoiceCreateSerializer(serializers.ModelSerializer):
    """发票创建序列化器"""
    items = InvoiceItemSerializer(many=True, required=False)
    
    class Meta:
        model = Invoice
        fields = [
            'invoice_number', 'invoice_code', 'invoice_type', 'direction',
            'issue_date', 'seller_name', 'seller_tax_number',
            'seller_address', 'seller_phone', 'seller_bank', 'seller_account',
            'buyer_name', 'buyer_tax_number', 'buyer_address',
            'buyer_phone', 'buyer_bank', 'buyer_account',
            'total_amount', 'tax_amount', 'amount_without_tax',
            'company', 'remarks', 'attachments', 'items'
        ]
    
    def create(self, validated_data):
        items_data = validated_data.pop('items', [])
        invoice = Invoice.objects.create(**validated_data)
        
        for item_data in items_data:
            InvoiceItem.objects.create(invoice=invoice, **item_data)
        
        # 设置认证期限
        if (invoice.direction == 'inbound' and 
            invoice.invoice_type.requires_authentication):
            from datetime import timedelta
            invoice.authentication_deadline = (
                invoice.issue_date + 
                timedelta(days=invoice.invoice_type.authentication_deadline)
            )
            invoice.save()
        
        return invoice


class InvoiceBatchOperationSerializer(serializers.Serializer):
    """发票批量操作序列化器"""
    invoice_ids = serializers.ListField(
        child=serializers.IntegerField(),
        min_length=1,
        help_text='发票ID列表'
    )
    operation = serializers.ChoiceField(
        choices=[
            ('authenticate', '批量认证'),
            ('deduct', '批量抵扣'),
            ('validate', '批量验证'),
            ('export', '批量导出'),
        ],
        help_text='操作类型'
    )
    parameters = serializers.JSONField(
        required=False,
        default=dict,
        help_text='操作参数'
    )


class InvoiceSearchSerializer(serializers.Serializer):
    """发票搜索序列化器"""
    invoice_number = serializers.CharField(required=False, help_text='发票号码')
    invoice_code = serializers.CharField(required=False, help_text='发票代码')
    seller_name = serializers.CharField(required=False, help_text='销售方名称')
    buyer_name = serializers.CharField(required=False, help_text='购买方名称')
    tax_number = serializers.CharField(required=False, help_text='税号')
    direction = serializers.ChoiceField(
        choices=Invoice.DIRECTION_CHOICES,
        required=False,
        help_text='发票方向'
    )
    status = serializers.ChoiceField(
        choices=Invoice.STATUS_CHOICES,
        required=False,
        help_text='发票状态'
    )
    invoice_type = serializers.IntegerField(required=False, help_text='发票类型ID')
    company = serializers.IntegerField(required=False, help_text='企业ID')
    issue_date_from = serializers.DateField(required=False, help_text='开票日期起')
    issue_date_to = serializers.DateField(required=False, help_text='开票日期止')
    amount_from = serializers.DecimalField(
        max_digits=15, decimal_places=2,
        required=False, help_text='金额起'
    )
    amount_to = serializers.DecimalField(
        max_digits=15, decimal_places=2,
        required=False, help_text='金额止'
    )
    authentication_overdue = serializers.BooleanField(
        required=False, help_text='是否认证逾期'
    )

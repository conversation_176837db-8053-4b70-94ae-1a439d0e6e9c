from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.db.models import Sum, Count
from .models import (
    InvoiceType, Invoice, InvoiceItem, InvoiceAuthentication,
    InvoiceDeduction, InvoiceTemplate, InvoiceValidation,
    InvoiceStatistics, InvoiceReminder
)


@admin.register(InvoiceType)
class InvoiceTypeAdmin(admin.ModelAdmin):
    """发票类型管理"""
    list_display = [
        'name', 'code', 'type_category_display', 'can_deduct',
        'requires_authentication', 'authentication_deadline', 'is_active'
    ]
    list_filter = ['type_category', 'can_deduct', 'requires_authentication', 'is_active']
    search_fields = ['name', 'code', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'code', 'type_category', 'description')
        }),
        ('配置信息', {
            'fields': ('can_deduct', 'requires_authentication', 'authentication_deadline')
        }),
        ('税率配置', {
            'fields': ('default_tax_rates',)
        }),
        ('状态', {
            'fields': ('is_active',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def type_category_display(self, obj):
        """发票分类显示"""
        category_colors = {
            'vat_special': '#f5222d',
            'vat_ordinary': '#1890ff',
            'vat_electronic': '#52c41a',
            'receipt': '#faad14',
            'other': '#d9d9d9',
        }
        color = category_colors.get(obj.type_category, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_type_category_display()
        )
    type_category_display.short_description = '发票分类'


class InvoiceItemInline(admin.TabularInline):
    """发票明细内联"""
    model = InvoiceItem
    extra = 1
    fields = ['item_name', 'specification', 'unit', 'quantity', 'unit_price', 'amount', 'tax_rate', 'tax_amount']
    readonly_fields = []


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    """发票管理"""
    list_display = [
        'invoice_number', 'invoice_code', 'seller_name', 'buyer_name',
        'direction_display', 'status_display', 'total_amount', 'issue_date',
        'authentication_status', 'created_at'
    ]
    list_filter = [
        'direction', 'status', 'invoice_type', 'issue_date',
        'authentication_date', 'company'
    ]
    search_fields = [
        'invoice_number', 'invoice_code', 'seller_name', 'buyer_name',
        'seller_tax_number', 'buyer_tax_number'
    ]
    readonly_fields = ['created_at', 'updated_at', 'authentication_status']
    date_hierarchy = 'issue_date'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('invoice_number', 'invoice_code', 'invoice_type', 'direction', 'issue_date')
        }),
        ('销售方信息', {
            'fields': ('seller_name', 'seller_tax_number', 'seller_address', 'seller_phone', 'seller_bank', 'seller_account')
        }),
        ('购买方信息', {
            'fields': ('buyer_name', 'buyer_tax_number', 'buyer_address', 'buyer_phone', 'buyer_bank', 'buyer_account')
        }),
        ('金额信息', {
            'fields': ('total_amount', 'tax_amount', 'amount_without_tax')
        }),
        ('状态和关联', {
            'fields': ('status', 'company')
        }),
        ('认证信息', {
            'fields': ('authentication_date', 'authentication_deadline', 'authenticated_by'),
            'classes': ('collapse',)
        }),
        ('抵扣信息', {
            'fields': ('deduction_date', 'deduction_period', 'deducted_amount'),
            'classes': ('collapse',)
        }),
        ('审核信息', {
            'fields': ('reviewed_by', 'reviewed_at', 'review_notes'),
            'classes': ('collapse',)
        }),
        ('附加信息', {
            'fields': ('remarks', 'attachments'),
            'classes': ('collapse',)
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    inlines = [InvoiceItemInline]
    actions = ['authenticate_invoices', 'mark_deducted', 'validate_invoices']
    
    def direction_display(self, obj):
        """发票方向显示"""
        direction_colors = {
            'inbound': '#52c41a',
            'outbound': '#1890ff',
        }
        color = direction_colors.get(obj.direction, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_direction_display()
        )
    direction_display.short_description = '发票方向'
    
    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'draft': '#d9d9d9',
            'issued': '#1890ff',
            'received': '#faad14',
            'authenticated': '#52c41a',
            'deducted': '#722ed1',
            'invalid': '#f5222d',
            'red_flushed': '#fa8c16',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'
    
    def authentication_status(self, obj):
        """认证状态"""
        if obj.direction == 'outbound':
            return '-'
        
        if not obj.invoice_type.requires_authentication:
            return format_html('<span style="color: #d9d9d9;">无需认证</span>')
        
        if obj.status == 'authenticated':
            return format_html('<span style="color: #52c41a;">✓ 已认证</span>')
        elif obj.is_authentication_overdue():
            return format_html('<span style="color: #f5222d;">✗ 已逾期</span>')
        else:
            days_left = obj.days_until_authentication_deadline()
            if days_left is not None:
                if days_left <= 7:
                    color = '#fa8c16'
                elif days_left <= 30:
                    color = '#faad14'
                else:
                    color = '#1890ff'
                return format_html(
                    '<span style="color: {};">○ {}天后到期</span>',
                    color, days_left
                )
            return format_html('<span style="color: #d9d9d9;">待认证</span>')
    authentication_status.short_description = '认证状态'
    
    def authenticate_invoices(self, request, queryset):
        """批量认证发票"""
        authenticated_count = 0
        for invoice in queryset:
            if invoice.can_authenticate():
                invoice.status = 'authenticated'
                invoice.authentication_date = timezone.now().date()
                invoice.authenticated_by = request.user
                invoice.save()
                authenticated_count += 1
        
        self.message_user(request, f"成功认证 {authenticated_count} 张发票")
    authenticate_invoices.short_description = "批量认证发票"
    
    def mark_deducted(self, request, queryset):
        """标记为已抵扣"""
        deducted_count = 0
        for invoice in queryset:
            if invoice.can_deduct():
                invoice.status = 'deducted'
                invoice.deduction_date = timezone.now().date()
                invoice.deducted_amount = invoice.tax_amount
                invoice.save()
                deducted_count += 1
        
        self.message_user(request, f"成功标记 {deducted_count} 张发票为已抵扣")
    mark_deducted.short_description = "标记为已抵扣"
    
    def validate_invoices(self, request, queryset):
        """验证发票"""
        validated_count = queryset.count()
        # 这里可以实现具体的验证逻辑
        self.message_user(request, f"已验证 {validated_count} 张发票")
    validate_invoices.short_description = "验证发票"
    
    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(InvoiceItem)
class InvoiceItemAdmin(admin.ModelAdmin):
    """发票明细管理"""
    list_display = [
        'invoice_info', 'item_name', 'specification', 'quantity',
        'unit_price', 'amount', 'tax_rate_display', 'tax_amount'
    ]
    list_filter = ['tax_rate', 'category_name', 'created_at']
    search_fields = ['item_name', 'specification', 'category_name', 'invoice__invoice_number']
    readonly_fields = ['created_at']
    
    def invoice_info(self, obj):
        """发票信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:invoice_management_invoice_change', args=[obj.invoice.pk]),
            obj.invoice.invoice_number
        )
    invoice_info.short_description = '发票'
    
    def tax_rate_display(self, obj):
        """税率显示"""
        rate_percentage = float(obj.tax_rate) * 100
        return f"{rate_percentage:.2f}%"
    tax_rate_display.short_description = '税率'


@admin.register(InvoiceAuthentication)
class InvoiceAuthenticationAdmin(admin.ModelAdmin):
    """发票认证记录管理"""
    list_display = [
        'invoice_info', 'authentication_method_display', 'authentication_result_display',
        'authentication_time', 'authenticated_by', 'retry_count'
    ]
    list_filter = ['authentication_method', 'authentication_result', 'authentication_time']
    search_fields = ['invoice__invoice_number', 'authentication_code', 'error_message']
    readonly_fields = ['created_at']
    
    def invoice_info(self, obj):
        """发票信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:invoice_management_invoice_change', args=[obj.invoice.pk]),
            obj.invoice.invoice_number
        )
    invoice_info.short_description = '发票'
    
    def authentication_method_display(self, obj):
        """认证方式显示"""
        method_colors = {
            'online': '#52c41a',
            'manual': '#1890ff',
            'scan': '#faad14',
            'batch': '#722ed1',
        }
        color = method_colors.get(obj.authentication_method, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_authentication_method_display()
        )
    authentication_method_display.short_description = '认证方式'
    
    def authentication_result_display(self, obj):
        """认证结果显示"""
        result_colors = {
            'success': '#52c41a',
            'failed': '#f5222d',
            'pending': '#faad14',
            'timeout': '#fa8c16',
        }
        color = result_colors.get(obj.authentication_result, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_authentication_result_display()
        )
    authentication_result_display.short_description = '认证结果'


@admin.register(InvoiceDeduction)
class InvoiceDeductionAdmin(admin.ModelAdmin):
    """发票抵扣记录管理"""
    list_display = [
        'invoice_info', 'deduction_type_display', 'deduction_period',
        'deduction_amount', 'deduction_date', 'processed_by'
    ]
    list_filter = ['deduction_type', 'deduction_period', 'deduction_date']
    search_fields = ['invoice__invoice_number', 'deduction_period', 'declaration_form']
    readonly_fields = ['created_at']
    
    def invoice_info(self, obj):
        """发票信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:invoice_management_invoice_change', args=[obj.invoice.pk]),
            obj.invoice.invoice_number
        )
    invoice_info.short_description = '发票'
    
    def deduction_type_display(self, obj):
        """抵扣类型显示"""
        type_colors = {
            'input_tax': '#52c41a',
            'output_tax': '#1890ff',
            'transfer': '#faad14',
            'adjustment': '#722ed1',
        }
        color = type_colors.get(obj.deduction_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_deduction_type_display()
        )
    deduction_type_display.short_description = '抵扣类型'


@admin.register(InvoiceTemplate)
class InvoiceTemplateAdmin(admin.ModelAdmin):
    """发票模板管理"""
    list_display = [
        'name', 'invoice_type', 'company', 'default_tax_rate_display',
        'auto_calculate_tax', 'is_active', 'created_by'
    ]
    list_filter = ['invoice_type', 'company', 'auto_calculate_tax', 'is_active']
    search_fields = ['name', 'company__name']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'invoice_type', 'company')
        }),
        ('默认信息', {
            'fields': ('default_seller_info', 'default_buyer_info', 'default_items'),
            'classes': ('wide',)
        }),
        ('配置信息', {
            'fields': ('auto_calculate_tax', 'default_tax_rate')
        }),
        ('状态', {
            'fields': ('is_active',)
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def default_tax_rate_display(self, obj):
        """默认税率显示"""
        rate_percentage = float(obj.default_tax_rate) * 100
        return f"{rate_percentage:.2f}%"
    default_tax_rate_display.short_description = '默认税率'
    
    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(InvoiceValidation)
class InvoiceValidationAdmin(admin.ModelAdmin):
    """发票验证记录管理"""
    list_display = [
        'invoice_info', 'validation_type_display', 'validation_result_display',
        'validation_message_preview', 'validated_at', 'validated_by'
    ]
    list_filter = ['validation_type', 'validation_result', 'validated_at']
    search_fields = ['invoice__invoice_number', 'validation_message']
    readonly_fields = ['validated_at']
    
    def invoice_info(self, obj):
        """发票信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:invoice_management_invoice_change', args=[obj.invoice.pk]),
            obj.invoice.invoice_number
        )
    invoice_info.short_description = '发票'
    
    def validation_type_display(self, obj):
        """验证类型显示"""
        type_colors = {
            'format': '#1890ff',
            'tax_bureau': '#52c41a',
            'duplicate': '#faad14',
            'amount': '#722ed1',
        }
        color = type_colors.get(obj.validation_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_validation_type_display()
        )
    validation_type_display.short_description = '验证类型'
    
    def validation_result_display(self, obj):
        """验证结果显示"""
        result_colors = {
            'pass': '#52c41a',
            'warning': '#faad14',
            'error': '#f5222d',
        }
        color = result_colors.get(obj.validation_result, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_validation_result_display()
        )
    validation_result_display.short_description = '验证结果'
    
    def validation_message_preview(self, obj):
        """验证信息预览"""
        if len(obj.validation_message) > 50:
            return obj.validation_message[:50] + '...'
        return obj.validation_message
    validation_message_preview.short_description = '验证信息'


@admin.register(InvoiceStatistics)
class InvoiceStatisticsAdmin(admin.ModelAdmin):
    """发票统计管理"""
    list_display = [
        'company', 'period', 'total_invoices', 'inbound_invoices',
        'outbound_invoices', 'total_amount_display', 'authentication_rate_display'
    ]
    list_filter = ['period', 'company']
    search_fields = ['company__name', 'period']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('company', 'period')
        }),
        ('数量统计', {
            'fields': ('total_invoices', 'inbound_invoices', 'outbound_invoices',
                      'authenticated_invoices', 'deducted_invoices')
        }),
        ('金额统计', {
            'fields': ('total_amount', 'inbound_amount', 'outbound_amount',
                      'tax_amount', 'deducted_tax_amount')
        }),
        ('认证统计', {
            'fields': ('authentication_rate', 'overdue_authentication_count')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def total_amount_display(self, obj):
        """总金额显示"""
        return format_html(
            '<span style="font-weight: bold;">¥{:,.2f}</span>',
            float(obj.total_amount)
        )
    total_amount_display.short_description = '总金额'

    def authentication_rate_display(self, obj):
        """认证率显示"""
        rate = float(obj.authentication_rate)
        if rate >= 90:
            color = '#52c41a'
        elif rate >= 70:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}%</span>',
            color, rate
        )
    authentication_rate_display.short_description = '认证率'


@admin.register(InvoiceReminder)
class InvoiceReminderAdmin(admin.ModelAdmin):
    """发票提醒管理"""
    list_display = [
        'title', 'company', 'reminder_type_display', 'status_display',
        'scheduled_time', 'sent_time'
    ]
    list_filter = ['reminder_type', 'status', 'scheduled_time']
    search_fields = ['title', 'content', 'company__name']
    readonly_fields = ['created_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('invoice', 'company', 'reminder_type')
        }),
        ('提醒内容', {
            'fields': ('title', 'content')
        }),
        ('发送配置', {
            'fields': ('recipients', 'send_methods')
        }),
        ('发送状态', {
            'fields': ('status', 'scheduled_time', 'sent_time', 'error_message')
        }),
        ('创建时间', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

    def reminder_type_display(self, obj):
        """提醒类型显示"""
        type_colors = {
            'authentication_deadline': '#faad14',
            'deduction_deadline': '#1890ff',
            'overdue_authentication': '#f5222d',
            'monthly_summary': '#52c41a',
        }
        color = type_colors.get(obj.reminder_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_reminder_type_display()
        )
    reminder_type_display.short_description = '提醒类型'

    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'pending': '#faad14',
            'sent': '#52c41a',
            'failed': '#f5222d',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'


# 自定义管理页面标题
admin.site.site_header = '代理记账管理系统'
admin.site.site_title = '发票管理'
admin.site.index_title = '发票管理'

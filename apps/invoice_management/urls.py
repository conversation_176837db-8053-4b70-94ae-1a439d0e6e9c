from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'types', views.InvoiceTypeViewSet, basename='invoice-type')
router.register(r'invoices', views.InvoiceViewSet, basename='invoice')
router.register(r'items', views.InvoiceItemViewSet, basename='invoice-item')
router.register(r'authentications', views.InvoiceAuthenticationViewSet, basename='invoice-authentication')
router.register(r'deductions', views.InvoiceDeductionViewSet, basename='invoice-deduction')
router.register(r'templates', views.InvoiceTemplateViewSet, basename='invoice-template')
router.register(r'validations', views.InvoiceValidationViewSet, basename='invoice-validation')
router.register(r'statistics', views.InvoiceStatisticsViewSet, basename='invoice-statistics')
router.register(r'reminders', views.InvoiceReminderViewSet, basename='invoice-reminder')

app_name = 'invoice_management'

urlpatterns = [
    path('', include(router.urls)),
]

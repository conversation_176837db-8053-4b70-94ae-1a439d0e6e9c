from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db.models import Sum, Count
from .models import SimpleFinancialRecord, FinancialRecord, TaxRecord, FinancialReport, FinancialDocument, AccountingEntry


@admin.register(SimpleFinancialRecord)
class SimpleFinancialRecordAdmin(admin.ModelAdmin):
    """简化财务记录管理"""
    list_display = ['transaction_date', 'user_info', 'company_info', 'record_type', 
                   'amount', 'description', 'category', 'payment_method', 'status', 'created_at']
    list_filter = ['record_type', 'payment_method', 'status', 'category', 'transaction_date', 'created_at']
    search_fields = ['description', 'category', 'invoice_number', 'counterparty', 
                    'user__phone', 'user__nickname', 'company__name']
    date_hierarchy = 'transaction_date'
    list_editable = ['status']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'company', 'accountant')
        }),
        ('交易详情', {
            'fields': ('record_type', 'transaction_date', 'amount', 'description', 'category')
        }),
        ('发票信息', {
            'fields': ('invoice_number', 'counterparty', 'payment_method')
        }),
        ('状态', {
            'fields': ('status',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def user_info(self, obj):
        if obj.user:
            return format_html(
                '<a href="{}">{}</a><br/><small>{}</small>',
                reverse('admin:users_user_change', args=[obj.user.pk]),
                obj.user.nickname or obj.user.phone,
                obj.user.phone
            )
        return '-'
    user_info.short_description = '用户'
    
    def company_info(self, obj):
        if obj.company:
            return format_html(
                '<a href="{}">{}</a>',
                reverse('admin:companies_company_change', args=[obj.company.pk]),
                obj.company.name
            )
        return '-'
    company_info.short_description = '企业'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'company', 'accountant')


@admin.register(FinancialRecord)
class FinancialRecordAdmin(admin.ModelAdmin):
    """财务记录管理"""
    list_display = ['company_info', 'year', 'month', 'status', 'total_income_display',
                   'total_expense_display', 'net_profit_display', 'submitted_at']
    list_filter = ['status', 'year', 'month', 'submitted_at']
    search_fields = ['company__name', 'remarks']
    date_hierarchy = 'submitted_at'
    readonly_fields = ['submitted_at', 'updated_at', 'total_income_display', 'total_expense_display',
                      'gross_profit_display', 'net_profit_display', 'total_tax_display']

    fieldsets = (
        ('基本信息', {
            'fields': ('company', 'user', 'year', 'month', 'status')
        }),
        ('收入信息', {
            'fields': ('main_business_income', 'other_business_income', 'non_operating_income', 'total_income_display')
        }),
        ('支出信息', {
            'fields': ('main_business_cost', 'other_business_cost', 'management_expense',
                      'sales_expense', 'financial_expense', 'non_operating_expense', 'total_expense_display')
        }),
        ('税费信息', {
            'fields': ('vat_payable', 'income_tax_payable', 'other_taxes_payable', 'total_tax_display')
        }),
        ('汇总信息', {
            'fields': ('gross_profit_display', 'net_profit_display')
        }),
        ('处理信息', {
            'fields': ('accountant', 'processed_at', 'remarks', 'rejection_reason')
        }),
        ('时间信息', {
            'fields': ('submitted_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def company_info(self, obj):
        if obj.company:
            return format_html(
                '<a href="{}">{}</a>',
                reverse('admin:companies_company_change', args=[obj.company.pk]),
                obj.company.name
            )
        return '-'
    company_info.short_description = '企业'

    def total_income_display(self, obj):
        return f"¥{obj.total_income:,.2f}"
    total_income_display.short_description = '总收入'

    def total_expense_display(self, obj):
        return f"¥{obj.total_expense:,.2f}"
    total_expense_display.short_description = '总支出'

    def gross_profit_display(self, obj):
        return f"¥{obj.gross_profit:,.2f}"
    gross_profit_display.short_description = '毛利润'

    def net_profit_display(self, obj):
        return f"¥{obj.net_profit:,.2f}"
    net_profit_display.short_description = '净利润'

    def total_tax_display(self, obj):
        return f"¥{obj.total_tax:,.2f}"
    total_tax_display.short_description = '总税费'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('company', 'user', 'accountant')


@admin.register(TaxRecord)
class TaxRecordAdmin(admin.ModelAdmin):
    """税务记录管理"""
    list_display = ['company_info', 'tax_type', 'tax_period_display', 'tax_amount',
                   'status', 'declaration_date', 'payment_date']
    list_filter = ['tax_type', 'status', 'tax_period_start', 'declaration_date']
    search_fields = ['company__name', 'tax_type']
    date_hierarchy = 'tax_period_start'
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('company', 'financial_record', 'processed_by')
        }),
        ('税务信息', {
            'fields': ('tax_type', 'tax_period_start', 'tax_period_end', 'taxable_amount', 'tax_rate', 'tax_amount', 'paid_amount')
        }),
        ('申报状态', {
            'fields': ('status', 'declaration_date', 'payment_date')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def company_info(self, obj):
        if obj.company:
            return format_html(
                '<a href="{}">{}</a>',
                reverse('admin:companies_company_change', args=[obj.company.pk]),
                obj.company.name
            )
        return '-'
    company_info.short_description = '企业'

    def tax_period_display(self, obj):
        return f"{obj.tax_period_start} 至 {obj.tax_period_end}"
    tax_period_display.short_description = '税期'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('company', 'financial_record', 'processed_by')


@admin.register(FinancialReport)
class FinancialReportAdmin(admin.ModelAdmin):
    """财务报表管理"""
    list_display = ['company_info', 'report_type', 'year', 'month', 'quarter', 'generated_at']
    list_filter = ['report_type', 'year', 'month', 'quarter', 'generated_at']
    search_fields = ['company__name', 'report_type']
    date_hierarchy = 'generated_at'
    readonly_fields = ['generated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('company', 'generated_by')
        }),
        ('报表信息', {
            'fields': ('report_type', 'year', 'month', 'quarter')
        }),
        ('报表数据', {
            'fields': ('report_data',),
            'classes': ('collapse',)
        }),
        ('文件信息', {
            'fields': ('file_path',)
        }),
        ('时间信息', {
            'fields': ('generated_at',),
            'classes': ('collapse',)
        })
    )

    def company_info(self, obj):
        if obj.company:
            return format_html(
                '<a href="{}">{}</a>',
                reverse('admin:companies_company_change', args=[obj.company.pk]),
                obj.company.name
            )
        return '-'
    company_info.short_description = '企业'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('company', 'generated_by')


@admin.register(FinancialDocument)
class FinancialDocumentAdmin(admin.ModelAdmin):
    """财务凭证文档管理"""
    list_display = ['financial_record_info', 'document_type', 'document_name', 'amount', 'date', 'uploaded_at']
    list_filter = ['document_type', 'date', 'uploaded_at']
    search_fields = ['financial_record__company__name', 'document_name', 'description']
    date_hierarchy = 'date'
    readonly_fields = ['uploaded_at', 'file_size', 'file_format']

    fieldsets = (
        ('基本信息', {
            'fields': ('financial_record', 'document_type', 'document_name')
        }),
        ('文档信息', {
            'fields': ('amount', 'date', 'description')
        }),
        ('文件信息', {
            'fields': ('file_path', 'file_size', 'file_format')
        }),
        ('时间信息', {
            'fields': ('uploaded_at',),
            'classes': ('collapse',)
        })
    )

    def financial_record_info(self, obj):
        if obj.financial_record and obj.financial_record.company:
            return format_html(
                '<a href="{}">{}</a>',
                reverse('admin:companies_company_change', args=[obj.financial_record.company.pk]),
                obj.financial_record.company.name
            )
        return '-'
    financial_record_info.short_description = '关联企业'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('financial_record__company')


@admin.register(AccountingEntry)
class AccountingEntryAdmin(admin.ModelAdmin):
    """会计分录管理"""
    list_display = ['financial_record_info', 'account_name', 'entry_type', 'amount', 'summary', 'created_at']
    list_filter = ['entry_type', 'created_at']
    search_fields = ['financial_record__company__name', 'account_name', 'summary']
    date_hierarchy = 'created_at'
    readonly_fields = ['created_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('financial_record',)
        }),
        ('分录信息', {
            'fields': ('account_code', 'account_name', 'entry_type', 'amount')
        }),
        ('摘要备注', {
            'fields': ('summary', 'remarks')
        }),
        ('时间信息', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        })
    )

    def financial_record_info(self, obj):
        if obj.financial_record:
            return format_html(
                '<a href="{}">{}</a><br/><small>{}-{}</small>',
                reverse('admin:finance_financialrecord_change', args=[obj.financial_record.pk]),
                obj.financial_record.company.name,
                obj.financial_record.year,
                obj.financial_record.month
            )
        return '-'
    financial_record_info.short_description = '财务记录'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('financial_record__company')

from rest_framework import serializers
from decimal import Decimal
from django.utils import timezone
from .models import SimpleFinancialRecord, FinancialRecord, FinancialDocument, TaxRecord, FinancialReport, AccountingEntry


class SimpleFinancialRecordSerializer(serializers.ModelSerializer):
    """简化财务记录序列化器"""
    user_phone = serializers.CharField(source='user.phone', read_only=True)
    company_name = serializers.CharField(source='company.name', read_only=True)
    accountant_name = serializers.CharField(source='accountant.username', read_only=True)

    class Meta:
        model = SimpleFinancialRecord
        fields = [
            'id', 'user', 'user_phone', 'company', 'company_name', 'accountant', 'accountant_name',
            'record_type', 'transaction_date', 'amount', 'description', 'category',
            'invoice_number', 'counterparty', 'payment_method', 'status',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'user_phone', 'company_name', 'accountant_name', 'created_at', 'updated_at']
    
    def validate_amount(self, value):
        """验证金额"""
        if value <= 0:
            raise serializers.ValidationError('金额必须大于0')
        if value > Decimal('*********.99'):
            raise serializers.ValidationError('金额不能超过999,999,999.99')
        return value
    
    def validate_transaction_date(self, value):
        """验证交易日期"""
        if value > timezone.now().date():
            raise serializers.ValidationError('交易日期不能是未来日期')
        return value


class SimpleFinancialRecordCreateSerializer(serializers.ModelSerializer):
    """简化财务记录创建序列化器"""

    class Meta:
        model = SimpleFinancialRecord
        fields = [
            'record_type', 'transaction_date', 'amount', 'description', 'category',
            'invoice_number', 'counterparty', 'payment_method'
        ]
    
    def create(self, validated_data):
        """创建财务记录"""
        user = self.context['request'].user
        
        # 获取用户的企业
        try:
            company = user.companies.first()
            if not company:
                raise serializers.ValidationError('请先创建企业信息')
        except:
            raise serializers.ValidationError('请先创建企业信息')
        
        validated_data['user'] = user
        validated_data['company'] = company
        
        return super().create(validated_data)


class FinancialDocumentSerializer(serializers.ModelSerializer):
    """财务文档序列化器"""
    financial_record_info = serializers.SerializerMethodField()
    file_url = serializers.SerializerMethodField()
    
    class Meta:
        model = FinancialDocument
        fields = [
            'id', 'financial_record', 'financial_record_info', 'document_type',
            'file_path', 'file_url', 'file_name', 'file_size', 'upload_time'
        ]
        read_only_fields = ['id', 'financial_record_info', 'file_url', 'file_size', 'upload_time']
    
    def get_financial_record_info(self, obj):
        """获取财务记录信息"""
        if obj.financial_record:
            return {
                'id': obj.financial_record.id,
                'description': obj.financial_record.description,
                'amount': float(obj.financial_record.amount),
                'transaction_date': obj.financial_record.transaction_date
            }
        return None
    
    def get_file_url(self, obj):
        """获取文件URL"""
        if obj.file_path:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.file_path.url)
            return obj.file_path.url
        return None


class TaxRecordSerializer(serializers.ModelSerializer):
    """报税记录序列化器"""
    company_name = serializers.CharField(source='company.name', read_only=True)
    financial_record_info = serializers.SerializerMethodField()
    processed_by_name = serializers.CharField(source='processed_by.username', read_only=True)
    
    class Meta:
        model = TaxRecord
        fields = [
            'id', 'company', 'company_name', 'financial_record', 'financial_record_info',
            'tax_type', 'tax_period', 'tax_amount', 'status', 'filing_date',
            'processed_by', 'processed_by_name', 'remarks', 'created_at'
        ]
        read_only_fields = ['id', 'company_name', 'financial_record_info', 'processed_by_name', 'created_at']
    
    def get_financial_record_info(self, obj):
        """获取财务记录信息"""
        if obj.financial_record:
            return {
                'id': obj.financial_record.id,
                'description': obj.financial_record.description,
                'amount': float(obj.financial_record.amount)
            }
        return None


class FinancialReportSerializer(serializers.ModelSerializer):
    """财务报表序列化器"""
    company_name = serializers.CharField(source='company.name', read_only=True)
    generated_by_name = serializers.CharField(source='generated_by.username', read_only=True)
    
    class Meta:
        model = FinancialReport
        fields = [
            'id', 'company', 'company_name', 'report_type', 'report_period',
            'start_date', 'end_date', 'report_data', 'generated_by', 'generated_by_name',
            'generated_at'
        ]
        read_only_fields = ['id', 'company_name', 'generated_by_name', 'generated_at']


class AccountingEntrySerializer(serializers.ModelSerializer):
    """会计分录序列化器"""
    financial_record_info = serializers.SerializerMethodField()
    
    class Meta:
        model = AccountingEntry
        fields = [
            'id', 'financial_record', 'financial_record_info', 'account_name',
            'account_code', 'debit_amount', 'credit_amount', 'entry_date', 'description'
        ]
        read_only_fields = ['id', 'financial_record_info']
    
    def get_financial_record_info(self, obj):
        """获取财务记录信息"""
        if obj.financial_record:
            return {
                'id': obj.financial_record.id,
                'description': obj.financial_record.description,
                'amount': float(obj.financial_record.amount)
            }
        return None
    
    def validate(self, attrs):
        """验证会计分录"""
        debit_amount = attrs.get('debit_amount', Decimal('0'))
        credit_amount = attrs.get('credit_amount', Decimal('0'))
        
        # 借贷金额不能同时为0
        if debit_amount == 0 and credit_amount == 0:
            raise serializers.ValidationError('借方金额和贷方金额不能同时为0')
        
        # 借贷金额不能同时有值
        if debit_amount > 0 and credit_amount > 0:
            raise serializers.ValidationError('借方金额和贷方金额不能同时有值')
        
        return attrs


class FinancialSubmissionSerializer(serializers.Serializer):
    """财务资料提交序列化器"""
    record_type = serializers.ChoiceField(choices=[
        ('income', '收入'),
        ('expense', '支出'),
    ])
    transaction_date = serializers.DateField()
    amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    description = serializers.CharField(max_length=500)
    category = serializers.CharField(max_length=100)
    invoice_number = serializers.CharField(max_length=100, required=False, allow_blank=True)
    counterparty = serializers.CharField(max_length=200, required=False, allow_blank=True)
    payment_method = serializers.ChoiceField(choices=[
        ('cash', '现金'),
        ('bank_transfer', '银行转账'),
        ('alipay', '支付宝'),
        ('wechat', '微信支付'),
        ('check', '支票'),
    ], required=False)
    documents = serializers.ListField(
        child=serializers.FileField(),
        required=False,
        allow_empty=True,
        help_text='相关文档文件'
    )
    
    def validate_amount(self, value):
        """验证金额"""
        if value <= 0:
            raise serializers.ValidationError('金额必须大于0')
        return value
    
    def validate_transaction_date(self, value):
        """验证交易日期"""
        if value > timezone.now().date():
            raise serializers.ValidationError('交易日期不能是未来日期')
        return value
    
    def validate_documents(self, value):
        """验证文档文件"""
        if value:
            for file in value:
                # 检查文件大小（10MB限制）
                if file.size > 10 * 1024 * 1024:
                    raise serializers.ValidationError(f'文件 {file.name} 大小不能超过10MB')
                
                # 检查文件格式
                allowed_extensions = ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx', '.xls', '.xlsx']
                import os
                ext = os.path.splitext(file.name)[1].lower()
                if ext not in allowed_extensions:
                    raise serializers.ValidationError(f'文件 {file.name} 格式不支持，只支持: {", ".join(allowed_extensions)}')
        
        return value


class FinancialReportGenerateSerializer(serializers.Serializer):
    """财务报表生成序列化器"""
    report_type = serializers.ChoiceField(choices=FinancialReport.REPORT_TYPE_CHOICES)
    start_date = serializers.DateField()
    end_date = serializers.DateField()
    
    def validate(self, attrs):
        """验证日期范围"""
        start_date = attrs['start_date']
        end_date = attrs['end_date']
        
        if start_date >= end_date:
            raise serializers.ValidationError('开始日期必须早于结束日期')
        
        # 检查日期范围不能超过1年
        if (end_date - start_date).days > 365:
            raise serializers.ValidationError('报表日期范围不能超过1年')
        
        return attrs


class ComprehensiveFinancialSubmissionSerializer(serializers.Serializer):
    """综合财务数据提交序列化器 - 匹配前端数据格式"""
    taxpayer_type = serializers.CharField(max_length=50, help_text='纳税人类型')
    month = serializers.CharField(max_length=10, help_text='所属月份，格式：YYYY-MM')

    # 收入数据
    income_data = serializers.DictField(child=serializers.DecimalField(max_digits=15, decimal_places=2))

    # 支出数据
    expense_data = serializers.DictField(child=serializers.DecimalField(max_digits=15, decimal_places=2))

    # 上传的文件ID列表
    uploaded_files = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        allow_empty=True,
        help_text='上传的文件ID列表'
    )

    def validate_month(self, value):
        """验证月份格式"""
        import re
        if not re.match(r'^\d{4}-\d{2}$', value):
            raise serializers.ValidationError('月份格式错误，应为YYYY-MM')

        from datetime import datetime
        try:
            datetime.strptime(value, '%Y-%m')
        except ValueError:
            raise serializers.ValidationError('无效的月份')

        return value

    def validate_income_data(self, value):
        """验证收入数据"""
        required_fields = ['main_business', 'other_business', 'non_operating']
        for field in required_fields:
            if field not in value:
                raise serializers.ValidationError(f'缺少收入字段: {field}')
            if value[field] < 0:
                raise serializers.ValidationError(f'收入金额不能为负数: {field}')
        return value

    def validate_expense_data(self, value):
        """验证支出数据"""
        required_fields = ['main_business_cost', 'management_fee', 'sales_fee', 'finance_fee']
        for field in required_fields:
            if field not in value:
                raise serializers.ValidationError(f'缺少支出字段: {field}')
            if value[field] < 0:
                raise serializers.ValidationError(f'支出金额不能为负数: {field}')
        return value


class TaxSubmissionSerializer(serializers.Serializer):
    """报税提交序列化器"""
    tax_type = serializers.ChoiceField(choices=TaxRecord.TAX_TYPE_CHOICES)
    tax_period = serializers.CharField(max_length=20)
    financial_records = serializers.ListField(
        child=serializers.IntegerField(),
        help_text='相关财务记录ID列表'
    )
    
    def validate_financial_records(self, value):
        """验证财务记录"""
        if not value:
            raise serializers.ValidationError('请选择相关的财务记录')
        
        # 检查财务记录是否存在且属于当前用户
        user = self.context['request'].user
        try:
            company = user.companies.first()
            if not company:
                raise serializers.ValidationError('请先创建企业信息')
        except:
            raise serializers.ValidationError('请先创建企业信息')
        
        existing_records = FinancialRecord.objects.filter(
            id__in=value,
            company=company
        ).values_list('id', flat=True)
        
        missing_records = set(value) - set(existing_records)
        if missing_records:
            raise serializers.ValidationError(f'财务记录 {list(missing_records)} 不存在或无权限访问')
        
        return value

from django.db import models
from django.conf import settings
from decimal import Decimal


class SimpleFinancialRecord(models.Model):
    """简化财务记录"""
    RECORD_TYPE_CHOICES = [
        ('income', '收入'),
        ('expense', '支出'),
    ]

    PAYMENT_METHOD_CHOICES = [
        ('cash', '现金'),
        ('bank_transfer', '银行转账'),
        ('alipay', '支付宝'),
        ('wechat', '微信支付'),
        ('check', '支票'),
    ]

    STATUS_CHOICES = [
        ('pending', '待审核'),
        ('approved', '已审核'),
        ('rejected', '已拒绝'),
    ]

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='simple_financial_records', verbose_name='用户')
    company = models.ForeignKey('companies.Company', on_delete=models.CASCADE, related_name='simple_financial_records', verbose_name='企业')
    accountant = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='processed_financial_records', verbose_name='处理会计师')

    record_type = models.CharField('记录类型', max_length=20, choices=RECORD_TYPE_CHOICES)
    transaction_date = models.DateField('交易日期')
    amount = models.DecimalField('金额', max_digits=12, decimal_places=2)
    description = models.CharField('描述', max_length=500)
    category = models.CharField('分类', max_length=100)
    invoice_number = models.CharField('发票号码', max_length=100, blank=True)
    counterparty = models.CharField('交易对方', max_length=200, blank=True)
    payment_method = models.CharField('支付方式', max_length=20, choices=PAYMENT_METHOD_CHOICES, default='cash')
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='pending')

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'simple_financial_records'
        verbose_name = '财务记录'
        verbose_name_plural = '财务记录管理'
        ordering = ['-transaction_date', '-created_at']

    def __str__(self):
        return f'{self.company.name} - {self.get_record_type_display()} - {self.amount}'


class FinancialRecord(models.Model):
    """财务记录"""
    STATUS_CHOICES = [
        ('submitted', '已提交'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('rejected', '已拒绝'),
        ('revision_required', '需要修改'),
    ]
    
    # 基本信息
    company = models.ForeignKey('companies.Company', on_delete=models.CASCADE, related_name='financial_records', verbose_name='企业')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='financial_records', verbose_name='提交用户')
    
    # 财务期间
    year = models.PositiveIntegerField('年份')
    month = models.PositiveIntegerField('月份')
    
    # 收入信息
    main_business_income = models.DecimalField('主营业务收入', max_digits=15, decimal_places=2, default=Decimal('0.00'))
    other_business_income = models.DecimalField('其他业务收入', max_digits=15, decimal_places=2, default=Decimal('0.00'))
    non_operating_income = models.DecimalField('营业外收入', max_digits=15, decimal_places=2, default=Decimal('0.00'))
    
    # 支出信息
    main_business_cost = models.DecimalField('主营业务成本', max_digits=15, decimal_places=2, default=Decimal('0.00'))
    other_business_cost = models.DecimalField('其他业务成本', max_digits=15, decimal_places=2, default=Decimal('0.00'))
    management_expense = models.DecimalField('管理费用', max_digits=15, decimal_places=2, default=Decimal('0.00'))
    sales_expense = models.DecimalField('销售费用', max_digits=15, decimal_places=2, default=Decimal('0.00'))
    financial_expense = models.DecimalField('财务费用', max_digits=15, decimal_places=2, default=Decimal('0.00'))
    non_operating_expense = models.DecimalField('营业外支出', max_digits=15, decimal_places=2, default=Decimal('0.00'))
    
    # 税费信息
    vat_payable = models.DecimalField('应交增值税', max_digits=15, decimal_places=2, default=Decimal('0.00'))
    income_tax_payable = models.DecimalField('应交所得税', max_digits=15, decimal_places=2, default=Decimal('0.00'))
    other_taxes_payable = models.DecimalField('其他应交税费', max_digits=15, decimal_places=2, default=Decimal('0.00'))
    
    # 处理状态
    status = models.CharField('处理状态', max_length=20, choices=STATUS_CHOICES, default='submitted')
    remarks = models.TextField('备注说明', blank=True)
    
    # 处理信息
    accountant = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, 
                                  blank=True, null=True, related_name='processed_records', verbose_name='处理会计师')
    processed_at = models.DateTimeField('处理时间', blank=True, null=True)
    rejection_reason = models.TextField('拒绝原因', blank=True)
    
    # 时间戳
    submitted_at = models.DateTimeField('提交时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'financial_records'
        verbose_name = '财务记录'
        verbose_name_plural = '财务记录'
        unique_together = ['company', 'year', 'month']
        ordering = ['-year', '-month', '-submitted_at']
    
    def __str__(self):
        return f"{self.company.name} - {self.year}年{self.month}月"
    
    @property
    def total_income(self):
        """总收入"""
        return self.main_business_income + self.other_business_income + self.non_operating_income
    
    @property
    def total_expense(self):
        """总支出"""
        return (self.main_business_cost + self.other_business_cost + 
                self.management_expense + self.sales_expense + 
                self.financial_expense + self.non_operating_expense)
    
    @property
    def gross_profit(self):
        """毛利润"""
        return self.total_income - self.total_expense
    
    @property
    def total_tax(self):
        """总税费"""
        return self.vat_payable + self.income_tax_payable + self.other_taxes_payable
    
    @property
    def net_profit(self):
        """净利润"""
        return self.gross_profit - self.total_tax


class FinancialDocument(models.Model):
    """财务凭证文档"""
    DOCUMENT_TYPE_CHOICES = [
        ('invoice', '发票'),
        ('receipt', '收据'),
        ('bank_statement', '银行流水'),
        ('contract', '合同'),
        ('voucher', '记账凭证'),
        ('other', '其他'),
    ]
    
    financial_record = models.ForeignKey(FinancialRecord, on_delete=models.CASCADE, 
                                       related_name='documents', verbose_name='财务记录')
    document_type = models.CharField('文档类型', max_length=20, choices=DOCUMENT_TYPE_CHOICES)
    document_name = models.CharField('文档名称', max_length=200)
    file_path = models.FileField('文件路径', upload_to='financial_documents/')
    file_size = models.PositiveIntegerField('文件大小', default=0)
    file_format = models.CharField('文件格式', max_length=10, blank=True)
    
    # 文档信息
    amount = models.DecimalField('金额', max_digits=15, decimal_places=2, blank=True, null=True)
    date = models.DateField('日期', blank=True, null=True)
    description = models.TextField('描述', blank=True)
    
    uploaded_at = models.DateTimeField('上传时间', auto_now_add=True)
    
    class Meta:
        db_table = 'financial_documents'
        verbose_name = '财务凭证'
        verbose_name_plural = '财务凭证'
        ordering = ['-uploaded_at']
    
    def __str__(self):
        return f"{self.financial_record} - {self.document_name}"


class TaxRecord(models.Model):
    """报税记录"""
    TAX_TYPE_CHOICES = [
        ('vat', '增值税'),
        ('income_tax', '企业所得税'),
        ('individual_tax', '个人所得税'),
        ('stamp_tax', '印花税'),
        ('other', '其他税种'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '待申报'),
        ('submitted', '已申报'),
        ('approved', '已通过'),
        ('rejected', '被拒绝'),
    ]
    
    company = models.ForeignKey('companies.Company', on_delete=models.CASCADE, 
                               related_name='tax_records', verbose_name='企业')
    financial_record = models.ForeignKey(FinancialRecord, on_delete=models.CASCADE, 
                                       related_name='tax_records', verbose_name='财务记录')
    
    # 税务信息
    tax_type = models.CharField('税种', max_length=20, choices=TAX_TYPE_CHOICES)
    tax_period_start = models.DateField('税期开始')
    tax_period_end = models.DateField('税期结束')
    
    # 税额信息
    taxable_amount = models.DecimalField('应税金额', max_digits=15, decimal_places=2, default=Decimal('0.00'))
    tax_rate = models.DecimalField('税率', max_digits=5, decimal_places=4, default=Decimal('0.00'))
    tax_amount = models.DecimalField('应纳税额', max_digits=15, decimal_places=2, default=Decimal('0.00'))
    paid_amount = models.DecimalField('已缴税额', max_digits=15, decimal_places=2, default=Decimal('0.00'))
    
    # 申报状态
    status = models.CharField('申报状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    declaration_date = models.DateField('申报日期', blank=True, null=True)
    payment_date = models.DateField('缴税日期', blank=True, null=True)
    
    # 处理信息
    processed_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, 
                                   blank=True, null=True, related_name='processed_tax_records', verbose_name='处理人')
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'tax_records'
        verbose_name = '报税记录'
        verbose_name_plural = '报税记录'
        ordering = ['-tax_period_end', '-created_at']
    
    def __str__(self):
        return f"{self.company.name} - {self.get_tax_type_display()} - {self.tax_period_start}至{self.tax_period_end}"


class FinancialReport(models.Model):
    """财务报表"""
    REPORT_TYPE_CHOICES = [
        ('balance_sheet', '资产负债表'),
        ('income_statement', '利润表'),
        ('cash_flow', '现金流量表'),
        ('equity_statement', '所有者权益变动表'),
    ]
    
    company = models.ForeignKey('companies.Company', on_delete=models.CASCADE, 
                               related_name='financial_reports', verbose_name='企业')
    report_type = models.CharField('报表类型', max_length=20, choices=REPORT_TYPE_CHOICES)
    
    # 报表期间
    year = models.PositiveIntegerField('年份')
    month = models.PositiveIntegerField('月份', blank=True, null=True)
    quarter = models.PositiveIntegerField('季度', blank=True, null=True)
    
    # 报表数据
    report_data = models.JSONField('报表数据', default=dict)
    
    # 生成信息
    generated_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, 
                                   blank=True, null=True, related_name='generated_reports', verbose_name='生成人')
    generated_at = models.DateTimeField('生成时间', auto_now_add=True)
    
    # 文件信息
    file_path = models.FileField('报表文件', upload_to='financial_reports/', blank=True, null=True)
    
    class Meta:
        db_table = 'financial_reports'
        verbose_name = '财务报表'
        verbose_name_plural = '财务报表'
        ordering = ['-year', '-month', '-generated_at']
    
    def __str__(self):
        period = f"{self.year}年"
        if self.month:
            period += f"{self.month}月"
        elif self.quarter:
            period += f"第{self.quarter}季度"
        return f"{self.company.name} - {self.get_report_type_display()} - {period}"


class AccountingEntry(models.Model):
    """会计分录"""
    ENTRY_TYPE_CHOICES = [
        ('debit', '借方'),
        ('credit', '贷方'),
    ]
    
    financial_record = models.ForeignKey(FinancialRecord, on_delete=models.CASCADE, 
                                       related_name='accounting_entries', verbose_name='财务记录')
    
    # 分录信息
    account_code = models.CharField('科目代码', max_length=20)
    account_name = models.CharField('科目名称', max_length=100)
    entry_type = models.CharField('借贷方向', max_length=10, choices=ENTRY_TYPE_CHOICES)
    amount = models.DecimalField('金额', max_digits=15, decimal_places=2)
    
    # 摘要和备注
    summary = models.CharField('摘要', max_length=200, blank=True)
    remarks = models.TextField('备注', blank=True)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        db_table = 'accounting_entries'
        verbose_name = '会计分录'
        verbose_name_plural = '会计分录'
        ordering = ['entry_type', 'account_code']
    
    def __str__(self):
        return f"{self.account_name} - {self.get_entry_type_display()} - {self.amount}"

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'records', views.FinancialRecordViewSet, basename='financial-record')
router.register(r'documents', views.FinancialDocumentViewSet, basename='financial-document')
router.register(r'tax-records', views.TaxRecordViewSet, basename='tax-record')
router.register(r'reports', views.FinancialReportViewSet, basename='financial-report')
router.register(r'accounting-entries', views.AccountingEntryViewSet, basename='accounting-entry')

urlpatterns = [
    # 财务记录
    path('submit/', views.SubmitFinancialDataView.as_view(), name='submit_financial_data'),
    path('submit-comprehensive/', views.submit_comprehensive_financial_data, name='submit_comprehensive_financial_data'),
    path('dashboard/', views.FinancialDashboardView.as_view(), name='financial_dashboard'),
    
    # 财务报表
    path('reports/generate/', views.GenerateReportView.as_view(), name='generate_report'),
    path('reports/advanced/', views.generate_advanced_report, name='generate_advanced_report'),

    
    # 报税记录
    path('tax/submit/', views.SubmitTaxView.as_view(), name='submit_tax'),
    path('tax/calendar/', views.TaxCalendarView.as_view(), name='tax_calendar'),
    
    # 其他路由
    path('', include(router.urls)),
]

from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.views import APIView
from rest_framework.parsers import <PERSON>PartPars<PERSON>, FormParser
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db.models import Sum, Q
from decimal import Decimal
import json

from .models import SimpleFinancialRecord, FinancialRecord, FinancialDocument, TaxRecord, FinancialReport, AccountingEntry
from .serializers import (
    SimpleFinancialRecordSerializer, SimpleFinancialRecordCreateSerializer, FinancialDocumentSerializer,
    TaxRecordSerializer, FinancialReportSerializer, AccountingEntrySerializer,
    FinancialSubmissionSerializer, ComprehensiveFinancialSubmissionSerializer,
    FinancialReportGenerateSerializer, TaxSubmissionSerializer
)
from utils.response import APIResponse
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class FinancialRecordViewSet(viewsets.ModelViewSet):
    """财务记录ViewSet"""
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """获取查询集"""
        if self.request.user.is_staff:
            return SimpleFinancialRecord.objects.all().select_related('user', 'company', 'accountant')

        # 普通用户只能查看自己企业的记录
        try:
            company = self.request.user.companies.first()
            if company:
                return SimpleFinancialRecord.objects.filter(company=company).select_related('user', 'company', 'accountant')
        except Exception as e:
            # 记录错误日志
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"获取财务记录失败: {str(e)}")

        return SimpleFinancialRecord.objects.none()

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return SimpleFinancialRecordCreateSerializer
        return SimpleFinancialRecordSerializer

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """审核财务记录（会计师操作）"""
        if not request.user.is_staff:
            return APIResponse.forbidden(message='无权限执行此操作')

        record = self.get_object()
        action_type = request.data.get('action')  # 'approve' or 'reject'
        remarks = request.data.get('remarks', '')

        if action_type == 'approve':
            record.status = 'approved'
            record.accountant = request.user
            record.save()
            return APIResponse.success(message='财务记录审核通过')
        elif action_type == 'reject':
            record.status = 'rejected'
            record.accountant = request.user
            record.save()
            return APIResponse.success(message='财务记录已拒绝')
        else:
            return APIResponse.error(message='无效的操作类型')

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """财务统计"""
        try:
            company = request.user.companies.first()
            if not company:
                return APIResponse.error(message='请先创建企业信息')

            # 获取查询参数
            start_date = request.query_params.get('start_date')
            end_date = request.query_params.get('end_date')

            queryset = FinancialRecord.objects.filter(company=company, status='approved')

            if start_date:
                queryset = queryset.filter(transaction_date__gte=start_date)
            if end_date:
                queryset = queryset.filter(transaction_date__lte=end_date)

            # 获取简化财务记录进行统计
            simple_queryset = SimpleFinancialRecord.objects.filter(company=company, status='approved')

            if start_date:
                simple_queryset = simple_queryset.filter(transaction_date__gte=start_date)
            if end_date:
                simple_queryset = simple_queryset.filter(transaction_date__lte=end_date)

            # 统计收入和支出
            income = simple_queryset.filter(record_type='income').aggregate(
                total=Sum('amount')
            )['total'] or Decimal('0')

            expense = simple_queryset.filter(record_type='expense').aggregate(
                total=Sum('amount')
            )['total'] or Decimal('0')

            # 按类别统计
            category_stats = {}
            for record in simple_queryset.values('category', 'record_type').annotate(total=Sum('amount')):
                category = record['category']
                if category not in category_stats:
                    category_stats[category] = {'income': 0, 'expense': 0}
                category_stats[category][record['record_type']] = float(record['total'])

            return APIResponse.success(data={
                'total_income': float(income),
                'total_expense': float(expense),
                'net_profit': float(income - expense),
                'category_statistics': category_stats,
                'record_count': simple_queryset.count()
            })
        except Exception as e:
            return APIResponse.error(message=f'统计失败: {str(e)}')


class FinancialDocumentViewSet(viewsets.ModelViewSet):
    """财务文档ViewSet"""
    serializer_class = FinancialDocumentSerializer
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def get_queryset(self):
        """获取查询集"""
        if self.request.user.is_staff:
            return FinancialDocument.objects.all().select_related('financial_record')

        # 普通用户只能查看自己企业的文档
        try:
            company = self.request.user.companies.first()
            if company:
                return FinancialDocument.objects.filter(
                    financial_record__company=company
                ).select_related('financial_record')
        except Exception as e:
            # 记录错误日志
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"获取财务文档失败: {str(e)}")

        return FinancialDocument.objects.none()


class TaxRecordViewSet(viewsets.ModelViewSet):
    """报税记录ViewSet"""
    serializer_class = TaxRecordSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """获取查询集"""
        if self.request.user.is_staff:
            return TaxRecord.objects.all().select_related('company', 'financial_record', 'processed_by')

        # 普通用户只能查看自己企业的报税记录
        try:
            company = self.request.user.companies.first()
            if company:
                return TaxRecord.objects.filter(company=company).select_related('company', 'financial_record', 'processed_by')
        except Exception as e:
            # 记录错误日志
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"获取税务记录失败: {str(e)}")

        return TaxRecord.objects.none()

    @action(detail=True, methods=['post'])
    def process(self, request, pk=None):
        """处理报税（会计师操作）"""
        if not request.user.is_staff:
            return APIResponse.forbidden(message='无权限执行此操作')

        tax_record = self.get_object()
        action_type = request.data.get('action')  # 'approve' or 'reject'
        remarks = request.data.get('remarks', '')

        if action_type == 'approve':
            tax_record.status = 'filed'
            tax_record.filing_date = timezone.now().date()
            tax_record.processed_by = request.user
            tax_record.remarks = remarks
            tax_record.save()
            return APIResponse.success(message='报税处理完成')
        elif action_type == 'reject':
            tax_record.status = 'rejected'
            tax_record.processed_by = request.user
            tax_record.remarks = remarks
            tax_record.save()
            return APIResponse.success(message='报税已拒绝')
        else:
            return APIResponse.error(message='无效的操作类型')


class FinancialReportViewSet(viewsets.ModelViewSet):
    """财务报表ViewSet"""
    serializer_class = FinancialReportSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """获取查询集"""
        if self.request.user.is_staff:
            return FinancialReport.objects.all().select_related('company', 'generated_by')

        # 普通用户只能查看自己企业的报表
        try:
            company = self.request.user.companies.first()
            if company:
                return FinancialReport.objects.filter(company=company).select_related('company', 'generated_by')
        except Exception as e:
            # 记录错误日志
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"获取财务报告失败: {str(e)}")

        return FinancialReport.objects.none()


class AccountingEntryViewSet(viewsets.ModelViewSet):
    """会计分录ViewSet"""
    serializer_class = AccountingEntrySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """获取查询集"""
        if self.request.user.is_staff:
            return AccountingEntry.objects.all().select_related('financial_record')

        # 普通用户只能查看自己企业的会计分录
        try:
            company = self.request.user.companies.first()
            if company:
                return AccountingEntry.objects.filter(
                    financial_record__company=company
                ).select_related('financial_record')
        except Exception as e:
            # 记录错误日志
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"获取会计分录失败: {str(e)}")

        return AccountingEntry.objects.none()


class SubmitFinancialDataView(APIView):
    """提交财务数据"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """提交财务资料"""
        serializer = FinancialSubmissionSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            try:
                # 获取用户企业
                company = request.user.companies.first()
                if not company:
                    return APIResponse.error(message='请先创建企业信息')

                # 创建财务记录
                financial_record = SimpleFinancialRecord.objects.create(
                    user=request.user,
                    company=company,
                    record_type=serializer.validated_data['record_type'],
                    transaction_date=serializer.validated_data['transaction_date'],
                    amount=serializer.validated_data['amount'],
                    description=serializer.validated_data['description'],
                    category=serializer.validated_data['category'],
                    invoice_number=serializer.validated_data.get('invoice_number', ''),
                    counterparty=serializer.validated_data.get('counterparty', ''),
                    payment_method=serializer.validated_data.get('payment_method', 'cash'),
                    status='pending'
                )

                # 处理上传的文档
                documents = serializer.validated_data.get('documents', [])
                for doc in documents:
                    FinancialDocument.objects.create(
                        financial_record=financial_record,
                        document_type='invoice',  # 默认为发票类型
                        file_path=doc,
                        file_name=doc.name,
                        file_size=doc.size
                    )

                return APIResponse.created(
                    data=SimpleFinancialRecordSerializer(financial_record, context={'request': request}).data,
                    message='财务资料提交成功'
                )
            except Exception as e:
                return APIResponse.error(message=f'提交失败: {str(e)}')

        return APIResponse.error(message='数据验证失败', errors=serializer.errors)


class GenerateReportView(APIView):
    """生成财务报表"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """生成报表"""
        serializer = FinancialReportGenerateSerializer(data=request.data)
        if serializer.is_valid():
            try:
                company = request.user.companies.first()
                if not company:
                    return APIResponse.error(message='请先创建企业信息')

                report_type = serializer.validated_data['report_type']
                start_date = serializer.validated_data['start_date']
                end_date = serializer.validated_data['end_date']

                # 获取指定期间的财务记录
                records = FinancialRecord.objects.filter(
                    company=company,
                    status='approved',
                    transaction_date__gte=start_date,
                    transaction_date__lte=end_date
                )

                # 生成报表数据
                report_data = self._generate_report_data(records, report_type)

                # 创建报表记录
                report = FinancialReport.objects.create(
                    company=company,
                    report_type=report_type,
                    report_period=f"{start_date} 至 {end_date}",
                    start_date=start_date,
                    end_date=end_date,
                    report_data=report_data,
                    generated_by=request.user
                )

                return APIResponse.created(
                    data=FinancialReportSerializer(report, context={'request': request}).data,
                    message='报表生成成功'
                )
            except Exception as e:
                return APIResponse.error(message=f'报表生成失败: {str(e)}')

        return APIResponse.error(message='数据验证失败', errors=serializer.errors)

    def _generate_report_data(self, records, report_type):
        """生成报表数据"""
        if report_type == 'income_statement':
            # 损益表
            income_total = records.filter(record_type='income').aggregate(
                total=Sum('amount')
            )['total'] or Decimal('0')

            expense_total = records.filter(record_type='expense').aggregate(
                total=Sum('amount')
            )['total'] or Decimal('0')

            return {
                'total_income': float(income_total),
                'total_expense': float(expense_total),
                'net_profit': float(income_total - expense_total),
                'income_details': list(records.filter(record_type='income').values(
                    'category', 'description', 'amount', 'transaction_date'
                )),
                'expense_details': list(records.filter(record_type='expense').values(
                    'category', 'description', 'amount', 'transaction_date'
                ))
            }
        elif report_type == 'balance_sheet':
            # 资产负债表（简化版）
            return {
                'assets': {
                    'current_assets': 0,
                    'fixed_assets': 0,
                    'total_assets': 0
                },
                'liabilities': {
                    'current_liabilities': 0,
                    'long_term_liabilities': 0,
                    'total_liabilities': 0
                },
                'equity': {
                    'owner_equity': 0,
                    'retained_earnings': 0,
                    'total_equity': 0
                }
            }
        elif report_type == 'cash_flow':
            # 现金流量表
            cash_inflow = records.filter(
                record_type='income',
                payment_method__in=['cash', 'bank_transfer']
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

            cash_outflow = records.filter(
                record_type='expense',
                payment_method__in=['cash', 'bank_transfer']
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

            return {
                'operating_activities': {
                    'cash_inflow': float(cash_inflow),
                    'cash_outflow': float(cash_outflow),
                    'net_cash_flow': float(cash_inflow - cash_outflow)
                },
                'investing_activities': {
                    'cash_inflow': 0,
                    'cash_outflow': 0,
                    'net_cash_flow': 0
                },
                'financing_activities': {
                    'cash_inflow': 0,
                    'cash_outflow': 0,
                    'net_cash_flow': 0
                }
            }
        else:
            return {}


class SubmitTaxView(APIView):
    """提交报税申请"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """提交报税"""
        serializer = TaxSubmissionSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            try:
                company = request.user.companies.first()
                if not company:
                    return APIResponse.error(message='请先创建企业信息')

                tax_type = serializer.validated_data['tax_type']
                tax_period = serializer.validated_data['tax_period']
                financial_record_ids = serializer.validated_data['financial_records']

                # 获取相关财务记录
                financial_records = FinancialRecord.objects.filter(
                    id__in=financial_record_ids,
                    company=company,
                    status='approved'
                )

                # 计算税额（简化计算）
                total_amount = financial_records.aggregate(
                    total=Sum('amount')
                )['total'] or Decimal('0')

                # 根据税种计算税额
                if tax_type == 'vat':
                    # 增值税（简化为3%）
                    tax_amount = total_amount * Decimal('0.03')
                elif tax_type == 'income_tax':
                    # 企业所得税（简化为25%）
                    tax_amount = total_amount * Decimal('0.25')
                else:
                    tax_amount = Decimal('0')

                # 创建报税记录
                tax_record = TaxRecord.objects.create(
                    company=company,
                    tax_type=tax_type,
                    tax_period=tax_period,
                    tax_amount=tax_amount,
                    status='pending'
                )

                # 关联财务记录（简化处理，只关联第一个）
                if financial_records.exists():
                    tax_record.financial_record = financial_records.first()
                    tax_record.save()

                return APIResponse.created(
                    data=TaxRecordSerializer(tax_record, context={'request': request}).data,
                    message='报税申请提交成功'
                )
            except Exception as e:
                return APIResponse.error(message=f'报税提交失败: {str(e)}')

        return APIResponse.error(message='数据验证失败', errors=serializer.errors)


class TaxCalendarView(APIView):
    """报税日历"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取报税日历"""
        try:
            company = request.user.companies.first()
            if not company:
                return APIResponse.error(message='请先创建企业信息')

            # 获取当前年月
            year = request.query_params.get('year', timezone.now().year)
            month = request.query_params.get('month', timezone.now().month)

            # 获取该月的报税记录
            tax_records = TaxRecord.objects.filter(
                company=company,
                created_at__year=year,
                created_at__month=month
            ).values('tax_type', 'tax_period', 'status', 'filing_date', 'created_at')

            # 生成报税提醒
            tax_reminders = [
                {'date': f'{year}-{month:02d}-15', 'type': 'vat', 'description': '增值税申报截止日'},
                {'date': f'{year}-{month:02d}-31', 'type': 'income_tax', 'description': '企业所得税申报截止日'},
            ]

            return APIResponse.success(data={
                'tax_records': list(tax_records),
                'tax_reminders': tax_reminders,
                'year': year,
                'month': month
            })
        except Exception as e:
            return APIResponse.error(message=f'获取报税日历失败: {str(e)}')


class FinancialDashboardView(APIView):
    """财务仪表板"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取财务仪表板数据"""
        try:
            company = request.user.companies.first()
            if not company:
                return APIResponse.error(message='请先创建企业信息')

            # 获取本月数据
            current_month = timezone.now().replace(day=1)

            # 本月财务记录统计
            monthly_records = SimpleFinancialRecord.objects.filter(
                company=company,
                transaction_date__gte=current_month,
                status='approved'
            )

            monthly_income = monthly_records.filter(record_type='income').aggregate(
                total=Sum('amount')
            )['total'] or Decimal('0')

            monthly_expense = monthly_records.filter(record_type='expense').aggregate(
                total=Sum('amount')
            )['total'] or Decimal('0')

            # 待处理记录数量
            pending_records = SimpleFinancialRecord.objects.filter(
                company=company,
                status='pending'
            ).count()

            # 最近的报税记录
            recent_tax_records = TaxRecord.objects.filter(
                company=company
            ).order_by('-created_at')[:5]

            return APIResponse.success(data={
                'monthly_summary': {
                    'income': float(monthly_income),
                    'expense': float(monthly_expense),
                    'profit': float(monthly_income - monthly_expense)
                },
                'pending_records_count': pending_records,
                'recent_tax_records': TaxRecordSerializer(recent_tax_records, many=True, context={'request': request}).data
            })
        except Exception as e:
            return APIResponse.error(message=f'获取仪表板数据失败: {str(e)}')


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def submit_comprehensive_financial_data(request):
    """
    提交综合财务数据 - 匹配前端数据格式
    """
    serializer = ComprehensiveFinancialSubmissionSerializer(data=request.data)
    if not serializer.is_valid():
        return APIResponse.error(
            message='数据验证失败',
            data=serializer.errors
        )

    try:
        # 获取用户的企业
        user = request.user
        company = user.companies.first()
        if not company:
            return APIResponse.error(message='请先创建企业信息')

        # 解析月份
        month_str = serializer.validated_data['month']
        year, month = map(int, month_str.split('-'))

        # 检查是否已存在该月份的记录
        existing_record = FinancialRecord.objects.filter(
            company=company,
            year=year,
            month=month
        ).first()

        if existing_record:
            return APIResponse.error(
                message=f'{year}年{month}月的财务数据已存在，请勿重复提交'
            )

        # 创建财务记录
        income_data = serializer.validated_data['income_data']
        expense_data = serializer.validated_data['expense_data']

        financial_record = FinancialRecord.objects.create(
            company=company,
            user=user,
            year=year,
            month=month,
            taxpayer_type=serializer.validated_data['taxpayer_type'],

            # 收入数据
            main_business_income=income_data['main_business'],
            other_business_income=income_data['other_business'],
            non_operating_income=income_data['non_operating'],

            # 支出数据
            main_business_cost=expense_data['main_business_cost'],
            management_expense=expense_data['management_fee'],
            sales_expense=expense_data['sales_fee'],
            financial_expense=expense_data['finance_fee'],

            status='pending'
        )

        # 关联上传的文件
        uploaded_files = serializer.validated_data.get('uploaded_files', [])
        if uploaded_files:
            from apps.files.models import FileUpload
            for file_id in uploaded_files:
                try:
                    file_obj = FileUpload.objects.get(id=file_id, user=user)
                    FinancialDocument.objects.create(
                        financial_record=financial_record,
                        document_type='voucher',
                        file_path=file_obj.file_path,
                        file_name=file_obj.original_name,
                        file_size=file_obj.file_size
                    )
                except FileUpload.DoesNotExist:
                    continue

        return APIResponse.success(
            message='财务数据提交成功',
            data={
                'id': financial_record.id,
                'year': financial_record.year,
                'month': financial_record.month,
                'total_income': float(financial_record.total_income),
                'total_expense': float(financial_record.total_expense),
                'net_profit': float(financial_record.gross_profit),
                'status': financial_record.status
            }
        )

    except Exception as e:
        return APIResponse.error(message=f'提交失败: {str(e)}')


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def generate_advanced_report(request):
    """
    生成高级财务报表
    """
    try:
        user = request.user
        company = user.companies.first()
        if not company:
            return APIResponse.error(message='请先创建企业信息')

        report_type = request.data.get('report_type', 'profit_loss')
        start_date = request.data.get('start_date')
        end_date = request.data.get('end_date')

        if not start_date or not end_date:
            return APIResponse.error(message='请提供开始和结束日期')

        from datetime import datetime
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

        # 获取期间内的财务记录
        financial_records = FinancialRecord.objects.filter(
            company=company,
            submitted_at__date__gte=start_date,
            submitted_at__date__lte=end_date,
            status='approved'
        )

        if report_type == 'profit_loss':
            # 利润表
            report_data = generate_profit_loss_report(financial_records, start_date, end_date)
        elif report_type == 'balance_sheet':
            # 资产负债表
            report_data = generate_balance_sheet_report(financial_records, start_date, end_date)
        elif report_type == 'cash_flow':
            # 现金流量表
            report_data = generate_cash_flow_report(financial_records, start_date, end_date)
        elif report_type == 'tax_summary':
            # 税务汇总表
            report_data = generate_tax_summary_report(financial_records, start_date, end_date)
        else:
            return APIResponse.error(message='不支持的报表类型')

        # 保存报表记录
        report = FinancialReport.objects.create(
            company=company,
            user=user,
            report_type=report_type,
            start_date=start_date,
            end_date=end_date,
            report_data=report_data,
            status='completed'
        )

        return APIResponse.success(
            message='报表生成成功',
            data={
                'report_id': report.id,
                'report_type': report_type,
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'report_data': report_data,
                'generated_at': report.generated_at.isoformat()
            }
        )

    except Exception as e:
        return APIResponse.error(message=f'生成报表失败: {str(e)}')


def generate_profit_loss_report(financial_records, start_date, end_date):
    """生成利润表"""
    from decimal import Decimal

    total_income = Decimal('0')
    total_expense = Decimal('0')

    income_details = {}
    expense_details = {}

    for record in financial_records:
        # 收入汇总
        total_income += record.main_business_income + record.other_business_income + record.non_operating_income

        # 支出汇总
        total_expense += (record.main_business_cost + record.other_business_cost +
                         record.management_expense + record.sales_expense +
                         record.financial_expense + record.non_operating_expense)

        # 收入明细
        if record.main_business_income > 0:
            income_details['主营业务收入'] = income_details.get('主营业务收入', Decimal('0')) + record.main_business_income
        if record.other_business_income > 0:
            income_details['其他业务收入'] = income_details.get('其他业务收入', Decimal('0')) + record.other_business_income
        if record.non_operating_income > 0:
            income_details['营业外收入'] = income_details.get('营业外收入', Decimal('0')) + record.non_operating_income

        # 支出明细
        if record.main_business_cost > 0:
            expense_details['主营业务成本'] = expense_details.get('主营业务成本', Decimal('0')) + record.main_business_cost
        if record.management_expense > 0:
            expense_details['管理费用'] = expense_details.get('管理费用', Decimal('0')) + record.management_expense
        if record.sales_expense > 0:
            expense_details['销售费用'] = expense_details.get('销售费用', Decimal('0')) + record.sales_expense
        if record.financial_expense > 0:
            expense_details['财务费用'] = expense_details.get('财务费用', Decimal('0')) + record.financial_expense

    gross_profit = total_income - total_expense

    return {
        'report_name': '利润表',
        'period': f'{start_date} 至 {end_date}',
        'summary': {
            'total_income': float(total_income),
            'total_expense': float(total_expense),
            'gross_profit': float(gross_profit),
            'profit_margin': float(gross_profit / total_income * 100) if total_income > 0 else 0
        },
        'income_details': {k: float(v) for k, v in income_details.items()},
        'expense_details': {k: float(v) for k, v in expense_details.items()},
        'generated_at': datetime.now().isoformat()
    }


def generate_balance_sheet_report(financial_records, start_date, end_date):
    """生成资产负债表（简化版）"""
    return {
        'report_name': '资产负债表',
        'period': f'{start_date} 至 {end_date}',
        'assets': {
            'current_assets': {
                '货币资金': 100000.00,
                '应收账款': 50000.00,
                '存货': 30000.00
            },
            'fixed_assets': {
                '固定资产': 200000.00,
                '无形资产': 50000.00
            }
        },
        'liabilities': {
            'current_liabilities': {
                '应付账款': 40000.00,
                '短期借款': 20000.00
            },
            'long_term_liabilities': {
                '长期借款': 100000.00
            }
        },
        'equity': {
            '实收资本': 200000.00,
            '未分配利润': 70000.00
        }
    }


def generate_cash_flow_report(financial_records, start_date, end_date):
    """生成现金流量表"""
    from decimal import Decimal

    operating_inflow = Decimal('0')
    operating_outflow = Decimal('0')

    for record in financial_records:
        operating_inflow += record.main_business_income + record.other_business_income
        operating_outflow += record.main_business_cost + record.management_expense + record.sales_expense

    return {
        'report_name': '现金流量表',
        'period': f'{start_date} 至 {end_date}',
        'operating_activities': {
            'cash_inflow': float(operating_inflow),
            'cash_outflow': float(operating_outflow),
            'net_cash_flow': float(operating_inflow - operating_outflow)
        },
        'investing_activities': {
            'cash_inflow': 0,
            'cash_outflow': 0,
            'net_cash_flow': 0
        },
        'financing_activities': {
            'cash_inflow': 0,
            'cash_outflow': 0,
            'net_cash_flow': 0
        }
    }


def generate_tax_summary_report(financial_records, start_date, end_date):
    """生成税务汇总表"""
    from decimal import Decimal

    total_vat = Decimal('0')
    total_income_tax = Decimal('0')

    for record in financial_records:
        total_vat += record.vat_payable
        total_income_tax += record.income_tax_payable

    return {
        'report_name': '税务汇总表',
        'period': f'{start_date} 至 {end_date}',
        'tax_summary': {
            'vat_payable': float(total_vat),
            'income_tax_payable': float(total_income_tax),
            'other_taxes': 0,
            'total_tax': float(total_vat + total_income_tax)
        },
        'tax_details': [
            {'tax_type': '增值税', 'amount': float(total_vat)},
            {'tax_type': '企业所得税', 'amount': float(total_income_tax)}
        ]
    }

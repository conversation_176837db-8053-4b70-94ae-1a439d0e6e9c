from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import AllowAny
from django.contrib.auth import get_user_model
from django.utils import timezone
from utils.response import APIResponse
from utils.jwt_auth import generate_jwt_token
from .services import WechatService
from .models import WechatUser, WechatConfig
import logging
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response

logger = logging.getLogger(__name__)
User = get_user_model()


class WechatLoginView(APIView):
    """微信小程序登录"""
    permission_classes = [AllowAny]
    
    def post(self, request):
        """
        微信小程序登录
        
        参数:
        - code: 微信授权码
        - encrypted_data: 加密的用户信息 (可选)
        - iv: 初始向量 (可选)
        """
        code = request.data.get('code')
        encrypted_data = request.data.get('encrypted_data')
        iv = request.data.get('iv')
        
        if not code:
            return APIResponse.error(message="缺少授权码", code=400)
        
        try:
            # 检查微信配置
            config = WechatConfig.get_active_config()
            if not config:
                return APIResponse.error(message="微信小程序配置未设置", code=500)
            
            if not config.enable_user_auth:
                return APIResponse.error(message="用户授权功能未启用", code=403)
            
            # 初始化微信服务
            wechat_service = WechatService()
            
            # 通过code获取session信息
            session_info = wechat_service.code_to_session(code)
            
            # 解密用户信息（如果提供）
            user_info = None
            if encrypted_data and iv:
                try:
                    user_info = wechat_service.get_user_info(
                        encrypted_data, iv, session_info['session_key']
                    )
                except Exception as e:
                    logger.warning(f"解密用户信息失败: {e}")
                    # 不阻断登录流程，继续使用基本信息
            
            # 创建或更新微信用户
            wechat_user = wechat_service.create_or_update_user(
                session_info, user_info, request
            )
            
            # 更新最后登录时间
            wechat_user.last_login_at = timezone.now()
            wechat_user.save()
            
            # 创建或关联系统用户
            if not wechat_user.user:
                # 创建系统用户
                username = f"wx_{wechat_user.openid[-8:]}"
                user = User.objects.create_user(
                    username=username,
                    first_name=wechat_user.nickname or '微信用户'
                )
                wechat_user.user = user
                wechat_user.save()
            
            # 生成JWT token
            token = generate_jwt_token(wechat_user.user)
            
            # 返回用户信息
            user_data = {
                'id': wechat_user.user.id,
                'username': wechat_user.user.username,
                'nickname': wechat_user.nickname,
                'avatar_url': wechat_user.avatar_url,
                'gender': wechat_user.gender,
                'city': wechat_user.city,
                'province': wechat_user.province,
                'country': wechat_user.country,
                'openid': wechat_user.openid,
                'is_new_user': not bool(user_info)  # 如果没有用户信息说明是新用户
            }
            
            return APIResponse.success(data={
                'token': token,
                'user': user_data,
                'expires_in': 7200  # 2小时
            }, message="登录成功")
            
        except Exception as e:
            logger.error(f"微信登录失败: {e}")
            return APIResponse.error(message=f"登录失败: {str(e)}", code=500)


class WechatUserInfoView(APIView):
    """更新微信用户信息"""
    
    def post(self, request):
        """
        更新用户信息
        
        参数:
        - nickname: 昵称
        - avatar_url: 头像URL
        - gender: 性别
        """
        try:
            # 获取微信用户
            wechat_user = WechatUser.objects.get(user=request.user)
            
            # 更新用户信息
            nickname = request.data.get('nickname')
            avatar_url = request.data.get('avatar_url')
            gender = request.data.get('gender')
            
            if nickname is not None:
                wechat_user.nickname = nickname
                # 同时更新系统用户的first_name
                wechat_user.user.first_name = nickname
                wechat_user.user.save()
            
            if avatar_url is not None:
                wechat_user.avatar_url = avatar_url
            
            if gender is not None:
                wechat_user.gender = int(gender)
            
            wechat_user.save()
            
            return APIResponse.success(data={
                'nickname': wechat_user.nickname,
                'avatar_url': wechat_user.avatar_url,
                'gender': wechat_user.gender
            }, message="用户信息更新成功")
            
        except WechatUser.DoesNotExist:
            return APIResponse.error(message="微信用户不存在", code=404)
        except Exception as e:
            logger.error(f"更新用户信息失败: {e}")
            return APIResponse.error(message="更新失败", code=500)


class WechatConfigView(APIView):
    """获取微信小程序配置（公开信息）"""
    permission_classes = [AllowAny]
    
    def get(self, request):
        """获取公开的微信配置信息"""
        try:
            config = WechatConfig.get_active_config()
            if not config:
                return APIResponse.error(message="微信配置未设置", code=404)
            
            # 只返回公开信息，不包含敏感数据
            data = {
                'app_id': config.app_id,
                'mini_program_name': config.mini_program_name,
                'enable_user_auth': config.enable_user_auth,
                'enable_payment': config.enable_payment,
                'enable_message': config.enable_message
            }
            
            return APIResponse.success(data=data)
            
        except Exception as e:
            logger.error(f"获取微信配置失败: {e}")
            return APIResponse.error(message="获取配置失败", code=500)


class WechatPhoneView(APIView):
    """获取微信手机号"""
    
    def post(self, request):
        """
        获取微信手机号
        
        参数:
        - code: 手机号授权码
        """
        code = request.data.get('code')
        if not code:
            return APIResponse.error(message="缺少授权码", code=400)
        
        try:
            wechat_service = WechatService()
            access_token = wechat_service.get_access_token()
            
            # 调用微信API获取手机号
            import requests
            url = f"{wechat_service.config.api_base_url}/wxa/business/getuserphonenumber"
            
            response = requests.post(
                url,
                params={'access_token': access_token},
                json={'code': code},
                timeout=10
            )
            
            result = response.json()
            
            if result.get('errcode') == 0:
                phone_info = result.get('phone_info', {})
                phone_number = phone_info.get('phoneNumber')
                
                if phone_number:
                    # 更新用户手机号
                    request.user.username = phone_number
                    request.user.save()
                    
                    return APIResponse.success(data={
                        'phone_number': phone_number
                    }, message="手机号获取成功")
                else:
                    return APIResponse.error(message="未获取到手机号", code=400)
            else:
                error_msg = result.get('errmsg', '获取手机号失败')
                return APIResponse.error(message=error_msg, code=400)
                
        except Exception as e:
            logger.error(f"获取微信手机号失败: {e}")
            return APIResponse.error(message="获取手机号失败", code=500)

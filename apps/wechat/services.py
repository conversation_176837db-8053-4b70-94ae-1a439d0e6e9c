import requests
import json
import logging
from django.conf import settings
from django.core.cache import cache
from .models import WechatConfig, WechatUser, WechatLoginLog

logger = logging.getLogger(__name__)


class WechatService:
    """微信小程序服务"""
    
    def __init__(self):
        self.config = WechatConfig.get_active_config()
        if not self.config:
            raise ValueError("未找到可用的微信小程序配置")
    
    def get_access_token(self):
        """获取访问令牌"""
        cache_key = f"wechat_access_token_{self.config.app_id}"
        access_token = cache.get(cache_key)
        
        if not access_token:
            url = f"{self.config.api_base_url}/cgi-bin/token"
            params = {
                'grant_type': 'client_credential',
                'appid': self.config.app_id,
                'secret': self.config.app_secret
            }
            
            try:
                response = requests.get(url, params=params, timeout=10)
                data = response.json()
                
                if 'access_token' in data:
                    access_token = data['access_token']
                    expires_in = data.get('expires_in', 7200)
                    # 提前5分钟过期
                    cache.set(cache_key, access_token, expires_in - 300)
                else:
                    logger.error(f"获取access_token失败: {data}")
                    raise Exception(f"获取access_token失败: {data.get('errmsg', '未知错误')}")
                    
            except requests.RequestException as e:
                logger.error(f"请求微信API失败: {e}")
                raise Exception(f"请求微信API失败: {e}")
        
        return access_token
    
    def code_to_session(self, js_code):
        """通过授权码获取session信息"""
        url = f"{self.config.api_base_url}/sns/jscode2session"
        params = {
            'appid': self.config.app_id,
            'secret': self.config.app_secret,
            'js_code': js_code,
            'grant_type': 'authorization_code'
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            data = response.json()
            
            if 'openid' in data:
                return {
                    'openid': data['openid'],
                    'session_key': data.get('session_key', ''),
                    'unionid': data.get('unionid', '')
                }
            else:
                error_msg = data.get('errmsg', '未知错误')
                logger.error(f"code2session失败: {data}")
                raise Exception(f"授权失败: {error_msg}")
                
        except requests.RequestException as e:
            logger.error(f"请求微信API失败: {e}")
            raise Exception(f"网络请求失败: {e}")
    
    def get_user_info(self, encrypted_data, iv, session_key):
        """解密用户信息"""
        try:
            from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
            from cryptography.hazmat.backends import default_backend
            import base64
            
            # Base64解码
            session_key = base64.b64decode(session_key)
            encrypted_data = base64.b64decode(encrypted_data)
            iv = base64.b64decode(iv)
            
            # AES解密
            cipher = Cipher(
                algorithms.AES(session_key),
                modes.CBC(iv),
                backend=default_backend()
            )
            decryptor = cipher.decryptor()
            decrypted = decryptor.update(encrypted_data) + decryptor.finalize()
            
            # 去除padding
            pad = decrypted[-1]
            decrypted = decrypted[:-pad]
            
            # 解析JSON
            user_info = json.loads(decrypted.decode('utf-8'))
            return user_info
            
        except Exception as e:
            logger.error(f"解密用户信息失败: {e}")
            raise Exception(f"解密用户信息失败: {e}")
    
    def create_or_update_user(self, session_info, user_info=None, request=None):
        """创建或更新微信用户"""
        openid = session_info['openid']
        session_key = session_info.get('session_key', '')
        unionid = session_info.get('unionid', '')
        
        # 获取或创建微信用户
        wechat_user, created = WechatUser.objects.get_or_create(
            openid=openid,
            defaults={
                'session_key': session_key,
                'unionid': unionid,
            }
        )
        
        # 更新session_key
        if not created:
            wechat_user.session_key = session_key
            if unionid:
                wechat_user.unionid = unionid
        
        # 更新用户信息
        if user_info:
            wechat_user.nickname = user_info.get('nickName', '')
            wechat_user.avatar_url = user_info.get('avatarUrl', '')
            wechat_user.gender = user_info.get('gender', 0)
            wechat_user.country = user_info.get('country', '')
            wechat_user.province = user_info.get('province', '')
            wechat_user.city = user_info.get('city', '')
            wechat_user.language = user_info.get('language', 'zh_CN')
        
        wechat_user.save()
        
        # 记录登录日志
        self.log_login(wechat_user, 'code', True, request)
        
        return wechat_user
    
    def log_login(self, wechat_user, login_type, is_success, request=None, error_message=''):
        """记录登录日志"""
        device_info = {}
        ip_address = None
        user_agent = ''
        
        if request:
            # 获取设备信息
            device_info = {
                'platform': request.META.get('HTTP_X_PLATFORM', ''),
                'version': request.META.get('HTTP_X_VERSION', ''),
                'brand': request.META.get('HTTP_X_BRAND', ''),
                'model': request.META.get('HTTP_X_MODEL', ''),
                'system': request.META.get('HTTP_X_SYSTEM', ''),
            }
            
            # 获取IP地址
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip_address = x_forwarded_for.split(',')[0].strip()
            else:
                ip_address = request.META.get('REMOTE_ADDR')
            
            user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        WechatLoginLog.objects.create(
            wechat_user=wechat_user,
            login_type=login_type,
            is_success=is_success,
            error_message=error_message,
            device_info=device_info,
            ip_address=ip_address,
            user_agent=user_agent
        )
    
    def send_template_message(self, openid, template_id, data, page=''):
        """发送模板消息"""
        if not self.config.enable_message:
            logger.warning("消息推送功能未启用")
            return False
        
        access_token = self.get_access_token()
        url = f"{self.config.api_base_url}/cgi-bin/message/wxopen/template/send"
        
        payload = {
            'touser': openid,
            'template_id': template_id,
            'page': page,
            'data': data
        }
        
        try:
            response = requests.post(
                url,
                params={'access_token': access_token},
                json=payload,
                timeout=10
            )
            result = response.json()
            
            if result.get('errcode') == 0:
                return True
            else:
                logger.error(f"发送模板消息失败: {result}")
                return False
                
        except requests.RequestException as e:
            logger.error(f"发送模板消息请求失败: {e}")
            return False

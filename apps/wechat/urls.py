from django.urls import path
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from . import views

@csrf_exempt
def wechat_api_root(request):
    """微信API根视图"""
    return JsonResponse({
        'code': 200,
        'message': '微信API服务正常',
        'endpoints': {
            'login': '/api/v1/wechat/login/',
            'user_info': '/api/v1/wechat/user/info/',
            'user_phone': '/api/v1/wechat/user/phone/',
            'config': '/api/v1/wechat/config/',
        }
    })

urlpatterns = [
    # API根路径
    path('', wechat_api_root, name='wechat_api_root'),

    # 微信登录
    path('login/', views.WechatLoginView.as_view(), name='wechat_login'),

    # 用户信息
    path('user/info/', views.WechatUserInfoView.as_view(), name='wechat_user_info'),

    # 获取手机号
    path('user/phone/', views.WechatPhoneView.as_view(), name='wechat_phone'),

    # 微信配置
    path('config/', views.WechatConfigView.as_view(), name='wechat_config'),
]

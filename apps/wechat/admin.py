from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import WechatConfig, WechatUser, WechatLoginLog


@admin.register(WechatConfig)
class WechatConfigAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'app_id', 'mini_program_name', 'is_active', 
        'is_default', 'enable_user_auth', 'created_at'
    ]
    list_filter = ['is_active', 'is_default', 'enable_user_auth', 'enable_payment']
    search_fields = ['name', 'app_id', 'mini_program_name']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'app_id', 'app_secret', 'mini_program_name', 'description')
        }),
        ('API配置', {
            'fields': ('api_base_url',)
        }),
        ('功能开关', {
            'fields': ('enable_user_auth', 'enable_payment', 'enable_message')
        }),
        ('状态设置', {
            'fields': ('is_active', 'is_default')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)
        if obj.is_default:
            # 确保只有一个默认配置
            WechatConfig.objects.exclude(pk=obj.pk).update(is_default=False)


@admin.register(WechatUser)
class WechatUserAdmin(admin.ModelAdmin):
    list_display = [
        'display_name', 'avatar_preview', 'openid_short', 'gender_display', 
        'city_info', 'user_link', 'is_active', 'last_login_at', 'created_at'
    ]
    list_filter = ['gender', 'is_active', 'country', 'province', 'created_at']
    search_fields = ['nickname', 'openid', 'city', 'province']
    readonly_fields = [
        'openid', 'unionid', 'session_key', 'created_at', 
        'updated_at', 'avatar_preview_large'
    ]
    raw_id_fields = ['user']
    
    fieldsets = (
        ('微信信息', {
            'fields': ('openid', 'unionid', 'session_key')
        }),
        ('用户资料', {
            'fields': (
                'nickname', 'avatar_url', 'avatar_preview_large',
                'gender', 'language'
            )
        }),
        ('地理信息', {
            'fields': ('country', 'province', 'city')
        }),
        ('系统关联', {
            'fields': ('user', 'is_active', 'last_login_at')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def avatar_preview(self, obj):
        """头像预览（小图）"""
        if obj.avatar_url:
            return format_html(
                '<img src="{}" style="width: 40px; height: 40px; border-radius: 50%;" />',
                obj.avatar_url
            )
        return '无头像'
    avatar_preview.short_description = '头像'
    
    def avatar_preview_large(self, obj):
        """头像预览（大图）"""
        if obj.avatar_url:
            return format_html(
                '<img src="{}" style="width: 100px; height: 100px; border-radius: 10px;" />',
                obj.avatar_url
            )
        return '无头像'
    avatar_preview_large.short_description = '头像预览'
    
    def openid_short(self, obj):
        """OpenID简短显示"""
        return f"{obj.openid[:8]}...{obj.openid[-8:]}" if obj.openid else ''
    openid_short.short_description = 'OpenID'
    
    def city_info(self, obj):
        """城市信息"""
        parts = [obj.country, obj.province, obj.city]
        return ' '.join([p for p in parts if p])
    city_info.short_description = '地区'
    
    def user_link(self, obj):
        """关联用户链接"""
        if obj.user:
            url = reverse('admin:auth_user_change', args=[obj.user.pk])
            return format_html('<a href="{}">{}</a>', url, obj.user.username)
        return '未关联'
    user_link.short_description = '系统用户'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(WechatLoginLog)
class WechatLoginLogAdmin(admin.ModelAdmin):
    list_display = [
        'wechat_user', 'login_type', 'is_success', 'ip_address', 
        'device_brand', 'created_at'
    ]
    list_filter = ['login_type', 'is_success', 'created_at']
    search_fields = ['wechat_user__nickname', 'wechat_user__openid', 'ip_address']
    readonly_fields = ['created_at', 'device_info_display']
    
    fieldsets = (
        ('登录信息', {
            'fields': ('wechat_user', 'login_type', 'is_success', 'error_message')
        }),
        ('设备信息', {
            'fields': ('device_info_display', 'ip_address', 'user_agent')
        }),
        ('时间信息', {
            'fields': ('created_at',)
        })
    )
    
    def device_brand(self, obj):
        """设备品牌"""
        device_info = obj.device_info or {}
        return device_info.get('brand', '未知')
    device_brand.short_description = '设备品牌'
    
    def device_info_display(self, obj):
        """设备信息显示"""
        if not obj.device_info:
            return '无设备信息'
        
        info = obj.device_info
        html = '<table style="border-collapse: collapse;">'
        
        for key, value in info.items():
            html += f'<tr><td style="padding: 2px 8px; border: 1px solid #ddd;"><strong>{key}:</strong></td>'
            html += f'<td style="padding: 2px 8px; border: 1px solid #ddd;">{value}</td></tr>'
        
        html += '</table>'
        return mark_safe(html)
    device_info_display.short_description = '设备详情'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('wechat_user')
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False

from django.db import models
from django.core.exceptions import ValidationError
from django.conf import settings


class WechatConfig(models.Model):
    """微信小程序配置"""
    
    name = models.CharField('配置名称', max_length=100, default='默认配置')
    app_id = models.CharField('小程序AppID', max_length=100, unique=True)
    app_secret = models.CharField('小程序AppSecret', max_length=100)
    
    # 小程序基本信息
    mini_program_name = models.CharField('小程序名称', max_length=100, blank=True)
    description = models.TextField('描述', blank=True)
    
    # API配置
    api_base_url = models.URLField('API基础URL', default='https://api.weixin.qq.com')
    
    # 功能开关
    enable_user_auth = models.BooleanField('启用用户授权', default=True)
    enable_payment = models.BooleanField('启用支付功能', default=False)
    enable_message = models.BooleanField('启用消息推送', default=False)
    
    # 状态
    is_active = models.BooleanField('是否启用', default=True)
    is_default = models.BooleanField('是否为默认配置', default=False)
    
    # 时间字段
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '微信小程序配置'
        verbose_name_plural = '微信小程序配置'
        db_table = 'wechat_config'
        ordering = ['-is_default', '-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.app_id})"
    
    def save(self, *args, **kwargs):
        # 确保只有一个默认配置
        if self.is_default:
            WechatConfig.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)
    
    def clean(self):
        if self.app_id and len(self.app_id) < 10:
            raise ValidationError({'app_id': 'AppID长度不能少于10位'})
        if self.app_secret and len(self.app_secret) < 20:
            raise ValidationError({'app_secret': 'AppSecret长度不能少于20位'})
    
    @classmethod
    def get_default_config(cls):
        """获取默认配置"""
        try:
            return cls.objects.filter(is_active=True, is_default=True).first()
        except cls.DoesNotExist:
            return None
    
    @classmethod
    def get_active_config(cls):
        """获取激活的配置"""
        return cls.get_default_config() or cls.objects.filter(is_active=True).first()


class WechatUser(models.Model):
    """微信用户信息"""
    
    # 微信用户标识
    openid = models.CharField('OpenID', max_length=100, unique=True)
    unionid = models.CharField('UnionID', max_length=100, blank=True, null=True)
    session_key = models.CharField('会话密钥', max_length=100, blank=True)
    
    # 用户基本信息
    nickname = models.CharField('昵称', max_length=100, blank=True)
    avatar_url = models.URLField('头像URL', blank=True)
    gender = models.IntegerField('性别', choices=[(0, '未知'), (1, '男'), (2, '女')], default=0)
    country = models.CharField('国家', max_length=50, blank=True)
    province = models.CharField('省份', max_length=50, blank=True)
    city = models.CharField('城市', max_length=50, blank=True)
    language = models.CharField('语言', max_length=20, default='zh_CN')
    
    # 关联系统用户
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='wechat_user'
    )
    
    # 状态信息
    is_active = models.BooleanField('是否激活', default=True)
    last_login_at = models.DateTimeField('最后登录时间', null=True, blank=True)
    
    # 时间字段
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '微信用户'
        verbose_name_plural = '微信用户'
        db_table = 'wechat_user'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.nickname or self.openid}"
    
    @property
    def display_name(self):
        """显示名称"""
        return self.nickname or f"用户{self.openid[-6:]}"
    
    @property
    def gender_display(self):
        """性别显示"""
        gender_map = {0: '未知', 1: '男', 2: '女'}
        return gender_map.get(self.gender, '未知')


class WechatLoginLog(models.Model):
    """微信登录日志"""
    
    wechat_user = models.ForeignKey(
        WechatUser, 
        on_delete=models.CASCADE,
        related_name='login_logs',
        verbose_name='微信用户'
    )
    
    # 登录信息
    login_type = models.CharField(
        '登录类型', 
        max_length=20, 
        choices=[
            ('code', '授权码登录'),
            ('phone', '手机号登录'),
            ('refresh', '刷新登录')
        ],
        default='code'
    )
    
    # 设备信息
    device_info = models.JSONField('设备信息', default=dict, blank=True)
    ip_address = models.GenericIPAddressField('IP地址', null=True, blank=True)
    user_agent = models.TextField('用户代理', blank=True)
    
    # 结果
    is_success = models.BooleanField('是否成功', default=True)
    error_message = models.TextField('错误信息', blank=True)
    
    # 时间
    created_at = models.DateTimeField('登录时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '微信登录日志'
        verbose_name_plural = '微信登录日志'
        db_table = 'wechat_login_log'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.wechat_user.display_name} - {self.get_login_type_display()}"

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q, Count, Avg, Sum, F
from django.db import models
from django.shortcuts import get_object_or_404
from datetime import datetime, timedelta
import uuid
import json
import requests
from .models import (
    ThirdPartyProvider, APIRequest, TaxBureauInterface, BankInterface,
    OCRService, ESignatureService, IntegrationTask, IntegrationLog,
    IntegrationConfig
)
from .serializers import (
    ThirdPartyProviderSerializer, APIRequestSerializer, TaxBureauInterfaceSerializer,
    BankInterfaceSerializer, OCRServiceSerializer, ESignatureServiceSerializer,
    IntegrationTaskSerializer, IntegrationLogSerializer, IntegrationConfigSerializer,
    ThirdPartyProviderListSerializer, IntegrationTaskListSerializer,
    InvoiceVerificationSerializer, OCRRecognitionSerializer, DocumentSignatureSerializer,
    BankTransactionSerializer, TaskExecutionSerializer
)
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class ThirdPartyProviderViewSet(viewsets.ModelViewSet):
    """第三方服务提供商ViewSet"""
    queryset = ThirdPartyProvider.objects.all()
    serializer_class = ThirdPartyProviderSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # 按服务类型过滤
        provider_type = self.request.query_params.get('provider_type')
        if provider_type:
            queryset = queryset.filter(provider_type=provider_type)
        
        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        return queryset.filter(is_active=True).order_by('provider_type', 'name')
    
    def get_serializer_class(self):
        if self.action == 'list':
            return ThirdPartyProviderListSerializer
        return ThirdPartyProviderSerializer
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def test_connection(self, request, pk=None):
        """测试连接"""
        provider = self.get_object()
        
        try:
            # 执行连接测试
            success = self._test_provider_connection(provider)
            
            # 更新测试结果
            provider.last_test_time = timezone.now()
            provider.last_test_result = success
            if success:
                provider.status = 'active'
                provider.error_message = ''
            else:
                provider.status = 'error'
                provider.error_message = '连接测试失败'
            provider.save()
            
            if success:
                return Response({'message': '连接测试成功'})
            else:
                return Response(
                    {'error': '连接测试失败'},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        except Exception as e:
            provider.last_test_time = timezone.now()
            provider.last_test_result = False
            provider.status = 'error'
            provider.error_message = str(e)
            provider.save()
            
            return Response(
                {'error': f'连接测试失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _test_provider_connection(self, provider):
        """测试服务提供商连接"""
        if provider.provider_type == 'tax_bureau':
            return self._test_tax_bureau_connection(provider)
        elif provider.provider_type == 'bank':
            return self._test_bank_connection(provider)
        elif provider.provider_type == 'ocr':
            return self._test_ocr_connection(provider)
        elif provider.provider_type == 'e_signature':
            return self._test_signature_connection(provider)
        else:
            return self._test_generic_connection(provider)
    
    def _test_tax_bureau_connection(self, provider):
        """测试税务局连接"""
        # 模拟税务局接口测试
        try:
            # 这里可以实现具体的税务局接口测试逻辑
            return True
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"税务局接口测试失败: {str(e)}")
            return False
    
    def _test_bank_connection(self, provider):
        """测试银行连接"""
        # 模拟银行接口测试
        try:
            # 这里可以实现具体的银行接口测试逻辑
            return True
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"银行接口测试失败: {str(e)}")
            return False
    
    def _test_ocr_connection(self, provider):
        """测试OCR连接"""
        # 模拟OCR服务测试
        try:
            # 这里可以实现具体的OCR服务测试逻辑
            return True
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"OCR服务测试失败: {str(e)}")
            return False
    
    def _test_signature_connection(self, provider):
        """测试电子签章连接"""
        # 模拟电子签章服务测试
        try:
            # 这里可以实现具体的电子签章服务测试逻辑
            return True
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"电子签章服务测试失败: {str(e)}")
            return False
    
    def _test_generic_connection(self, provider):
        """测试通用连接"""
        try:
            if provider.api_base_url:
                response = requests.get(
                    provider.api_base_url,
                    timeout=provider.timeout,
                    headers={'User-Agent': 'DaiLiJiZhang/1.0'}
                )
                return response.status_code < 400
            return True
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"通用接口测试失败: {str(e)}")
            return False
    
    @action(detail=True, methods=['get'])
    def statistics(self, request, pk=None):
        """获取服务提供商统计信息"""
        provider = self.get_object()
        
        # 最近30天的请求统计
        thirty_days_ago = timezone.now() - timedelta(days=30)
        recent_requests = APIRequest.objects.filter(
            provider=provider,
            start_time__gte=thirty_days_ago
        )
        
        # 按天统计
        daily_stats = []
        for i in range(30):
            date = (timezone.now() - timedelta(days=i)).date()
            day_requests = recent_requests.filter(start_time__date=date)
            
            daily_stats.insert(0, {
                'date': date.strftime('%Y-%m-%d'),
                'total_requests': day_requests.count(),
                'success_requests': day_requests.filter(status='success').count(),
                'failed_requests': day_requests.filter(status='failed').count(),
                'avg_duration': day_requests.aggregate(avg=Avg('duration'))['avg'] or 0
            })
        
        # 错误统计
        error_stats = recent_requests.filter(status='failed').values('error_code').annotate(
            count=Count('id')
        ).order_by('-count')[:10]
        
        return Response({
            'provider': ThirdPartyProviderSerializer(provider).data,
            'daily_stats': daily_stats,
            'error_stats': list(error_stats),
            'summary': {
                'total_requests': recent_requests.count(),
                'success_rate': provider.get_success_rate(),
                'avg_duration': recent_requests.aggregate(avg=Avg('duration'))['avg'] or 0,
                'last_request_time': recent_requests.order_by('-start_time').first().start_time if recent_requests.exists() else None
            }
        })


class APIRequestViewSet(viewsets.ReadOnlyModelViewSet):
    """API请求记录ViewSet"""
    queryset = APIRequest.objects.all()
    serializer_class = APIRequestSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # 按服务提供商过滤
        provider_id = self.request.query_params.get('provider')
        if provider_id:
            queryset = queryset.filter(provider_id=provider_id)
        
        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # 按时间范围过滤
        start_time = self.request.query_params.get('start_time')
        if start_time:
            queryset = queryset.filter(start_time__gte=start_time)
        
        end_time = self.request.query_params.get('end_time')
        if end_time:
            queryset = queryset.filter(start_time__lte=end_time)
        
        return queryset.order_by('-start_time')


class TaxBureauInterfaceViewSet(viewsets.ModelViewSet):
    """税务局接口ViewSet"""
    queryset = TaxBureauInterface.objects.all()
    serializer_class = TaxBureauInterfaceSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # 按接口类型过滤
        interface_type = self.request.query_params.get('interface_type')
        if interface_type:
            queryset = queryset.filter(interface_type=interface_type)
        
        # 按服务提供商过滤
        provider_id = self.request.query_params.get('provider')
        if provider_id:
            queryset = queryset.filter(provider_id=provider_id)
        
        return queryset.filter(is_active=True).order_by('interface_type', 'interface_name')


class BankInterfaceViewSet(viewsets.ModelViewSet):
    """银行接口ViewSet"""
    queryset = BankInterface.objects.all()
    serializer_class = BankInterfaceSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # 按接口类型过滤
        interface_type = self.request.query_params.get('interface_type')
        if interface_type:
            queryset = queryset.filter(interface_type=interface_type)
        
        # 按服务提供商过滤
        provider_id = self.request.query_params.get('provider')
        if provider_id:
            queryset = queryset.filter(provider_id=provider_id)
        
        return queryset.filter(is_active=True).order_by('interface_type', 'interface_name')


class OCRServiceViewSet(viewsets.ModelViewSet):
    """OCR识别服务ViewSet"""
    queryset = OCRService.objects.all()
    serializer_class = OCRServiceSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # 按识别类型过滤
        recognition_type = self.request.query_params.get('recognition_type')
        if recognition_type:
            queryset = queryset.filter(recognition_type=recognition_type)
        
        # 按服务提供商过滤
        provider_id = self.request.query_params.get('provider')
        if provider_id:
            queryset = queryset.filter(provider_id=provider_id)
        
        return queryset.filter(is_active=True).order_by('recognition_type', 'service_name')


class ESignatureServiceViewSet(viewsets.ModelViewSet):
    """电子签章服务ViewSet"""
    queryset = ESignatureService.objects.all()
    serializer_class = ESignatureServiceSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # 按签章类型过滤
        signature_type = self.request.query_params.get('signature_type')
        if signature_type:
            queryset = queryset.filter(signature_type=signature_type)
        
        # 按服务提供商过滤
        provider_id = self.request.query_params.get('provider')
        if provider_id:
            queryset = queryset.filter(provider_id=provider_id)
        
        return queryset.filter(is_active=True).order_by('signature_type', 'service_name')


class IntegrationTaskViewSet(viewsets.ModelViewSet):
    """集成任务ViewSet"""
    queryset = IntegrationTask.objects.all()
    serializer_class = IntegrationTaskSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user
        
        # 根据用户权限过滤
        if not user.is_staff:
            queryset = queryset.filter(created_by=user)
        
        # 按任务类型过滤
        task_type = self.request.query_params.get('task_type')
        if task_type:
            queryset = queryset.filter(task_type=task_type)
        
        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # 按服务提供商过滤
        provider_id = self.request.query_params.get('provider')
        if provider_id:
            queryset = queryset.filter(provider_id=provider_id)
        
        return queryset.order_by('-created_at')
    
    def get_serializer_class(self):
        if self.action == 'list':
            return IntegrationTaskListSerializer
        return IntegrationTaskSerializer
    
    def perform_create(self, serializer):
        # 生成唯一任务ID
        task_id = f"TASK_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
        serializer.save(
            task_id=task_id,
            created_by=self.request.user
        )
    
    @action(detail=True, methods=['post'])
    def execute(self, request, pk=None):
        """执行任务"""
        task = self.get_object()
        
        if task.status not in ['pending', 'failed']:
            return Response(
                {'error': '任务状态不允许执行'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # 更新任务状态
            task.status = 'processing'
            task.start_time = timezone.now()
            task.save()
            
            # 执行具体任务
            result = self._execute_integration_task(task)
            
            # 更新任务结果
            task.status = 'completed'
            task.end_time = timezone.now()
            task.progress = 100
            task.output_data = result
            task.save()
            
            return Response({
                'message': '任务执行成功',
                'task_id': task.task_id,
                'result': result
            })
            
        except Exception as e:
            task.status = 'failed'
            task.end_time = timezone.now()
            task.error_message = str(e)
            task.save()
            
            # 记录错误日志
            IntegrationLog.objects.create(
                task=task,
                provider=task.provider,
                level='ERROR',
                message=f'任务执行失败: {str(e)}',
                details={'error': str(e), 'task_type': task.task_type}
            )
            
            return Response(
                {'error': f'任务执行失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _execute_integration_task(self, task):
        """执行集成任务"""
        if task.task_type == 'invoice_verification':
            return self._execute_invoice_verification(task)
        elif task.task_type == 'ocr_recognition':
            return self._execute_ocr_recognition(task)
        elif task.task_type == 'document_signature':
            return self._execute_document_signature(task)
        elif task.task_type == 'bank_transaction':
            return self._execute_bank_transaction(task)
        elif task.task_type == 'tax_declaration':
            return self._execute_tax_declaration(task)
        else:
            return {'message': '任务类型不支持'}
    
    def _execute_invoice_verification(self, task):
        """执行发票验证"""
        # 模拟发票验证逻辑
        input_data = task.input_data
        
        # 记录日志
        IntegrationLog.objects.create(
            task=task,
            provider=task.provider,
            level='INFO',
            message='开始发票验证',
            details=input_data
        )
        
        # 模拟验证结果
        result = {
            'verification_result': 'success',
            'invoice_info': {
                'invoice_number': input_data.get('invoice_number'),
                'invoice_code': input_data.get('invoice_code'),
                'seller_name': '示例销售方',
                'buyer_name': '示例购买方',
                'total_amount': input_data.get('total_amount'),
                'tax_amount': float(input_data.get('total_amount', 0)) * 0.13,
                'issue_date': input_data.get('issue_date'),
                'status': 'valid'
            }
        }
        
        return result
    
    def _execute_ocr_recognition(self, task):
        """执行OCR识别"""
        # 模拟OCR识别逻辑
        input_data = task.input_data
        
        # 记录日志
        IntegrationLog.objects.create(
            task=task,
            provider=task.provider,
            level='INFO',
            message='开始OCR识别',
            details=input_data
        )
        
        # 模拟识别结果
        result = {
            'recognition_result': 'success',
            'extracted_data': {
                'invoice_number': '12345678',
                'invoice_code': '**********',
                'seller_name': '示例企业有限公司',
                'buyer_name': '购买方企业',
                'total_amount': '1130.00',
                'tax_amount': '130.00',
                'issue_date': '2024-01-01'
            },
            'confidence_scores': {
                'invoice_number': 0.95,
                'invoice_code': 0.98,
                'seller_name': 0.92,
                'buyer_name': 0.89,
                'total_amount': 0.96,
                'tax_amount': 0.94,
                'issue_date': 0.97
            }
        }
        
        return result
    
    def _execute_document_signature(self, task):
        """执行文档签章"""
        # 模拟文档签章逻辑
        input_data = task.input_data
        
        # 记录日志
        IntegrationLog.objects.create(
            task=task,
            provider=task.provider,
            level='INFO',
            message='开始文档签章',
            details=input_data
        )
        
        # 模拟签章结果
        result = {
            'signature_result': 'success',
            'signed_document_url': '/media/signed_documents/example_signed.pdf',
            'signature_info': {
                'signer_name': input_data.get('signer_info', {}).get('name', '签章人'),
                'signature_time': timezone.now().isoformat(),
                'certificate_serial': 'CERT123456789',
                'signature_algorithm': 'SHA256withRSA'
            }
        }
        
        return result
    
    def _execute_bank_transaction(self, task):
        """执行银行交易"""
        # 模拟银行交易逻辑
        input_data = task.input_data
        
        # 记录日志
        IntegrationLog.objects.create(
            task=task,
            provider=task.provider,
            level='INFO',
            message='开始银行交易',
            details=input_data
        )
        
        # 模拟交易结果
        result = {
            'transaction_result': 'success',
            'transaction_id': f"TXN{timezone.now().strftime('%Y%m%d%H%M%S')}",
            'transaction_info': {
                'amount': input_data.get('amount'),
                'currency': input_data.get('currency', 'CNY'),
                'transaction_time': timezone.now().isoformat(),
                'status': 'completed'
            }
        }
        
        return result
    
    def _execute_tax_declaration(self, task):
        """执行税务申报"""
        # 模拟税务申报逻辑
        input_data = task.input_data
        
        # 记录日志
        IntegrationLog.objects.create(
            task=task,
            provider=task.provider,
            level='INFO',
            message='开始税务申报',
            details=input_data
        )
        
        # 模拟申报结果
        result = {
            'declaration_result': 'success',
            'declaration_id': f"DECL{timezone.now().strftime('%Y%m%d%H%M%S')}",
            'declaration_info': {
                'tax_period': input_data.get('tax_period'),
                'declaration_time': timezone.now().isoformat(),
                'status': 'submitted'
            }
        }
        
        return result

    @action(detail=True, methods=['post'])
    def retry(self, request, pk=None):
        """重试任务"""
        task = self.get_object()

        if not task.can_retry():
            return Response(
                {'error': '任务不能重试'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 重置任务状态
        task.status = 'pending'
        task.retry_count += 1
        task.error_code = ''
        task.error_message = ''
        task.progress = 0
        task.save()

        return Response({'message': '任务已重新排队'})

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """取消任务"""
        task = self.get_object()

        if task.status not in ['pending', 'processing']:
            return Response(
                {'error': '任务状态不允许取消'},
                status=status.HTTP_400_BAD_REQUEST
            )

        task.status = 'cancelled'
        task.end_time = timezone.now()
        task.save()

        return Response({'message': '任务已取消'})


class IntegrationLogViewSet(viewsets.ReadOnlyModelViewSet):
    """集成日志ViewSet"""
    queryset = IntegrationLog.objects.all()
    serializer_class = IntegrationLogSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按任务过滤
        task_id = self.request.query_params.get('task')
        if task_id:
            queryset = queryset.filter(task_id=task_id)

        # 按服务提供商过滤
        provider_id = self.request.query_params.get('provider')
        if provider_id:
            queryset = queryset.filter(provider_id=provider_id)

        # 按日志级别过滤
        level = self.request.query_params.get('level')
        if level:
            queryset = queryset.filter(level=level)

        return queryset.order_by('-timestamp')


class IntegrationConfigViewSet(viewsets.ModelViewSet):
    """集成配置ViewSet"""
    queryset = IntegrationConfig.objects.all()
    serializer_class = IntegrationConfigSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user

        # 根据用户权限过滤
        if not user.is_staff:
            queryset = queryset.filter(
                Q(config_type='user', user=user) |
                Q(config_type='company', company__in=user.companies.all()) |
                Q(config_type='global')
            )

        # 按配置类型过滤
        config_type = self.request.query_params.get('config_type')
        if config_type:
            queryset = queryset.filter(config_type=config_type)

        # 按服务提供商过滤
        provider_id = self.request.query_params.get('provider')
        if provider_id:
            queryset = queryset.filter(provider_id=provider_id)

        return queryset.filter(is_active=True).order_by('config_type', 'config_key')

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


# 业务操作视图
class BusinessOperationViewSet(viewsets.ViewSet):
    """业务操作ViewSet"""
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['post'])
    def verify_invoice(self, request):
        """发票验证"""
        serializer = InvoiceVerificationSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data

        # 查找可用的税务局接口
        tax_interface = TaxBureauInterface.objects.filter(
            interface_type='invoice_verification',
            is_active=True,
            provider__status='active'
        ).first()

        if not tax_interface:
            return Response(
                {'error': '未找到可用的发票验证接口'},
                status=status.HTTP_404_NOT_FOUND
            )

        # 创建集成任务
        task = IntegrationTask.objects.create(
            task_id=f"INV_VERIFY_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}",
            task_type='invoice_verification',
            provider=tax_interface.provider,
            title=f"发票验证 - {data['invoice_number']}",
            description=f"验证发票号码为 {data['invoice_number']} 的发票",
            input_data=data,
            created_by=request.user
        )

        return Response({
            'message': '发票验证任务已创建',
            'task_id': task.task_id,
            'task_url': f'/api/v1/third-party-integration/tasks/{task.id}/'
        })

    @action(detail=False, methods=['post'])
    def ocr_recognition(self, request):
        """OCR识别"""
        serializer = OCRRecognitionSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data

        # 查找可用的OCR服务
        ocr_service = OCRService.objects.filter(
            recognition_type=data['recognition_type'],
            is_active=True,
            provider__status='active'
        ).first()

        if not ocr_service:
            return Response(
                {'error': f"未找到可用的{data['recognition_type']}识别服务"},
                status=status.HTTP_404_NOT_FOUND
            )

        # 处理上传的文件
        image_file = data['image_file']
        # 这里可以保存文件到指定位置

        # 创建集成任务
        task = IntegrationTask.objects.create(
            task_id=f"OCR_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}",
            task_type='ocr_recognition',
            provider=ocr_service.provider,
            title=f"OCR识别 - {ocr_service.get_recognition_type_display()}",
            description=f"识别{ocr_service.get_recognition_type_display()}图片",
            input_data={
                'recognition_type': data['recognition_type'],
                'image_file_path': f'/media/ocr_images/{image_file.name}',
                'extract_fields': data.get('extract_fields', []),
                'confidence_threshold': data.get('confidence_threshold', 0.8)
            },
            input_files=[f'/media/ocr_images/{image_file.name}'],
            created_by=request.user
        )

        return Response({
            'message': 'OCR识别任务已创建',
            'task_id': task.task_id,
            'task_url': f'/api/v1/third-party-integration/tasks/{task.id}/'
        })

    @action(detail=False, methods=['post'])
    def sign_document(self, request):
        """文档签章"""
        serializer = DocumentSignatureSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data

        # 查找可用的电子签章服务
        signature_service = ESignatureService.objects.filter(
            signature_type=data['signature_type'],
            is_active=True,
            provider__status='active'
        ).first()

        if not signature_service:
            return Response(
                {'error': f"未找到可用的{data['signature_type']}签章服务"},
                status=status.HTTP_404_NOT_FOUND
            )

        # 处理上传的文档
        document_file = data['document_file']
        # 这里可以保存文件到指定位置

        # 创建集成任务
        task = IntegrationTask.objects.create(
            task_id=f"SIGN_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}",
            task_type='document_signature',
            provider=signature_service.provider,
            title=f"文档签章 - {signature_service.get_signature_type_display()}",
            description=f"对文档进行{signature_service.get_signature_type_display()}",
            input_data={
                'signature_type': data['signature_type'],
                'document_file_path': f'/media/documents/{document_file.name}',
                'signature_position': data.get('signature_position', {}),
                'signer_info': data['signer_info']
            },
            input_files=[f'/media/documents/{document_file.name}'],
            created_by=request.user
        )

        return Response({
            'message': '文档签章任务已创建',
            'task_id': task.task_id,
            'task_url': f'/api/v1/third-party-integration/tasks/{task.id}/'
        })

    @action(detail=False, methods=['post'])
    def bank_transaction(self, request):
        """银行交易"""
        serializer = BankTransactionSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data

        # 查找可用的银行接口
        bank_interface = BankInterface.objects.filter(
            interface_type=data['transaction_type'],
            is_active=True,
            provider__status='active'
        ).first()

        if not bank_interface:
            return Response(
                {'error': f"未找到可用的{data['transaction_type']}接口"},
                status=status.HTTP_404_NOT_FOUND
            )

        # 创建集成任务
        task = IntegrationTask.objects.create(
            task_id=f"BANK_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}",
            task_type='bank_transaction',
            provider=bank_interface.provider,
            title=f"银行交易 - {data['transaction_type']}",
            description=f"执行银行{data['transaction_type']}操作",
            input_data=data,
            created_by=request.user
        )

        return Response({
            'message': '银行交易任务已创建',
            'task_id': task.task_id,
            'task_url': f'/api/v1/third-party-integration/tasks/{task.id}/'
        })

    @action(detail=False, methods=['post'])
    def execute_task(self, request):
        """执行自定义任务"""
        serializer = TaskExecutionSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data

        # 获取服务提供商
        try:
            provider = ThirdPartyProvider.objects.get(
                id=data['provider_id'],
                is_active=True,
                status='active'
            )
        except ThirdPartyProvider.DoesNotExist:
            return Response(
                {'error': '服务提供商不存在或不可用'},
                status=status.HTTP_404_NOT_FOUND
            )

        # 创建集成任务
        task = IntegrationTask.objects.create(
            task_id=f"CUSTOM_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}",
            task_type=data['task_type'],
            provider=provider,
            title=data['title'],
            description=data.get('description', ''),
            input_data=data['input_data'],
            scheduled_time=data.get('scheduled_time'),
            callback_url=data.get('callback_url'),
            max_retries=data.get('max_retries', 3),
            created_by=request.user
        )

        return Response({
            'message': '任务已创建',
            'task_id': task.task_id,
            'task_url': f'/api/v1/third-party-integration/tasks/{task.id}/'
        })

    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """集成仪表板"""
        # 服务提供商统计
        provider_stats = ThirdPartyProvider.objects.values('provider_type').annotate(
            total=Count('id'),
            active=Count('id', filter=Q(status='active')),
            inactive=Count('id', filter=Q(status__in=['inactive', 'error', 'maintenance']))
        )

        # 任务统计
        task_stats = IntegrationTask.objects.values('status').annotate(
            count=Count('id')
        )

        # 最近7天的任务趋势
        seven_days_ago = timezone.now() - timedelta(days=7)
        daily_tasks = []
        for i in range(7):
            date = (timezone.now() - timedelta(days=i)).date()
            day_tasks = IntegrationTask.objects.filter(created_at__date=date)

            daily_tasks.insert(0, {
                'date': date.strftime('%Y-%m-%d'),
                'total': day_tasks.count(),
                'completed': day_tasks.filter(status='completed').count(),
                'failed': day_tasks.filter(status='failed').count()
            })

        # 错误统计
        error_stats = IntegrationTask.objects.filter(
            status='failed',
            created_at__gte=seven_days_ago
        ).values('error_code').annotate(
            count=Count('id')
        ).order_by('-count')[:10]

        # 服务提供商性能
        provider_performance = ThirdPartyProvider.objects.filter(
            total_requests__gt=0
        ).annotate(
            success_rate=F('success_requests') * 100.0 / F('total_requests')
        ).order_by('-success_rate')[:10]

        return Response({
            'provider_stats': list(provider_stats),
            'task_stats': {item['status']: item['count'] for item in task_stats},
            'daily_tasks': daily_tasks,
            'error_stats': list(error_stats),
            'provider_performance': [
                {
                    'name': p.name,
                    'provider_type': p.get_provider_type_display(),
                    'success_rate': p.get_success_rate(),
                    'total_requests': p.total_requests
                }
                for p in provider_performance
            ]
        })

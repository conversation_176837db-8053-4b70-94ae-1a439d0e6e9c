from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    ThirdPartyProvider, APIRequest, TaxBureauInterface, BankInterface,
    OCRService, ESignatureService, IntegrationTask, IntegrationLog,
    IntegrationConfig
)

User = get_user_model()


class ThirdPartyProviderSerializer(serializers.ModelSerializer):
    """第三方服务提供商序列化器"""
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    provider_type_display = serializers.CharField(source='get_provider_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    success_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = ThirdPartyProvider
        fields = [
            'id', 'name', 'provider_type', 'provider_type_display', 'description',
            'api_base_url', 'api_version', 'authentication_type', 'config_params',
            'rate_limit', 'timeout', 'status', 'status_display', 'last_test_time',
            'last_test_result', 'error_message', 'total_requests', 'success_requests',
            'failed_requests', 'success_rate', 'is_active', 'created_by',
            'created_by_name', 'created_at', 'updated_at'
        ]
        extra_kwargs = {
            'api_key': {'write_only': True},
            'secret_key': {'write_only': True},
        }
    
    def get_success_rate(self, obj):
        return obj.get_success_rate()


class APIRequestSerializer(serializers.ModelSerializer):
    """API请求记录序列化器"""
    provider_name = serializers.CharField(source='provider.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    is_success = serializers.SerializerMethodField()
    
    class Meta:
        model = APIRequest
        fields = [
            'id', 'provider', 'provider_name', 'request_id', 'method', 'endpoint',
            'headers', 'parameters', 'request_body', 'status_code', 'response_headers',
            'response_body', 'status', 'status_display', 'start_time', 'end_time',
            'duration', 'error_code', 'error_message', 'retry_count', 'company',
            'business_type', 'business_id', 'is_success', 'created_by',
            'created_by_name'
        ]
    
    def get_is_success(self, obj):
        return obj.is_success()


class TaxBureauInterfaceSerializer(serializers.ModelSerializer):
    """税务局接口序列化器"""
    provider_name = serializers.CharField(source='provider.name', read_only=True)
    interface_type_display = serializers.CharField(source='get_interface_type_display', read_only=True)
    
    class Meta:
        model = TaxBureauInterface
        fields = [
            'id', 'provider', 'provider_name', 'interface_type', 'interface_type_display',
            'interface_name', 'endpoint', 'method', 'required_params', 'optional_params',
            'response_format', 'supported_regions', 'taxpayer_types', 'is_active',
            'created_at', 'updated_at'
        ]


class BankInterfaceSerializer(serializers.ModelSerializer):
    """银行接口序列化器"""
    provider_name = serializers.CharField(source='provider.name', read_only=True)
    interface_type_display = serializers.CharField(source='get_interface_type_display', read_only=True)
    
    class Meta:
        model = BankInterface
        fields = [
            'id', 'provider', 'provider_name', 'interface_type', 'interface_type_display',
            'interface_name', 'endpoint', 'method', 'required_params', 'optional_params',
            'response_format', 'bank_code', 'supported_currencies', 'transaction_limits',
            'is_active', 'created_at', 'updated_at'
        ]


class OCRServiceSerializer(serializers.ModelSerializer):
    """OCR识别服务序列化器"""
    provider_name = serializers.CharField(source='provider.name', read_only=True)
    recognition_type_display = serializers.CharField(source='get_recognition_type_display', read_only=True)
    
    class Meta:
        model = OCRService
        fields = [
            'id', 'provider', 'provider_name', 'recognition_type', 'recognition_type_display',
            'service_name', 'endpoint', 'supported_formats', 'max_file_size', 'accuracy_rate',
            'extract_fields', 'confidence_threshold', 'pricing_model', 'price_per_request',
            'is_active', 'created_at', 'updated_at'
        ]


class ESignatureServiceSerializer(serializers.ModelSerializer):
    """电子签章服务序列化器"""
    provider_name = serializers.CharField(source='provider.name', read_only=True)
    signature_type_display = serializers.CharField(source='get_signature_type_display', read_only=True)
    
    class Meta:
        model = ESignatureService
        fields = [
            'id', 'provider', 'provider_name', 'signature_type', 'signature_type_display',
            'service_name', 'endpoint', 'supported_formats', 'certificate_type',
            'encryption_algorithm', 'signature_position', 'watermark_enabled',
            'legal_validity', 'compliance_standards', 'is_active', 'created_at', 'updated_at'
        ]


class IntegrationTaskSerializer(serializers.ModelSerializer):
    """集成任务序列化器"""
    provider_name = serializers.CharField(source='provider.name', read_only=True)
    company_name = serializers.CharField(source='company.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    task_type_display = serializers.CharField(source='get_task_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    duration = serializers.SerializerMethodField()
    can_retry = serializers.SerializerMethodField()
    
    class Meta:
        model = IntegrationTask
        fields = [
            'id', 'task_id', 'task_type', 'task_type_display', 'provider', 'provider_name',
            'title', 'description', 'input_data', 'input_files', 'output_data',
            'output_files', 'status', 'status_display', 'progress', 'scheduled_time',
            'start_time', 'end_time', 'duration', 'error_code', 'error_message',
            'retry_count', 'max_retries', 'can_retry', 'company', 'company_name',
            'business_type', 'business_id', 'callback_url', 'webhook_data',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
    
    def get_duration(self, obj):
        return obj.get_duration()
    
    def get_can_retry(self, obj):
        return obj.can_retry()


class IntegrationLogSerializer(serializers.ModelSerializer):
    """集成日志序列化器"""
    task_id = serializers.CharField(source='task.task_id', read_only=True)
    provider_name = serializers.CharField(source='provider.name', read_only=True)
    
    class Meta:
        model = IntegrationLog
        fields = [
            'id', 'task', 'task_id', 'provider', 'provider_name', 'level',
            'message', 'details', 'request_id', 'api_endpoint', 'timestamp'
        ]


class IntegrationConfigSerializer(serializers.ModelSerializer):
    """集成配置序列化器"""
    provider_name = serializers.CharField(source='provider.name', read_only=True)
    company_name = serializers.CharField(source='company.name', read_only=True)
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    config_type_display = serializers.CharField(source='get_config_type_display', read_only=True)
    
    class Meta:
        model = IntegrationConfig
        fields = [
            'id', 'config_type', 'config_type_display', 'config_key', 'config_value',
            'description', 'provider', 'provider_name', 'company', 'company_name',
            'user', 'user_name', 'is_encrypted', 'is_active', 'created_by',
            'created_by_name', 'created_at', 'updated_at'
        ]


# 简化版序列化器用于列表显示
class ThirdPartyProviderListSerializer(serializers.ModelSerializer):
    """第三方服务提供商列表序列化器"""
    provider_type_display = serializers.CharField(source='get_provider_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    success_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = ThirdPartyProvider
        fields = [
            'id', 'name', 'provider_type_display', 'status_display',
            'success_rate', 'total_requests', 'is_active', 'updated_at'
        ]
    
    def get_success_rate(self, obj):
        return obj.get_success_rate()


class IntegrationTaskListSerializer(serializers.ModelSerializer):
    """集成任务列表序列化器"""
    provider_name = serializers.CharField(source='provider.name', read_only=True)
    task_type_display = serializers.CharField(source='get_task_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = IntegrationTask
        fields = [
            'id', 'task_id', 'title', 'task_type_display', 'provider_name',
            'status_display', 'progress', 'created_at'
        ]


# 业务操作序列化器
class InvoiceVerificationSerializer(serializers.Serializer):
    """发票验证序列化器"""
    invoice_number = serializers.CharField(max_length=50, help_text='发票号码')
    invoice_code = serializers.CharField(max_length=50, help_text='发票代码')
    issue_date = serializers.DateField(help_text='开票日期')
    total_amount = serializers.DecimalField(max_digits=15, decimal_places=2, help_text='价税合计')
    verification_code = serializers.CharField(max_length=20, required=False, help_text='校验码')


class OCRRecognitionSerializer(serializers.Serializer):
    """OCR识别序列化器"""
    recognition_type = serializers.ChoiceField(
        choices=OCRService.RECOGNITION_TYPES,
        help_text='识别类型'
    )
    image_file = serializers.FileField(help_text='图片文件')
    extract_fields = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        help_text='需要提取的字段'
    )
    confidence_threshold = serializers.FloatField(
        min_value=0.0,
        max_value=1.0,
        required=False,
        default=0.8,
        help_text='置信度阈值'
    )


class DocumentSignatureSerializer(serializers.Serializer):
    """文档签章序列化器"""
    signature_type = serializers.ChoiceField(
        choices=ESignatureService.SIGNATURE_TYPES,
        help_text='签章类型'
    )
    document_file = serializers.FileField(help_text='文档文件')
    signature_position = serializers.JSONField(
        required=False,
        help_text='签章位置'
    )
    signer_info = serializers.JSONField(help_text='签章人信息')
    certificate_password = serializers.CharField(
        max_length=200,
        required=False,
        help_text='证书密码'
    )


class BankTransactionSerializer(serializers.Serializer):
    """银行交易序列化器"""
    transaction_type = serializers.ChoiceField(
        choices=[
            ('transfer', '转账'),
            ('payment', '付款'),
            ('collection', '收款'),
            ('query', '查询'),
        ],
        help_text='交易类型'
    )
    account_number = serializers.CharField(max_length=50, help_text='账户号码')
    amount = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
        required=False,
        help_text='交易金额'
    )
    currency = serializers.CharField(
        max_length=10,
        default='CNY',
        help_text='币种'
    )
    recipient_account = serializers.CharField(
        max_length=50,
        required=False,
        help_text='收款账户'
    )
    recipient_name = serializers.CharField(
        max_length=200,
        required=False,
        help_text='收款人姓名'
    )
    purpose = serializers.CharField(
        max_length=500,
        required=False,
        help_text='交易用途'
    )


class TaskExecutionSerializer(serializers.Serializer):
    """任务执行序列化器"""
    task_type = serializers.ChoiceField(
        choices=IntegrationTask.TASK_TYPES,
        help_text='任务类型'
    )
    provider_id = serializers.IntegerField(help_text='服务提供商ID')
    title = serializers.CharField(max_length=200, help_text='任务标题')
    description = serializers.CharField(
        required=False,
        help_text='任务描述'
    )
    input_data = serializers.JSONField(help_text='输入数据')
    scheduled_time = serializers.DateTimeField(
        required=False,
        help_text='计划执行时间'
    )
    callback_url = serializers.URLField(
        required=False,
        help_text='回调URL'
    )
    max_retries = serializers.IntegerField(
        min_value=0,
        max_value=10,
        default=3,
        help_text='最大重试次数'
    )

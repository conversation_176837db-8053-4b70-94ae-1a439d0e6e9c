from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.db.models import Count, Avg
from .models import (
    ThirdPartyProvider, APIRequest, TaxBureauInterface, BankInterface,
    OCRService, ESignatureService, IntegrationTask, IntegrationLog,
    IntegrationConfig
)


@admin.register(ThirdPartyProvider)
class ThirdPartyProviderAdmin(admin.ModelAdmin):
    """第三方服务提供商管理"""
    list_display = [
        'name', 'provider_type_display', 'status_display', 'success_rate_display',
        'total_requests', 'last_test_time', 'is_active'
    ]
    list_filter = ['provider_type', 'status', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['total_requests', 'success_requests', 'failed_requests', 'last_test_time', 'last_test_result', 'created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'provider_type', 'description')
        }),
        ('连接配置', {
            'fields': ('api_base_url', 'api_version', 'authentication_type')
        }),
        ('认证信息', {
            'fields': ('api_key', 'secret_key', 'app_id', 'certificate_path'),
            'classes': ('collapse',)
        }),
        ('配置参数', {
            'fields': ('config_params', 'rate_limit', 'timeout')
        }),
        ('状态信息', {
            'fields': ('status', 'last_test_time', 'last_test_result', 'error_message')
        }),
        ('统计信息', {
            'fields': ('total_requests', 'success_requests', 'failed_requests'),
            'classes': ('collapse',)
        }),
        ('其他', {
            'fields': ('is_active', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    actions = ['test_connection', 'reset_statistics']
    
    def provider_type_display(self, obj):
        """服务类型显示"""
        type_colors = {
            'tax_bureau': '#1890ff',
            'bank': '#52c41a',
            'ocr': '#faad14',
            'e_signature': '#722ed1',
            'payment': '#fa8c16',
            'sms': '#13c2c2',
            'email': '#eb2f96',
            'storage': '#f5222d',
            'other': '#d9d9d9',
        }
        color = type_colors.get(obj.provider_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_provider_type_display()
        )
    provider_type_display.short_description = '服务类型'
    
    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'active': '#52c41a',
            'inactive': '#d9d9d9',
            'maintenance': '#faad14',
            'error': '#f5222d',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'
    
    def success_rate_display(self, obj):
        """成功率显示"""
        rate = obj.get_success_rate()
        if rate >= 95:
            color = '#52c41a'
        elif rate >= 90:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, f"{rate:.1f}%"
        )
    success_rate_display.short_description = '成功率'
    
    def test_connection(self, request, queryset):
        """测试连接"""
        tested_count = 0
        for provider in queryset:
            # 这里可以实现具体的连接测试逻辑
            provider.last_test_time = timezone.now()
            provider.last_test_result = True  # 模拟测试成功
            provider.save()
            tested_count += 1
        
        self.message_user(request, f"成功测试 {tested_count} 个服务提供商的连接")
    test_connection.short_description = "测试连接"
    
    def reset_statistics(self, request, queryset):
        """重置统计"""
        updated = queryset.update(
            total_requests=0,
            success_requests=0,
            failed_requests=0
        )
        self.message_user(request, f"成功重置 {updated} 个服务提供商的统计数据")
    reset_statistics.short_description = "重置统计"
    
    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(APIRequest)
class APIRequestAdmin(admin.ModelAdmin):
    """API请求记录管理"""
    list_display = [
        'request_id', 'provider_info', 'method', 'endpoint_short',
        'status_display', 'status_code', 'duration_display', 'start_time'
    ]
    list_filter = ['status', 'method', 'provider', 'start_time']
    search_fields = ['request_id', 'endpoint', 'error_message']
    readonly_fields = ['request_id', 'start_time', 'end_time', 'duration']
    date_hierarchy = 'start_time'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('provider', 'request_id', 'method', 'endpoint')
        }),
        ('请求信息', {
            'fields': ('headers', 'parameters', 'request_body'),
            'classes': ('collapse',)
        }),
        ('响应信息', {
            'fields': ('status_code', 'response_headers', 'response_body'),
            'classes': ('collapse',)
        }),
        ('执行信息', {
            'fields': ('status', 'start_time', 'end_time', 'duration')
        }),
        ('错误信息', {
            'fields': ('error_code', 'error_message', 'retry_count'),
            'classes': ('collapse',)
        }),
        ('关联信息', {
            'fields': ('company', 'business_type', 'business_id', 'created_by'),
            'classes': ('collapse',)
        }),
    )
    
    def provider_info(self, obj):
        """服务提供商信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:third_party_integration_thirdpartyprovider_change', args=[obj.provider.pk]),
            obj.provider.name
        )
    provider_info.short_description = '服务提供商'
    
    def endpoint_short(self, obj):
        """端点简短显示"""
        if len(obj.endpoint) > 50:
            return obj.endpoint[:47] + '...'
        return obj.endpoint
    endpoint_short.short_description = '接口端点'
    
    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'pending': '#faad14',
            'processing': '#1890ff',
            'success': '#52c41a',
            'failed': '#f5222d',
            'timeout': '#fa8c16',
            'cancelled': '#d9d9d9',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'
    
    def duration_display(self, obj):
        """执行时长显示"""
        if obj.duration:
            if obj.duration < 1:
                return f"{obj.duration*1000:.0f}ms"
            else:
                return f"{obj.duration:.2f}s"
        return '-'
    duration_display.short_description = '执行时长'


@admin.register(TaxBureauInterface)
class TaxBureauInterfaceAdmin(admin.ModelAdmin):
    """税务局接口管理"""
    list_display = [
        'interface_name', 'provider_info', 'interface_type_display',
        'method', 'supported_regions_display', 'is_active'
    ]
    list_filter = ['interface_type', 'provider', 'is_active']
    search_fields = ['interface_name', 'endpoint']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('provider', 'interface_type', 'interface_name', 'endpoint')
        }),
        ('接口配置', {
            'fields': ('method', 'required_params', 'optional_params', 'response_format')
        }),
        ('业务配置', {
            'fields': ('supported_regions', 'taxpayer_types')
        }),
        ('状态', {
            'fields': ('is_active',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def provider_info(self, obj):
        """服务提供商信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:third_party_integration_thirdpartyprovider_change', args=[obj.provider.pk]),
            obj.provider.name
        )
    provider_info.short_description = '服务提供商'
    
    def interface_type_display(self, obj):
        """接口类型显示"""
        type_colors = {
            'invoice_verification': '#1890ff',
            'tax_declaration': '#52c41a',
            'tax_payment': '#faad14',
            'certificate_download': '#722ed1',
            'policy_query': '#fa8c16',
            'taxpayer_info': '#13c2c2',
            'invoice_issue': '#eb2f96',
            'invoice_cancel': '#f5222d',
        }
        color = type_colors.get(obj.interface_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_interface_type_display()
        )
    interface_type_display.short_description = '接口类型'
    
    def supported_regions_display(self, obj):
        """支持地区显示"""
        regions = obj.supported_regions or []
        if len(regions) > 3:
            return f"{', '.join(regions[:3])}... ({len(regions)}个)"
        return ', '.join(regions) if regions else '全部'
    supported_regions_display.short_description = '支持地区'


@admin.register(BankInterface)
class BankInterfaceAdmin(admin.ModelAdmin):
    """银行接口管理"""
    list_display = [
        'interface_name', 'provider_info', 'interface_type_display',
        'bank_code', 'supported_currencies_display', 'is_active'
    ]
    list_filter = ['interface_type', 'provider', 'is_active']
    search_fields = ['interface_name', 'endpoint', 'bank_code']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('provider', 'interface_type', 'interface_name', 'endpoint')
        }),
        ('接口配置', {
            'fields': ('method', 'required_params', 'optional_params', 'response_format')
        }),
        ('银行配置', {
            'fields': ('bank_code', 'supported_currencies', 'transaction_limits')
        }),
        ('状态', {
            'fields': ('is_active',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def provider_info(self, obj):
        """服务提供商信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:third_party_integration_thirdpartyprovider_change', args=[obj.provider.pk]),
            obj.provider.name
        )
    provider_info.short_description = '服务提供商'
    
    def interface_type_display(self, obj):
        """接口类型显示"""
        type_colors = {
            'account_query': '#1890ff',
            'balance_query': '#52c41a',
            'transaction_query': '#faad14',
            'transfer': '#722ed1',
            'payment': '#fa8c16',
            'collection': '#13c2c2',
            'statement_download': '#eb2f96',
            'account_verification': '#f5222d',
        }
        color = type_colors.get(obj.interface_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_interface_type_display()
        )
    interface_type_display.short_description = '接口类型'
    
    def supported_currencies_display(self, obj):
        """支持币种显示"""
        currencies = obj.supported_currencies or []
        return ', '.join(currencies) if currencies else 'CNY'
    supported_currencies_display.short_description = '支持币种'


@admin.register(OCRService)
class OCRServiceAdmin(admin.ModelAdmin):
    """OCR识别服务管理"""
    list_display = [
        'service_name', 'provider_info', 'recognition_type_display',
        'accuracy_rate_display', 'max_file_size', 'price_per_request', 'is_active'
    ]
    list_filter = ['recognition_type', 'provider', 'is_active']
    search_fields = ['service_name', 'endpoint']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('provider', 'recognition_type', 'service_name', 'endpoint')
        }),
        ('服务配置', {
            'fields': ('supported_formats', 'max_file_size', 'accuracy_rate')
        }),
        ('识别配置', {
            'fields': ('extract_fields', 'confidence_threshold')
        }),
        ('计费配置', {
            'fields': ('pricing_model', 'price_per_request')
        }),
        ('状态', {
            'fields': ('is_active',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def provider_info(self, obj):
        """服务提供商信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:third_party_integration_thirdpartyprovider_change', args=[obj.provider.pk]),
            obj.provider.name
        )
    provider_info.short_description = '服务提供商'
    
    def recognition_type_display(self, obj):
        """识别类型显示"""
        type_colors = {
            'invoice': '#1890ff',
            'id_card': '#52c41a',
            'business_license': '#faad14',
            'bank_card': '#722ed1',
            'contract': '#fa8c16',
            'receipt': '#13c2c2',
            'tax_certificate': '#eb2f96',
            'general_text': '#f5222d',
        }
        color = type_colors.get(obj.recognition_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_recognition_type_display()
        )
    recognition_type_display.short_description = '识别类型'
    
    def accuracy_rate_display(self, obj):
        """准确率显示"""
        rate = obj.accuracy_rate * 100
        if rate >= 95:
            color = '#52c41a'
        elif rate >= 90:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, f"{rate:.1f}%"
        )
    accuracy_rate_display.short_description = '准确率'


@admin.register(ESignatureService)
class ESignatureServiceAdmin(admin.ModelAdmin):
    """电子签章服务管理"""
    list_display = [
        'service_name', 'provider_info', 'signature_type_display',
        'certificate_type', 'legal_validity', 'is_active'
    ]
    list_filter = ['signature_type', 'provider', 'legal_validity', 'is_active']
    search_fields = ['service_name', 'endpoint', 'certificate_type']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('provider', 'signature_type', 'service_name', 'endpoint')
        }),
        ('服务配置', {
            'fields': ('supported_formats', 'certificate_type', 'encryption_algorithm')
        }),
        ('签章配置', {
            'fields': ('signature_position', 'watermark_enabled')
        }),
        ('法律效力', {
            'fields': ('legal_validity', 'compliance_standards')
        }),
        ('状态', {
            'fields': ('is_active',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def provider_info(self, obj):
        """服务提供商信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:third_party_integration_thirdpartyprovider_change', args=[obj.provider.pk]),
            obj.provider.name
        )
    provider_info.short_description = '服务提供商'
    
    def signature_type_display(self, obj):
        """签章类型显示"""
        type_colors = {
            'personal': '#1890ff',
            'company': '#52c41a',
            'contract': '#faad14',
            'approval': '#722ed1',
            'timestamp': '#fa8c16',
            'certificate': '#13c2c2',
        }
        color = type_colors.get(obj.signature_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_signature_type_display()
        )
    signature_type_display.short_description = '签章类型'


@admin.register(IntegrationTask)
class IntegrationTaskAdmin(admin.ModelAdmin):
    """集成任务管理"""
    list_display = [
        'task_id', 'title', 'task_type_display', 'provider_info',
        'status_display', 'progress_display', 'duration_display', 'created_at'
    ]
    list_filter = ['task_type', 'status', 'provider', 'created_at']
    search_fields = ['task_id', 'title', 'description']
    readonly_fields = ['task_id', 'start_time', 'end_time', 'created_at', 'updated_at']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('基本信息', {
            'fields': ('task_id', 'task_type', 'provider', 'title', 'description')
        }),
        ('输入数据', {
            'fields': ('input_data', 'input_files'),
            'classes': ('collapse',)
        }),
        ('输出数据', {
            'fields': ('output_data', 'output_files'),
            'classes': ('collapse',)
        }),
        ('执行状态', {
            'fields': ('status', 'progress', 'scheduled_time', 'start_time', 'end_time')
        }),
        ('错误信息', {
            'fields': ('error_code', 'error_message', 'retry_count', 'max_retries'),
            'classes': ('collapse',)
        }),
        ('关联信息', {
            'fields': ('company', 'business_type', 'business_id'),
            'classes': ('collapse',)
        }),
        ('回调配置', {
            'fields': ('callback_url', 'webhook_data'),
            'classes': ('collapse',)
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['retry_tasks', 'cancel_tasks']

    def provider_info(self, obj):
        """服务提供商信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:third_party_integration_thirdpartyprovider_change', args=[obj.provider.pk]),
            obj.provider.name
        )
    provider_info.short_description = '服务提供商'

    def task_type_display(self, obj):
        """任务类型显示"""
        type_colors = {
            'invoice_verification': '#1890ff',
            'ocr_recognition': '#52c41a',
            'document_signature': '#faad14',
            'bank_transaction': '#722ed1',
            'tax_declaration': '#fa8c16',
            'data_sync': '#13c2c2',
            'batch_process': '#eb2f96',
        }
        color = type_colors.get(obj.task_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_task_type_display()
        )
    task_type_display.short_description = '任务类型'

    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'pending': '#faad14',
            'processing': '#1890ff',
            'completed': '#52c41a',
            'failed': '#f5222d',
            'cancelled': '#d9d9d9',
            'retry': '#fa8c16',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'

    def progress_display(self, obj):
        """进度显示"""
        progress = obj.progress
        if progress >= 100:
            color = '#52c41a'
        elif progress >= 50:
            color = '#faad14'
        else:
            color = '#1890ff'

        return format_html(
            '<div style="width: 100px; background: #f0f0f0; border-radius: 3px;">'
            '<div style="width: {}%; background: {}; height: 20px; border-radius: 3px; text-align: center; color: white; font-size: 12px; line-height: 20px;">'
            '{}%</div></div>',
            progress, color, progress
        )
    progress_display.short_description = '进度'

    def duration_display(self, obj):
        """执行时长显示"""
        duration = obj.get_duration()
        if duration:
            if duration < 60:
                return f"{duration:.1f}秒"
            elif duration < 3600:
                return f"{duration/60:.1f}分钟"
            else:
                return f"{duration/3600:.1f}小时"
        return '-'
    duration_display.short_description = '执行时长'

    def retry_tasks(self, request, queryset):
        """重试任务"""
        retryable_tasks = queryset.filter(status='failed')
        retry_count = 0

        for task in retryable_tasks:
            if task.can_retry():
                task.status = 'pending'
                task.retry_count += 1
                task.error_code = ''
                task.error_message = ''
                task.save()
                retry_count += 1

        self.message_user(request, f"成功重试 {retry_count} 个任务")
    retry_tasks.short_description = "重试选中的任务"

    def cancel_tasks(self, request, queryset):
        """取消任务"""
        updated = queryset.filter(status__in=['pending', 'processing']).update(status='cancelled')
        self.message_user(request, f"成功取消 {updated} 个任务")
    cancel_tasks.short_description = "取消选中的任务"


@admin.register(IntegrationLog)
class IntegrationLogAdmin(admin.ModelAdmin):
    """集成日志管理"""
    list_display = [
        'timestamp', 'task_info', 'provider_info', 'level_display',
        'message_short', 'request_id'
    ]
    list_filter = ['level', 'provider', 'timestamp']
    search_fields = ['message', 'request_id', 'api_endpoint']
    readonly_fields = ['timestamp']
    date_hierarchy = 'timestamp'

    fieldsets = (
        ('基本信息', {
            'fields': ('task', 'provider', 'level', 'message')
        }),
        ('详细信息', {
            'fields': ('details',),
            'classes': ('collapse',)
        }),
        ('请求信息', {
            'fields': ('request_id', 'api_endpoint'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('timestamp',),
            'classes': ('collapse',)
        }),
    )

    def task_info(self, obj):
        """任务信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:third_party_integration_integrationtask_change', args=[obj.task.pk]),
            obj.task.task_id
        )
    task_info.short_description = '集成任务'

    def provider_info(self, obj):
        """服务提供商信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:third_party_integration_thirdpartyprovider_change', args=[obj.provider.pk]),
            obj.provider.name
        )
    provider_info.short_description = '服务提供商'

    def level_display(self, obj):
        """日志级别显示"""
        level_colors = {
            'DEBUG': '#d9d9d9',
            'INFO': '#1890ff',
            'WARNING': '#faad14',
            'ERROR': '#f5222d',
            'CRITICAL': '#722ed1',
        }
        color = level_colors.get(obj.level, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.level
        )
    level_display.short_description = '日志级别'

    def message_short(self, obj):
        """消息简短显示"""
        if len(obj.message) > 100:
            return obj.message[:97] + '...'
        return obj.message
    message_short.short_description = '日志消息'


@admin.register(IntegrationConfig)
class IntegrationConfigAdmin(admin.ModelAdmin):
    """集成配置管理"""
    list_display = [
        'config_key', 'config_type_display', 'provider_info',
        'company_info', 'is_encrypted', 'is_active', 'updated_at'
    ]
    list_filter = ['config_type', 'is_encrypted', 'is_active', 'provider']
    search_fields = ['config_key', 'description']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('config_type', 'config_key', 'config_value', 'description')
        }),
        ('关联对象', {
            'fields': ('provider', 'company', 'user')
        }),
        ('配置属性', {
            'fields': ('is_encrypted', 'is_active')
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def config_type_display(self, obj):
        """配置类型显示"""
        type_colors = {
            'global': '#1890ff',
            'provider': '#52c41a',
            'company': '#faad14',
            'user': '#722ed1',
        }
        color = type_colors.get(obj.config_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_config_type_display()
        )
    config_type_display.short_description = '配置类型'

    def provider_info(self, obj):
        """服务提供商信息"""
        if obj.provider:
            return format_html(
                '<a href="{}">{}</a>',
                reverse('admin:third_party_integration_thirdpartyprovider_change', args=[obj.provider.pk]),
                obj.provider.name
            )
        return '-'
    provider_info.short_description = '服务提供商'

    def company_info(self, obj):
        """企业信息"""
        if obj.company:
            return format_html(
                '<a href="{}">{}</a>',
                reverse('admin:companies_company_change', args=[obj.company.pk]),
                obj.company.name
            )
        return '-'
    company_info.short_description = '企业'

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


# 自定义管理页面标题
admin.site.site_header = '代理记账管理系统'
admin.site.site_title = '第三方系统集成'
admin.site.index_title = '第三方系统集成'

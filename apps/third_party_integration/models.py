from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from apps.companies.models import Company

User = get_user_model()


class ThirdPartyProvider(models.Model):
    """第三方服务提供商"""
    PROVIDER_TYPES = [
        ('tax_bureau', '税务局'),
        ('bank', '银行'),
        ('ocr', 'OCR识别'),
        ('e_signature', '电子签章'),
        ('payment', '支付平台'),
        ('sms', '短信服务'),
        ('email', '邮件服务'),
        ('storage', '云存储'),
        ('other', '其他'),
    ]
    
    STATUS_CHOICES = [
        ('active', '正常'),
        ('inactive', '停用'),
        ('maintenance', '维护中'),
        ('error', '异常'),
    ]
    
    name = models.CharField('服务商名称', max_length=200)
    provider_type = models.CharField('服务类型', max_length=20, choices=PROVIDER_TYPES)
    description = models.TextField('服务描述', blank=True)
    
    # 连接配置
    api_base_url = models.URLField('API基础URL', blank=True)
    api_version = models.CharField('API版本', max_length=20, blank=True)
    authentication_type = models.CharField('认证类型', max_length=50, default='api_key')
    
    # 认证信息
    api_key = models.CharField('API密钥', max_length=500, blank=True)
    secret_key = models.CharField('密钥', max_length=500, blank=True)
    app_id = models.CharField('应用ID', max_length=200, blank=True)
    certificate_path = models.CharField('证书路径', max_length=500, blank=True)
    
    # 配置参数
    config_params = models.JSONField('配置参数', default=dict, help_text='服务商特定配置参数')
    rate_limit = models.IntegerField('请求限制(次/分钟)', default=100)
    timeout = models.IntegerField('超时时间(秒)', default=30)
    
    # 状态信息
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='active')
    last_test_time = models.DateTimeField('最后测试时间', null=True, blank=True)
    last_test_result = models.BooleanField('最后测试结果', default=False)
    error_message = models.TextField('错误信息', blank=True)
    
    # 统计信息
    total_requests = models.IntegerField('总请求数', default=0)
    success_requests = models.IntegerField('成功请求数', default=0)
    failed_requests = models.IntegerField('失败请求数', default=0)
    
    is_active = models.BooleanField('是否启用', default=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_providers', verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '第三方服务提供商'
        verbose_name_plural = '第三方服务提供商'
        ordering = ['provider_type', 'name']
    
    def __str__(self):
        return f"{self.get_provider_type_display()} - {self.name}"
    
    def get_success_rate(self):
        """获取成功率"""
        if self.total_requests == 0:
            return 0
        return (self.success_requests / self.total_requests) * 100


class APIRequest(models.Model):
    """API请求记录"""
    REQUEST_METHODS = [
        ('GET', 'GET'),
        ('POST', 'POST'),
        ('PUT', 'PUT'),
        ('DELETE', 'DELETE'),
        ('PATCH', 'PATCH'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('success', '成功'),
        ('failed', '失败'),
        ('timeout', '超时'),
        ('cancelled', '已取消'),
    ]
    
    provider = models.ForeignKey(ThirdPartyProvider, on_delete=models.CASCADE, related_name='requests', verbose_name='服务提供商')
    
    # 请求信息
    request_id = models.CharField('请求ID', max_length=100, unique=True)
    method = models.CharField('请求方法', max_length=10, choices=REQUEST_METHODS)
    endpoint = models.CharField('接口端点', max_length=500)
    headers = models.JSONField('请求头', default=dict)
    parameters = models.JSONField('请求参数', default=dict)
    request_body = models.TextField('请求体', blank=True)
    
    # 响应信息
    status_code = models.IntegerField('状态码', null=True, blank=True)
    response_headers = models.JSONField('响应头', default=dict)
    response_body = models.TextField('响应体', blank=True)
    
    # 执行信息
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    start_time = models.DateTimeField('开始时间', auto_now_add=True)
    end_time = models.DateTimeField('结束时间', null=True, blank=True)
    duration = models.FloatField('执行时长(秒)', null=True, blank=True)
    
    # 错误信息
    error_code = models.CharField('错误代码', max_length=100, blank=True)
    error_message = models.TextField('错误信息', blank=True)
    retry_count = models.IntegerField('重试次数', default=0)
    
    # 关联信息
    company = models.ForeignKey(Company, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='关联企业')
    business_type = models.CharField('业务类型', max_length=50, blank=True)
    business_id = models.CharField('业务ID', max_length=100, blank=True)
    
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='创建人')
    
    class Meta:
        verbose_name = 'API请求记录'
        verbose_name_plural = 'API请求记录'
        ordering = ['-start_time']
    
    def __str__(self):
        return f"{self.provider.name} - {self.request_id}"
    
    def is_success(self):
        """是否成功"""
        return self.status == 'success' and 200 <= (self.status_code or 0) < 300


class TaxBureauInterface(models.Model):
    """税务局接口"""
    INTERFACE_TYPES = [
        ('invoice_verification', '发票验证'),
        ('tax_declaration', '税务申报'),
        ('tax_payment', '税款缴纳'),
        ('certificate_download', '证书下载'),
        ('policy_query', '政策查询'),
        ('taxpayer_info', '纳税人信息'),
        ('invoice_issue', '发票开具'),
        ('invoice_cancel', '发票作废'),
    ]
    
    provider = models.ForeignKey(ThirdPartyProvider, on_delete=models.CASCADE, related_name='tax_interfaces', verbose_name='服务提供商')
    interface_type = models.CharField('接口类型', max_length=30, choices=INTERFACE_TYPES)
    interface_name = models.CharField('接口名称', max_length=200)
    endpoint = models.CharField('接口端点', max_length=500)
    
    # 接口配置
    method = models.CharField('请求方法', max_length=10, default='POST')
    required_params = models.JSONField('必需参数', default=list, help_text='接口必需参数列表')
    optional_params = models.JSONField('可选参数', default=list, help_text='接口可选参数列表')
    response_format = models.JSONField('响应格式', default=dict, help_text='响应数据格式说明')
    
    # 业务配置
    supported_regions = models.JSONField('支持地区', default=list, help_text='支持的税务局地区')
    taxpayer_types = models.JSONField('纳税人类型', default=list, help_text='支持的纳税人类型')
    
    is_active = models.BooleanField('是否启用', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '税务局接口'
        verbose_name_plural = '税务局接口'
        unique_together = ['provider', 'interface_type']
        ordering = ['interface_type', 'interface_name']
    
    def __str__(self):
        return f"{self.provider.name} - {self.get_interface_type_display()}"


class BankInterface(models.Model):
    """银行接口"""
    INTERFACE_TYPES = [
        ('account_query', '账户查询'),
        ('balance_query', '余额查询'),
        ('transaction_query', '交易查询'),
        ('transfer', '转账'),
        ('payment', '付款'),
        ('collection', '收款'),
        ('statement_download', '对账单下载'),
        ('account_verification', '账户验证'),
    ]
    
    provider = models.ForeignKey(ThirdPartyProvider, on_delete=models.CASCADE, related_name='bank_interfaces', verbose_name='服务提供商')
    interface_type = models.CharField('接口类型', max_length=30, choices=INTERFACE_TYPES)
    interface_name = models.CharField('接口名称', max_length=200)
    endpoint = models.CharField('接口端点', max_length=500)
    
    # 接口配置
    method = models.CharField('请求方法', max_length=10, default='POST')
    required_params = models.JSONField('必需参数', default=list)
    optional_params = models.JSONField('可选参数', default=list)
    response_format = models.JSONField('响应格式', default=dict)
    
    # 银行配置
    bank_code = models.CharField('银行代码', max_length=20, blank=True)
    supported_currencies = models.JSONField('支持币种', default=list)
    transaction_limits = models.JSONField('交易限额', default=dict)
    
    is_active = models.BooleanField('是否启用', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '银行接口'
        verbose_name_plural = '银行接口'
        unique_together = ['provider', 'interface_type']
        ordering = ['interface_type', 'interface_name']
    
    def __str__(self):
        return f"{self.provider.name} - {self.get_interface_type_display()}"


class OCRService(models.Model):
    """OCR识别服务"""
    RECOGNITION_TYPES = [
        ('invoice', '发票识别'),
        ('id_card', '身份证识别'),
        ('business_license', '营业执照识别'),
        ('bank_card', '银行卡识别'),
        ('contract', '合同识别'),
        ('receipt', '收据识别'),
        ('tax_certificate', '税务证书识别'),
        ('general_text', '通用文字识别'),
    ]
    
    provider = models.ForeignKey(ThirdPartyProvider, on_delete=models.CASCADE, related_name='ocr_services', verbose_name='服务提供商')
    recognition_type = models.CharField('识别类型', max_length=30, choices=RECOGNITION_TYPES)
    service_name = models.CharField('服务名称', max_length=200)
    endpoint = models.CharField('接口端点', max_length=500)
    
    # 服务配置
    supported_formats = models.JSONField('支持格式', default=list, help_text='支持的图片格式')
    max_file_size = models.IntegerField('最大文件大小(MB)', default=10)
    accuracy_rate = models.FloatField('准确率', default=0.95, help_text='识别准确率')
    
    # 识别配置
    extract_fields = models.JSONField('提取字段', default=list, help_text='可提取的字段列表')
    confidence_threshold = models.FloatField('置信度阈值', default=0.8)
    
    # 计费配置
    pricing_model = models.CharField('计费模式', max_length=20, default='per_request')
    price_per_request = models.DecimalField('单次价格', max_digits=10, decimal_places=4, default=0)
    
    is_active = models.BooleanField('是否启用', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = 'OCR识别服务'
        verbose_name_plural = 'OCR识别服务'
        unique_together = ['provider', 'recognition_type']
        ordering = ['recognition_type', 'service_name']
    
    def __str__(self):
        return f"{self.provider.name} - {self.get_recognition_type_display()}"


class ESignatureService(models.Model):
    """电子签章服务"""
    SIGNATURE_TYPES = [
        ('personal', '个人签名'),
        ('company', '企业签章'),
        ('contract', '合同签署'),
        ('approval', '审批签字'),
        ('timestamp', '时间戳'),
        ('certificate', '数字证书'),
    ]
    
    provider = models.ForeignKey(ThirdPartyProvider, on_delete=models.CASCADE, related_name='signature_services', verbose_name='服务提供商')
    signature_type = models.CharField('签章类型', max_length=20, choices=SIGNATURE_TYPES)
    service_name = models.CharField('服务名称', max_length=200)
    endpoint = models.CharField('接口端点', max_length=500)
    
    # 服务配置
    supported_formats = models.JSONField('支持格式', default=list, help_text='支持的文档格式')
    certificate_type = models.CharField('证书类型', max_length=50, blank=True)
    encryption_algorithm = models.CharField('加密算法', max_length=50, default='RSA')
    
    # 签章配置
    signature_position = models.JSONField('签章位置', default=dict, help_text='默认签章位置配置')
    watermark_enabled = models.BooleanField('启用水印', default=True)
    
    # 法律效力
    legal_validity = models.BooleanField('具有法律效力', default=True)
    compliance_standards = models.JSONField('合规标准', default=list, help_text='符合的法律法规标准')
    
    is_active = models.BooleanField('是否启用', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '电子签章服务'
        verbose_name_plural = '电子签章服务'
        unique_together = ['provider', 'signature_type']
        ordering = ['signature_type', 'service_name']
    
    def __str__(self):
        return f"{self.provider.name} - {self.get_signature_type_display()}"


class IntegrationTask(models.Model):
    """集成任务"""
    TASK_TYPES = [
        ('invoice_verification', '发票验证'),
        ('ocr_recognition', 'OCR识别'),
        ('document_signature', '文档签章'),
        ('bank_transaction', '银行交易'),
        ('tax_declaration', '税务申报'),
        ('data_sync', '数据同步'),
        ('batch_process', '批量处理'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
        ('retry', '重试中'),
    ]
    
    task_id = models.CharField('任务ID', max_length=100, unique=True)
    task_type = models.CharField('任务类型', max_length=30, choices=TASK_TYPES)
    provider = models.ForeignKey(ThirdPartyProvider, on_delete=models.CASCADE, verbose_name='服务提供商')
    
    # 任务信息
    title = models.CharField('任务标题', max_length=200)
    description = models.TextField('任务描述', blank=True)
    
    # 输入数据
    input_data = models.JSONField('输入数据', default=dict)
    input_files = models.JSONField('输入文件', default=list, help_text='输入文件路径列表')
    
    # 输出数据
    output_data = models.JSONField('输出数据', default=dict)
    output_files = models.JSONField('输出文件', default=list, help_text='输出文件路径列表')
    
    # 执行状态
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    progress = models.IntegerField('进度百分比', default=0)
    
    # 时间信息
    scheduled_time = models.DateTimeField('计划执行时间', null=True, blank=True)
    start_time = models.DateTimeField('开始时间', null=True, blank=True)
    end_time = models.DateTimeField('结束时间', null=True, blank=True)
    
    # 错误信息
    error_code = models.CharField('错误代码', max_length=100, blank=True)
    error_message = models.TextField('错误信息', blank=True)
    retry_count = models.IntegerField('重试次数', default=0)
    max_retries = models.IntegerField('最大重试次数', default=3)
    
    # 关联信息
    company = models.ForeignKey(Company, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='关联企业')
    business_type = models.CharField('业务类型', max_length=50, blank=True)
    business_id = models.CharField('业务ID', max_length=100, blank=True)
    
    # 回调配置
    callback_url = models.URLField('回调URL', blank=True)
    webhook_data = models.JSONField('回调数据', default=dict)
    
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '集成任务'
        verbose_name_plural = '集成任务'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.get_task_type_display()} - {self.task_id}"
    
    def get_duration(self):
        """获取执行时长"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None
    
    def can_retry(self):
        """是否可以重试"""
        return self.status == 'failed' and self.retry_count < self.max_retries


class IntegrationLog(models.Model):
    """集成日志"""
    LOG_LEVELS = [
        ('DEBUG', 'DEBUG'),
        ('INFO', 'INFO'),
        ('WARNING', 'WARNING'),
        ('ERROR', 'ERROR'),
        ('CRITICAL', 'CRITICAL'),
    ]
    
    task = models.ForeignKey(IntegrationTask, on_delete=models.CASCADE, related_name='logs', verbose_name='集成任务')
    provider = models.ForeignKey(ThirdPartyProvider, on_delete=models.CASCADE, verbose_name='服务提供商')
    
    # 日志信息
    level = models.CharField('日志级别', max_length=10, choices=LOG_LEVELS)
    message = models.TextField('日志消息')
    details = models.JSONField('详细信息', default=dict)
    
    # 请求信息
    request_id = models.CharField('请求ID', max_length=100, blank=True)
    api_endpoint = models.CharField('API端点', max_length=500, blank=True)
    
    # 时间信息
    timestamp = models.DateTimeField('时间戳', auto_now_add=True)
    
    class Meta:
        verbose_name = '集成日志'
        verbose_name_plural = '集成日志'
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.level} - {self.message[:50]}"


class IntegrationConfig(models.Model):
    """集成配置"""
    CONFIG_TYPES = [
        ('global', '全局配置'),
        ('provider', '服务商配置'),
        ('company', '企业配置'),
        ('user', '用户配置'),
    ]
    
    config_type = models.CharField('配置类型', max_length=20, choices=CONFIG_TYPES)
    config_key = models.CharField('配置键', max_length=200)
    config_value = models.JSONField('配置值', default=dict)
    description = models.TextField('配置描述', blank=True)
    
    # 关联对象
    provider = models.ForeignKey(ThirdPartyProvider, on_delete=models.CASCADE, null=True, blank=True, verbose_name='服务提供商')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True, verbose_name='企业')
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, verbose_name='用户')
    
    # 配置属性
    is_encrypted = models.BooleanField('是否加密', default=False)
    is_active = models.BooleanField('是否启用', default=True)
    
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_configs', verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '集成配置'
        verbose_name_plural = '集成配置'
        unique_together = ['config_type', 'config_key', 'provider', 'company', 'user']
        ordering = ['config_type', 'config_key']
    
    def __str__(self):
        return f"{self.get_config_type_display()} - {self.config_key}"

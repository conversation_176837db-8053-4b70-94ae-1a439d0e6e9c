from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views

router = DefaultRouter()
router.register(r'providers', views.ThirdPartyProviderViewSet, basename='provider')
router.register(r'api-requests', views.APIRequestViewSet, basename='api-request')
router.register(r'tax-interfaces', views.TaxBureauInterfaceViewSet, basename='tax-interface')
router.register(r'bank-interfaces', views.BankInterfaceViewSet, basename='bank-interface')
router.register(r'ocr-services', views.OCRServiceViewSet, basename='ocr-service')
router.register(r'signature-services', views.ESignatureServiceViewSet, basename='signature-service')
router.register(r'tasks', views.IntegrationTaskViewSet, basename='integration-task')
router.register(r'logs', views.IntegrationLogViewSet, basename='integration-log')
router.register(r'configs', views.IntegrationConfigViewSet, basename='integration-config')
router.register(r'business', views.BusinessOperationViewSet, basename='business-operation')

app_name = 'third_party_integration'

urlpatterns = [
    path('', include(router.urls)),
]

from django.db import models
from django.utils import timezone
from django.contrib.auth import get_user_model
from decimal import Decimal

User = get_user_model()


class MarketingActivity(models.Model):
    """营销活动"""
    ACTIVITY_TYPE_CHOICES = [
        ('discount', '折扣活动'),
        ('cashback', '返现活动'),
        ('gift', '赠品活动'),
        ('trial', '试用活动'),
        ('referral', '推荐活动'),
        ('festival', '节日活动'),
    ]

    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('pending', '待审核'),
        ('active', '进行中'),
        ('paused', '已暂停'),
        ('ended', '已结束'),
        ('cancelled', '已取消'),
    ]

    TARGET_USER_CHOICES = [
        ('all', '所有用户'),
        ('new', '新用户'),
        ('old', '老用户'),
        ('vip', 'VIP用户'),
        ('inactive', '不活跃用户'),
    ]

    name = models.CharField('活动名称', max_length=100)
    description = models.TextField('活动描述')
    activity_type = models.CharField('活动类型', max_length=20, choices=ACTIVITY_TYPE_CHOICES)

    # 活动规则
    rules = models.JSONField('活动规则', default=dict, help_text='活动的具体规则配置')
    discount_rate = models.DecimalField('折扣率', max_digits=5, decimal_places=2, blank=True, null=True, help_text='如0.8表示8折')
    discount_amount = models.DecimalField('折扣金额', max_digits=10, decimal_places=2, blank=True, null=True)
    min_amount = models.DecimalField('最低消费金额', max_digits=10, decimal_places=2, default=Decimal('0.00'))
    max_discount = models.DecimalField('最大折扣金额', max_digits=10, decimal_places=2, blank=True, null=True)

    # 目标用户
    target_users = models.CharField('目标用户', max_length=20, choices=TARGET_USER_CHOICES, default='all')
    user_limit = models.PositiveIntegerField('用户参与限制', default=1, help_text='每个用户可参与次数')
    total_limit = models.PositiveIntegerField('总参与限制', blank=True, null=True, help_text='活动总参与次数限制')

    # 时间设置
    start_time = models.DateTimeField('开始时间')
    end_time = models.DateTimeField('结束时间')

    # 状态和统计
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='draft')
    participant_count = models.PositiveIntegerField('参与人数', default=0)
    success_count = models.PositiveIntegerField('成功转化数', default=0)
    total_cost = models.DecimalField('总成本', max_digits=12, decimal_places=2, default=Decimal('0.00'))

    # 创建信息
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='创建者')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'marketing_activities'
        verbose_name = '营销活动'
        verbose_name_plural = '营销活动管理'
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    @property
    def is_active(self):
        """是否正在进行中"""
        now = timezone.now()
        return (self.status == 'active' and
                self.start_time <= now <= self.end_time)

    @property
    def conversion_rate(self):
        """转化率"""
        if self.participant_count > 0:
            return round(self.success_count / self.participant_count * 100, 2)
        return 0


class ActivityParticipant(models.Model):
    """活动参与记录"""
    STATUS_CHOICES = [
        ('joined', '已参与'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]

    activity = models.ForeignKey(MarketingActivity, on_delete=models.CASCADE, verbose_name='活动')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')

    # 参与信息
    join_time = models.DateTimeField('参与时间', auto_now_add=True)
    complete_time = models.DateTimeField('完成时间', blank=True, null=True)
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='joined')

    # 奖励信息
    reward_amount = models.DecimalField('奖励金额', max_digits=10, decimal_places=2, default=Decimal('0.00'))
    reward_description = models.CharField('奖励描述', max_length=200, blank=True)
    is_rewarded = models.BooleanField('是否已发放奖励', default=False)

    # 额外数据
    extra_data = models.JSONField('额外数据', default=dict, blank=True)

    class Meta:
        db_table = 'marketing_activity_participants'
        verbose_name = '活动参与记录'
        verbose_name_plural = '活动参与记录'
        unique_together = ['activity', 'user']
        ordering = ['-join_time']

    def __str__(self):
        return f"{self.user.phone} - {self.activity.name}"


class RecommendationTag(models.Model):
    """推荐标签"""
    TAG_TYPE_CHOICES = [
        ('service', '服务推荐'),
        ('package', '套餐推荐'),
        ('feature', '功能推荐'),
        ('content', '内容推荐'),
    ]

    DISPLAY_POSITION_CHOICES = [
        ('banner', '横幅位置'),
        ('card', '卡片位置'),
        ('list', '列表位置'),
        ('detail', '详情位置'),
    ]

    name = models.CharField('标签名称', max_length=50)
    tag_type = models.CharField('标签类型', max_length=20, choices=TAG_TYPE_CHOICES)
    display_text = models.CharField('显示文本', max_length=20)

    # 样式配置
    background_color = models.CharField('背景色', max_length=20, default='#FF4D4F')
    text_color = models.CharField('文字颜色', max_length=20, default='#FFFFFF')
    border_color = models.CharField('边框颜色', max_length=20, blank=True)

    # 显示配置
    display_position = models.CharField('显示位置', max_length=20, choices=DISPLAY_POSITION_CHOICES, default='card')
    sort_order = models.PositiveIntegerField('排序', default=0)
    is_active = models.BooleanField('是否启用', default=True)

    # 使用统计
    use_count = models.PositiveIntegerField('使用次数', default=0)

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'marketing_recommendation_tags'
        verbose_name = '推荐标签'
        verbose_name_plural = '推荐标签管理'
        ordering = ['tag_type', 'sort_order', 'name']

    def __str__(self):
        return f"{self.get_tag_type_display()} - {self.name}"


class InvitationReward(models.Model):
    """邀请奖励配置"""
    REWARD_TYPE_CHOICES = [
        ('cash', '现金奖励'),
        ('discount', '折扣券'),
        ('service', '服务赠送'),
        ('points', '积分奖励'),
    ]

    STATUS_CHOICES = [
        ('active', '生效中'),
        ('inactive', '已停用'),
        ('expired', '已过期'),
    ]

    name = models.CharField('奖励名称', max_length=100)
    description = models.TextField('奖励描述')
    reward_type = models.CharField('奖励类型', max_length=20, choices=REWARD_TYPE_CHOICES)

    # 奖励配置
    inviter_reward = models.DecimalField('邀请人奖励', max_digits=10, decimal_places=2, default=Decimal('0.00'))
    invitee_reward = models.DecimalField('被邀请人奖励', max_digits=10, decimal_places=2, default=Decimal('0.00'))

    # 条件设置
    min_invitations = models.PositiveIntegerField('最少邀请人数', default=1)
    require_order = models.BooleanField('需要下单', default=False, help_text='被邀请人是否需要下单才能获得奖励')
    min_order_amount = models.DecimalField('最低订单金额', max_digits=10, decimal_places=2, default=Decimal('0.00'))

    # 限制设置
    max_invitations = models.PositiveIntegerField('最大邀请人数', blank=True, null=True, help_text='每人最多可邀请人数')
    daily_limit = models.PositiveIntegerField('每日限制', blank=True, null=True, help_text='每日最多奖励次数')
    total_budget = models.DecimalField('总预算', max_digits=12, decimal_places=2, blank=True, null=True)

    # 时间设置
    start_time = models.DateTimeField('开始时间', default=timezone.now)
    end_time = models.DateTimeField('结束时间', blank=True, null=True)

    # 状态和统计
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='active')
    total_invitations = models.PositiveIntegerField('总邀请数', default=0)
    successful_invitations = models.PositiveIntegerField('成功邀请数', default=0)
    total_cost = models.DecimalField('总花费', max_digits=12, decimal_places=2, default=Decimal('0.00'))

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'marketing_invitation_rewards'
        verbose_name = '邀请奖励'
        verbose_name_plural = '邀请奖励管理'
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    @property
    def is_active(self):
        """是否有效"""
        now = timezone.now()
        if self.status != 'active':
            return False
        if self.end_time and now > self.end_time:
            return False
        if self.total_budget and self.total_cost >= self.total_budget:
            return False
        return True


class InvitationRecord(models.Model):
    """邀请记录"""
    STATUS_CHOICES = [
        ('pending', '待确认'),
        ('confirmed', '已确认'),
        ('rewarded', '已奖励'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]

    reward_config = models.ForeignKey(InvitationReward, on_delete=models.CASCADE, verbose_name='奖励配置')
    inviter = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_invitations', verbose_name='邀请人')
    invitee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_invitations', verbose_name='被邀请人')

    # 邀请信息
    invitation_code = models.CharField('邀请码', max_length=20, unique=True)
    invitation_time = models.DateTimeField('邀请时间', auto_now_add=True)
    confirmation_time = models.DateTimeField('确认时间', blank=True, null=True)
    reward_time = models.DateTimeField('奖励时间', blank=True, null=True)

    # 状态和奖励
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    inviter_reward_amount = models.DecimalField('邀请人奖励金额', max_digits=10, decimal_places=2, default=Decimal('0.00'))
    invitee_reward_amount = models.DecimalField('被邀请人奖励金额', max_digits=10, decimal_places=2, default=Decimal('0.00'))

    # 关联订单（如果需要）
    related_order = models.ForeignKey('services.Order', on_delete=models.SET_NULL, null=True, blank=True, verbose_name='关联订单')

    # 额外信息
    notes = models.TextField('备注', blank=True)
    extra_data = models.JSONField('额外数据', default=dict, blank=True)

    class Meta:
        db_table = 'marketing_invitation_records'
        verbose_name = '邀请记录'
        verbose_name_plural = '邀请记录管理'
        unique_together = ['inviter', 'invitee']
        ordering = ['-invitation_time']

    def __str__(self):
        return f"{self.inviter.phone} 邀请 {self.invitee.phone}"


class PromotionConfig(models.Model):
    """促销配置"""
    PROMOTION_TYPE_CHOICES = [
        ('first_order', '首单优惠'),
        ('bulk_discount', '批量折扣'),
        ('time_limited', '限时优惠'),
        ('member_exclusive', '会员专享'),
        ('seasonal', '季节性促销'),
    ]

    name = models.CharField('促销名称', max_length=100)
    promotion_type = models.CharField('促销类型', max_length=20, choices=PROMOTION_TYPE_CHOICES)
    description = models.TextField('促销描述')

    # 促销规则
    discount_rate = models.DecimalField('折扣率', max_digits=5, decimal_places=2, blank=True, null=True)
    discount_amount = models.DecimalField('折扣金额', max_digits=10, decimal_places=2, blank=True, null=True)
    min_amount = models.DecimalField('最低金额', max_digits=10, decimal_places=2, default=Decimal('0.00'))
    max_discount = models.DecimalField('最大折扣', max_digits=10, decimal_places=2, blank=True, null=True)

    # 适用范围
    applicable_services = models.ManyToManyField('services.ServicePackage', blank=True, verbose_name='适用服务')
    applicable_user_types = models.CharField('适用用户类型', max_length=100, default='all',
                                           help_text='all,new,vip等，多个用逗号分隔')

    # 时间和限制
    start_time = models.DateTimeField('开始时间')
    end_time = models.DateTimeField('结束时间')
    usage_limit = models.PositiveIntegerField('使用限制', blank=True, null=True, help_text='总使用次数限制')
    user_limit = models.PositiveIntegerField('用户限制', default=1, help_text='每用户使用次数限制')

    # 状态和统计
    is_active = models.BooleanField('是否启用', default=True)
    usage_count = models.PositiveIntegerField('使用次数', default=0)
    total_discount = models.DecimalField('总折扣金额', max_digits=12, decimal_places=2, default=Decimal('0.00'))

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'marketing_promotion_configs'
        verbose_name = '促销配置'
        verbose_name_plural = '促销配置管理'
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    @property
    def is_valid(self):
        """是否有效"""
        now = timezone.now()
        if not self.is_active:
            return False
        if not (self.start_time <= now <= self.end_time):
            return False
        if self.usage_limit and self.usage_count >= self.usage_limit:
            return False
        return True

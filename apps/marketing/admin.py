from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db.models import Sum, Count
from .models import (
    MarketingActivity, ActivityParticipant, RecommendationTag,
    InvitationReward, InvitationRecord, PromotionConfig
)


@admin.register(MarketingActivity)
class MarketingActivityAdmin(admin.ModelAdmin):
    """营销活动管理"""
    list_display = [
        'name', 'activity_type', 'status', 'target_users',
        'participant_count', 'success_count', 'conversion_rate_display',
        'start_time', 'end_time', 'is_active_display'
    ]
    list_filter = ['activity_type', 'status', 'target_users', 'start_time', 'created_at']
    search_fields = ['name', 'description']
    list_editable = ['status']
    readonly_fields = ['participant_count', 'success_count', 'total_cost', 'created_at', 'updated_at']
    date_hierarchy = 'start_time'

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'activity_type', 'status')
        }),
        ('活动规则', {
            'fields': ('rules', 'discount_rate', 'discount_amount', 'min_amount', 'max_discount')
        }),
        ('目标用户', {
            'fields': ('target_users', 'user_limit', 'total_limit')
        }),
        ('时间设置', {
            'fields': ('start_time', 'end_time')
        }),
        ('统计信息', {
            'fields': ('participant_count', 'success_count', 'total_cost', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def conversion_rate_display(self, obj):
        """转化率显示"""
        rate = obj.conversion_rate
        if rate >= 20:
            color = '#52c41a'  # 绿色
        elif rate >= 10:
            color = '#faad14'  # 橙色
        else:
            color = '#f5222d'  # 红色

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}%</span>',
            color, "{:.1f}".format(rate)
        )
    conversion_rate_display.short_description = '转化率'

    def is_active_display(self, obj):
        """活动状态显示"""
        if obj.is_active:
            return format_html('<span style="color: #52c41a;">●</span> 进行中')
        return format_html('<span style="color: #d9d9d9;">●</span> 未进行')
    is_active_display.short_description = '当前状态'

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(ActivityParticipant)
class ActivityParticipantAdmin(admin.ModelAdmin):
    """活动参与记录管理"""
    list_display = [
        'activity', 'user_info', 'status', 'reward_amount',
        'is_rewarded', 'join_time', 'complete_time'
    ]
    list_filter = ['activity', 'status', 'is_rewarded', 'join_time']
    search_fields = ['activity__name', 'user__phone', 'user__nickname']
    readonly_fields = ['join_time']

    fieldsets = (
        ('基本信息', {
            'fields': ('activity', 'user', 'status')
        }),
        ('奖励信息', {
            'fields': ('reward_amount', 'reward_description', 'is_rewarded')
        }),
        ('时间信息', {
            'fields': ('join_time', 'complete_time')
        }),
        ('额外数据', {
            'fields': ('extra_data',),
            'classes': ('collapse',)
        })
    )

    def user_info(self, obj):
        """用户信息"""
        return format_html(
            '<a href="{}">{}</a><br/><small>{}</small>',
            reverse('admin:users_user_change', args=[obj.user.pk]),
            obj.user.nickname or obj.user.phone,
            obj.user.phone
        )
    user_info.short_description = '用户'


@admin.register(RecommendationTag)
class RecommendationTagAdmin(admin.ModelAdmin):
    """推荐标签管理"""
    list_display = [
        'name', 'tag_type', 'display_text', 'tag_preview',
        'display_position', 'use_count', 'sort_order', 'is_active'
    ]
    list_filter = ['tag_type', 'display_position', 'is_active', 'created_at']
    search_fields = ['name', 'display_text']
    list_editable = ['sort_order', 'is_active']
    readonly_fields = ['use_count', 'created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'tag_type', 'display_text')
        }),
        ('样式配置', {
            'fields': ('background_color', 'text_color', 'border_color')
        }),
        ('显示设置', {
            'fields': ('display_position', 'sort_order', 'is_active')
        }),
        ('统计信息', {
            'fields': ('use_count', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def tag_preview(self, obj):
        """标签预览"""
        style = "background-color: {}; color: {}; padding: 4px 8px; border-radius: 4px; font-size: 12px;".format(
            obj.background_color, obj.text_color
        )
        if obj.border_color:
            style += " border: 1px solid {};".format(obj.border_color)

        return format_html(
            '<span style="{}">{}</span>',
            style, obj.display_text
        )
    tag_preview.short_description = '标签预览'


@admin.register(InvitationReward)
class InvitationRewardAdmin(admin.ModelAdmin):
    """邀请奖励管理"""
    list_display = [
        'name', 'reward_type', 'inviter_reward', 'invitee_reward',
        'total_invitations', 'successful_invitations', 'success_rate_display',
        'status', 'is_active_display'
    ]
    list_filter = ['reward_type', 'status', 'require_order', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['total_invitations', 'successful_invitations', 'total_cost', 'created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'reward_type', 'status')
        }),
        ('奖励配置', {
            'fields': ('inviter_reward', 'invitee_reward')
        }),
        ('条件设置', {
            'fields': ('min_invitations', 'require_order', 'min_order_amount')
        }),
        ('限制设置', {
            'fields': ('max_invitations', 'daily_limit', 'total_budget')
        }),
        ('时间设置', {
            'fields': ('start_time', 'end_time')
        }),
        ('统计信息', {
            'fields': ('total_invitations', 'successful_invitations', 'total_cost', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def success_rate_display(self, obj):
        """成功率显示"""
        if obj.total_invitations > 0:
            rate = obj.successful_invitations / obj.total_invitations * 100
            if rate >= 50:
                color = '#52c41a'
            elif rate >= 20:
                color = '#faad14'
            else:
                color = '#f5222d'

            return format_html(
                '<span style="color: {}; font-weight: bold;">{}%</span>',
                color, "{:.1f}".format(rate)
            )
        return '-'
    success_rate_display.short_description = '成功率'

    def is_active_display(self, obj):
        """是否有效显示"""
        if obj.is_active:
            return format_html('<span style="color: #52c41a;">●</span> 有效')
        return format_html('<span style="color: #d9d9d9;">●</span> 无效')
    is_active_display.short_description = '当前状态'


@admin.register(InvitationRecord)
class InvitationRecordAdmin(admin.ModelAdmin):
    """邀请记录管理"""
    list_display = [
        'invitation_code', 'inviter_info', 'invitee_info', 'status',
        'inviter_reward_amount', 'invitee_reward_amount', 'invitation_time'
    ]
    list_filter = ['reward_config', 'status', 'invitation_time']
    search_fields = ['invitation_code', 'inviter__phone', 'invitee__phone']
    readonly_fields = ['invitation_code', 'invitation_time']

    fieldsets = (
        ('基本信息', {
            'fields': ('reward_config', 'inviter', 'invitee', 'invitation_code', 'status')
        }),
        ('奖励信息', {
            'fields': ('inviter_reward_amount', 'invitee_reward_amount')
        }),
        ('时间信息', {
            'fields': ('invitation_time', 'confirmation_time', 'reward_time')
        }),
        ('关联信息', {
            'fields': ('related_order', 'notes'),
            'classes': ('collapse',)
        }),
        ('额外数据', {
            'fields': ('extra_data',),
            'classes': ('collapse',)
        })
    )

    def inviter_info(self, obj):
        """邀请人信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:users_user_change', args=[obj.inviter.pk]),
            obj.inviter.nickname or obj.inviter.phone
        )
    inviter_info.short_description = '邀请人'

    def invitee_info(self, obj):
        """被邀请人信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:users_user_change', args=[obj.invitee.pk]),
            obj.invitee.nickname or obj.invitee.phone
        )
    invitee_info.short_description = '被邀请人'


@admin.register(PromotionConfig)
class PromotionConfigAdmin(admin.ModelAdmin):
    """促销配置管理"""
    list_display = [
        'name', 'promotion_type', 'discount_display', 'usage_count',
        'total_discount', 'start_time', 'end_time', 'is_valid_display'
    ]
    list_filter = ['promotion_type', 'is_active', 'start_time', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['usage_count', 'total_discount', 'created_at', 'updated_at']
    filter_horizontal = ['applicable_services']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'promotion_type', 'description')
        }),
        ('促销规则', {
            'fields': ('discount_rate', 'discount_amount', 'min_amount', 'max_discount')
        }),
        ('适用范围', {
            'fields': ('applicable_services', 'applicable_user_types')
        }),
        ('时间和限制', {
            'fields': ('start_time', 'end_time', 'usage_limit', 'user_limit')
        }),
        ('状态设置', {
            'fields': ('is_active',)
        }),
        ('统计信息', {
            'fields': ('usage_count', 'total_discount', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def discount_display(self, obj):
        """折扣显示"""
        if obj.discount_rate:
            return "{:.0f}折".format(obj.discount_rate * 10)
        elif obj.discount_amount:
            return "减{}元".format(obj.discount_amount)
        return '-'
    discount_display.short_description = '折扣'

    def is_valid_display(self, obj):
        """是否有效显示"""
        if obj.is_valid:
            return format_html('<span style="color: #52c41a;">●</span> 有效')
        return format_html('<span style="color: #d9d9d9;">●</span> 无效')
    is_valid_display.short_description = '当前状态'

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.db.models import Count, Q
from .models import (
    TaxEventType, TaxEventTemplate, TaxEvent, TaxReminder,
    TaxPolicy, TaxCalendarSettings
)


@admin.register(TaxEventType)
class TaxEventTypeAdmin(admin.ModelAdmin):
    """税务事件类型管理"""
    list_display = [
        'name', 'category_display', 'applicable_taxpayers_display',
        'default_advance_days', 'is_mandatory', 'color_preview', 'is_active'
    ]
    list_filter = ['category', 'applicable_taxpayers', 'is_mandatory', 'is_active']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'category', 'applicable_taxpayers', 'description')
        }),
        ('默认配置', {
            'fields': ('default_advance_days', 'is_mandatory', 'penalty_info')
        }),
        ('显示设置', {
            'fields': ('color_code', 'is_active')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def category_display(self, obj):
        """分类显示"""
        category_colors = {
            'filing': '#1890ff',
            'payment': '#52c41a',
            'declaration': '#faad14',
            'inspection': '#f5222d',
            'policy': '#722ed1',
            'reminder': '#13c2c2',
        }
        color = category_colors.get(obj.category, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_category_display()
        )
    category_display.short_description = '事件分类'
    
    def applicable_taxpayers_display(self, obj):
        """适用纳税人显示"""
        return obj.get_applicable_taxpayers_display()
    applicable_taxpayers_display.short_description = '适用纳税人'
    
    def color_preview(self, obj):
        """颜色预览"""
        return format_html(
            '<div style="width: 20px; height: 20px; background-color: {}; border: 1px solid #ccc; border-radius: 3px;"></div>',
            obj.color_code
        )
    color_preview.short_description = '颜色'


@admin.register(TaxEventTemplate)
class TaxEventTemplateAdmin(admin.ModelAdmin):
    """税务事件模板管理"""
    list_display = [
        'title', 'event_type', 'recurrence_type_display', 'advance_days',
        'applicable_scope', 'is_active', 'created_by'
    ]
    list_filter = ['event_type', 'recurrence_type', 'is_active', 'created_at']
    search_fields = ['title', 'description', 'content_template']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('event_type', 'title', 'description')
        }),
        ('时间配置', {
            'fields': ('recurrence_type', 'due_day', 'due_month', 'custom_rule')
        }),
        ('提醒配置', {
            'fields': ('advance_days', 'reminder_levels')
        }),
        ('内容模板', {
            'fields': ('content_template', 'required_documents', 'process_steps'),
            'classes': ('wide',)
        }),
        ('适用范围', {
            'fields': ('applicable_regions', 'applicable_industries')
        }),
        ('其他信息', {
            'fields': ('is_active', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def recurrence_type_display(self, obj):
        """重复类型显示"""
        type_colors = {
            'once': '#d9d9d9',
            'monthly': '#1890ff',
            'quarterly': '#52c41a',
            'yearly': '#faad14',
            'custom': '#722ed1',
        }
        color = type_colors.get(obj.recurrence_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_recurrence_type_display()
        )
    recurrence_type_display.short_description = '重复类型'
    
    def applicable_scope(self, obj):
        """适用范围"""
        regions = len(obj.applicable_regions) if obj.applicable_regions else 0
        industries = len(obj.applicable_industries) if obj.applicable_industries else 0
        return "{}个地区, {}个行业".format(regions, industries)
    applicable_scope.short_description = '适用范围'
    
    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(TaxEvent)
class TaxEventAdmin(admin.ModelAdmin):
    """税务事件管理"""
    list_display = [
        'title', 'event_type', 'company_info', 'due_date', 'status_display',
        'priority_display', 'urgency_display', 'assigned_to', 'reminder_status'
    ]
    list_filter = [
        'status', 'priority', 'event_type', 'due_date', 'assigned_to'
    ]
    search_fields = ['title', 'description', 'company__name']
    readonly_fields = ['created_at', 'updated_at', 'urgency_display']
    date_hierarchy = 'due_date'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('template', 'event_type', 'title', 'description')
        }),
        ('时间信息', {
            'fields': ('start_date', 'due_date', 'completed_date')
        }),
        ('状态和分配', {
            'fields': ('status', 'priority', 'company', 'assigned_to')
        }),
        ('内容信息', {
            'fields': ('required_documents', 'process_steps', 'notes'),
            'classes': ('wide',)
        }),
        ('提醒信息', {
            'fields': ('reminder_sent', 'last_reminder_date')
        }),
        ('完成信息', {
            'fields': ('completion_notes', 'attachments'),
            'classes': ('collapse',)
        }),
        ('时间记录', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    actions = ['mark_completed', 'send_reminder', 'assign_to_me']
    
    def company_info(self, obj):
        """企业信息"""
        if obj.company:
            return format_html(
                '<a href="{}">{}</a>',
                reverse('admin:companies_company_change', args=[obj.company.pk]),
                obj.company.name
            )
        return '-'
    company_info.short_description = '关联企业'
    
    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'pending': '#faad14',
            'in_progress': '#1890ff',
            'completed': '#52c41a',
            'overdue': '#f5222d',
            'cancelled': '#d9d9d9',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'
    
    def priority_display(self, obj):
        """优先级显示"""
        priority_colors = {
            'urgent': '#f5222d',
            'high': '#fa8c16',
            'normal': '#1890ff',
            'low': '#52c41a',
        }
        color = priority_colors.get(obj.priority, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_priority_display()
        )
    priority_display.short_description = '优先级'
    
    def urgency_display(self, obj):
        """紧急程度显示"""
        urgency = obj.get_urgency_level()
        days_left = obj.days_until_due()
        
        urgency_colors = {
            'overdue': '#f5222d',
            'urgent': '#fa8c16',
            'high': '#faad14',
            'normal': '#1890ff',
            'low': '#52c41a',
            'completed': '#722ed1',
        }
        
        urgency_texts = {
            'overdue': '已逾期',
            'urgent': '紧急',
            'high': '较急',
            'normal': '正常',
            'low': '不急',
            'completed': '已完成',
        }
        
        color = urgency_colors.get(urgency, '#d9d9d9')
        text = urgency_texts.get(urgency, '未知')
        
        if days_left is not None and urgency != 'completed':
            if days_left >= 0:
                text += " ({}天)".format(days_left)
            else:
                text += " (逾期{}天)".format(-days_left)
        
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, text
        )
    urgency_display.short_description = '紧急程度'
    
    def reminder_status(self, obj):
        """提醒状态"""
        if obj.reminder_sent:
            return format_html('<span style="color: #52c41a;">✓ 已提醒</span>')
        else:
            return format_html('<span style="color: #faad14;">○ 未提醒</span>')
    reminder_status.short_description = '提醒状态'
    
    def mark_completed(self, request, queryset):
        """标记为完成"""
        updated = queryset.filter(status__in=['pending', 'in_progress']).update(
            status='completed',
            completed_date=timezone.now().date()
        )
        self.message_user(request, "成功标记 {} 个事件为完成".format(updated))
    mark_completed.short_description = "标记为完成"
    
    def send_reminder(self, request, queryset):
        """发送提醒"""
        # 这里可以实现发送提醒的逻辑
        count = queryset.count()
        self.message_user(request, "已为 {} 个事件发送提醒".format(count))
    send_reminder.short_description = "发送提醒"
    
    def assign_to_me(self, request, queryset):
        """分配给我"""
        updated = queryset.filter(assigned_to__isnull=True).update(
            assigned_to=request.user
        )
        self.message_user(request, "成功分配 {} 个事件给您".format(updated))
    assign_to_me.short_description = "分配给我"


@admin.register(TaxReminder)
class TaxReminderAdmin(admin.ModelAdmin):
    """税务提醒管理"""
    list_display = [
        'title', 'event_info', 'recipient', 'reminder_type_display',
        'send_method_display', 'scheduled_time', 'send_status', 'read_status'
    ]
    list_filter = ['reminder_type', 'send_method', 'is_sent', 'is_read', 'scheduled_time']
    search_fields = ['title', 'content', 'event__title', 'recipient__username']
    readonly_fields = ['created_at']
    date_hierarchy = 'scheduled_time'
    
    def event_info(self, obj):
        """事件信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:tax_calendar_taxevent_change', args=[obj.event.pk]),
            obj.event.title
        )
    event_info.short_description = '关联事件'
    
    def reminder_type_display(self, obj):
        """提醒类型显示"""
        type_colors = {
            'advance': '#1890ff',
            'due': '#faad14',
            'overdue': '#f5222d',
            'completion': '#52c41a',
        }
        color = type_colors.get(obj.reminder_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_reminder_type_display()
        )
    reminder_type_display.short_description = '提醒类型'
    
    def send_method_display(self, obj):
        """发送方式显示"""
        return obj.get_send_method_display()
    send_method_display.short_description = '发送方式'
    
    def send_status(self, obj):
        """发送状态"""
        if obj.is_sent:
            return format_html(
                '<span style="color: #52c41a;">✓ 已发送</span><br/><small>{}</small>',
                obj.sent_time.strftime('%m-%d %H:%M') if obj.sent_time else ''
            )
        else:
            return format_html('<span style="color: #faad14;">○ 待发送</span>')
    send_status.short_description = '发送状态'
    
    def read_status(self, obj):
        """阅读状态"""
        if not obj.is_sent:
            return '-'
        elif obj.is_read:
            return format_html(
                '<span style="color: #52c41a;">✓ 已读</span><br/><small>{}</small>',
                obj.read_time.strftime('%m-%d %H:%M') if obj.read_time else ''
            )
        else:
            return format_html('<span style="color: #faad14;">○ 未读</span>')
    read_status.short_description = '阅读状态'


@admin.register(TaxPolicy)
class TaxPolicyAdmin(admin.ModelAdmin):
    """税务政策管理"""
    list_display = [
        'title', 'policy_type_display', 'policy_level_display',
        'issuing_authority', 'issue_date', 'effective_status', 'view_count'
    ]
    list_filter = ['policy_type', 'policy_level', 'issue_date', 'effective_date', 'is_active']
    search_fields = ['title', 'summary', 'issuing_authority', 'policy_number']
    readonly_fields = ['view_count', 'created_at', 'updated_at']
    date_hierarchy = 'issue_date'

    fieldsets = (
        ('基本信息', {
            'fields': ('title', 'policy_type', 'policy_level', 'summary')
        }),
        ('发布信息', {
            'fields': ('issuing_authority', 'policy_number', 'issue_date', 'effective_date', 'expiry_date')
        }),
        ('内容信息', {
            'fields': ('content', 'key_points'),
            'classes': ('wide',)
        }),
        ('适用范围', {
            'fields': ('applicable_regions', 'applicable_taxpayers', 'applicable_industries')
        }),
        ('关联信息', {
            'fields': ('related_events', 'tags')
        }),
        ('附件和链接', {
            'fields': ('attachments', 'external_links'),
            'classes': ('collapse',)
        }),
        ('状态信息', {
            'fields': ('is_active', 'view_count', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    filter_horizontal = ['related_events']

    def policy_type_display(self, obj):
        """政策类型显示"""
        type_colors = {
            'law': '#f5222d',
            'regulation': '#fa8c16',
            'notice': '#1890ff',
            'interpretation': '#52c41a',
            'announcement': '#722ed1',
        }
        color = type_colors.get(obj.policy_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_policy_type_display()
        )
    policy_type_display.short_description = '政策类型'

    def policy_level_display(self, obj):
        """政策级别显示"""
        level_colors = {
            'national': '#f5222d',
            'provincial': '#fa8c16',
            'municipal': '#1890ff',
            'district': '#52c41a',
        }
        color = level_colors.get(obj.policy_level, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_policy_level_display()
        )
    policy_level_display.short_description = '政策级别'

    def effective_status(self, obj):
        """有效状态"""
        if obj.is_effective():
            return format_html('<span style="color: #52c41a;">✓ 有效</span>')
        else:
            return format_html('<span style="color: #f5222d;">✗ 无效</span>')
    effective_status.short_description = '有效状态'

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(TaxCalendarSettings)
class TaxCalendarSettingsAdmin(admin.ModelAdmin):
    """税务日历设置管理"""
    list_display = [
        'company', 'default_view', 'enable_reminders', 'default_reminder_days',
        'visible_types_count', 'updated_at'
    ]
    list_filter = ['default_view', 'enable_reminders', 'show_weekends', 'hide_completed_events']
    search_fields = ['company__name']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('企业信息', {
            'fields': ('company',)
        }),
        ('显示设置', {
            'fields': ('default_view', 'show_weekends', 'start_of_week', 'timezone')
        }),
        ('提醒设置', {
            'fields': ('enable_reminders', 'default_reminder_days', 'reminder_methods')
        }),
        ('事件过滤', {
            'fields': ('visible_event_types', 'hide_completed_events')
        }),
        ('个性化设置', {
            'fields': ('custom_colors',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    filter_horizontal = ['visible_event_types']

    def visible_types_count(self, obj):
        """可见事件类型数量"""
        return obj.visible_event_types.count()
    visible_types_count.short_description = '可见事件类型数'


# 自定义管理页面标题
admin.site.site_header = '代理记账管理系统'
admin.site.site_title = '税务日历管理'
admin.site.index_title = '税务日历管理'

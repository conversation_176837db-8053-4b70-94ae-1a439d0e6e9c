from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

router = DefaultRouter()
router.register(r'event-types', views.TaxEventTypeViewSet, basename='tax-event-type')
router.register(r'templates', views.TaxEventTemplateViewSet, basename='tax-event-template')
router.register(r'events', views.TaxEventViewSet, basename='tax-event')
router.register(r'reminders', views.TaxReminderViewSet, basename='tax-reminder')
router.register(r'policies', views.TaxPolicyViewSet, basename='tax-policy')
router.register(r'settings', views.TaxCalendarSettingsViewSet, basename='tax-calendar-settings')

app_name = 'tax_calendar'

urlpatterns = [
    path('', include(router.urls)),
]

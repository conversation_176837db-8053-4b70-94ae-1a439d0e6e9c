from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    TaxEventType, TaxEventTemplate, TaxEvent, TaxReminder,
    TaxPolicy, TaxCalendarSettings
)

User = get_user_model()


class TaxEventTypeSerializer(serializers.ModelSerializer):
    """税务事件类型序列化器"""
    category_display = serializers.CharField(source='get_category_display', read_only=True)
    applicable_taxpayers_display = serializers.CharField(source='get_applicable_taxpayers_display', read_only=True)
    
    class Meta:
        model = TaxEventType
        fields = [
            'id', 'name', 'category', 'category_display',
            'applicable_taxpayers', 'applicable_taxpayers_display',
            'description', 'default_advance_days', 'is_mandatory',
            'penalty_info', 'color_code', 'is_active',
            'created_at', 'updated_at'
        ]


class TaxEventTemplateSerializer(serializers.ModelSerializer):
    """税务事件模板序列化器"""
    event_type_name = serializers.Char<PERSON><PERSON>(source='event_type.name', read_only=True)
    recurrence_type_display = serializers.Char<PERSON><PERSON>(source='get_recurrence_type_display', read_only=True)
    created_by_name = serializers.Char<PERSON>ield(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = TaxEventTemplate
        fields = [
            'id', 'event_type', 'event_type_name', 'title', 'description',
            'recurrence_type', 'recurrence_type_display', 'due_day', 'due_month',
            'custom_rule', 'advance_days', 'reminder_levels',
            'content_template', 'required_documents', 'process_steps',
            'applicable_regions', 'applicable_industries',
            'is_active', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]


class TaxEventSerializer(serializers.ModelSerializer):
    """税务事件序列化器"""
    event_type_name = serializers.CharField(source='event_type.name', read_only=True)
    event_type_color = serializers.CharField(source='event_type.color_code', read_only=True)
    company_name = serializers.CharField(source='company.name', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.get_full_name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    
    # 计算字段
    is_overdue = serializers.SerializerMethodField()
    days_until_due = serializers.SerializerMethodField()
    urgency_level = serializers.SerializerMethodField()
    
    class Meta:
        model = TaxEvent
        fields = [
            'id', 'template', 'event_type', 'event_type_name', 'event_type_color',
            'title', 'description', 'due_date', 'start_date', 'completed_date',
            'status', 'status_display', 'priority', 'priority_display',
            'company', 'company_name', 'assigned_to', 'assigned_to_name',
            'required_documents', 'process_steps', 'notes',
            'reminder_sent', 'last_reminder_date',
            'completion_notes', 'attachments',
            'is_overdue', 'days_until_due', 'urgency_level',
            'created_at', 'updated_at'
        ]
    
    def get_is_overdue(self, obj):
        return obj.is_overdue()
    
    def get_days_until_due(self, obj):
        return obj.days_until_due()
    
    def get_urgency_level(self, obj):
        return obj.get_urgency_level()


class TaxReminderSerializer(serializers.ModelSerializer):
    """税务提醒序列化器"""
    event_title = serializers.CharField(source='event.title', read_only=True)
    event_due_date = serializers.DateField(source='event.due_date', read_only=True)
    recipient_name = serializers.CharField(source='recipient.get_full_name', read_only=True)
    reminder_type_display = serializers.CharField(source='get_reminder_type_display', read_only=True)
    send_method_display = serializers.CharField(source='get_send_method_display', read_only=True)
    
    class Meta:
        model = TaxReminder
        fields = [
            'id', 'event', 'event_title', 'event_due_date',
            'reminder_type', 'reminder_type_display',
            'send_method', 'send_method_display',
            'recipient', 'recipient_name', 'recipient_info',
            'title', 'content', 'scheduled_time', 'sent_time',
            'is_sent', 'send_result', 'is_read', 'read_time',
            'created_at'
        ]


class TaxPolicySerializer(serializers.ModelSerializer):
    """税务政策序列化器"""
    policy_type_display = serializers.CharField(source='get_policy_type_display', read_only=True)
    policy_level_display = serializers.CharField(source='get_policy_level_display', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    is_effective = serializers.SerializerMethodField()
    related_events_count = serializers.SerializerMethodField()
    
    class Meta:
        model = TaxPolicy
        fields = [
            'id', 'title', 'policy_type', 'policy_type_display',
            'policy_level', 'policy_level_display',
            'issuing_authority', 'policy_number',
            'issue_date', 'effective_date', 'expiry_date',
            'summary', 'content', 'key_points',
            'applicable_regions', 'applicable_taxpayers', 'applicable_industries',
            'tags', 'attachments', 'external_links',
            'is_active', 'is_effective', 'view_count',
            'related_events_count', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]
    
    def get_is_effective(self, obj):
        return obj.is_effective()
    
    def get_related_events_count(self, obj):
        return obj.related_events.count()


class TaxCalendarSettingsSerializer(serializers.ModelSerializer):
    """税务日历设置序列化器"""
    company_name = serializers.CharField(source='company.name', read_only=True)
    visible_event_types_count = serializers.SerializerMethodField()
    
    class Meta:
        model = TaxCalendarSettings
        fields = [
            'id', 'company', 'company_name',
            'default_view', 'show_weekends', 'start_of_week',
            'enable_reminders', 'default_reminder_days', 'reminder_methods',
            'visible_event_types', 'visible_event_types_count', 'hide_completed_events',
            'custom_colors', 'timezone',
            'created_at', 'updated_at'
        ]
    
    def get_visible_event_types_count(self, obj):
        return obj.visible_event_types.count()


# 简化版序列化器用于列表显示
class TaxEventListSerializer(serializers.ModelSerializer):
    """税务事件列表序列化器"""
    event_type_name = serializers.CharField(source='event_type.name', read_only=True)
    event_type_color = serializers.CharField(source='event_type.color_code', read_only=True)
    company_name = serializers.CharField(source='company.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    urgency_level = serializers.SerializerMethodField()
    days_until_due = serializers.SerializerMethodField()
    
    class Meta:
        model = TaxEvent
        fields = [
            'id', 'title', 'event_type_name', 'event_type_color',
            'company_name', 'due_date', 'status', 'status_display',
            'priority', 'urgency_level', 'days_until_due'
        ]
    
    def get_urgency_level(self, obj):
        return obj.get_urgency_level()
    
    def get_days_until_due(self, obj):
        return obj.days_until_due()


class TaxPolicyListSerializer(serializers.ModelSerializer):
    """税务政策列表序列化器"""
    policy_type_display = serializers.CharField(source='get_policy_type_display', read_only=True)
    policy_level_display = serializers.CharField(source='get_policy_level_display', read_only=True)
    is_effective = serializers.SerializerMethodField()
    
    class Meta:
        model = TaxPolicy
        fields = [
            'id', 'title', 'policy_type_display', 'policy_level_display',
            'issuing_authority', 'issue_date', 'effective_date',
            'is_active', 'is_effective', 'view_count'
        ]
    
    def get_is_effective(self, obj):
        return obj.is_effective()


class CalendarEventSerializer(serializers.Serializer):
    """日历事件序列化器（用于前端日历显示）"""
    id = serializers.IntegerField()
    title = serializers.CharField()
    start = serializers.DateField(source='due_date')
    end = serializers.DateField(source='due_date')
    color = serializers.CharField(source='event_type.color_code')
    textColor = serializers.SerializerMethodField()
    
    # 事件详情
    event_type = serializers.CharField(source='event_type.name')
    status = serializers.CharField()
    priority = serializers.CharField()
    company = serializers.CharField(source='company.name', allow_null=True)
    assigned_to = serializers.CharField(source='assigned_to.get_full_name', allow_null=True)
    urgency = serializers.SerializerMethodField()
    
    def get_textColor(self, obj):
        # 根据背景色自动选择文字颜色
        return '#ffffff'
    
    def get_urgency(self, obj):
        return obj.get_urgency_level()


class TaxEventCreateSerializer(serializers.ModelSerializer):
    """税务事件创建序列化器"""
    class Meta:
        model = TaxEvent
        fields = [
            'template', 'event_type', 'title', 'description',
            'due_date', 'start_date', 'status', 'priority',
            'company', 'assigned_to', 'required_documents',
            'process_steps', 'notes'
        ]
    
    def create(self, validated_data):
        # 如果选择了模板，自动填充模板内容
        template = validated_data.get('template')
        if template:
            if not validated_data.get('title'):
                validated_data['title'] = template.title
            if not validated_data.get('description'):
                validated_data['description'] = template.description
            if not validated_data.get('required_documents'):
                validated_data['required_documents'] = template.required_documents
            if not validated_data.get('process_steps'):
                validated_data['process_steps'] = template.process_steps
        
        return super().create(validated_data)

from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from apps.companies.models import Company

User = get_user_model()


class TaxEventType(models.Model):
    """税务事件类型"""
    EVENT_CATEGORIES = [
        ('filing', '申报类'),
        ('payment', '缴税类'),
        ('declaration', '申请类'),
        ('inspection', '检查类'),
        ('policy', '政策类'),
        ('reminder', '提醒类'),
    ]
    
    TAXPAYER_TYPES = [
        ('all', '所有纳税人'),
        ('individual', '个体工商户'),
        ('small_scale', '小规模纳税人'),
        ('general', '一般纳税人'),
        ('enterprise', '企业'),
    ]
    
    name = models.CharField('事件类型名称', max_length=100)
    category = models.CharField('事件分类', max_length=20, choices=EVENT_CATEGORIES)
    applicable_taxpayers = models.CharField('适用纳税人', max_length=20, choices=TAXPAYER_TYPES, default='all')
    description = models.TextField('类型描述', blank=True)
    
    # 默认配置
    default_advance_days = models.IntegerField('默认提前天数', default=7, help_text='提前多少天提醒')
    is_mandatory = models.BooleanField('是否强制性', default=True, help_text='是否为法定必须事项')
    penalty_info = models.TextField('逾期后果', blank=True, help_text='逾期不办理的后果说明')
    
    # 颜色标识
    color_code = models.CharField('颜色代码', max_length=7, default='#1890ff', help_text='日历显示颜色')
    
    is_active = models.BooleanField('是否启用', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '税务事件类型'
        verbose_name_plural = '税务事件类型'
        ordering = ['category', 'name']
    
    def __str__(self):
        return f"{self.get_category_display()} - {self.name}"


class TaxEventTemplate(models.Model):
    """税务事件模板"""
    RECURRENCE_TYPES = [
        ('once', '一次性'),
        ('monthly', '每月'),
        ('quarterly', '每季度'),
        ('yearly', '每年'),
        ('custom', '自定义'),
    ]
    
    event_type = models.ForeignKey(TaxEventType, on_delete=models.CASCADE, verbose_name='事件类型')
    title = models.CharField('事件标题', max_length=200)
    description = models.TextField('事件描述')
    
    # 时间配置
    recurrence_type = models.CharField('重复类型', max_length=20, choices=RECURRENCE_TYPES, default='monthly')
    due_day = models.IntegerField('截止日期', help_text='每月/季度/年的第几天', null=True, blank=True)
    due_month = models.IntegerField('截止月份', help_text='每年的第几月', null=True, blank=True)
    
    # 自定义重复规则
    custom_rule = models.JSONField('自定义规则', default=dict, blank=True, help_text='自定义重复规则配置')
    
    # 提醒配置
    advance_days = models.IntegerField('提前提醒天数', default=7)
    reminder_levels = models.JSONField('提醒级别', default=list, help_text='多级提醒配置')
    
    # 内容模板
    content_template = models.TextField('内容模板', help_text='支持变量替换的内容模板')
    required_documents = models.JSONField('所需资料', default=list, help_text='办理所需的资料清单')
    process_steps = models.JSONField('办理流程', default=list, help_text='具体办理步骤')
    
    # 适用范围
    applicable_regions = models.JSONField('适用地区', default=list, help_text='适用的地区代码')
    applicable_industries = models.JSONField('适用行业', default=list, help_text='适用的行业代码')
    
    is_active = models.BooleanField('是否启用', default=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '税务事件模板'
        verbose_name_plural = '税务事件模板'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.title} ({self.get_recurrence_type_display()})"


class TaxEvent(models.Model):
    """税务事件"""
    STATUS_CHOICES = [
        ('pending', '待办'),
        ('in_progress', '进行中'),
        ('completed', '已完成'),
        ('overdue', '已逾期'),
        ('cancelled', '已取消'),
    ]
    
    PRIORITY_LEVELS = [
        ('low', '低'),
        ('normal', '普通'),
        ('high', '高'),
        ('urgent', '紧急'),
    ]
    
    # 基本信息
    template = models.ForeignKey(TaxEventTemplate, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='事件模板')
    event_type = models.ForeignKey(TaxEventType, on_delete=models.CASCADE, verbose_name='事件类型')
    title = models.CharField('事件标题', max_length=200)
    description = models.TextField('事件描述')
    
    # 时间信息
    due_date = models.DateField('截止日期')
    start_date = models.DateField('开始日期', null=True, blank=True)
    completed_date = models.DateField('完成日期', null=True, blank=True)
    
    # 状态和优先级
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    priority = models.CharField('优先级', max_length=10, choices=PRIORITY_LEVELS, default='normal')
    
    # 关联信息
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True, verbose_name='关联企业')
    assigned_to = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='assigned_tax_events', verbose_name='负责人'
    )
    
    # 内容信息
    required_documents = models.JSONField('所需资料', default=list)
    process_steps = models.JSONField('办理流程', default=list)
    notes = models.TextField('备注信息', blank=True)
    
    # 提醒信息
    reminder_sent = models.BooleanField('已发送提醒', default=False)
    last_reminder_date = models.DateTimeField('最后提醒时间', null=True, blank=True)
    
    # 结果信息
    completion_notes = models.TextField('完成备注', blank=True)
    attachments = models.JSONField('相关附件', default=list, help_text='附件文件路径列表')
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '税务事件'
        verbose_name_plural = '税务事件'
        ordering = ['due_date', '-priority']
        indexes = [
            models.Index(fields=['due_date', 'status']),
            models.Index(fields=['company', 'due_date']),
            models.Index(fields=['assigned_to', 'status']),
        ]
    
    def __str__(self):
        return f"{self.title} - {self.due_date}"
    
    def is_overdue(self):
        """检查是否逾期"""
        if self.status in ['completed', 'cancelled']:
            return False
        return timezone.now().date() > self.due_date
    
    def days_until_due(self):
        """距离截止日期的天数"""
        if self.status in ['completed', 'cancelled']:
            return None
        delta = self.due_date - timezone.now().date()
        return delta.days
    
    def get_urgency_level(self):
        """获取紧急程度"""
        days_left = self.days_until_due()
        if days_left is None:
            return 'completed'
        elif days_left < 0:
            return 'overdue'
        elif days_left <= 1:
            return 'urgent'
        elif days_left <= 3:
            return 'high'
        elif days_left <= 7:
            return 'normal'
        else:
            return 'low'


class TaxReminder(models.Model):
    """税务提醒"""
    REMINDER_TYPES = [
        ('advance', '提前提醒'),
        ('due', '到期提醒'),
        ('overdue', '逾期提醒'),
        ('completion', '完成提醒'),
    ]
    
    SEND_METHODS = [
        ('system', '系统通知'),
        ('email', '邮件'),
        ('sms', '短信'),
        ('wechat', '微信'),
        ('push', '推送'),
    ]
    
    event = models.ForeignKey(TaxEvent, on_delete=models.CASCADE, related_name='reminders', verbose_name='税务事件')
    reminder_type = models.CharField('提醒类型', max_length=20, choices=REMINDER_TYPES)
    send_method = models.CharField('发送方式', max_length=20, choices=SEND_METHODS)
    
    # 接收人
    recipient = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='接收人')
    recipient_info = models.JSONField('接收人信息', default=dict, help_text='邮箱、手机号等联系方式')
    
    # 内容
    title = models.CharField('提醒标题', max_length=200)
    content = models.TextField('提醒内容')
    
    # 发送状态
    scheduled_time = models.DateTimeField('计划发送时间')
    sent_time = models.DateTimeField('实际发送时间', null=True, blank=True)
    is_sent = models.BooleanField('是否已发送', default=False)
    send_result = models.TextField('发送结果', blank=True)
    
    # 响应状态
    is_read = models.BooleanField('是否已读', default=False)
    read_time = models.DateTimeField('阅读时间', null=True, blank=True)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '税务提醒'
        verbose_name_plural = '税务提醒'
        ordering = ['-scheduled_time']
    
    def __str__(self):
        return f"{self.title} - {self.recipient.get_full_name()}"


class TaxPolicy(models.Model):
    """税务政策"""
    POLICY_TYPES = [
        ('law', '法律法规'),
        ('regulation', '部门规章'),
        ('notice', '政策通知'),
        ('interpretation', '政策解读'),
        ('announcement', '公告'),
    ]
    
    POLICY_LEVELS = [
        ('national', '国家级'),
        ('provincial', '省级'),
        ('municipal', '市级'),
        ('district', '区县级'),
    ]
    
    title = models.CharField('政策标题', max_length=300)
    policy_type = models.CharField('政策类型', max_length=20, choices=POLICY_TYPES)
    policy_level = models.CharField('政策级别', max_length=20, choices=POLICY_LEVELS)
    
    # 发布信息
    issuing_authority = models.CharField('发布机关', max_length=200)
    policy_number = models.CharField('政策文号', max_length=100, blank=True)
    issue_date = models.DateField('发布日期')
    effective_date = models.DateField('生效日期')
    expiry_date = models.DateField('失效日期', null=True, blank=True)
    
    # 内容信息
    summary = models.TextField('政策摘要')
    content = models.TextField('政策内容')
    key_points = models.JSONField('政策要点', default=list, help_text='政策关键点列表')
    
    # 适用范围
    applicable_regions = models.JSONField('适用地区', default=list)
    applicable_taxpayers = models.JSONField('适用纳税人', default=list)
    applicable_industries = models.JSONField('适用行业', default=list)
    
    # 关联信息
    related_events = models.ManyToManyField(TaxEventType, blank=True, verbose_name='相关事件类型')
    tags = models.JSONField('标签', default=list, help_text='政策标签')
    
    # 附件和链接
    attachments = models.JSONField('相关附件', default=list)
    external_links = models.JSONField('外部链接', default=list)
    
    # 状态
    is_active = models.BooleanField('是否有效', default=True)
    view_count = models.IntegerField('查看次数', default=0)
    
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='录入人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '税务政策'
        verbose_name_plural = '税务政策'
        ordering = ['-issue_date']
    
    def __str__(self):
        return self.title
    
    def is_effective(self):
        """检查政策是否有效"""
        now = timezone.now().date()
        if not self.is_active:
            return False
        if now < self.effective_date:
            return False
        if self.expiry_date and now > self.expiry_date:
            return False
        return True


class TaxCalendarSettings(models.Model):
    """税务日历设置"""
    company = models.OneToOneField(Company, on_delete=models.CASCADE, verbose_name='企业')
    
    # 显示设置
    default_view = models.CharField('默认视图', max_length=20, 
                                  choices=[('month', '月视图'), ('week', '周视图'), ('day', '日视图')], 
                                  default='month')
    show_weekends = models.BooleanField('显示周末', default=True)
    start_of_week = models.IntegerField('一周开始', default=1, help_text='1=周一, 0=周日')
    
    # 提醒设置
    enable_reminders = models.BooleanField('启用提醒', default=True)
    default_reminder_days = models.IntegerField('默认提醒天数', default=7)
    reminder_methods = models.JSONField('提醒方式', default=list, help_text='启用的提醒方式列表')
    
    # 事件过滤
    visible_event_types = models.ManyToManyField(TaxEventType, blank=True, verbose_name='显示的事件类型')
    hide_completed_events = models.BooleanField('隐藏已完成事件', default=False)
    
    # 个性化设置
    custom_colors = models.JSONField('自定义颜色', default=dict, help_text='事件类型的自定义颜色')
    timezone = models.CharField('时区', max_length=50, default='Asia/Shanghai')
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '税务日历设置'
        verbose_name_plural = '税务日历设置'
    
    def __str__(self):
        return f"{self.company.name} - 日历设置"

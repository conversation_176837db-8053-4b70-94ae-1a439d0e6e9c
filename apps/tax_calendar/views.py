from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q, Count, Avg
from django.shortcuts import get_object_or_404
from datetime import datetime, timedelta, date
from .models import (
    TaxEventType, TaxEventTemplate, TaxEvent, TaxReminder,
    TaxPolicy, TaxCalendarSettings
)
from .serializers import (
    TaxEventTypeSerializer, TaxEventTemplateSerializer, TaxEventSerializer,
    TaxReminderSerializer, TaxPolicySerializer, TaxCalendarSettingsSerializer,
    TaxEventListSerializer, TaxPolicyListSerializer, CalendarEventSerializer,
    TaxEventCreateSerializer
)
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class TaxEventTypeViewSet(viewsets.ModelViewSet):
    """税务事件类型ViewSet"""
    queryset = TaxEventType.objects.all()
    serializer_class = TaxEventTypeSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category=category)
        
        applicable_taxpayers = self.request.query_params.get('applicable_taxpayers')
        if applicable_taxpayers:
            queryset = queryset.filter(applicable_taxpayers=applicable_taxpayers)
        
        return queryset.filter(is_active=True)


class TaxEventTemplateViewSet(viewsets.ModelViewSet):
    """税务事件模板ViewSet"""
    queryset = TaxEventTemplate.objects.all()
    serializer_class = TaxEventTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        event_type = self.request.query_params.get('event_type')
        if event_type:
            queryset = queryset.filter(event_type_id=event_type)
        
        recurrence_type = self.request.query_params.get('recurrence_type')
        if recurrence_type:
            queryset = queryset.filter(recurrence_type=recurrence_type)
        
        return queryset.filter(is_active=True).order_by('-created_at')
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class TaxEventViewSet(viewsets.ModelViewSet):
    """税务事件ViewSet"""
    queryset = TaxEvent.objects.all()
    serializer_class = TaxEventSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user
        
        # 根据用户权限过滤
        if not user.is_staff:
            # 普通用户只能看到自己企业的事件
            queryset = queryset.filter(company__owner=user)
        else:
            # 管理员可以看到所有事件
            company_id = self.request.query_params.get('company')
            if company_id:
                queryset = queryset.filter(company_id=company_id)
            
            assigned_to_me = self.request.query_params.get('assigned_to_me')
            if assigned_to_me == 'true':
                queryset = queryset.filter(assigned_to=user)
        
        # 状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # 事件类型过滤
        event_type = self.request.query_params.get('event_type')
        if event_type:
            queryset = queryset.filter(event_type_id=event_type)
        
        # 日期范围过滤
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')
        if date_from:
            queryset = queryset.filter(due_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(due_date__lte=date_to)
        
        # 紧急程度过滤
        urgency = self.request.query_params.get('urgency')
        if urgency == 'overdue':
            queryset = queryset.filter(
                due_date__lt=timezone.now().date(),
                status__in=['pending', 'in_progress']
            )
        elif urgency == 'urgent':
            urgent_date = timezone.now().date() + timedelta(days=1)
            queryset = queryset.filter(
                due_date__lte=urgent_date,
                status__in=['pending', 'in_progress']
            )
        
        return queryset.order_by('due_date', '-priority')
    
    def get_serializer_class(self):
        if self.action == 'list':
            return TaxEventListSerializer
        elif self.action == 'create':
            return TaxEventCreateSerializer
        return TaxEventSerializer
    
    @action(detail=True, methods=['post'])
    def mark_completed(self, request, pk=None):
        """标记为完成"""
        event = self.get_object()
        event.status = 'completed'
        event.completed_date = timezone.now().date()
        event.completion_notes = request.data.get('notes', '')
        event.save()
        
        return Response({'message': '事件已标记为完成'})
    
    @action(detail=True, methods=['post'])
    def send_reminder(self, request, pk=None):
        """发送提醒"""
        event = self.get_object()
        
        # 创建提醒记录
        reminder = TaxReminder.objects.create(
            event=event,
            reminder_type='advance',
            send_method='system',
            recipient=request.user,
            title=f"税务提醒：{event.title}",
            content=f"您有一个税务事件即将到期：{event.title}，截止日期：{event.due_date}",
            scheduled_time=timezone.now(),
            is_sent=True,
            sent_time=timezone.now()
        )
        
        # 更新事件提醒状态
        event.reminder_sent = True
        event.last_reminder_date = timezone.now()
        event.save()
        
        return Response({'message': '提醒已发送'})
    
    @action(detail=False, methods=['get'])
    def calendar_events(self, request):
        """获取日历事件数据"""
        queryset = self.get_queryset()
        
        # 日历视图通常需要特定月份的数据
        year = request.query_params.get('year')
        month = request.query_params.get('month')
        
        if year and month:
            start_date = date(int(year), int(month), 1)
            if int(month) == 12:
                end_date = date(int(year) + 1, 1, 1) - timedelta(days=1)
            else:
                end_date = date(int(year), int(month) + 1, 1) - timedelta(days=1)
            
            queryset = queryset.filter(due_date__range=[start_date, end_date])
        
        serializer = CalendarEventSerializer(queryset, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """税务事件仪表板"""
        user = request.user
        
        # 基础统计
        total_events = self.get_queryset().count()
        pending_events = self.get_queryset().filter(status='pending').count()
        overdue_events = self.get_queryset().filter(
            due_date__lt=timezone.now().date(),
            status__in=['pending', 'in_progress']
        ).count()
        
        # 本月事件
        today = timezone.now().date()
        month_start = today.replace(day=1)
        if today.month == 12:
            month_end = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            month_end = today.replace(month=today.month + 1, day=1) - timedelta(days=1)
        
        month_events = self.get_queryset().filter(
            due_date__range=[month_start, month_end]
        ).count()
        
        # 紧急事件（7天内到期）
        urgent_date = today + timedelta(days=7)
        urgent_events = self.get_queryset().filter(
            due_date__lte=urgent_date,
            status__in=['pending', 'in_progress']
        ).count()
        
        # 我的任务（如果是员工）
        my_tasks = 0
        if user.is_staff:
            my_tasks = self.get_queryset().filter(
                assigned_to=user,
                status__in=['pending', 'in_progress']
            ).count()
        
        return Response({
            'total_events': total_events,
            'pending_events': pending_events,
            'overdue_events': overdue_events,
            'month_events': month_events,
            'urgent_events': urgent_events,
            'my_tasks': my_tasks,
        })
    
    @action(detail=False, methods=['post'])
    def bulk_create_from_template(self, request):
        """从模板批量创建事件"""
        template_id = request.data.get('template_id')
        companies = request.data.get('companies', [])
        year = request.data.get('year', timezone.now().year)
        
        if not template_id:
            return Response(
                {'error': '模板ID不能为空'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            template = TaxEventTemplate.objects.get(id=template_id)
        except TaxEventTemplate.DoesNotExist:
            return Response(
                {'error': '模板不存在'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        created_events = []
        
        # 根据模板的重复类型生成事件
        if template.recurrence_type == 'monthly':
            # 每月生成
            for month in range(1, 13):
                due_date = date(year, month, template.due_day or 15)
                for company_id in companies:
                    event = TaxEvent.objects.create(
                        template=template,
                        event_type=template.event_type,
                        title=template.title,
                        description=template.description,
                        due_date=due_date,
                        company_id=company_id,
                        required_documents=template.required_documents,
                        process_steps=template.process_steps,
                    )
                    created_events.append(event)
        
        elif template.recurrence_type == 'quarterly':
            # 每季度生成
            for quarter in [3, 6, 9, 12]:  # 每季度末月
                due_date = date(year, quarter, template.due_day or 15)
                for company_id in companies:
                    event = TaxEvent.objects.create(
                        template=template,
                        event_type=template.event_type,
                        title=template.title,
                        description=template.description,
                        due_date=due_date,
                        company_id=company_id,
                        required_documents=template.required_documents,
                        process_steps=template.process_steps,
                    )
                    created_events.append(event)
        
        elif template.recurrence_type == 'yearly':
            # 每年生成
            due_date = date(year, template.due_month or 12, template.due_day or 31)
            for company_id in companies:
                event = TaxEvent.objects.create(
                    template=template,
                    event_type=template.event_type,
                    title=template.title,
                    description=template.description,
                    due_date=due_date,
                    company_id=company_id,
                    required_documents=template.required_documents,
                    process_steps=template.process_steps,
                )
                created_events.append(event)
        
        return Response({
            'message': f'成功创建 {len(created_events)} 个事件',
            'created_count': len(created_events)
        })


class TaxReminderViewSet(viewsets.ModelViewSet):
    """税务提醒ViewSet"""
    queryset = TaxReminder.objects.all()
    serializer_class = TaxReminderSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user
        
        # 普通用户只能看到自己的提醒
        if not user.is_staff:
            queryset = queryset.filter(recipient=user)
        
        # 过滤条件
        reminder_type = self.request.query_params.get('reminder_type')
        if reminder_type:
            queryset = queryset.filter(reminder_type=reminder_type)
        
        is_sent = self.request.query_params.get('is_sent')
        if is_sent == 'true':
            queryset = queryset.filter(is_sent=True)
        elif is_sent == 'false':
            queryset = queryset.filter(is_sent=False)
        
        is_read = self.request.query_params.get('is_read')
        if is_read == 'true':
            queryset = queryset.filter(is_read=True)
        elif is_read == 'false':
            queryset = queryset.filter(is_read=False)
        
        return queryset.order_by('-scheduled_time')
    
    @action(detail=True, methods=['post'])
    def mark_read(self, request, pk=None):
        """标记为已读"""
        reminder = self.get_object()
        reminder.is_read = True
        reminder.read_time = timezone.now()
        reminder.save()
        
        return Response({'message': '提醒已标记为已读'})


class TaxPolicyViewSet(viewsets.ModelViewSet):
    """税务政策ViewSet"""
    queryset = TaxPolicy.objects.all()
    serializer_class = TaxPolicySerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # 过滤条件
        policy_type = self.request.query_params.get('policy_type')
        if policy_type:
            queryset = queryset.filter(policy_type=policy_type)
        
        policy_level = self.request.query_params.get('policy_level')
        if policy_level:
            queryset = queryset.filter(policy_level=policy_level)
        
        is_effective = self.request.query_params.get('is_effective')
        if is_effective == 'true':
            today = timezone.now().date()
            queryset = queryset.filter(
                is_active=True,
                effective_date__lte=today
            ).filter(
                Q(expiry_date__isnull=True) | Q(expiry_date__gt=today)
            )
        
        # 搜索
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) |
                Q(summary__icontains=search) |
                Q(issuing_authority__icontains=search)
            )
        
        return queryset.order_by('-issue_date')
    
    def get_serializer_class(self):
        if self.action == 'list':
            return TaxPolicyListSerializer
        return TaxPolicySerializer
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def increment_view(self, request, pk=None):
        """增加查看次数"""
        policy = self.get_object()
        policy.view_count += 1
        policy.save(update_fields=['view_count'])
        
        return Response({'message': '查看次数已更新'})


class TaxCalendarSettingsViewSet(viewsets.ModelViewSet):
    """税务日历设置ViewSet"""
    queryset = TaxCalendarSettings.objects.all()
    serializer_class = TaxCalendarSettingsSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user
        
        # 普通用户只能看到自己企业的设置
        if not user.is_staff:
            queryset = queryset.filter(company__owner=user)
        
        return queryset
    
    @action(detail=False, methods=['get'])
    def my_settings(self, request):
        """获取我的设置"""
        user = request.user
        
        # 如果用户有企业，获取企业设置
        if hasattr(user, 'company'):
            try:
                settings = TaxCalendarSettings.objects.get(company=user.company)
                serializer = self.get_serializer(settings)
                return Response(serializer.data)
            except TaxCalendarSettings.DoesNotExist:
                # 如果没有设置，创建默认设置
                settings = TaxCalendarSettings.objects.create(company=user.company)
                serializer = self.get_serializer(settings)
                return Response(serializer.data)
        
        return Response({'error': '用户没有关联企业'}, status=status.HTTP_400_BAD_REQUEST)

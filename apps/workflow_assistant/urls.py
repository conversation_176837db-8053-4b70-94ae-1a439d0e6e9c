from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from . import views

router = DefaultRouter()
router.register(r'templates', views.WorkflowTemplateViewSet, basename='workflow-template')
router.register(r'instances', views.WorkflowInstanceViewSet, basename='workflow-instance')
router.register(r'steps', views.WorkflowStepViewSet, basename='workflow-step')
router.register(r'tasks', views.TaskAssignmentViewSet, basename='task-assignment')
router.register(r'optimizations', views.ProcessOptimizationViewSet, basename='process-optimization')
router.register(r'reminders', views.SmartReminderViewSet, basename='smart-reminder')
router.register(r'analytics', views.WorkflowAnalyticsViewSet, basename='workflow-analytics')
router.register(r'automation-rules', views.AutomationRuleViewSet, basename='automation-rule')
router.register(r'configs', views.WorkflowConfigViewSet, basename='workflow-config')
router.register(r'assistant', views.WorkflowAssistantViewSet, basename='workflow-assistant')

app_name = 'workflow_assistant'

urlpatterns = [
    path('', include(router.urls)),
]

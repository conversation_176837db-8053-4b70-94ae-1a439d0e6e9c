from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import json

User = get_user_model()


class WorkflowTemplate(models.Model):
    """工作流模板"""
    WORKFLOW_TYPES = [
        ('finance_audit', '财务审核'),
        ('order_process', '订单处理'),
        ('invoice_issue', '发票开具'),
        ('tax_filing', '税务申报'),
        ('customer_service', '客户服务'),
        ('document_review', '文档审核'),
        ('approval_process', '审批流程'),
        ('data_analysis', '数据分析'),
        ('custom', '自定义'),
    ]

    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('active', '启用'),
        ('inactive', '停用'),
        ('archived', '归档'),
    ]

    name = models.CharField('模板名称', max_length=200)
    workflow_type = models.CharField('工作流类型', max_length=20, choices=WORKFLOW_TYPES)
    description = models.TextField('模板描述', blank=True)

    # 流程配置
    steps_config = models.JSONField('步骤配置', default=list, help_text='工作流步骤定义')
    rules_config = models.JSONField('规则配置', default=dict, help_text='业务规则和条件')
    automation_config = models.JSONField('自动化配置', default=dict, help_text='自动化任务配置')

    # 性能指标
    estimated_duration = models.IntegerField('预估时长(分钟)', default=60)
    complexity_score = models.FloatField('复杂度评分', default=1.0, validators=[MinValueValidator(0.1), MaxValueValidator(10.0)])
    success_rate = models.FloatField('成功率', default=1.0, validators=[MinValueValidator(0), MaxValueValidator(1)])

    # 状态管理
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='draft')
    version = models.CharField('版本号', max_length=20, default='1.0')
    is_default = models.BooleanField('默认模板', default=False)

    # 使用统计
    usage_count = models.IntegerField('使用次数', default=0)
    last_used_at = models.DateTimeField('最后使用时间', null=True, blank=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_workflow_templates', verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '工作流模板'
        verbose_name_plural = '工作流模板'
        ordering = ['-created_at']

    def __str__(self):
        return self.name


class WorkflowInstance(models.Model):
    """工作流实例"""
    STATUS_CHOICES = [
        ('pending', '待开始'),
        ('running', '进行中'),
        ('paused', '暂停'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]

    PRIORITY_CHOICES = [
        (1, '低'),
        (2, '中'),
        (3, '高'),
        (4, '紧急'),
    ]

    instance_id = models.CharField('实例ID', max_length=100, unique=True)
    template = models.ForeignKey(WorkflowTemplate, on_delete=models.CASCADE, verbose_name='工作流模板')
    name = models.CharField('实例名称', max_length=200)
    description = models.TextField('实例描述', blank=True)

    # 执行配置
    input_data = models.JSONField('输入数据', default=dict)
    context_data = models.JSONField('上下文数据', default=dict)
    variables = models.JSONField('流程变量', default=dict)

    # 状态信息
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    current_step = models.CharField('当前步骤', max_length=100, blank=True)
    progress = models.IntegerField('进度百分比', default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])

    # 优先级和调度
    priority = models.IntegerField('优先级', choices=PRIORITY_CHOICES, default=2)
    scheduled_start_time = models.DateTimeField('计划开始时间', null=True, blank=True)
    deadline = models.DateTimeField('截止时间', null=True, blank=True)

    # 执行信息
    start_time = models.DateTimeField('开始时间', null=True, blank=True)
    end_time = models.DateTimeField('结束时间', null=True, blank=True)
    execution_time = models.FloatField('执行时长(秒)', null=True, blank=True)

    # 结果信息
    output_data = models.JSONField('输出数据', default=dict)
    error_message = models.TextField('错误信息', blank=True)

    # 关联信息
    parent_instance = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name='父实例')
    related_object_type = models.CharField('关联对象类型', max_length=50, blank=True)
    related_object_id = models.CharField('关联对象ID', max_length=100, blank=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_workflow_instances', verbose_name='创建人')
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_workflow_instances', verbose_name='分配给')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '工作流实例'
        verbose_name_plural = '工作流实例'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.instance_id})"

    def get_duration(self):
        """获取执行时长"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        elif self.start_time:
            return (timezone.now() - self.start_time).total_seconds()
        return 0


class WorkflowStep(models.Model):
    """工作流步骤"""
    STEP_TYPES = [
        ('manual', '手动任务'),
        ('automatic', '自动任务'),
        ('approval', '审批任务'),
        ('notification', '通知任务'),
        ('condition', '条件判断'),
        ('parallel', '并行任务'),
        ('subprocess', '子流程'),
        ('script', '脚本任务'),
    ]

    STATUS_CHOICES = [
        ('pending', '待执行'),
        ('running', '执行中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('skipped', '跳过'),
        ('cancelled', '已取消'),
    ]

    instance = models.ForeignKey(WorkflowInstance, on_delete=models.CASCADE, related_name='steps', verbose_name='工作流实例')
    step_id = models.CharField('步骤ID', max_length=100)
    name = models.CharField('步骤名称', max_length=200)
    step_type = models.CharField('步骤类型', max_length=20, choices=STEP_TYPES)
    description = models.TextField('步骤描述', blank=True)

    # 步骤配置
    step_config = models.JSONField('步骤配置', default=dict)
    input_mapping = models.JSONField('输入映射', default=dict)
    output_mapping = models.JSONField('输出映射', default=dict)

    # 执行顺序
    sequence_number = models.IntegerField('执行顺序', default=1)
    parent_step = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name='父步骤')

    # 状态信息
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    progress = models.IntegerField('进度百分比', default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])

    # 执行信息
    start_time = models.DateTimeField('开始时间', null=True, blank=True)
    end_time = models.DateTimeField('结束时间', null=True, blank=True)
    execution_time = models.FloatField('执行时长(秒)', null=True, blank=True)

    # 结果信息
    result_data = models.JSONField('结果数据', default=dict)
    error_message = models.TextField('错误信息', blank=True)

    # 分配信息
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_workflow_steps', verbose_name='分配给')

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '工作流步骤'
        verbose_name_plural = '工作流步骤'
        ordering = ['sequence_number']
        unique_together = ['instance', 'step_id']

    def __str__(self):
        return f"{self.instance.name} - {self.name}"


class TaskAssignment(models.Model):
    """任务分配"""
    ASSIGNMENT_TYPES = [
        ('manual', '手动分配'),
        ('automatic', '自动分配'),
        ('load_balance', '负载均衡'),
        ('skill_based', '技能匹配'),
        ('priority_based', '优先级分配'),
    ]

    STATUS_CHOICES = [
        ('pending', '待接受'),
        ('accepted', '已接受'),
        ('in_progress', '进行中'),
        ('completed', '已完成'),
        ('rejected', '已拒绝'),
        ('reassigned', '已重新分配'),
    ]

    assignment_id = models.CharField('分配ID', max_length=100, unique=True)
    workflow_step = models.ForeignKey(WorkflowStep, on_delete=models.CASCADE, related_name='assignments', verbose_name='工作流步骤')

    # 分配信息
    assigned_to = models.ForeignKey(User, on_delete=models.CASCADE, related_name='task_assignments', verbose_name='分配给')
    assigned_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_assignments', verbose_name='分配人')
    assignment_type = models.CharField('分配类型', max_length=20, choices=ASSIGNMENT_TYPES, default='manual')

    # 任务信息
    title = models.CharField('任务标题', max_length=200)
    description = models.TextField('任务描述', blank=True)
    instructions = models.TextField('执行说明', blank=True)

    # 时间管理
    assigned_at = models.DateTimeField('分配时间', auto_now_add=True)
    due_date = models.DateTimeField('截止时间', null=True, blank=True)
    estimated_duration = models.IntegerField('预估时长(分钟)', default=60)

    # 状态管理
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    progress = models.IntegerField('进度百分比', default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])

    # 执行信息
    accepted_at = models.DateTimeField('接受时间', null=True, blank=True)
    started_at = models.DateTimeField('开始时间', null=True, blank=True)
    completed_at = models.DateTimeField('完成时间', null=True, blank=True)
    actual_duration = models.FloatField('实际时长(分钟)', null=True, blank=True)

    # 结果信息
    result_data = models.JSONField('结果数据', default=dict)
    comments = models.TextField('备注', blank=True)

    # 评估信息
    quality_score = models.FloatField('质量评分', null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(10)])
    efficiency_score = models.FloatField('效率评分', null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(10)])

    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '任务分配'
        verbose_name_plural = '任务分配'
        ordering = ['-assigned_at']

    def __str__(self):
        return f"{self.title} - {self.assigned_to.get_full_name()}"


class ProcessOptimization(models.Model):
    """流程优化"""
    OPTIMIZATION_TYPES = [
        ('time_reduction', '时间优化'),
        ('cost_reduction', '成本优化'),
        ('quality_improvement', '质量提升'),
        ('automation', '自动化改进'),
        ('resource_optimization', '资源优化'),
        ('bottleneck_removal', '瓶颈消除'),
    ]

    STATUS_CHOICES = [
        ('identified', '已识别'),
        ('analyzing', '分析中'),
        ('proposed', '已提议'),
        ('approved', '已批准'),
        ('implementing', '实施中'),
        ('completed', '已完成'),
        ('rejected', '已拒绝'),
    ]

    optimization_id = models.CharField('优化ID', max_length=100, unique=True)
    workflow_template = models.ForeignKey(WorkflowTemplate, on_delete=models.CASCADE, related_name='optimizations', verbose_name='工作流模板')

    # 优化信息
    title = models.CharField('优化标题', max_length=200)
    optimization_type = models.CharField('优化类型', max_length=30, choices=OPTIMIZATION_TYPES)
    description = models.TextField('优化描述')

    # 问题分析
    current_issues = models.JSONField('当前问题', default=list)
    root_causes = models.JSONField('根本原因', default=list)
    impact_analysis = models.JSONField('影响分析', default=dict)

    # 优化方案
    proposed_solution = models.TextField('建议方案')
    implementation_steps = models.JSONField('实施步骤', default=list)
    required_resources = models.JSONField('所需资源', default=dict)

    # 预期效果
    expected_benefits = models.JSONField('预期收益', default=dict)
    estimated_savings = models.DecimalField('预估节省成本', max_digits=15, decimal_places=2, null=True, blank=True)
    time_reduction = models.FloatField('时间减少百分比', null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(100)])
    quality_improvement = models.FloatField('质量提升百分比', null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(100)])

    # 状态管理
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='identified')
    priority = models.IntegerField('优先级', choices=[(1, '低'), (2, '中'), (3, '高'), (4, '紧急')], default=2)

    # 时间管理
    identified_at = models.DateTimeField('识别时间', auto_now_add=True)
    target_completion_date = models.DateTimeField('目标完成时间', null=True, blank=True)
    actual_completion_date = models.DateTimeField('实际完成时间', null=True, blank=True)

    # 人员信息
    identified_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='identified_optimizations', verbose_name='识别人')
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_optimizations', verbose_name='负责人')
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_optimizations', verbose_name='批准人')

    # 实施结果
    implementation_notes = models.TextField('实施说明', blank=True)
    actual_benefits = models.JSONField('实际收益', default=dict)
    lessons_learned = models.TextField('经验总结', blank=True)

    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '流程优化'
        verbose_name_plural = '流程优化'
        ordering = ['-identified_at']

    def __str__(self):
        return self.title


class SmartReminder(models.Model):
    """智能提醒"""
    REMINDER_TYPES = [
        ('deadline', '截止提醒'),
        ('milestone', '里程碑提醒'),
        ('overdue', '逾期提醒'),
        ('approval', '审批提醒'),
        ('follow_up', '跟进提醒'),
        ('maintenance', '维护提醒'),
        ('custom', '自定义提醒'),
    ]

    TRIGGER_TYPES = [
        ('time_based', '基于时间'),
        ('event_based', '基于事件'),
        ('condition_based', '基于条件'),
        ('manual', '手动触发'),
    ]

    STATUS_CHOICES = [
        ('active', '活跃'),
        ('triggered', '已触发'),
        ('sent', '已发送'),
        ('acknowledged', '已确认'),
        ('snoozed', '已延迟'),
        ('cancelled', '已取消'),
    ]

    reminder_id = models.CharField('提醒ID', max_length=100, unique=True)
    title = models.CharField('提醒标题', max_length=200)
    reminder_type = models.CharField('提醒类型', max_length=20, choices=REMINDER_TYPES)
    trigger_type = models.CharField('触发类型', max_length=20, choices=TRIGGER_TYPES)

    # 关联对象
    workflow_instance = models.ForeignKey(WorkflowInstance, on_delete=models.CASCADE, null=True, blank=True, related_name='reminders', verbose_name='工作流实例')
    workflow_step = models.ForeignKey(WorkflowStep, on_delete=models.CASCADE, null=True, blank=True, related_name='reminders', verbose_name='工作流步骤')
    task_assignment = models.ForeignKey(TaskAssignment, on_delete=models.CASCADE, null=True, blank=True, related_name='reminders', verbose_name='任务分配')

    # 提醒内容
    message = models.TextField('提醒消息')
    action_required = models.TextField('需要的行动', blank=True)

    # 触发条件
    trigger_condition = models.JSONField('触发条件', default=dict)
    trigger_time = models.DateTimeField('触发时间', null=True, blank=True)
    advance_notice = models.IntegerField('提前通知(分钟)', default=0)

    # 重复设置
    is_recurring = models.BooleanField('是否重复', default=False)
    recurrence_pattern = models.JSONField('重复模式', default=dict)

    # 接收人
    recipients = models.ManyToManyField(User, related_name='received_reminders', verbose_name='接收人')

    # 状态管理
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='active')
    is_urgent = models.BooleanField('紧急', default=False)

    # 执行信息
    triggered_at = models.DateTimeField('触发时间', null=True, blank=True)
    sent_at = models.DateTimeField('发送时间', null=True, blank=True)
    acknowledged_at = models.DateTimeField('确认时间', null=True, blank=True)
    acknowledged_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='acknowledged_reminders', verbose_name='确认人')

    # 延迟设置
    snooze_until = models.DateTimeField('延迟到', null=True, blank=True)
    snooze_count = models.IntegerField('延迟次数', default=0)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_reminders', verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '智能提醒'
        verbose_name_plural = '智能提醒'
        ordering = ['-created_at']

    def __str__(self):
        return self.title


class WorkflowAnalytics(models.Model):
    """工作流分析"""
    METRIC_TYPES = [
        ('performance', '性能指标'),
        ('efficiency', '效率指标'),
        ('quality', '质量指标'),
        ('cost', '成本指标'),
        ('user_satisfaction', '用户满意度'),
        ('bottleneck', '瓶颈分析'),
    ]

    analytics_id = models.CharField('分析ID', max_length=100, unique=True)
    workflow_template = models.ForeignKey(WorkflowTemplate, on_delete=models.CASCADE, related_name='analytics', verbose_name='工作流模板')

    # 分析信息
    metric_type = models.CharField('指标类型', max_length=20, choices=METRIC_TYPES)
    analysis_period_start = models.DateTimeField('分析期间开始')
    analysis_period_end = models.DateTimeField('分析期间结束')

    # 性能指标
    total_instances = models.IntegerField('总实例数', default=0)
    completed_instances = models.IntegerField('完成实例数', default=0)
    failed_instances = models.IntegerField('失败实例数', default=0)
    average_duration = models.FloatField('平均时长(小时)', null=True, blank=True)
    median_duration = models.FloatField('中位数时长(小时)', null=True, blank=True)

    # 效率指标
    throughput = models.FloatField('吞吐量(实例/天)', null=True, blank=True)
    cycle_time = models.FloatField('周期时间(小时)', null=True, blank=True)
    wait_time = models.FloatField('等待时间(小时)', null=True, blank=True)
    processing_time = models.FloatField('处理时间(小时)', null=True, blank=True)

    # 质量指标
    error_rate = models.FloatField('错误率', null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(1)])
    rework_rate = models.FloatField('返工率', null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(1)])
    first_pass_yield = models.FloatField('一次通过率', null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(1)])

    # 成本指标
    total_cost = models.DecimalField('总成本', max_digits=15, decimal_places=2, null=True, blank=True)
    cost_per_instance = models.DecimalField('单实例成本', max_digits=15, decimal_places=2, null=True, blank=True)
    labor_cost = models.DecimalField('人工成本', max_digits=15, decimal_places=2, null=True, blank=True)
    system_cost = models.DecimalField('系统成本', max_digits=15, decimal_places=2, null=True, blank=True)

    # 用户满意度
    satisfaction_score = models.FloatField('满意度评分', null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(10)])
    nps_score = models.FloatField('NPS评分', null=True, blank=True, validators=[MinValueValidator(-100), MaxValueValidator(100)])

    # 瓶颈分析
    bottleneck_steps = models.JSONField('瓶颈步骤', default=list)
    bottleneck_analysis = models.JSONField('瓶颈分析', default=dict)
    improvement_suggestions = models.JSONField('改进建议', default=list)

    # 详细数据
    detailed_metrics = models.JSONField('详细指标', default=dict)
    trend_data = models.JSONField('趋势数据', default=dict)
    comparison_data = models.JSONField('对比数据', default=dict)

    # 生成信息
    generated_at = models.DateTimeField('生成时间', auto_now_add=True)
    generated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='generated_analytics', verbose_name='生成人')

    class Meta:
        verbose_name = '工作流分析'
        verbose_name_plural = '工作流分析'
        ordering = ['-generated_at']

    def __str__(self):
        return f"{self.workflow_template.name} - {self.get_metric_type_display()}"


class AutomationRule(models.Model):
    """自动化规则"""
    RULE_TYPES = [
        ('assignment', '任务分配'),
        ('escalation', '升级规则'),
        ('notification', '通知规则'),
        ('approval', '审批规则'),
        ('routing', '路由规则'),
        ('validation', '验证规则'),
        ('transformation', '数据转换'),
    ]

    TRIGGER_EVENTS = [
        ('workflow_start', '工作流开始'),
        ('step_complete', '步骤完成'),
        ('step_fail', '步骤失败'),
        ('deadline_approach', '截止时间临近'),
        ('overdue', '逾期'),
        ('approval_request', '审批请求'),
        ('data_change', '数据变更'),
        ('custom_event', '自定义事件'),
    ]

    STATUS_CHOICES = [
        ('active', '启用'),
        ('inactive', '停用'),
        ('testing', '测试中'),
        ('error', '错误'),
    ]

    rule_id = models.CharField('规则ID', max_length=100, unique=True)
    name = models.CharField('规则名称', max_length=200)
    rule_type = models.CharField('规则类型', max_length=20, choices=RULE_TYPES)
    description = models.TextField('规则描述', blank=True)

    # 触发条件
    trigger_event = models.CharField('触发事件', max_length=20, choices=TRIGGER_EVENTS)
    trigger_conditions = models.JSONField('触发条件', default=dict)

    # 规则配置
    rule_config = models.JSONField('规则配置', default=dict)
    action_config = models.JSONField('动作配置', default=dict)

    # 适用范围
    workflow_templates = models.ManyToManyField(WorkflowTemplate, blank=True, related_name='automation_rules', verbose_name='适用工作流')

    # 状态管理
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='active')
    priority = models.IntegerField('优先级', default=1, validators=[MinValueValidator(1), MaxValueValidator(10)])

    # 执行统计
    execution_count = models.IntegerField('执行次数', default=0)
    success_count = models.IntegerField('成功次数', default=0)
    last_executed_at = models.DateTimeField('最后执行时间', null=True, blank=True)

    # 性能指标
    average_execution_time = models.FloatField('平均执行时间(秒)', null=True, blank=True)
    error_rate = models.FloatField('错误率', null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(1)])

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_automation_rules', verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '自动化规则'
        verbose_name_plural = '自动化规则'
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def get_success_rate(self):
        """获取成功率"""
        if self.execution_count == 0:
            return 0
        return (self.success_count / self.execution_count) * 100


class WorkflowConfig(models.Model):
    """工作流配置"""
    CONFIG_TYPES = [
        ('general', '通用配置'),
        ('notification', '通知配置'),
        ('assignment', '分配配置'),
        ('escalation', '升级配置'),
        ('performance', '性能配置'),
        ('security', '安全配置'),
    ]

    config_type = models.CharField('配置类型', max_length=20, choices=CONFIG_TYPES)
    config_key = models.CharField('配置键', max_length=100)
    config_value = models.JSONField('配置值', default=dict)
    description = models.TextField('配置描述', blank=True)

    # 配置属性
    is_system = models.BooleanField('系统配置', default=False)
    is_active = models.BooleanField('是否启用', default=True)

    # 适用范围
    workflow_template = models.ForeignKey(WorkflowTemplate, on_delete=models.CASCADE, null=True, blank=True, related_name='configs', verbose_name='工作流模板')

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_workflow_configs', verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '工作流配置'
        verbose_name_plural = '工作流配置'
        unique_together = ['config_type', 'config_key', 'workflow_template']
        ordering = ['config_type', 'config_key']

    def __str__(self):
        return f"{self.get_config_type_display()} - {self.config_key}"

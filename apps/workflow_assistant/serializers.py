from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    WorkflowTemplate, WorkflowInstance, WorkflowStep, TaskAssignment,
    ProcessOptimization, SmartReminder, WorkflowAnalytics, AutomationRule, WorkflowConfig
)

User = get_user_model()


class WorkflowTemplateSerializer(serializers.ModelSerializer):
    """工作流模板序列化器"""
    workflow_type_display = serializers.CharField(source='get_workflow_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = WorkflowTemplate
        fields = [
            'id', 'name', 'workflow_type', 'workflow_type_display', 'description',
            'steps_config', 'rules_config', 'automation_config', 'estimated_duration',
            'complexity_score', 'success_rate', 'status', 'status_display',
            'version', 'is_default', 'usage_count', 'last_used_at',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]


class WorkflowInstanceSerializer(serializers.ModelSerializer):
    """工作流实例序列化器"""
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    template_name = serializers.CharField(source='template.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.get_full_name', read_only=True)
    duration = serializers.SerializerMethodField()
    
    class Meta:
        model = WorkflowInstance
        fields = [
            'id', 'instance_id', 'template', 'template_name', 'name', 'description',
            'input_data', 'context_data', 'variables', 'status', 'status_display',
            'current_step', 'progress', 'priority', 'priority_display',
            'scheduled_start_time', 'deadline', 'start_time', 'end_time',
            'execution_time', 'duration', 'output_data', 'error_message',
            'parent_instance', 'related_object_type', 'related_object_id',
            'created_by', 'created_by_name', 'assigned_to', 'assigned_to_name',
            'created_at', 'updated_at'
        ]
    
    def get_duration(self, obj):
        return obj.get_duration()


class WorkflowStepSerializer(serializers.ModelSerializer):
    """工作流步骤序列化器"""
    step_type_display = serializers.CharField(source='get_step_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    instance_name = serializers.CharField(source='instance.name', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.get_full_name', read_only=True)
    
    class Meta:
        model = WorkflowStep
        fields = [
            'id', 'instance', 'instance_name', 'step_id', 'name', 'step_type',
            'step_type_display', 'description', 'step_config', 'input_mapping',
            'output_mapping', 'sequence_number', 'parent_step', 'status',
            'status_display', 'progress', 'start_time', 'end_time',
            'execution_time', 'result_data', 'error_message', 'assigned_to',
            'assigned_to_name', 'created_at', 'updated_at'
        ]


class TaskAssignmentSerializer(serializers.ModelSerializer):
    """任务分配序列化器"""
    assignment_type_display = serializers.CharField(source='get_assignment_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.get_full_name', read_only=True)
    assigned_by_name = serializers.CharField(source='assigned_by.get_full_name', read_only=True)
    workflow_step_name = serializers.CharField(source='workflow_step.name', read_only=True)
    
    class Meta:
        model = TaskAssignment
        fields = [
            'id', 'assignment_id', 'workflow_step', 'workflow_step_name',
            'assigned_to', 'assigned_to_name', 'assigned_by', 'assigned_by_name',
            'assignment_type', 'assignment_type_display', 'title', 'description',
            'instructions', 'assigned_at', 'due_date', 'estimated_duration',
            'status', 'status_display', 'progress', 'accepted_at', 'started_at',
            'completed_at', 'actual_duration', 'result_data', 'comments',
            'quality_score', 'efficiency_score', 'updated_at'
        ]


class ProcessOptimizationSerializer(serializers.ModelSerializer):
    """流程优化序列化器"""
    optimization_type_display = serializers.CharField(source='get_optimization_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    workflow_template_name = serializers.CharField(source='workflow_template.name', read_only=True)
    identified_by_name = serializers.CharField(source='identified_by.get_full_name', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.get_full_name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.get_full_name', read_only=True)
    
    class Meta:
        model = ProcessOptimization
        fields = [
            'id', 'optimization_id', 'workflow_template', 'workflow_template_name',
            'title', 'optimization_type', 'optimization_type_display', 'description',
            'current_issues', 'root_causes', 'impact_analysis', 'proposed_solution',
            'implementation_steps', 'required_resources', 'expected_benefits',
            'estimated_savings', 'time_reduction', 'quality_improvement',
            'status', 'status_display', 'priority', 'priority_display',
            'identified_at', 'target_completion_date', 'actual_completion_date',
            'identified_by', 'identified_by_name', 'assigned_to', 'assigned_to_name',
            'approved_by', 'approved_by_name', 'implementation_notes',
            'actual_benefits', 'lessons_learned', 'updated_at'
        ]


class SmartReminderSerializer(serializers.ModelSerializer):
    """智能提醒序列化器"""
    reminder_type_display = serializers.CharField(source='get_reminder_type_display', read_only=True)
    trigger_type_display = serializers.CharField(source='get_trigger_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    acknowledged_by_name = serializers.CharField(source='acknowledged_by.get_full_name', read_only=True)
    recipient_names = serializers.SerializerMethodField()
    
    class Meta:
        model = SmartReminder
        fields = [
            'id', 'reminder_id', 'title', 'reminder_type', 'reminder_type_display',
            'trigger_type', 'trigger_type_display', 'workflow_instance',
            'workflow_step', 'task_assignment', 'message', 'action_required',
            'trigger_condition', 'trigger_time', 'advance_notice', 'is_recurring',
            'recurrence_pattern', 'status', 'status_display', 'is_urgent',
            'triggered_at', 'sent_at', 'acknowledged_at', 'acknowledged_by',
            'acknowledged_by_name', 'snooze_until', 'snooze_count',
            'created_by', 'created_by_name', 'recipient_names',
            'created_at', 'updated_at'
        ]
    
    def get_recipient_names(self, obj):
        return [user.get_full_name() for user in obj.recipients.all()]


class WorkflowAnalyticsSerializer(serializers.ModelSerializer):
    """工作流分析序列化器"""
    metric_type_display = serializers.CharField(source='get_metric_type_display', read_only=True)
    workflow_template_name = serializers.CharField(source='workflow_template.name', read_only=True)
    generated_by_name = serializers.CharField(source='generated_by.get_full_name', read_only=True)
    
    class Meta:
        model = WorkflowAnalytics
        fields = [
            'id', 'analytics_id', 'workflow_template', 'workflow_template_name',
            'metric_type', 'metric_type_display', 'analysis_period_start',
            'analysis_period_end', 'total_instances', 'completed_instances',
            'failed_instances', 'average_duration', 'median_duration',
            'throughput', 'cycle_time', 'wait_time', 'processing_time',
            'error_rate', 'rework_rate', 'first_pass_yield', 'total_cost',
            'cost_per_instance', 'labor_cost', 'system_cost', 'satisfaction_score',
            'nps_score', 'bottleneck_steps', 'bottleneck_analysis',
            'improvement_suggestions', 'detailed_metrics', 'trend_data',
            'comparison_data', 'generated_at', 'generated_by', 'generated_by_name'
        ]


class AutomationRuleSerializer(serializers.ModelSerializer):
    """自动化规则序列化器"""
    rule_type_display = serializers.CharField(source='get_rule_type_display', read_only=True)
    trigger_event_display = serializers.CharField(source='get_trigger_event_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    success_rate = serializers.SerializerMethodField()
    workflow_template_names = serializers.SerializerMethodField()
    
    class Meta:
        model = AutomationRule
        fields = [
            'id', 'rule_id', 'name', 'rule_type', 'rule_type_display',
            'description', 'trigger_event', 'trigger_event_display',
            'trigger_conditions', 'rule_config', 'action_config',
            'status', 'status_display', 'priority', 'execution_count',
            'success_count', 'success_rate', 'last_executed_at',
            'average_execution_time', 'error_rate', 'workflow_template_names',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
    
    def get_success_rate(self, obj):
        return obj.get_success_rate()
    
    def get_workflow_template_names(self, obj):
        return [template.name for template in obj.workflow_templates.all()]


class WorkflowConfigSerializer(serializers.ModelSerializer):
    """工作流配置序列化器"""
    config_type_display = serializers.CharField(source='get_config_type_display', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    workflow_template_name = serializers.CharField(source='workflow_template.name', read_only=True)
    
    class Meta:
        model = WorkflowConfig
        fields = [
            'id', 'config_type', 'config_type_display', 'config_key',
            'config_value', 'description', 'is_system', 'is_active',
            'workflow_template', 'workflow_template_name', 'created_by',
            'created_by_name', 'created_at', 'updated_at'
        ]


# 简化版序列化器用于列表显示
class WorkflowTemplateListSerializer(serializers.ModelSerializer):
    """工作流模板列表序列化器"""
    workflow_type_display = serializers.CharField(source='get_workflow_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = WorkflowTemplate
        fields = [
            'id', 'name', 'workflow_type_display', 'status_display',
            'complexity_score', 'success_rate', 'usage_count', 'updated_at'
        ]


class WorkflowInstanceListSerializer(serializers.ModelSerializer):
    """工作流实例列表序列化器"""
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    template_name = serializers.CharField(source='template.name', read_only=True)
    
    class Meta:
        model = WorkflowInstance
        fields = [
            'id', 'instance_id', 'name', 'template_name', 'status_display',
            'priority_display', 'progress', 'created_at'
        ]


class TaskAssignmentListSerializer(serializers.ModelSerializer):
    """任务分配列表序列化器"""
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.get_full_name', read_only=True)
    
    class Meta:
        model = TaskAssignment
        fields = [
            'id', 'assignment_id', 'title', 'assigned_to_name',
            'status_display', 'progress', 'due_date', 'assigned_at'
        ]


# 业务操作序列化器
class WorkflowExecutionSerializer(serializers.Serializer):
    """工作流执行序列化器"""
    template_id = serializers.IntegerField(help_text='工作流模板ID')
    input_data = serializers.JSONField(help_text='输入数据')
    priority = serializers.ChoiceField(
        choices=WorkflowInstance.PRIORITY_CHOICES,
        default=2,
        help_text='优先级'
    )
    assigned_to = serializers.IntegerField(
        required=False,
        help_text='分配给用户ID'
    )
    scheduled_start_time = serializers.DateTimeField(
        required=False,
        help_text='计划开始时间'
    )
    deadline = serializers.DateTimeField(
        required=False,
        help_text='截止时间'
    )


class TaskCompletionSerializer(serializers.Serializer):
    """任务完成序列化器"""
    result_data = serializers.JSONField(help_text='结果数据')
    comments = serializers.CharField(
        max_length=1000,
        required=False,
        help_text='备注'
    )
    quality_score = serializers.FloatField(
        min_value=0,
        max_value=10,
        required=False,
        help_text='质量评分'
    )


class ProcessOptimizationRequestSerializer(serializers.Serializer):
    """流程优化请求序列化器"""
    workflow_template_id = serializers.IntegerField(help_text='工作流模板ID')
    optimization_type = serializers.ChoiceField(
        choices=ProcessOptimization.OPTIMIZATION_TYPES,
        help_text='优化类型'
    )
    title = serializers.CharField(max_length=200, help_text='优化标题')
    description = serializers.CharField(help_text='优化描述')
    current_issues = serializers.ListField(
        child=serializers.CharField(),
        help_text='当前问题'
    )
    proposed_solution = serializers.CharField(help_text='建议方案')


class ReminderCreationSerializer(serializers.Serializer):
    """提醒创建序列化器"""
    title = serializers.CharField(max_length=200, help_text='提醒标题')
    reminder_type = serializers.ChoiceField(
        choices=SmartReminder.REMINDER_TYPES,
        help_text='提醒类型'
    )
    trigger_type = serializers.ChoiceField(
        choices=SmartReminder.TRIGGER_TYPES,
        help_text='触发类型'
    )
    message = serializers.CharField(help_text='提醒消息')
    trigger_time = serializers.DateTimeField(
        required=False,
        help_text='触发时间'
    )
    recipient_ids = serializers.ListField(
        child=serializers.IntegerField(),
        help_text='接收人ID列表'
    )
    is_urgent = serializers.BooleanField(
        default=False,
        help_text='是否紧急'
    )

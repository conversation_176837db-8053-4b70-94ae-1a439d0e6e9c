from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.db.models import Count, Avg, Sum
import uuid
from .models import (
    WorkflowTemplate, WorkflowInstance, WorkflowStep, TaskAssignment,
    ProcessOptimization, SmartReminder, WorkflowAnalytics, AutomationRule, WorkflowConfig
)


@admin.register(WorkflowTemplate)
class WorkflowTemplateAdmin(admin.ModelAdmin):
    """工作流模板管理"""
    list_display = [
        'name', 'workflow_type_display', 'status_display', 'complexity_score',
        'success_rate_display', 'usage_count', 'last_used_at', 'updated_at'
    ]
    list_filter = ['workflow_type', 'status', 'is_default', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['usage_count', 'last_used_at', 'created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'workflow_type', 'description', 'status')
        }),
        ('流程配置', {
            'fields': ('steps_config', 'rules_config', 'automation_config')
        }),
        ('性能指标', {
            'fields': ('estimated_duration', 'complexity_score', 'success_rate')
        }),
        ('版本管理', {
            'fields': ('version', 'is_default')
        }),
        ('使用统计', {
            'fields': ('usage_count', 'last_used_at'),
            'classes': ('collapse',)
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['activate_templates', 'deactivate_templates', 'set_as_default']

    def workflow_type_display(self, obj):
        """工作流类型显示"""
        type_colors = {
            'finance_audit': '#1890ff',
            'order_process': '#52c41a',
            'invoice_issue': '#faad14',
            'tax_filing': '#f5222d',
            'customer_service': '#722ed1',
            'document_review': '#13c2c2',
            'approval_process': '#fa8c16',
            'data_analysis': '#eb2f96',
            'custom': '#8c8c8c',
        }
        color = type_colors.get(obj.workflow_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_workflow_type_display()
        )
    workflow_type_display.short_description = '工作流类型'

    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'draft': '#d9d9d9',
            'active': '#52c41a',
            'inactive': '#8c8c8c',
            'archived': '#f5222d',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'

    def success_rate_display(self, obj):
        """成功率显示"""
        rate = obj.success_rate * 100
        if rate >= 95:
            color = '#52c41a'
        elif rate >= 90:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, f"{rate:.1f}%"
        )
    success_rate_display.short_description = '成功率'

    def activate_templates(self, request, queryset):
        """激活模板"""
        updated = queryset.update(status='active')
        self.message_user(request, f"成功激活 {updated} 个模板")
    activate_templates.short_description = "激活选中的模板"

    def deactivate_templates(self, request, queryset):
        """停用模板"""
        updated = queryset.update(status='inactive')
        self.message_user(request, f"成功停用 {updated} 个模板")
    deactivate_templates.short_description = "停用选中的模板"

    def set_as_default(self, request, queryset):
        """设为默认"""
        for template in queryset:
            # 先取消同类型的其他默认模板
            WorkflowTemplate.objects.filter(
                workflow_type=template.workflow_type,
                is_default=True
            ).update(is_default=False)

            # 设置当前模板为默认
            template.is_default = True
            template.save()

        self.message_user(request, f"成功设置 {queryset.count()} 个默认模板")
    set_as_default.short_description = "设为默认模板"

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(WorkflowInstance)
class WorkflowInstanceAdmin(admin.ModelAdmin):
    """工作流实例管理"""
    list_display = [
        'instance_id', 'name', 'template_name', 'status_display', 'priority_display',
        'progress_display', 'assigned_to', 'created_at'
    ]
    list_filter = ['status', 'priority', 'template__workflow_type', 'created_at']
    search_fields = ['instance_id', 'name', 'description']
    readonly_fields = ['instance_id', 'start_time', 'end_time', 'execution_time', 'created_at', 'updated_at']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('基本信息', {
            'fields': ('instance_id', 'template', 'name', 'description')
        }),
        ('执行配置', {
            'fields': ('input_data', 'context_data', 'variables')
        }),
        ('状态信息', {
            'fields': ('status', 'current_step', 'progress')
        }),
        ('优先级和调度', {
            'fields': ('priority', 'scheduled_start_time', 'deadline')
        }),
        ('执行信息', {
            'fields': ('start_time', 'end_time', 'execution_time'),
            'classes': ('collapse',)
        }),
        ('结果信息', {
            'fields': ('output_data', 'error_message'),
            'classes': ('collapse',)
        }),
        ('关联信息', {
            'fields': ('parent_instance', 'related_object_type', 'related_object_id'),
            'classes': ('collapse',)
        }),
        ('人员信息', {
            'fields': ('created_by', 'assigned_to', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['start_instances', 'pause_instances', 'cancel_instances']

    def template_name(self, obj):
        """模板名称"""
        return obj.template.name
    template_name.short_description = '工作流模板'

    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'pending': '#d9d9d9',
            'running': '#faad14',
            'paused': '#fa8c16',
            'completed': '#52c41a',
            'failed': '#f5222d',
            'cancelled': '#8c8c8c',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'

    def priority_display(self, obj):
        """优先级显示"""
        priority_colors = {
            1: '#d9d9d9',
            2: '#1890ff',
            3: '#faad14',
            4: '#f5222d',
        }
        color = priority_colors.get(obj.priority, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_priority_display()
        )
    priority_display.short_description = '优先级'

    def progress_display(self, obj):
        """进度显示"""
        progress = obj.progress
        if progress >= 100:
            color = '#52c41a'
        elif progress >= 50:
            color = '#faad14'
        else:
            color = '#1890ff'

        return format_html(
            '<div style="width: 100px; background: #f0f0f0; border-radius: 3px;">'
            '<div style="width: {}%; background: {}; height: 20px; border-radius: 3px; text-align: center; color: white; font-size: 12px; line-height: 20px;">'
            '{}%</div></div>',
            progress, color, progress
        )
    progress_display.short_description = '进度'

    def start_instances(self, request, queryset):
        """启动实例"""
        updated = 0
        for instance in queryset.filter(status='pending'):
            instance.status = 'running'
            instance.start_time = timezone.now()
            instance.save()
            updated += 1

        self.message_user(request, f"成功启动 {updated} 个实例")
    start_instances.short_description = "启动选中的实例"

    def pause_instances(self, request, queryset):
        """暂停实例"""
        updated = queryset.filter(status='running').update(status='paused')
        self.message_user(request, f"成功暂停 {updated} 个实例")
    pause_instances.short_description = "暂停选中的实例"

    def cancel_instances(self, request, queryset):
        """取消实例"""
        updated = queryset.filter(status__in=['pending', 'running', 'paused']).update(
            status='cancelled',
            end_time=timezone.now()
        )
        self.message_user(request, f"成功取消 {updated} 个实例")
    cancel_instances.short_description = "取消选中的实例"

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
            if not obj.instance_id:
                obj.instance_id = f"WF_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
        super().save_model(request, obj, form, change)


@admin.register(WorkflowStep)
class WorkflowStepAdmin(admin.ModelAdmin):
    """工作流步骤管理"""
    list_display = [
        'step_id', 'name', 'instance_name', 'step_type_display', 'status_display',
        'assigned_to', 'sequence_number', 'updated_at'
    ]
    list_filter = ['step_type', 'status', 'created_at']
    search_fields = ['step_id', 'name', 'description']
    readonly_fields = ['start_time', 'end_time', 'execution_time', 'created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('instance', 'step_id', 'name', 'step_type', 'description')
        }),
        ('步骤配置', {
            'fields': ('step_config', 'input_mapping', 'output_mapping')
        }),
        ('执行顺序', {
            'fields': ('sequence_number', 'parent_step')
        }),
        ('状态信息', {
            'fields': ('status', 'progress')
        }),
        ('执行信息', {
            'fields': ('start_time', 'end_time', 'execution_time'),
            'classes': ('collapse',)
        }),
        ('结果信息', {
            'fields': ('result_data', 'error_message'),
            'classes': ('collapse',)
        }),
        ('分配信息', {
            'fields': ('assigned_to',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def instance_name(self, obj):
        """实例名称"""
        return obj.instance.name
    instance_name.short_description = '工作流实例'

    def step_type_display(self, obj):
        """步骤类型显示"""
        type_colors = {
            'manual': '#1890ff',
            'automatic': '#52c41a',
            'approval': '#faad14',
            'notification': '#13c2c2',
            'condition': '#722ed1',
            'parallel': '#fa8c16',
            'subprocess': '#eb2f96',
            'script': '#8c8c8c',
        }
        color = type_colors.get(obj.step_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_step_type_display()
        )
    step_type_display.short_description = '步骤类型'

    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'pending': '#d9d9d9',
            'running': '#faad14',
            'completed': '#52c41a',
            'failed': '#f5222d',
            'skipped': '#8c8c8c',
            'cancelled': '#8c8c8c',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'


@admin.register(TaskAssignment)
class TaskAssignmentAdmin(admin.ModelAdmin):
    """任务分配管理"""
    list_display = [
        'assignment_id', 'title', 'assigned_to_name', 'assignment_type_display',
        'status_display', 'due_date', 'progress_display', 'assigned_at'
    ]
    list_filter = ['assignment_type', 'status', 'assigned_at']
    search_fields = ['assignment_id', 'title', 'description']
    readonly_fields = ['assignment_id', 'assigned_at', 'accepted_at', 'started_at', 'completed_at', 'actual_duration']
    date_hierarchy = 'assigned_at'

    fieldsets = (
        ('基本信息', {
            'fields': ('assignment_id', 'workflow_step', 'title', 'description')
        }),
        ('分配信息', {
            'fields': ('assigned_to', 'assigned_by', 'assignment_type')
        }),
        ('任务信息', {
            'fields': ('instructions', 'due_date', 'estimated_duration')
        }),
        ('状态管理', {
            'fields': ('status', 'progress')
        }),
        ('执行信息', {
            'fields': ('assigned_at', 'accepted_at', 'started_at', 'completed_at', 'actual_duration'),
            'classes': ('collapse',)
        }),
        ('结果信息', {
            'fields': ('result_data', 'comments'),
            'classes': ('collapse',)
        }),
        ('评估信息', {
            'fields': ('quality_score', 'efficiency_score'),
            'classes': ('collapse',)
        }),
    )

    actions = ['reassign_tasks', 'mark_completed']

    def assigned_to_name(self, obj):
        """分配给"""
        return obj.assigned_to.get_full_name()
    assigned_to_name.short_description = '分配给'

    def assignment_type_display(self, obj):
        """分配类型显示"""
        type_colors = {
            'manual': '#1890ff',
            'automatic': '#52c41a',
            'load_balance': '#faad14',
            'skill_based': '#722ed1',
            'priority_based': '#f5222d',
        }
        color = type_colors.get(obj.assignment_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_assignment_type_display()
        )
    assignment_type_display.short_description = '分配类型'

    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'pending': '#d9d9d9',
            'accepted': '#1890ff',
            'in_progress': '#faad14',
            'completed': '#52c41a',
            'rejected': '#f5222d',
            'reassigned': '#8c8c8c',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'

    def progress_display(self, obj):
        """进度显示"""
        progress = obj.progress
        if progress >= 100:
            color = '#52c41a'
        elif progress >= 50:
            color = '#faad14'
        else:
            color = '#1890ff'

        return format_html(
            '<div style="width: 80px; background: #f0f0f0; border-radius: 3px;">'
            '<div style="width: {}%; background: {}; height: 16px; border-radius: 3px; text-align: center; color: white; font-size: 11px; line-height: 16px;">'
            '{}%</div></div>',
            progress, color, progress
        )
    progress_display.short_description = '进度'

    def reassign_tasks(self, request, queryset):
        """重新分配任务"""
        updated = queryset.filter(status__in=['pending', 'rejected']).update(status='reassigned')
        self.message_user(request, f"成功重新分配 {updated} 个任务")
    reassign_tasks.short_description = "重新分配选中的任务"

    def mark_completed(self, request, queryset):
        """标记完成"""
        updated = queryset.filter(status='in_progress').update(
            status='completed',
            completed_at=timezone.now(),
            progress=100
        )
        self.message_user(request, f"成功标记 {updated} 个任务为完成")
    mark_completed.short_description = "标记选中的任务为完成"

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            if not obj.assignment_id:
                obj.assignment_id = f"TASK_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
        super().save_model(request, obj, form, change)


@admin.register(ProcessOptimization)
class ProcessOptimizationAdmin(admin.ModelAdmin):
    """流程优化管理"""
    list_display = [
        'title', 'optimization_type_display', 'workflow_template_name', 'status_display',
        'priority_display', 'estimated_savings', 'identified_at'
    ]
    list_filter = ['optimization_type', 'status', 'priority', 'identified_at']
    search_fields = ['title', 'description', 'optimization_id']
    readonly_fields = ['optimization_id', 'identified_at', 'actual_completion_date']
    date_hierarchy = 'identified_at'

    fieldsets = (
        ('基本信息', {
            'fields': ('optimization_id', 'workflow_template', 'title', 'optimization_type', 'description')
        }),
        ('问题分析', {
            'fields': ('current_issues', 'root_causes', 'impact_analysis')
        }),
        ('优化方案', {
            'fields': ('proposed_solution', 'implementation_steps', 'required_resources')
        }),
        ('预期效果', {
            'fields': ('expected_benefits', 'estimated_savings', 'time_reduction', 'quality_improvement')
        }),
        ('状态管理', {
            'fields': ('status', 'priority')
        }),
        ('时间管理', {
            'fields': ('identified_at', 'target_completion_date', 'actual_completion_date')
        }),
        ('人员信息', {
            'fields': ('identified_by', 'assigned_to', 'approved_by'),
            'classes': ('collapse',)
        }),
        ('实施结果', {
            'fields': ('implementation_notes', 'actual_benefits', 'lessons_learned'),
            'classes': ('collapse',)
        }),
    )

    actions = ['approve_optimizations', 'start_implementation']

    def workflow_template_name(self, obj):
        """工作流模板名称"""
        return obj.workflow_template.name
    workflow_template_name.short_description = '工作流模板'

    def optimization_type_display(self, obj):
        """优化类型显示"""
        type_colors = {
            'time_reduction': '#1890ff',
            'cost_reduction': '#52c41a',
            'quality_improvement': '#faad14',
            'automation': '#722ed1',
            'resource_optimization': '#13c2c2',
            'bottleneck_removal': '#f5222d',
        }
        color = type_colors.get(obj.optimization_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_optimization_type_display()
        )
    optimization_type_display.short_description = '优化类型'

    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'identified': '#d9d9d9',
            'analyzing': '#1890ff',
            'proposed': '#faad14',
            'approved': '#52c41a',
            'implementing': '#fa8c16',
            'completed': '#52c41a',
            'rejected': '#f5222d',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'

    def priority_display(self, obj):
        """优先级显示"""
        priority_colors = {
            1: '#d9d9d9',
            2: '#1890ff',
            3: '#faad14',
            4: '#f5222d',
        }
        color = priority_colors.get(obj.priority, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_priority_display()
        )
    priority_display.short_description = '优先级'

    def approve_optimizations(self, request, queryset):
        """批准优化"""
        updated = queryset.filter(status='proposed').update(
            status='approved',
            approved_by=request.user
        )
        self.message_user(request, f"成功批准 {updated} 个优化方案")
    approve_optimizations.short_description = "批准选中的优化方案"

    def start_implementation(self, request, queryset):
        """开始实施"""
        updated = queryset.filter(status='approved').update(status='implementing')
        self.message_user(request, f"成功启动 {updated} 个优化实施")
    start_implementation.short_description = "开始实施选中的优化"

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.identified_by = request.user
            if not obj.optimization_id:
                obj.optimization_id = f"OPT_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
        super().save_model(request, obj, form, change)


@admin.register(SmartReminder)
class SmartReminderAdmin(admin.ModelAdmin):
    """智能提醒管理"""
    list_display = [
        'title', 'reminder_type_display', 'trigger_type_display', 'status_display',
        'is_urgent', 'trigger_time', 'created_at'
    ]
    list_filter = ['reminder_type', 'trigger_type', 'status', 'is_urgent', 'is_recurring', 'created_at']
    search_fields = ['title', 'message', 'reminder_id']
    readonly_fields = ['reminder_id', 'triggered_at', 'sent_at', 'acknowledged_at', 'created_at', 'updated_at']
    filter_horizontal = ['recipients']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('基本信息', {
            'fields': ('reminder_id', 'title', 'reminder_type', 'trigger_type')
        }),
        ('关联对象', {
            'fields': ('workflow_instance', 'workflow_step', 'task_assignment')
        }),
        ('提醒内容', {
            'fields': ('message', 'action_required')
        }),
        ('触发条件', {
            'fields': ('trigger_condition', 'trigger_time', 'advance_notice')
        }),
        ('重复设置', {
            'fields': ('is_recurring', 'recurrence_pattern')
        }),
        ('接收人', {
            'fields': ('recipients',)
        }),
        ('状态管理', {
            'fields': ('status', 'is_urgent')
        }),
        ('执行信息', {
            'fields': ('triggered_at', 'sent_at', 'acknowledged_at', 'acknowledged_by'),
            'classes': ('collapse',)
        }),
        ('延迟设置', {
            'fields': ('snooze_until', 'snooze_count'),
            'classes': ('collapse',)
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['trigger_reminders', 'acknowledge_reminders', 'cancel_reminders']

    def reminder_type_display(self, obj):
        """提醒类型显示"""
        type_colors = {
            'deadline': '#f5222d',
            'milestone': '#52c41a',
            'overdue': '#ff4d4f',
            'approval': '#faad14',
            'follow_up': '#1890ff',
            'maintenance': '#722ed1',
            'custom': '#8c8c8c',
        }
        color = type_colors.get(obj.reminder_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_reminder_type_display()
        )
    reminder_type_display.short_description = '提醒类型'

    def trigger_type_display(self, obj):
        """触发类型显示"""
        type_colors = {
            'time_based': '#1890ff',
            'event_based': '#52c41a',
            'condition_based': '#faad14',
            'manual': '#8c8c8c',
        }
        color = type_colors.get(obj.trigger_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_trigger_type_display()
        )
    trigger_type_display.short_description = '触发类型'

    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'active': '#52c41a',
            'triggered': '#faad14',
            'sent': '#1890ff',
            'acknowledged': '#52c41a',
            'snoozed': '#fa8c16',
            'cancelled': '#8c8c8c',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'

    def trigger_reminders(self, request, queryset):
        """触发提醒"""
        updated = queryset.filter(status='active').update(
            status='triggered',
            triggered_at=timezone.now()
        )
        self.message_user(request, f"成功触发 {updated} 个提醒")
    trigger_reminders.short_description = "触发选中的提醒"

    def acknowledge_reminders(self, request, queryset):
        """确认提醒"""
        updated = queryset.filter(status__in=['triggered', 'sent']).update(
            status='acknowledged',
            acknowledged_at=timezone.now(),
            acknowledged_by=request.user
        )
        self.message_user(request, f"成功确认 {updated} 个提醒")
    acknowledge_reminders.short_description = "确认选中的提醒"

    def cancel_reminders(self, request, queryset):
        """取消提醒"""
        updated = queryset.filter(status__in=['active', 'triggered']).update(status='cancelled')
        self.message_user(request, f"成功取消 {updated} 个提醒")
    cancel_reminders.short_description = "取消选中的提醒"

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
            if not obj.reminder_id:
                obj.reminder_id = f"RMD_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
        super().save_model(request, obj, form, change)


# 注册其他模型的简化admin
@admin.register(WorkflowAnalytics)
class WorkflowAnalyticsAdmin(admin.ModelAdmin):
    """工作流分析管理"""
    list_display = ['workflow_template', 'metric_type', 'analysis_period_start', 'analysis_period_end', 'generated_at']
    list_filter = ['metric_type', 'generated_at']
    readonly_fields = ['analytics_id', 'generated_at']

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.generated_by = request.user
            if not obj.analytics_id:
                obj.analytics_id = f"ANL_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
        super().save_model(request, obj, form, change)


@admin.register(AutomationRule)
class AutomationRuleAdmin(admin.ModelAdmin):
    """自动化规则管理"""
    list_display = ['name', 'rule_type', 'trigger_event', 'status', 'execution_count', 'success_rate_display', 'created_at']
    list_filter = ['rule_type', 'trigger_event', 'status', 'created_at']
    filter_horizontal = ['workflow_templates']

    def success_rate_display(self, obj):
        """成功率显示"""
        rate = obj.get_success_rate()
        if rate >= 95:
            color = '#52c41a'
        elif rate >= 90:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, f"{rate:.1f}%"
        )
    success_rate_display.short_description = '成功率'

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
            if not obj.rule_id:
                obj.rule_id = f"RULE_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
        super().save_model(request, obj, form, change)


@admin.register(WorkflowConfig)
class WorkflowConfigAdmin(admin.ModelAdmin):
    """工作流配置管理"""
    list_display = ['config_key', 'config_type', 'workflow_template', 'is_system', 'is_active', 'updated_at']
    list_filter = ['config_type', 'is_system', 'is_active']

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


# 自定义管理页面标题
admin.site.site_header = '代理记账管理系统'
admin.site.site_title = '智能业务流程助手'
admin.site.index_title = '智能业务流程助手'

import logging
from django.core.exceptions import ValidationError, PermissionDenied
from django.http import JsonResponse
from rest_framework import status

logger = logging.getLogger(__name__)

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q, Count, Avg, Sum, Max, Min
from django.shortcuts import get_object_or_404
from datetime import datetime, timedelta
import uuid
import json
import random
from .models import (
    WorkflowTemplate, WorkflowInstance, WorkflowStep, TaskAssignment,
    ProcessOptimization, SmartReminder, WorkflowAnalytics, AutomationRule, WorkflowConfig
)
from .serializers import (
    WorkflowTemplateSerializer, WorkflowInstanceSerializer, WorkflowStepSerializer,
    TaskAssignmentSerializer, ProcessOptimizationSerializer, SmartReminderSerializer,
    WorkflowAnalyticsSerializer, AutomationRuleSerializer, WorkflowConfigSerializer,
    WorkflowTemplateListSerializer, WorkflowInstanceListSerializer, TaskAssignmentListSerializer,
    WorkflowExecutionSerializer, TaskCompletionSerializer, ProcessOptimizationRequestSerializer,
    ReminderCreationSerializer
)
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class WorkflowTemplateViewSet(viewsets.ModelViewSet):
    """工作流模板ViewSet"""
    queryset = WorkflowTemplate.objects.all()
    serializer_class = WorkflowTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        try:
            queryset = super().get_queryset()

            # 按工作流类型过滤
            workflow_type = self.request.query_params.get('workflow_type')
            if workflow_type:
                queryset = queryset.filter(workflow_type=workflow_type)

            # 按状态过滤
            status_filter = self.request.query_params.get('status')
            if status_filter:
                queryset = queryset.filter(status=status_filter)

            # 只显示启用的
            if self.request.query_params.get('active_only') == 'true':
                queryset = queryset.filter(status='active')

            # 搜索
            search = self.request.query_params.get('search')
            if search:
                queryset = queryset.filter(
                    Q(name__icontains=search) |
                    Q(description__icontains=search)
                )

            return queryset.order_by('-created_at')

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def get_serializer_class(self):
        try:
            if self.action == 'list':
                return WorkflowTemplateListSerializer
            return WorkflowTemplateSerializer

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def perform_create(self, serializer):
        try:
            serializer.save(created_by=self.request.user)

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def execute(self, request, pk=None):
        """执行工作流"""
        try:
            template = self.get_object()
            serializer = WorkflowExecutionSerializer(data=request.data)

            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            data = serializer.validated_data

            # 检查模板状态
            if template.status != 'active':
                return Response(
                    {'error': '工作流模板未启用'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # 创建工作流实例
            instance = WorkflowInstance.objects.create(
                instance_id=f"WF_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}",
                template=template,
                name=f"{template.name} - {timezone.now().strftime('%Y-%m-%d %H:%M')}",
                input_data=data['input_data'],
                priority=data.get('priority', 2),
                scheduled_start_time=data.get('scheduled_start_time'),
                deadline=data.get('deadline'),
                created_by=request.user,
                assigned_to_id=data.get('assigned_to')
            )

            # 创建工作流步骤
            self._create_workflow_steps(instance, template.steps_config)

            # 更新模板使用统计
            template.usage_count += 1
            template.last_used_at = timezone.now()
            template.save()

            return Response({
                'message': '工作流执行成功',
                'instance_id': instance.instance_id,
                'instance': WorkflowInstanceSerializer(instance).data
            })

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['get'])
    def analytics(self, request, pk=None):
        """获取模板分析数据"""
        try:
            template = self.get_object()

            # 获取实例统计
            instances = WorkflowInstance.objects.filter(template=template)

            stats = {
                'template_info': {
                    'name': template.name,
                    'type': template.get_workflow_type_display(),
                    'complexity_score': template.complexity_score,
                    'success_rate': template.success_rate
                },
                'usage_stats': {
                    'total_instances': instances.count(),
                    'completed_instances': instances.filter(status='completed').count(),
                    'failed_instances': instances.filter(status='failed').count(),
                    'running_instances': instances.filter(status='running').count(),
                    'usage_count': template.usage_count,
                    'last_used_at': template.last_used_at
                },
                'performance_metrics': self._calculate_performance_metrics(instances),
                'recent_instances': [
                    {
                        'instance_id': instance.instance_id,
                        'name': instance.name,
                        'status': instance.get_status_display(),
                        'progress': instance.progress,
                        'created_at': instance.created_at
                    }
                    for instance in instances.order_by('-created_at')[:5]
                ]
            }

            return Response(stats)

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _create_workflow_steps(self, instance, steps_config):
        """创建工作流步骤"""
        try:
            for step_config in steps_config:
                WorkflowStep.objects.create(
                    instance=instance,
                    step_id=step_config.get('step_id', f"step_{uuid.uuid4().hex[:8]}"),
                    name=step_config.get('name', '未命名步骤'),
                    step_type=step_config.get('type', 'manual'),
                    description=step_config.get('description', ''),
                    step_config=step_config,
                    sequence_number=step_config.get('sequence', 1)
                )

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _calculate_performance_metrics(self, instances):
        """计算性能指标"""
        try:
            completed_instances = instances.filter(status='completed', execution_time__isnull=False)

            if not completed_instances.exists():
                return {
                    'average_duration': 0,
                    'median_duration': 0,
                    'min_duration': 0,
                    'max_duration': 0,
                    'completion_rate': 0
                }

            durations = [instance.execution_time for instance in completed_instances]
            durations.sort()

            return {
                'average_duration': sum(durations) / len(durations) / 3600,  # 转换为小时
                'median_duration': durations[len(durations) // 2] / 3600,
                'min_duration': min(durations) / 3600,
                'max_duration': max(durations) / 3600,
                'completion_rate': completed_instances.count() / instances.count() * 100
            }


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class WorkflowInstanceViewSet(viewsets.ModelViewSet):
    """工作流实例ViewSet"""
    queryset = WorkflowInstance.objects.all()
    serializer_class = WorkflowInstanceSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        try:
            queryset = super().get_queryset()

            # 按状态过滤
            status_filter = self.request.query_params.get('status')
            if status_filter:
                queryset = queryset.filter(status=status_filter)

            # 按优先级过滤
            priority = self.request.query_params.get('priority')
            if priority:
                queryset = queryset.filter(priority=priority)

            # 按模板过滤
            template_id = self.request.query_params.get('template')
            if template_id:
                queryset = queryset.filter(template_id=template_id)

            # 按分配人过滤
            assigned_to = self.request.query_params.get('assigned_to')
            if assigned_to:
                queryset = queryset.filter(assigned_to_id=assigned_to)

            # 我的工作流
            if self.request.query_params.get('my_workflows') == 'true':
                queryset = queryset.filter(
                    Q(created_by=self.request.user) |
                    Q(assigned_to=self.request.user)
                )

            return queryset.order_by('-created_at')

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def get_serializer_class(self):
        try:
            if self.action == 'list':
                return WorkflowInstanceListSerializer
            return WorkflowInstanceSerializer

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def perform_create(self, serializer):
        try:
            instance_id = f"WF_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
            serializer.save(created_by=self.request.user, instance_id=instance_id)

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def start(self, request, pk=None):
        """启动工作流实例"""
        try:
            instance = self.get_object()

            if instance.status != 'pending':
                return Response(
                    {'error': '工作流实例状态不允许启动'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # 启动工作流
            instance.status = 'running'
            instance.start_time = timezone.now()
            instance.current_step = 'step_1'  # 假设第一个步骤
            instance.progress = 10
            instance.save()

            # 启动第一个步骤
            first_step = instance.steps.filter(sequence_number=1).first()
            if first_step:
                first_step.status = 'running'
                first_step.start_time = timezone.now()
                first_step.save()

                # 如果是手动任务，创建任务分配
                if first_step.step_type == 'manual':
                    self._create_task_assignment(first_step, instance.assigned_to or request.user)

            return Response({
                'message': '工作流启动成功',
                'current_step': instance.current_step,
                'progress': instance.progress
            })

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def pause(self, request, pk=None):
        """暂停工作流实例"""
        try:
            instance = self.get_object()

            if instance.status != 'running':
                return Response(
                    {'error': '工作流实例状态不允许暂停'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            instance.status = 'paused'
            instance.save()

            # 暂停当前运行的步骤
            running_steps = instance.steps.filter(status='running')
            running_steps.update(status='paused')

            return Response({'message': '工作流暂停成功'})

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def resume(self, request, pk=None):
        """恢复工作流实例"""
        try:
            instance = self.get_object()

            if instance.status != 'paused':
                return Response(
                    {'error': '工作流实例状态不允许恢复'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            instance.status = 'running'
            instance.save()

            # 恢复暂停的步骤
            paused_steps = instance.steps.filter(status='paused')
            paused_steps.update(status='running')

            return Response({'message': '工作流恢复成功'})

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """取消工作流实例"""
        try:
            instance = self.get_object()

            if instance.status in ['completed', 'failed', 'cancelled']:
                return Response(
                    {'error': '工作流实例已结束，无法取消'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            instance.status = 'cancelled'
            instance.end_time = timezone.now()
            instance.save()

            # 取消所有未完成的步骤
            pending_steps = instance.steps.filter(status__in=['pending', 'running'])
            pending_steps.update(status='cancelled')

            return Response({'message': '工作流取消成功'})

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['get'])
    def progress(self, request, pk=None):
        """获取工作流进度"""
        try:
            instance = self.get_object()

            steps = instance.steps.all().order_by('sequence_number')
            step_progress = []

            for step in steps:
                step_progress.append({
                    'step_id': step.step_id,
                    'name': step.name,
                    'status': step.get_status_display(),
                    'progress': step.progress,
                    'start_time': step.start_time,
                    'end_time': step.end_time
                })

            return Response({
                'instance_id': instance.instance_id,
                'overall_progress': instance.progress,
                'current_step': instance.current_step,
                'status': instance.get_status_display(),
                'step_progress': step_progress
            })

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _create_task_assignment(self, step, assigned_to):
        """创建任务分配"""
        try:
            TaskAssignment.objects.create(
                assignment_id=f"TASK_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}",
                workflow_step=step,
                assigned_to=assigned_to,
                assigned_by=self.request.user,
                title=f"执行步骤: {step.name}",
                description=step.description,
                instructions=step.step_config.get('instructions', ''),
                estimated_duration=step.step_config.get('estimated_duration', 60)
            )


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class WorkflowStepViewSet(viewsets.ReadOnlyModelViewSet):
    """工作流步骤ViewSet"""
    queryset = WorkflowStep.objects.all()
    serializer_class = WorkflowStepSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        try:
            queryset = super().get_queryset()

            # 按实例过滤
            instance_id = self.request.query_params.get('instance')
            if instance_id:
                queryset = queryset.filter(instance_id=instance_id)

            # 按步骤类型过滤
            step_type = self.request.query_params.get('step_type')
            if step_type:
                queryset = queryset.filter(step_type=step_type)

            # 按状态过滤
            status_filter = self.request.query_params.get('status')
            if status_filter:
                queryset = queryset.filter(status=status_filter)

            return queryset.order_by('sequence_number')


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class TaskAssignmentViewSet(viewsets.ModelViewSet):
    """任务分配ViewSet"""
    queryset = TaskAssignment.objects.all()
    serializer_class = TaskAssignmentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        try:
            queryset = super().get_queryset()

            # 按状态过滤
            status_filter = self.request.query_params.get('status')
            if status_filter:
                queryset = queryset.filter(status=status_filter)

            # 按分配类型过滤
            assignment_type = self.request.query_params.get('assignment_type')
            if assignment_type:
                queryset = queryset.filter(assignment_type=assignment_type)

            # 我的任务
            if self.request.query_params.get('my_tasks') == 'true':
                queryset = queryset.filter(assigned_to=self.request.user)

            # 待处理任务
            if self.request.query_params.get('pending_only') == 'true':
                queryset = queryset.filter(status__in=['pending', 'accepted', 'in_progress'])

            return queryset.order_by('-assigned_at')

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def get_serializer_class(self):
        try:
            if self.action == 'list':
                return TaskAssignmentListSerializer
            return TaskAssignmentSerializer

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def accept(self, request, pk=None):
        """接受任务"""
        try:
            assignment = self.get_object()

            if assignment.assigned_to != request.user:
                return Response(
                    {'error': '只能接受分配给自己的任务'},
                    status=status.HTTP_403_FORBIDDEN
                )

            if assignment.status != 'pending':
                return Response(
                    {'error': '任务状态不允许接受'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            assignment.status = 'accepted'
            assignment.accepted_at = timezone.now()
            assignment.save()

            return Response({'message': '任务接受成功'})

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def start(self, request, pk=None):
        """开始任务"""
        try:
            assignment = self.get_object()

            if assignment.assigned_to != request.user:
                return Response(
                    {'error': '只能开始分配给自己的任务'},
                    status=status.HTTP_403_FORBIDDEN
                )

            if assignment.status not in ['accepted', 'pending']:
                return Response(
                    {'error': '任务状态不允许开始'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            assignment.status = 'in_progress'
            assignment.started_at = timezone.now()
            assignment.progress = 10
            assignment.save()

            # 更新工作流步骤状态
            step = assignment.workflow_step
            step.status = 'running'
            step.start_time = timezone.now()
            step.save()

            return Response({'message': '任务开始成功'})

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """完成任务"""
        try:
            assignment = self.get_object()
            serializer = TaskCompletionSerializer(data=request.data)

            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            if assignment.assigned_to != request.user:
                return Response(
                    {'error': '只能完成分配给自己的任务'},
                    status=status.HTTP_403_FORBIDDEN
                )

            if assignment.status != 'in_progress':
                return Response(
                    {'error': '任务状态不允许完成'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            data = serializer.validated_data

            # 更新任务状态
            assignment.status = 'completed'
            assignment.completed_at = timezone.now()
            assignment.progress = 100
            assignment.result_data = data['result_data']
            assignment.comments = data.get('comments', '')
            assignment.quality_score = data.get('quality_score')

            if assignment.started_at:
                assignment.actual_duration = (assignment.completed_at - assignment.started_at).total_seconds() / 60

            assignment.save()

            # 更新工作流步骤状态
            step = assignment.workflow_step
            step.status = 'completed'
            step.end_time = timezone.now()
            step.progress = 100
            step.result_data = data['result_data']

            if step.start_time:
                step.execution_time = (step.end_time - step.start_time).total_seconds()

            step.save()

            # 检查是否需要启动下一个步骤
            self._check_next_step(step)

            return Response({'message': '任务完成成功'})

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """拒绝任务"""
        try:
            assignment = self.get_object()
            reason = request.data.get('reason', '')

            if assignment.assigned_to != request.user:
                return Response(
                    {'error': '只能拒绝分配给自己的任务'},
                    status=status.HTTP_403_FORBIDDEN
                )

            if assignment.status != 'pending':
                return Response(
                    {'error': '任务状态不允许拒绝'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            assignment.status = 'rejected'
            assignment.comments = reason
            assignment.save()

            return Response({'message': '任务拒绝成功'})

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _check_next_step(self, current_step):
        """检查并启动下一个步骤"""
        try:
            instance = current_step.instance
            next_step = instance.steps.filter(
                sequence_number=current_step.sequence_number + 1
            ).first()

            if next_step:
                # 启动下一个步骤
                next_step.status = 'running'
                next_step.start_time = timezone.now()
                next_step.save()

                # 更新实例进度
                total_steps = instance.steps.count()
                completed_steps = instance.steps.filter(status='completed').count()
                instance.progress = int((completed_steps / total_steps) * 100)
                instance.current_step = next_step.step_id
                instance.save()

                # 如果是手动任务，创建任务分配
                if next_step.step_type == 'manual':
                    self._create_task_assignment_for_step(next_step)
            else:
                # 所有步骤完成，结束工作流
                instance.status = 'completed'
                instance.end_time = timezone.now()
                instance.progress = 100

                if instance.start_time:
                    instance.execution_time = (instance.end_time - instance.start_time).total_seconds()

                instance.save()

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _create_task_assignment_for_step(self, step):
        """为步骤创建任务分配"""
        try:
            # 这里可以实现智能分配逻辑
            # 暂时分配给实例的负责人
            assigned_to = step.instance.assigned_to or step.instance.created_by

            TaskAssignment.objects.create(
                assignment_id=f"TASK_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}",
                workflow_step=step,
                assigned_to=assigned_to,
                assignment_type='automatic',
                title=f"执行步骤: {step.name}",
                description=step.description,
                instructions=step.step_config.get('instructions', ''),
                estimated_duration=step.step_config.get('estimated_duration', 60)
            )


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class ProcessOptimizationViewSet(viewsets.ModelViewSet):
    """流程优化ViewSet"""
    queryset = ProcessOptimization.objects.all()
    serializer_class = ProcessOptimizationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        try:
            queryset = super().get_queryset()

            # 按优化类型过滤
            optimization_type = self.request.query_params.get('optimization_type')
            if optimization_type:
                queryset = queryset.filter(optimization_type=optimization_type)

            # 按状态过滤
            status_filter = self.request.query_params.get('status')
            if status_filter:
                queryset = queryset.filter(status=status_filter)

            # 按优先级过滤
            priority = self.request.query_params.get('priority')
            if priority:
                queryset = queryset.filter(priority=priority)

            return queryset.order_by('-identified_at')

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def perform_create(self, serializer):
        try:
            optimization_id = f"OPT_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
            serializer.save(identified_by=self.request.user, optimization_id=optimization_id)

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """批准优化方案"""
        try:
            optimization = self.get_object()

            if optimization.status != 'proposed':
                return Response(
                    {'error': '优化方案状态不允许批准'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            optimization.status = 'approved'
            optimization.approved_by = request.user
            optimization.save()

            return Response({'message': '优化方案批准成功'})

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """拒绝优化方案"""
        try:
            optimization = self.get_object()
            reason = request.data.get('reason', '')

            if optimization.status != 'proposed':
                return Response(
                    {'error': '优化方案状态不允许拒绝'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            optimization.status = 'rejected'
            optimization.implementation_notes = f"拒绝原因: {reason}"
            optimization.save()

            return Response({'message': '优化方案拒绝成功'})

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def start_implementation(self, request, pk=None):
        """开始实施优化"""
        try:
            optimization = self.get_object()

            if optimization.status != 'approved':
                return Response(
                    {'error': '优化方案状态不允许实施'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            optimization.status = 'implementing'
            optimization.assigned_to = request.user
            optimization.save()

            return Response({'message': '优化实施开始'})

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def complete_implementation(self, request, pk=None):
        """完成优化实施"""
        try:
            optimization = self.get_object()

            if optimization.status != 'implementing':
                return Response(
                    {'error': '优化方案状态不允许完成'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            actual_benefits = request.data.get('actual_benefits', {})
            lessons_learned = request.data.get('lessons_learned', '')

            optimization.status = 'completed'
            optimization.actual_completion_date = timezone.now()
            optimization.actual_benefits = actual_benefits
            optimization.lessons_learned = lessons_learned
            optimization.save()

            return Response({'message': '优化实施完成'})


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class SmartReminderViewSet(viewsets.ModelViewSet):
    """智能提醒ViewSet"""
    queryset = SmartReminder.objects.all()
    serializer_class = SmartReminderSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        try:
            queryset = super().get_queryset()

            # 按提醒类型过滤
            reminder_type = self.request.query_params.get('reminder_type')
            if reminder_type:
                queryset = queryset.filter(reminder_type=reminder_type)

            # 按状态过滤
            status_filter = self.request.query_params.get('status')
            if status_filter:
                queryset = queryset.filter(status=status_filter)

            # 我的提醒
            if self.request.query_params.get('my_reminders') == 'true':
                queryset = queryset.filter(recipients=self.request.user)

            # 待处理提醒
            if self.request.query_params.get('pending_only') == 'true':
                queryset = queryset.filter(status__in=['active', 'triggered', 'sent'])

            return queryset.order_by('-created_at')

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def perform_create(self, serializer):
        try:
            reminder_id = f"RMD_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
            serializer.save(created_by=self.request.user, reminder_id=reminder_id)

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def acknowledge(self, request, pk=None):
        """确认提醒"""
        try:
            reminder = self.get_object()

            if request.user not in reminder.recipients.all():
                return Response(
                    {'error': '只能确认发送给自己的提醒'},
                    status=status.HTTP_403_FORBIDDEN
                )

            if reminder.status not in ['triggered', 'sent']:
                return Response(
                    {'error': '提醒状态不允许确认'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            reminder.status = 'acknowledged'
            reminder.acknowledged_at = timezone.now()
            reminder.acknowledged_by = request.user
            reminder.save()

            return Response({'message': '提醒确认成功'})

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def snooze(self, request, pk=None):
        """延迟提醒"""
        try:
            reminder = self.get_object()
            snooze_minutes = request.data.get('snooze_minutes', 30)

            if request.user not in reminder.recipients.all():
                return Response(
                    {'error': '只能延迟发送给自己的提醒'},
                    status=status.HTTP_403_FORBIDDEN
                )

            if reminder.status not in ['triggered', 'sent']:
                return Response(
                    {'error': '提醒状态不允许延迟'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            reminder.status = 'snoozed'
            reminder.snooze_until = timezone.now() + timedelta(minutes=snooze_minutes)
            reminder.snooze_count += 1
            reminder.save()

            return Response({
                'message': '提醒延迟成功',
                'snooze_until': reminder.snooze_until
            })

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=False, methods=['post'])
    def create_reminder(self, request):
        """创建智能提醒"""
        try:
            serializer = ReminderCreationSerializer(data=request.data)

            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            data = serializer.validated_data

            # 创建提醒
            reminder = SmartReminder.objects.create(
                reminder_id=f"RMD_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}",
                title=data['title'],
                reminder_type=data['reminder_type'],
                trigger_type=data['trigger_type'],
                message=data['message'],
                trigger_time=data.get('trigger_time'),
                is_urgent=data.get('is_urgent', False),
                created_by=request.user
            )

            # 设置接收人
            reminder.recipients.set(data['recipient_ids'])

            return Response({
                'message': '智能提醒创建成功',
                'reminder_id': reminder.reminder_id,
                'reminder': SmartReminderSerializer(reminder).data
            })


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
# 其他ViewSet的简化版本
class WorkflowAnalyticsViewSet(viewsets.ReadOnlyModelViewSet):
    """工作流分析ViewSet"""
    queryset = WorkflowAnalytics.objects.all()
    serializer_class = WorkflowAnalyticsSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        try:
            queryset = super().get_queryset()

            # 按指标类型过滤
            metric_type = self.request.query_params.get('metric_type')
            if metric_type:
                queryset = queryset.filter(metric_type=metric_type)

            # 按工作流模板过滤
            template_id = self.request.query_params.get('template')
            if template_id:
                queryset = queryset.filter(workflow_template_id=template_id)

            return queryset.order_by('-generated_at')


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class AutomationRuleViewSet(viewsets.ModelViewSet):
    """自动化规则ViewSet"""
    queryset = AutomationRule.objects.all()
    serializer_class = AutomationRuleSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        try:
            queryset = super().get_queryset()

            # 按规则类型过滤
            rule_type = self.request.query_params.get('rule_type')
            if rule_type:
                queryset = queryset.filter(rule_type=rule_type)

            # 按状态过滤
            status_filter = self.request.query_params.get('status')
            if status_filter:
                queryset = queryset.filter(status=status_filter)

            # 只显示启用的
            if self.request.query_params.get('active_only') == 'true':
                queryset = queryset.filter(status='active')

            return queryset.order_by('-created_at')

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def perform_create(self, serializer):
        try:
            rule_id = f"RULE_{timezone.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"
            serializer.save(created_by=self.request.user, rule_id=rule_id)


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class WorkflowConfigViewSet(viewsets.ModelViewSet):
    """工作流配置ViewSet"""
    queryset = WorkflowConfig.objects.all()
    serializer_class = WorkflowConfigSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        try:
            queryset = super().get_queryset()

            # 按配置类型过滤
            config_type = self.request.query_params.get('config_type')
            if config_type:
                queryset = queryset.filter(config_type=config_type)

            # 只显示启用的
            if self.request.query_params.get('active_only') == 'true':
                queryset = queryset.filter(is_active=True)

            return queryset.order_by('config_type', 'config_key')

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def perform_create(self, serializer):
        try:
            serializer.save(created_by=self.request.user)


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
# 业务操作视图
class WorkflowAssistantViewSet(viewsets.ViewSet):
    """智能业务流程助手业务操作ViewSet"""
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """工作流助手仪表板"""
        try:
            # 工作流模板统计
            template_stats = WorkflowTemplate.objects.aggregate(
                total=Count('id'),
                active=Count('id', filter=Q(status='active')),
                avg_complexity=Avg('complexity_score'),
                avg_success_rate=Avg('success_rate')
            )

            # 工作流实例统计
            instance_stats = WorkflowInstance.objects.aggregate(
                total=Count('id'),
                running=Count('id', filter=Q(status='running')),
                completed=Count('id', filter=Q(status='completed')),
                failed=Count('id', filter=Q(status='failed'))
            )

            # 任务分配统计
            task_stats = TaskAssignment.objects.aggregate(
                total=Count('id'),
                pending=Count('id', filter=Q(status='pending')),
                in_progress=Count('id', filter=Q(status='in_progress')),
                completed=Count('id', filter=Q(status='completed'))
            )

            # 流程优化统计
            optimization_stats = ProcessOptimization.objects.aggregate(
                total=Count('id'),
                proposed=Count('id', filter=Q(status='proposed')),
                implementing=Count('id', filter=Q(status='implementing')),
                completed=Count('id', filter=Q(status='completed'))
            )

            # 智能提醒统计
            reminder_stats = SmartReminder.objects.aggregate(
                total=Count('id'),
                active=Count('id', filter=Q(status='active')),
                triggered=Count('id', filter=Q(status='triggered')),
                acknowledged=Count('id', filter=Q(status='acknowledged'))
            )

            # 最近7天的工作流趋势
            seven_days_ago = timezone.now() - timedelta(days=7)
            daily_workflows = []
            for i in range(7):
                date = (timezone.now() - timedelta(days=i)).date()
                day_instances = WorkflowInstance.objects.filter(created_at__date=date)

                daily_workflows.insert(0, {
                    'date': date.strftime('%Y-%m-%d'),
                    'total': day_instances.count(),
                    'completed': day_instances.filter(status='completed').count(),
                    'failed': day_instances.filter(status='failed').count()
                })

            # 热门工作流模板
            popular_templates = WorkflowTemplate.objects.filter(
                status='active'
            ).order_by('-usage_count')[:5]

            template_popularity = [
                {
                    'name': template.name,
                    'type': template.get_workflow_type_display(),
                    'usage_count': template.usage_count,
                    'success_rate': template.success_rate,
                    'complexity_score': template.complexity_score
                }
                for template in popular_templates
            ]

            # 我的任务统计
            my_tasks = TaskAssignment.objects.filter(assigned_to=request.user)
            my_task_stats = {
                'total': my_tasks.count(),
                'pending': my_tasks.filter(status='pending').count(),
                'in_progress': my_tasks.filter(status='in_progress').count(),
                'completed_today': my_tasks.filter(
                    status='completed',
                    completed_at__date=timezone.now().date()
                ).count()
            }

            return Response({
                'template_stats': template_stats,
                'instance_stats': instance_stats,
                'task_stats': task_stats,
                'optimization_stats': optimization_stats,
                'reminder_stats': reminder_stats,
                'daily_workflows': daily_workflows,
                'template_popularity': template_popularity,
                'my_task_stats': my_task_stats
            })

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=False, methods=['post'])
    def smart_assignment(self, request):
        """智能任务分配"""
        try:
            task_requirements = request.data.get('task_requirements', {})
            available_users = request.data.get('available_users', [])

            # 这里可以实现智能分配算法
            # 基于用户技能、工作负载、历史表现等因素

            # 模拟智能分配结果
            if available_users:
                recommended_user = random.choice(available_users)
                confidence_score = random.uniform(0.7, 0.95)

                return Response({
                    'recommended_user': recommended_user,
                    'confidence_score': confidence_score,
                    'reasoning': [
                        '用户技能匹配度高',
                        '当前工作负载适中',
                        '历史任务完成质量优秀'
                    ]
                })

            return Response({'error': '没有可用的用户'}, status=status.HTTP_400_BAD_REQUEST)

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=False, methods=['post'])
    def process_analysis(self, request):
        """流程分析"""
        try:
            template_id = request.data.get('template_id')
            analysis_type = request.data.get('analysis_type', 'performance')

            if not template_id:
                return Response(
                    {'error': '请提供工作流模板ID'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            template = get_object_or_404(WorkflowTemplate, id=template_id)
            instances = WorkflowInstance.objects.filter(template=template)

            # 生成分析报告
            analysis_result = self._generate_process_analysis(template, instances, analysis_type)

            return Response(analysis_result)

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _generate_process_analysis(self, template, instances, analysis_type):
        """生成流程分析报告"""
        try:
            completed_instances = instances.filter(status='completed')

            if analysis_type == 'performance':
                return {
                    'analysis_type': 'performance',
                    'template_name': template.name,
                    'total_instances': instances.count(),
                    'completed_instances': completed_instances.count(),
                    'completion_rate': completed_instances.count() / instances.count() * 100 if instances.count() > 0 else 0,
                    'average_duration': self._calculate_average_duration(completed_instances),
                    'bottlenecks': self._identify_bottlenecks(template),
                    'recommendations': self._generate_recommendations(template, instances)
                }
            elif analysis_type == 'efficiency':
                return {
                    'analysis_type': 'efficiency',
                    'template_name': template.name,
                    'throughput': self._calculate_throughput(instances),
                    'cycle_time': self._calculate_cycle_time(completed_instances),
                    'wait_time': self._calculate_wait_time(completed_instances),
                    'efficiency_score': random.uniform(0.7, 0.95),
                    'improvement_areas': [
                        '减少步骤间等待时间',
                        '优化审批流程',
                        '增加自动化程度'
                    ]
                }
            else:
                return {
                    'analysis_type': analysis_type,
                    'message': '分析类型暂不支持'
                }

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _calculate_average_duration(self, instances):
        """计算平均时长"""
        try:
            durations = [
                instance.execution_time for instance in instances
                if instance.execution_time
            ]
            return sum(durations) / len(durations) / 3600 if durations else 0  # 转换为小时

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _identify_bottlenecks(self, template):
        """识别瓶颈"""
        try:
            # 模拟瓶颈识别
            return [
                {
                    'step_name': '审批环节',
                    'average_duration': random.uniform(2, 8),
                    'bottleneck_score': random.uniform(0.6, 0.9)
                },
                {
                    'step_name': '数据录入',
                    'average_duration': random.uniform(1, 4),
                    'bottleneck_score': random.uniform(0.5, 0.8)
                }
            ]

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _generate_recommendations(self, template, instances):
        """生成改进建议"""
        try:
            return [
                '建议增加并行处理步骤',
                '优化审批流程，减少等待时间',
                '引入自动化工具提高效率',
                '加强人员培训，提高操作熟练度'
            ]

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _calculate_throughput(self, instances):
        """计算吞吐量"""
        try:
            # 最近30天的吞吐量
            thirty_days_ago = timezone.now() - timedelta(days=30)
            recent_instances = instances.filter(created_at__gte=thirty_days_ago)
            return recent_instances.count() / 30  # 每天的实例数

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _calculate_cycle_time(self, instances):
        """计算周期时间"""
        try:
            durations = [
                instance.execution_time for instance in instances
                if instance.execution_time
            ]
            return sum(durations) / len(durations) / 3600 if durations else 0  # 转换为小时

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def _calculate_wait_time(self, instances):
        """计算等待时间"""
        try:
            # 模拟等待时间计算
            return random.uniform(0.5, 3.0)  # 小时

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
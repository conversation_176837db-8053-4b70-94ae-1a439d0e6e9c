from django.db import models
from django.utils import timezone
from django.contrib.auth import get_user_model
from decimal import Decimal
from datetime import datetime, timedelta

User = get_user_model()


class CustomerAnalytics(models.Model):
    """客户分析数据"""
    PERIOD_TYPE_CHOICES = [
        ('daily', '日报'),
        ('weekly', '周报'),
        ('monthly', '月报'),
        ('quarterly', '季报'),
        ('yearly', '年报'),
    ]

    # 时间维度
    period_type = models.CharField('统计周期', max_length=20, choices=PERIOD_TYPE_CHOICES)
    period_start = models.DateField('统计开始日期')
    period_end = models.DateField('统计结束日期')

    # 客户获取数据
    new_customers = models.PositiveIntegerField('新增客户数', default=0)
    total_customers = models.PositiveIntegerField('总客户数', default=0)
    active_customers = models.PositiveIntegerField('活跃客户数', default=0)

    # 客户留存数据
    retention_rate = models.DecimalField('留存率', max_digits=5, decimal_places=2, default=Decimal('0.00'))
    churn_rate = models.DecimalField('流失率', max_digits=5, decimal_places=2, default=Decimal('0.00'))

    # 客户价值数据
    avg_customer_value = models.DecimalField('平均客户价值', max_digits=10, decimal_places=2, default=Decimal('0.00'))
    customer_lifetime_value = models.DecimalField('客户生命周期价值', max_digits=12, decimal_places=2, default=Decimal('0.00'))

    # 客户获取成本
    acquisition_cost = models.DecimalField('客户获取成本', max_digits=10, decimal_places=2, default=Decimal('0.00'))
    marketing_spend = models.DecimalField('营销支出', max_digits=12, decimal_places=2, default=Decimal('0.00'))

    # 客户分布
    individual_customers = models.PositiveIntegerField('个体户客户', default=0)
    small_scale_customers = models.PositiveIntegerField('小规模客户', default=0)
    general_customers = models.PositiveIntegerField('一般纳税人客户', default=0)

    # 地域分布（JSON格式存储）
    regional_distribution = models.JSONField('地域分布', default=dict, blank=True)

    # 创建信息
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'analytics_customer'
        verbose_name = '客户分析'
        verbose_name_plural = '客户分析数据'
        unique_together = ['period_type', 'period_start', 'period_end']
        ordering = ['-period_start']

    def __str__(self):
        return f"{self.get_period_type_display()} - {self.period_start} 至 {self.period_end}"


class ServiceEfficiencyAnalytics(models.Model):
    """服务效率分析数据"""
    PERIOD_TYPE_CHOICES = [
        ('daily', '日报'),
        ('weekly', '周报'),
        ('monthly', '月报'),
        ('quarterly', '季报'),
        ('yearly', '年报'),
    ]

    # 时间维度
    period_type = models.CharField('统计周期', max_length=20, choices=PERIOD_TYPE_CHOICES)
    period_start = models.DateField('统计开始日期')
    period_end = models.DateField('统计结束日期')

    # 处理效率数据
    total_records = models.PositiveIntegerField('总处理记录数', default=0)
    completed_records = models.PositiveIntegerField('已完成记录数', default=0)
    pending_records = models.PositiveIntegerField('待处理记录数', default=0)
    rejected_records = models.PositiveIntegerField('被拒绝记录数', default=0)

    # 时间效率
    avg_processing_time = models.DecimalField('平均处理时长(小时)', max_digits=8, decimal_places=2, default=Decimal('0.00'))
    fastest_processing_time = models.DecimalField('最快处理时长(小时)', max_digits=8, decimal_places=2, default=Decimal('0.00'))
    slowest_processing_time = models.DecimalField('最慢处理时长(小时)', max_digits=8, decimal_places=2, default=Decimal('0.00'))

    # 质量指标
    completion_rate = models.DecimalField('完成率', max_digits=5, decimal_places=2, default=Decimal('0.00'))
    rejection_rate = models.DecimalField('拒绝率', max_digits=5, decimal_places=2, default=Decimal('0.00'))
    rework_rate = models.DecimalField('返工率', max_digits=5, decimal_places=2, default=Decimal('0.00'))

    # 会计师工作量
    total_accountants = models.PositiveIntegerField('参与会计师数', default=0)
    avg_workload_per_accountant = models.DecimalField('人均工作量', max_digits=8, decimal_places=2, default=Decimal('0.00'))

    # 服务类型分布
    individual_records = models.PositiveIntegerField('个体户记录数', default=0)
    small_scale_records = models.PositiveIntegerField('小规模记录数', default=0)
    general_records = models.PositiveIntegerField('一般纳税人记录数', default=0)

    # 效率趋势（JSON格式存储每日数据）
    daily_efficiency = models.JSONField('每日效率数据', default=dict, blank=True)

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'analytics_service_efficiency'
        verbose_name = '服务效率分析'
        verbose_name_plural = '服务效率分析数据'
        unique_together = ['period_type', 'period_start', 'period_end']
        ordering = ['-period_start']

    def __str__(self):
        return f"服务效率 - {self.get_period_type_display()} - {self.period_start}"


class RevenueAnalytics(models.Model):
    """收入分析数据"""
    PERIOD_TYPE_CHOICES = [
        ('daily', '日报'),
        ('weekly', '周报'),
        ('monthly', '月报'),
        ('quarterly', '季报'),
        ('yearly', '年报'),
    ]

    # 时间维度
    period_type = models.CharField('统计周期', max_length=20, choices=PERIOD_TYPE_CHOICES)
    period_start = models.DateField('统计开始日期')
    period_end = models.DateField('统计结束日期')

    # 总收入数据
    total_revenue = models.DecimalField('总收入', max_digits=15, decimal_places=2, default=Decimal('0.00'))
    new_revenue = models.DecimalField('新增收入', max_digits=15, decimal_places=2, default=Decimal('0.00'))
    recurring_revenue = models.DecimalField('续费收入', max_digits=15, decimal_places=2, default=Decimal('0.00'))

    # 按服务类型分类收入
    individual_revenue = models.DecimalField('个体户收入', max_digits=12, decimal_places=2, default=Decimal('0.00'))
    small_scale_revenue = models.DecimalField('小规模收入', max_digits=12, decimal_places=2, default=Decimal('0.00'))
    general_revenue = models.DecimalField('一般纳税人收入', max_digits=12, decimal_places=2, default=Decimal('0.00'))

    # 订单数据
    total_orders = models.PositiveIntegerField('总订单数', default=0)
    new_orders = models.PositiveIntegerField('新订单数', default=0)
    renewal_orders = models.PositiveIntegerField('续费订单数', default=0)

    # 平均数据
    avg_order_value = models.DecimalField('平均订单价值', max_digits=10, decimal_places=2, default=Decimal('0.00'))
    avg_revenue_per_customer = models.DecimalField('人均收入', max_digits=10, decimal_places=2, default=Decimal('0.00'))

    # 增长数据
    revenue_growth_rate = models.DecimalField('收入增长率', max_digits=8, decimal_places=2, default=Decimal('0.00'))
    customer_growth_rate = models.DecimalField('客户增长率', max_digits=8, decimal_places=2, default=Decimal('0.00'))

    # 地域收入分布
    regional_revenue = models.JSONField('地域收入分布', default=dict, blank=True)

    # 收入趋势（JSON格式存储每日数据）
    daily_revenue = models.JSONField('每日收入数据', default=dict, blank=True)

    # 成本数据
    total_cost = models.DecimalField('总成本', max_digits=15, decimal_places=2, default=Decimal('0.00'))
    gross_profit = models.DecimalField('毛利润', max_digits=15, decimal_places=2, default=Decimal('0.00'))
    gross_margin = models.DecimalField('毛利率', max_digits=5, decimal_places=2, default=Decimal('0.00'))

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'analytics_revenue'
        verbose_name = '收入分析'
        verbose_name_plural = '收入分析数据'
        unique_together = ['period_type', 'period_start', 'period_end']
        ordering = ['-period_start']

    def __str__(self):
        return f"收入分析 - {self.get_period_type_display()} - {self.period_start}"


class BusinessMetrics(models.Model):
    """业务关键指标"""
    METRIC_TYPE_CHOICES = [
        ('kpi', '关键绩效指标'),
        ('financial', '财务指标'),
        ('operational', '运营指标'),
        ('customer', '客户指标'),
        ('quality', '质量指标'),
    ]

    # 指标基本信息
    name = models.CharField('指标名称', max_length=100)
    metric_type = models.CharField('指标类型', max_length=20, choices=METRIC_TYPE_CHOICES)
    description = models.TextField('指标描述', blank=True)
    unit = models.CharField('单位', max_length=20, blank=True)

    # 指标配置
    calculation_method = models.TextField('计算方法', help_text='指标的计算公式或方法')
    data_source = models.CharField('数据源', max_length=100, help_text='数据来源表或API')
    update_frequency = models.CharField('更新频率', max_length=20, choices=[
        ('realtime', '实时'),
        ('hourly', '每小时'),
        ('daily', '每日'),
        ('weekly', '每周'),
        ('monthly', '每月'),
    ], default='daily')

    # 目标值设置
    target_value = models.DecimalField('目标值', max_digits=15, decimal_places=2, blank=True, null=True)
    warning_threshold = models.DecimalField('预警阈值', max_digits=15, decimal_places=2, blank=True, null=True)
    critical_threshold = models.DecimalField('严重阈值', max_digits=15, decimal_places=2, blank=True, null=True)

    # 显示配置
    is_key_metric = models.BooleanField('关键指标', default=False, help_text='是否在仪表板显示')
    display_order = models.PositiveIntegerField('显示顺序', default=0)
    chart_type = models.CharField('图表类型', max_length=20, choices=[
        ('line', '折线图'),
        ('bar', '柱状图'),
        ('pie', '饼图'),
        ('gauge', '仪表盘'),
        ('number', '数字显示'),
    ], default='line')

    # 状态
    is_active = models.BooleanField('是否启用', default=True)

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'analytics_business_metrics'
        verbose_name = '业务指标'
        verbose_name_plural = '业务指标管理'
        ordering = ['metric_type', 'display_order', 'name']

    def __str__(self):
        return f"{self.get_metric_type_display()} - {self.name}"


class MetricValue(models.Model):
    """指标数值记录"""
    metric = models.ForeignKey(BusinessMetrics, on_delete=models.CASCADE, verbose_name='指标')

    # 数值和时间
    value = models.DecimalField('指标值', max_digits=15, decimal_places=2)
    recorded_at = models.DateTimeField('记录时间')
    period_start = models.DateTimeField('统计开始时间', blank=True, null=True)
    period_end = models.DateTimeField('统计结束时间', blank=True, null=True)

    # 状态标识
    status = models.CharField('状态', max_length=20, choices=[
        ('normal', '正常'),
        ('warning', '预警'),
        ('critical', '严重'),
    ], default='normal')

    # 额外信息
    notes = models.TextField('备注', blank=True)
    raw_data = models.JSONField('原始数据', default=dict, blank=True)

    created_at = models.DateTimeField('创建时间', auto_now_add=True)

    class Meta:
        db_table = 'analytics_metric_values'
        verbose_name = '指标数值'
        verbose_name_plural = '指标数值记录'
        ordering = ['-recorded_at']
        indexes = [
            models.Index(fields=['metric', 'recorded_at']),
            models.Index(fields=['recorded_at']),
        ]

    def __str__(self):
        return f"{self.metric.name} - {self.value} ({self.recorded_at.strftime('%Y-%m-%d %H:%M')})"


class ReportTemplate(models.Model):
    """报表模板"""
    REPORT_TYPE_CHOICES = [
        ('dashboard', '仪表板'),
        ('summary', '汇总报表'),
        ('detail', '详细报表'),
        ('trend', '趋势报表'),
        ('comparison', '对比报表'),
    ]

    EXPORT_FORMAT_CHOICES = [
        ('pdf', 'PDF'),
        ('excel', 'Excel'),
        ('csv', 'CSV'),
        ('json', 'JSON'),
    ]

    # 基本信息
    name = models.CharField('模板名称', max_length=100)
    report_type = models.CharField('报表类型', max_length=20, choices=REPORT_TYPE_CHOICES)
    description = models.TextField('模板描述', blank=True)

    # 配置信息
    metrics = models.ManyToManyField(BusinessMetrics, verbose_name='包含指标', blank=True)
    layout_config = models.JSONField('布局配置', default=dict, help_text='报表布局和样式配置')
    filter_config = models.JSONField('筛选配置', default=dict, help_text='默认筛选条件')

    # 生成设置
    auto_generate = models.BooleanField('自动生成', default=False)
    generate_frequency = models.CharField('生成频率', max_length=20, choices=[
        ('daily', '每日'),
        ('weekly', '每周'),
        ('monthly', '每月'),
        ('quarterly', '每季度'),
    ], blank=True)

    # 导出设置
    export_formats = models.CharField('导出格式', max_length=100, default='pdf,excel',
                                    help_text='支持的导出格式，多个用逗号分隔')

    # 权限设置
    is_public = models.BooleanField('公开报表', default=False)
    allowed_users = models.ManyToManyField(User, verbose_name='授权用户', blank=True, related_name='accessible_reports')

    # 状态
    is_active = models.BooleanField('是否启用', default=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='创建者', related_name='created_reports')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'analytics_report_templates'
        verbose_name = '报表模板'
        verbose_name_plural = '报表模板管理'
        ordering = ['report_type', 'name']

    def __str__(self):
        return f"{self.get_report_type_display()} - {self.name}"

    @property
    def export_format_list(self):
        """导出格式列表"""
        if self.export_formats:
            return [fmt.strip() for fmt in self.export_formats.split(',') if fmt.strip()]
        return []

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db.models import Avg, Sum, Count
from .models import (
    CustomerAnalytics, ServiceEfficiencyAnalytics, RevenueAnalytics,
    BusinessMetrics, MetricValue, ReportTemplate
)


@admin.register(CustomerAnalytics)
class CustomerAnalyticsAdmin(admin.ModelAdmin):
    """客户分析管理"""
    list_display = [
        'period_display', 'period_type', 'new_customers', 'total_customers',
        'active_customers', 'retention_rate_display', 'avg_customer_value_display'
    ]
    list_filter = ['period_type', 'period_start', 'created_at']
    search_fields = ['period_start', 'period_end']
    readonly_fields = ['created_at', 'updated_at']
    date_hierarchy = 'period_start'

    fieldsets = (
        ('统计周期', {
            'fields': ('period_type', 'period_start', 'period_end')
        }),
        ('客户数据', {
            'fields': ('new_customers', 'total_customers', 'active_customers')
        }),
        ('留存数据', {
            'fields': ('retention_rate', 'churn_rate')
        }),
        ('价值数据', {
            'fields': ('avg_customer_value', 'customer_lifetime_value', 'acquisition_cost', 'marketing_spend')
        }),
        ('客户分布', {
            'fields': ('individual_customers', 'small_scale_customers', 'general_customers')
        }),
        ('地域分布', {
            'fields': ('regional_distribution',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def period_display(self, obj):
        """周期显示"""
        return "{} 至 {}".format(obj.period_start, obj.period_end)
    period_display.short_description = '统计周期'

    def retention_rate_display(self, obj):
        """留存率显示"""
        rate = float(obj.retention_rate)
        if rate >= 80:
            color = '#52c41a'
        elif rate >= 60:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}%</span>',
            color, "{:.1f}".format(rate)
        )
    retention_rate_display.short_description = '留存率'

    def avg_customer_value_display(self, obj):
        """平均客户价值显示"""
        return "¥{:,.2f}".format(obj.avg_customer_value)
    avg_customer_value_display.short_description = '平均客户价值'


@admin.register(ServiceEfficiencyAnalytics)
class ServiceEfficiencyAnalyticsAdmin(admin.ModelAdmin):
    """服务效率分析管理"""
    list_display = [
        'period_display', 'period_type', 'total_records', 'completed_records',
        'completion_rate_display', 'avg_processing_time_display', 'total_accountants'
    ]
    list_filter = ['period_type', 'period_start', 'created_at']
    readonly_fields = ['created_at', 'updated_at']
    date_hierarchy = 'period_start'

    fieldsets = (
        ('统计周期', {
            'fields': ('period_type', 'period_start', 'period_end')
        }),
        ('处理数据', {
            'fields': ('total_records', 'completed_records', 'pending_records', 'rejected_records')
        }),
        ('时间效率', {
            'fields': ('avg_processing_time', 'fastest_processing_time', 'slowest_processing_time')
        }),
        ('质量指标', {
            'fields': ('completion_rate', 'rejection_rate', 'rework_rate')
        }),
        ('工作量', {
            'fields': ('total_accountants', 'avg_workload_per_accountant')
        }),
        ('服务分布', {
            'fields': ('individual_records', 'small_scale_records', 'general_records')
        }),
        ('效率趋势', {
            'fields': ('daily_efficiency',),
            'classes': ('collapse',)
        })
    )

    def period_display(self, obj):
        """周期显示"""
        return "{} 至 {}".format(obj.period_start, obj.period_end)
    period_display.short_description = '统计周期'

    def completion_rate_display(self, obj):
        """完成率显示"""
        rate = float(obj.completion_rate)
        if rate >= 95:
            color = '#52c41a'
        elif rate >= 85:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}%</span>',
            color, "{:.1f}".format(rate)
        )
    completion_rate_display.short_description = '完成率'

    def avg_processing_time_display(self, obj):
        """平均处理时长显示"""
        hours = float(obj.avg_processing_time)
        if hours <= 24:
            color = '#52c41a'
        elif hours <= 48:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {};">{}小时</span>',
            color, "{:.1f}".format(hours)
        )
    avg_processing_time_display.short_description = '平均处理时长'


@admin.register(RevenueAnalytics)
class RevenueAnalyticsAdmin(admin.ModelAdmin):
    """收入分析管理"""
    list_display = [
        'period_display', 'period_type', 'total_revenue_display', 'total_orders',
        'avg_order_value_display', 'revenue_growth_rate_display', 'gross_margin_display'
    ]
    list_filter = ['period_type', 'period_start', 'created_at']
    readonly_fields = ['created_at', 'updated_at']
    date_hierarchy = 'period_start'

    fieldsets = (
        ('统计周期', {
            'fields': ('period_type', 'period_start', 'period_end')
        }),
        ('收入数据', {
            'fields': ('total_revenue', 'new_revenue', 'recurring_revenue')
        }),
        ('服务收入', {
            'fields': ('individual_revenue', 'small_scale_revenue', 'general_revenue')
        }),
        ('订单数据', {
            'fields': ('total_orders', 'new_orders', 'renewal_orders')
        }),
        ('平均数据', {
            'fields': ('avg_order_value', 'avg_revenue_per_customer')
        }),
        ('增长数据', {
            'fields': ('revenue_growth_rate', 'customer_growth_rate')
        }),
        ('成本利润', {
            'fields': ('total_cost', 'gross_profit', 'gross_margin')
        }),
        ('地域分布', {
            'fields': ('regional_revenue',),
            'classes': ('collapse',)
        }),
        ('收入趋势', {
            'fields': ('daily_revenue',),
            'classes': ('collapse',)
        })
    )

    def period_display(self, obj):
        """周期显示"""
        return "{} 至 {}".format(obj.period_start, obj.period_end)
    period_display.short_description = '统计周期'

    def total_revenue_display(self, obj):
        """总收入显示"""
        return "¥{:,.2f}".format(obj.total_revenue)
    total_revenue_display.short_description = '总收入'

    def avg_order_value_display(self, obj):
        """平均订单价值显示"""
        return "¥{:,.2f}".format(obj.avg_order_value)
    avg_order_value_display.short_description = '平均订单价值'

    def revenue_growth_rate_display(self, obj):
        """收入增长率显示"""
        rate = float(obj.revenue_growth_rate)
        if rate >= 20:
            color = '#52c41a'
        elif rate >= 0:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}%</span>',
            color, "{:+.1f}".format(rate)
        )
    revenue_growth_rate_display.short_description = '收入增长率'

    def gross_margin_display(self, obj):
        """毛利率显示"""
        margin = float(obj.gross_margin)
        if margin >= 50:
            color = '#52c41a'
        elif margin >= 30:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}%</span>',
            color, "{:.1f}".format(margin)
        )
    gross_margin_display.short_description = '毛利率'


@admin.register(BusinessMetrics)
class BusinessMetricsAdmin(admin.ModelAdmin):
    """业务指标管理"""
    list_display = [
        'name', 'metric_type', 'unit', 'target_value', 'current_value_display',
        'is_key_metric', 'display_order', 'chart_type', 'update_frequency', 'is_active'
    ]
    list_filter = ['metric_type', 'is_key_metric', 'chart_type', 'update_frequency', 'is_active']
    search_fields = ['name', 'description']
    list_editable = ['is_key_metric', 'display_order', 'is_active']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'metric_type', 'description', 'unit')
        }),
        ('计算配置', {
            'fields': ('calculation_method', 'data_source', 'update_frequency')
        }),
        ('目标设置', {
            'fields': ('target_value', 'warning_threshold', 'critical_threshold')
        }),
        ('显示配置', {
            'fields': ('is_key_metric', 'display_order', 'chart_type')
        }),
        ('状态设置', {
            'fields': ('is_active',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def current_value_display(self, obj):
        """当前值显示"""
        try:
            latest_value = obj.metricvalue_set.latest('recorded_at')
            value = float(latest_value.value)

            # 根据阈值设置颜色
            color = '#1890ff'  # 默认蓝色
            if obj.critical_threshold and value <= float(obj.critical_threshold):
                color = '#f5222d'  # 红色
            elif obj.warning_threshold and value <= float(obj.warning_threshold):
                color = '#faad14'  # 橙色
            elif obj.target_value and value >= float(obj.target_value):
                color = '#52c41a'  # 绿色

            return format_html(
                '<span style="color: {}; font-weight: bold;">{} {}</span>',
                color, "{:.2f}".format(value), obj.unit or ''
            )
        except:
            return '-'
    current_value_display.short_description = '当前值'


@admin.register(MetricValue)
class MetricValueAdmin(admin.ModelAdmin):
    """指标数值管理"""
    list_display = [
        'metric', 'value_display', 'status_display', 'recorded_at', 'period_display'
    ]
    list_filter = ['metric', 'status', 'recorded_at']
    search_fields = ['metric__name', 'notes']
    readonly_fields = ['created_at']
    date_hierarchy = 'recorded_at'

    fieldsets = (
        ('基本信息', {
            'fields': ('metric', 'value', 'recorded_at', 'status')
        }),
        ('统计周期', {
            'fields': ('period_start', 'period_end')
        }),
        ('额外信息', {
            'fields': ('notes', 'raw_data'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        })
    )

    def value_display(self, obj):
        """数值显示"""
        unit = obj.metric.unit or ''
        return "{:.2f} {}".format(obj.value, unit)
    value_display.short_description = '指标值'

    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'normal': '#52c41a',
            'warning': '#faad14',
            'critical': '#f5222d',
        }
        status_texts = {
            'normal': '正常',
            'warning': '预警',
            'critical': '严重',
        }

        return format_html(
            '<span style="color: {};">● {}</span>',
            status_colors.get(obj.status, '#d9d9d9'),
            status_texts.get(obj.status, obj.status)
        )
    status_display.short_description = '状态'

    def period_display(self, obj):
        """周期显示"""
        if obj.period_start and obj.period_end:
            return "{} 至 {}".format(
                obj.period_start.strftime('%m-%d'),
                obj.period_end.strftime('%m-%d')
            )
        return '-'
    period_display.short_description = '统计周期'


@admin.register(ReportTemplate)
class ReportTemplateAdmin(admin.ModelAdmin):
    """报表模板管理"""
    list_display = [
        'name', 'report_type', 'metrics_count', 'auto_generate',
        'generate_frequency', 'export_formats_display', 'is_active'
    ]
    list_filter = ['report_type', 'auto_generate', 'generate_frequency', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    filter_horizontal = ['metrics', 'allowed_users']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'report_type', 'description')
        }),
        ('配置信息', {
            'fields': ('metrics', 'layout_config', 'filter_config')
        }),
        ('生成设置', {
            'fields': ('auto_generate', 'generate_frequency')
        }),
        ('导出设置', {
            'fields': ('export_formats',)
        }),
        ('权限设置', {
            'fields': ('is_public', 'allowed_users')
        }),
        ('状态设置', {
            'fields': ('is_active',)
        }),
        ('时间信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def metrics_count(self, obj):
        """指标数量"""
        return obj.metrics.count()
    metrics_count.short_description = '指标数量'

    def export_formats_display(self, obj):
        """导出格式显示"""
        formats = obj.export_format_list
        if formats:
            return ', '.join(formats)
        return '-'
    export_formats_display.short_description = '导出格式'

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

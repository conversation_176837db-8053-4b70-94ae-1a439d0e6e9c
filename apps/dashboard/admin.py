from django.contrib import admin
from django.urls import path
from django.shortcuts import render
from django.http import JsonResponse
from django.db.models import Count, Sum
from django.utils import timezone
from datetime import timedelta

# 自定义管理员站点
class CustomAdminSite(admin.AdminSite):
    site_header = '🏢 代理记账管理系统'
    site_title = '代理记账管理系统'
    index_title = '📊 数据统计中心'
    
    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('dashboard/data/', self.dashboard_data_view, name='dashboard_data'),
        ]
        return custom_urls + urls
    
    def dashboard_data_view(self, request):
        """仪表板数据API"""
        from apps.users.models import User
        from apps.companies.models import Company
        from apps.services.models import Order, ServicePackage
        from apps.payments.models import Payment
        
        # 基础统计
        total_users = User.objects.count()
        total_companies = Company.objects.count()
        total_orders = Order.objects.count()
        
        # 收入统计
        total_revenue = Payment.objects.filter(
            status='paid'
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        # 本月数据
        current_month = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        monthly_users = User.objects.filter(date_joined__gte=current_month).count()
        monthly_orders = Order.objects.filter(created_at__gte=current_month).count()
        monthly_revenue = Payment.objects.filter(
            status='paid',
            paid_at__gte=current_month
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        # 服务状态统计
        active_services = Order.objects.filter(status='active').count()
        pending_orders = Order.objects.filter(status='pending').count()
        
        # 即将到期的服务
        next_month = timezone.now() + timedelta(days=30)
        expiring_services = Order.objects.filter(
            service_end_date__lte=next_month,
            status='active'
        ).count()
        
        # 逾期支付
        overdue_payments = Payment.objects.filter(
            status='pending',
            created_at__lt=timezone.now() - timedelta(days=7)
        ).count()
        
        # 趋势数据 - 最近7个月
        trend_data = []
        revenue_data = []
        user_growth_data = []
        
        for i in range(6, -1, -1):
            month_start = (timezone.now().replace(day=1) - timedelta(days=i*30)).replace(day=1)
            month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)
            
            month_orders = Order.objects.filter(
                created_at__gte=month_start,
                created_at__lte=month_end
            ).count()
            
            month_revenue = Payment.objects.filter(
                status='paid',
                paid_at__gte=month_start,
                paid_at__lte=month_end
            ).aggregate(total=Sum('amount'))['total'] or 0
            
            month_users = User.objects.filter(
                date_joined__gte=month_start,
                date_joined__lte=month_end
            ).count()
            
            trend_data.append(month_orders)
            revenue_data.append(float(month_revenue))
            user_growth_data.append(month_users)
        
        # 服务类型分布
        service_distribution = []
        packages = ServicePackage.objects.annotate(
            order_count=Count('order')
        ).order_by('-order_count')[:5]
        
        for package in packages:
            service_distribution.append({
                'name': package.name,
                'value': package.order_count
            })
        
        # 热门服务排行
        popular_services = []
        for i, package in enumerate(packages[:5], 1):
            # 计算增长率（模拟数据）
            growth_rate = round((package.order_count * 0.1 + i * 2), 1)
            popular_services.append({
                'rank': i,
                'name': package.name,
                'orders': package.order_count,
                'growth': f"+{growth_rate}%"
            })
        
        # 今日数据
        today = timezone.now().date()
        today_users = User.objects.filter(date_joined__date=today).count()
        today_orders = Order.objects.filter(created_at__date=today).count()
        today_revenue = Payment.objects.filter(
            status='paid',
            paid_at__date=today
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        # 待处理工单
        pending_tickets = 5  # 模拟数据
        
        return JsonResponse({
            'basic_stats': {
                'total_users': total_users,
                'total_companies': total_companies,
                'total_orders': total_orders,
                'total_revenue': float(total_revenue),
                'monthly_users': monthly_users,
                'monthly_orders': monthly_orders,
                'monthly_revenue': float(monthly_revenue)
            },
            'service_stats': {
                'active_services': active_services,
                'pending_orders': pending_orders,
                'expiring_services': expiring_services,
                'overdue_payments': overdue_payments
            },
            'trend_data': {
                'orders': trend_data,
                'revenue': revenue_data,
                'users': user_growth_data
            },
            'service_distribution': service_distribution,
            'popular_services': popular_services,
            'today_stats': {
                'users': today_users,
                'orders': today_orders,
                'revenue': float(today_revenue),
                'tickets': pending_tickets
            }
        })

# 创建自定义管理员站点实例
custom_admin_site = CustomAdminSite(name='custom_admin')

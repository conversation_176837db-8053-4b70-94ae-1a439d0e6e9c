from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q, Count, Avg, Sum
from django.shortcuts import get_object_or_404
from datetime import timedelta
from .models import (
    OrderStatusFlow, OrderAssignment, OrderProgress, OrderQuality,
    OrderTimeline, ServiceTeam, TeamMembership, OrderMetrics
)
from .serializers import (
    OrderStatusFlowSerializer, OrderAssignmentSerializer, OrderProgressSerializer,
    OrderQualitySerializer, OrderTimelineSerializer, ServiceTeamSerializer,
    TeamMembershipSerializer, OrderMetricsSerializer,
    OrderAssignmentListSerializer, OrderProgressListSerializer, ServiceTeamListSerializer
)
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class OrderStatusFlowViewSet(viewsets.ModelViewSet):
    """订单状态流程ViewSet"""
    queryset = OrderStatusFlow.objects.all()
    serializer_class = OrderStatusFlowSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        service_type = self.request.query_params.get('service_type')
        if service_type:
            queryset = queryset.filter(service_type=service_type)
        return queryset.filter(is_active=True)


class OrderAssignmentViewSet(viewsets.ModelViewSet):
    """订单分配ViewSet"""
    queryset = OrderAssignment.objects.all()
    serializer_class = OrderAssignmentSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user
        
        # 根据用户权限过滤
        if not user.is_staff:
            # 普通用户只能看到自己的分配
            queryset = queryset.filter(assigned_to=user)
        else:
            # 管理员可以看到所有分配
            assigned_to = self.request.query_params.get('assigned_to')
            if assigned_to:
                queryset = queryset.filter(assigned_to_id=assigned_to)
            
            assignment_type = self.request.query_params.get('assignment_type')
            if assignment_type:
                queryset = queryset.filter(assignment_type=assignment_type)
        
        return queryset.order_by('-assigned_at')
    
    def get_serializer_class(self):
        if self.action == 'list':
            return OrderAssignmentListSerializer
        return OrderAssignmentSerializer
    
    @action(detail=False, methods=['post'])
    def auto_assign(self, request):
        """自动分配订单"""
        order_id = request.data.get('order_id')
        if not order_id:
            return Response(
                {'error': '订单ID不能为空'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 这里可以实现自动分配逻辑
        # 例如：负载均衡、技能匹配等
        
        return Response({'message': '自动分配成功'})
    
    @action(detail=False, methods=['get'])
    def workload_stats(self, request):
        """工作负载统计"""
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        # 获取所有员工的工作负载
        staff_users = User.objects.filter(is_staff=True)
        workload_data = []
        
        for user in staff_users:
            assignments = OrderAssignment.objects.filter(assigned_to=user)
            total_estimated = assignments.aggregate(
                total=Sum('estimated_hours')
            )['total'] or 0
            total_actual = assignments.aggregate(
                total=Sum('actual_hours')
            )['total'] or 0
            
            workload_data.append({
                'user_id': user.id,
                'user_name': user.get_full_name(),
                'total_assignments': assignments.count(),
                'total_estimated_hours': float(total_estimated),
                'total_actual_hours': float(total_actual),
            })
        
        return Response(workload_data)


class OrderProgressViewSet(viewsets.ModelViewSet):
    """订单进度ViewSet"""
    queryset = OrderProgress.objects.all()
    serializer_class = OrderProgressSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        order_id = self.request.query_params.get('order')
        if order_id:
            queryset = queryset.filter(order_id=order_id)
        
        progress_type = self.request.query_params.get('progress_type')
        if progress_type:
            queryset = queryset.filter(progress_type=progress_type)
        
        is_delayed = self.request.query_params.get('is_delayed')
        if is_delayed == 'true':
            queryset = queryset.filter(is_delayed=True)
        
        return queryset.order_by('order', 'created_at')
    
    def get_serializer_class(self):
        if self.action == 'list':
            return OrderProgressListSerializer
        return OrderProgressSerializer
    
    @action(detail=True, methods=['post'])
    def update_progress(self, request, pk=None):
        """更新进度"""
        progress = self.get_object()
        percentage = request.data.get('percentage')
        
        if percentage is not None:
            progress.progress_percentage = min(100, max(0, int(percentage)))
            if progress.progress_percentage == 100:
                progress.is_completed = True
                progress.actual_end = timezone.now()
            progress.save()
            
            return Response({'message': '进度更新成功'})
        
        return Response(
            {'error': '进度百分比不能为空'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    @action(detail=False, methods=['get'])
    def delayed_items(self, request):
        """获取延期项目"""
        delayed_progress = OrderProgress.objects.filter(is_delayed=True)
        serializer = self.get_serializer(delayed_progress, many=True)
        return Response(serializer.data)


class OrderQualityViewSet(viewsets.ModelViewSet):
    """订单质量ViewSet"""
    queryset = OrderQuality.objects.all()
    serializer_class = OrderQualitySerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        quality_level = self.request.query_params.get('quality_level')
        if quality_level:
            queryset = queryset.filter(quality_level=quality_level)
        
        has_issues = self.request.query_params.get('has_issues')
        if has_issues == 'true':
            queryset = queryset.filter(has_issues=True)
        
        return queryset.order_by('-reviewed_at', '-created_at')
    
    @action(detail=False, methods=['get'])
    def quality_stats(self, request):
        """质量统计"""
        stats = OrderQuality.objects.aggregate(
            avg_quality_score=Avg('quality_score'),
            avg_customer_satisfaction=Avg('customer_satisfaction'),
            total_with_issues=Count('id', filter=Q(has_issues=True)),
            total_reviewed=Count('id', filter=Q(reviewed_at__isnull=False))
        )
        
        # 质量等级分布
        quality_distribution = OrderQuality.objects.values('quality_level').annotate(
            count=Count('id')
        ).order_by('quality_level')
        
        return Response({
            'summary': stats,
            'quality_distribution': list(quality_distribution)
        })


class OrderTimelineViewSet(viewsets.ReadOnlyModelViewSet):
    """订单时间线ViewSet"""
    queryset = OrderTimeline.objects.all()
    serializer_class = OrderTimelineSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        order_id = self.request.query_params.get('order')
        if order_id:
            queryset = queryset.filter(order_id=order_id)
        
        event_type = self.request.query_params.get('event_type')
        if event_type:
            queryset = queryset.filter(event_type=event_type)
        
        return queryset.order_by('-created_at')


class ServiceTeamViewSet(viewsets.ModelViewSet):
    """服务团队ViewSet"""
    queryset = ServiceTeam.objects.all()
    serializer_class = ServiceTeamSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        is_active = self.request.query_params.get('is_active')
        if is_active == 'true':
            queryset = queryset.filter(is_active=True)
        
        return queryset.order_by('-created_at')
    
    def get_serializer_class(self):
        if self.action == 'list':
            return ServiceTeamListSerializer
        return ServiceTeamSerializer
    
    @action(detail=True, methods=['post'])
    def add_member(self, request, pk=None):
        """添加团队成员"""
        team = self.get_object()
        user_id = request.data.get('user_id')
        role = request.data.get('role', 'junior')
        skill_level = request.data.get('skill_level', 1)
        
        if not user_id:
            return Response(
                {'error': '用户ID不能为空'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            user = User.objects.get(id=user_id)
            
            membership, created = TeamMembership.objects.get_or_create(
                team=team,
                user=user,
                defaults={
                    'role': role,
                    'skill_level': skill_level,
                    'is_active': True
                }
            )
            
            if created:
                return Response({'message': '成员添加成功'})
            else:
                return Response(
                    {'error': '用户已是团队成员'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        except User.DoesNotExist:
            return Response(
                {'error': '用户不存在'},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=True, methods=['get'])
    def workload_analysis(self, request, pk=None):
        """团队工作负载分析"""
        team = self.get_object()
        current_workload = team.get_current_workload()
        max_workload = team.max_concurrent_orders
        
        # 成员工作负载分布
        member_workloads = []
        for membership in team.teammembership_set.filter(is_active=True):
            user_assignments = OrderAssignment.objects.filter(
                assigned_to=membership.user
            ).count()
            member_workloads.append({
                'user_id': membership.user.id,
                'user_name': membership.user.get_full_name(),
                'role': membership.get_role_display(),
                'assignments': user_assignments
            })
        
        return Response({
            'team_workload': {
                'current': current_workload,
                'maximum': max_workload,
                'percentage': (current_workload / max_workload * 100) if max_workload > 0 else 0
            },
            'member_workloads': member_workloads
        })


class TeamMembershipViewSet(viewsets.ModelViewSet):
    """团队成员ViewSet"""
    queryset = TeamMembership.objects.all()
    serializer_class = TeamMembershipSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        team_id = self.request.query_params.get('team')
        if team_id:
            queryset = queryset.filter(team_id=team_id)
        
        is_active = self.request.query_params.get('is_active')
        if is_active == 'true':
            queryset = queryset.filter(is_active=True)
        
        return queryset.order_by('-joined_at')


class OrderMetricsViewSet(viewsets.ReadOnlyModelViewSet):
    """订单指标ViewSet"""
    queryset = OrderMetrics.objects.all()
    serializer_class = OrderMetricsSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')
        
        if date_from:
            queryset = queryset.filter(date__gte=date_from)
        if date_to:
            queryset = queryset.filter(date__lte=date_to)
        
        return queryset.order_by('-date')
    
    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """订单管理仪表板"""
        # 最近30天的数据
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
        
        recent_metrics = OrderMetrics.objects.filter(
            date__range=[start_date, end_date]
        ).aggregate(
            total_orders=Sum('total_orders'),
            total_completed=Sum('completed_orders'),
            total_cancelled=Sum('cancelled_orders'),
            avg_quality=Avg('avg_quality_score'),
            avg_satisfaction=Avg('customer_satisfaction_rate')
        )
        
        return Response({
            'period': {
                'start_date': start_date,
                'end_date': end_date
            },
            'summary': recent_metrics,
            'trends': 'TODO: 实现趋势分析'
        })

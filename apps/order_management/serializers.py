from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    OrderStatusFlow, OrderAssignment, OrderProgress, OrderQuality,
    OrderTimeline, ServiceTeam, TeamMembership, OrderMetrics
)

User = get_user_model()


class OrderStatusFlowSerializer(serializers.ModelSerializer):
    """订单状态流程序列化器"""
    status_count = serializers.SerializerMethodField()
    
    class Meta:
        model = OrderStatusFlow
        fields = [
            'id', 'name', 'service_type', 'status_sequence',
            'status_count', 'is_active', 'created_at', 'updated_at'
        ]
    
    def get_status_count(self, obj):
        return len(obj.status_sequence) if obj.status_sequence else 0


class OrderAssignmentSerializer(serializers.ModelSerializer):
    """订单分配序列化器"""
    order_number = serializers.CharField(source='order.order_no', read_only=True)
    order_package = serializers.CharField(source='order.package.name', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.get_full_name', read_only=True)
    assigned_by_name = serializers.CharField(source='assigned_by.get_full_name', read_only=True)
    assignment_type_display = serializers.CharField(source='get_assignment_type_display', read_only=True)
    workload_variance = serializers.SerializerMethodField()
    
    class Meta:
        model = OrderAssignment
        fields = [
            'id', 'order', 'order_number', 'order_package',
            'assigned_to', 'assigned_to_name', 'assignment_type', 'assignment_type_display',
            'assigned_by', 'assigned_by_name', 'assigned_at', 'assignment_reason',
            'estimated_hours', 'actual_hours', 'workload_variance'
        ]
    
    def get_workload_variance(self, obj):
        if obj.estimated_hours and obj.actual_hours:
            return float(obj.actual_hours - obj.estimated_hours)
        return None


class OrderProgressSerializer(serializers.ModelSerializer):
    """订单进度序列化器"""
    order_number = serializers.CharField(source='order.order_no', read_only=True)
    progress_type_display = serializers.CharField(source='get_progress_type_display', read_only=True)
    responsible_person_name = serializers.CharField(source='responsible_person.get_full_name', read_only=True)
    delay_status = serializers.SerializerMethodField()
    
    class Meta:
        model = OrderProgress
        fields = [
            'id', 'order', 'order_number', 'progress_type', 'progress_type_display',
            'title', 'description', 'progress_percentage',
            'planned_start', 'planned_end', 'actual_start', 'actual_end',
            'responsible_person', 'responsible_person_name',
            'is_completed', 'is_delayed', 'delay_status',
            'created_at', 'updated_at'
        ]
    
    def get_delay_status(self, obj):
        return obj.check_delay()


class OrderQualitySerializer(serializers.ModelSerializer):
    """订单质量序列化器"""
    order_number = serializers.CharField(source='order.order_no', read_only=True)
    quality_level_display = serializers.CharField(source='get_quality_level_display', read_only=True)
    reviewed_by_name = serializers.CharField(source='reviewed_by.get_full_name', read_only=True)
    
    class Meta:
        model = OrderQuality
        fields = [
            'id', 'order', 'order_number',
            'quality_level', 'quality_level_display', 'quality_score',
            'customer_satisfaction', 'customer_feedback',
            'internal_review_score', 'internal_review_notes',
            'has_issues', 'issue_description', 'resolution_notes',
            'reviewed_by', 'reviewed_by_name', 'reviewed_at',
            'created_at', 'updated_at'
        ]


class OrderTimelineSerializer(serializers.ModelSerializer):
    """订单时间线序列化器"""
    order_number = serializers.CharField(source='order.order_no', read_only=True)
    event_type_display = serializers.CharField(source='get_event_type_display', read_only=True)
    actor_name = serializers.CharField(source='actor.get_full_name', read_only=True)
    
    class Meta:
        model = OrderTimeline
        fields = [
            'id', 'order', 'order_number', 'event_type', 'event_type_display',
            'title', 'description', 'actor', 'actor_name',
            'metadata', 'created_at'
        ]


class TeamMembershipSerializer(serializers.ModelSerializer):
    """团队成员序列化器"""
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    role_display = serializers.CharField(source='get_role_display', read_only=True)
    
    class Meta:
        model = TeamMembership
        fields = [
            'id', 'team', 'user', 'user_name', 'role', 'role_display',
            'skill_level', 'specialties', 'joined_at', 'is_active'
        ]


class ServiceTeamSerializer(serializers.ModelSerializer):
    """服务团队序列化器"""
    team_leader_name = serializers.CharField(source='team_leader.get_full_name', read_only=True)
    member_count = serializers.SerializerMethodField()
    current_workload = serializers.SerializerMethodField()
    workload_percentage = serializers.SerializerMethodField()
    members = TeamMembershipSerializer(source='teammembership_set', many=True, read_only=True)
    
    class Meta:
        model = ServiceTeam
        fields = [
            'id', 'name', 'description', 'team_leader', 'team_leader_name',
            'service_types', 'max_concurrent_orders', 'member_count',
            'current_workload', 'workload_percentage', 'is_active',
            'created_at', 'updated_at', 'members'
        ]
    
    def get_member_count(self, obj):
        return obj.members.filter(teammembership__is_active=True).count()
    
    def get_current_workload(self, obj):
        return obj.get_current_workload()
    
    def get_workload_percentage(self, obj):
        current = obj.get_current_workload()
        max_orders = obj.max_concurrent_orders
        if max_orders > 0:
            return round((current / max_orders) * 100, 1)
        return 0


class OrderMetricsSerializer(serializers.ModelSerializer):
    """订单指标序列化器"""
    completion_rate = serializers.SerializerMethodField()
    cancellation_rate = serializers.SerializerMethodField()
    avg_processing_hours = serializers.SerializerMethodField()
    avg_response_hours = serializers.SerializerMethodField()
    
    class Meta:
        model = OrderMetrics
        fields = [
            'id', 'date', 'total_orders', 'new_orders', 'completed_orders', 'cancelled_orders',
            'completion_rate', 'cancellation_rate',
            'avg_processing_time', 'avg_processing_hours',
            'avg_response_time', 'avg_response_hours',
            'avg_quality_score', 'customer_satisfaction_rate',
            'on_time_delivery_rate', 'first_time_resolution_rate',
            'created_at', 'updated_at'
        ]
    
    def get_completion_rate(self, obj):
        if obj.total_orders > 0:
            return round((obj.completed_orders / obj.total_orders) * 100, 2)
        return 0
    
    def get_cancellation_rate(self, obj):
        if obj.total_orders > 0:
            return round((obj.cancelled_orders / obj.total_orders) * 100, 2)
        return 0
    
    def get_avg_processing_hours(self, obj):
        if obj.avg_processing_time:
            return round(obj.avg_processing_time.total_seconds() / 3600, 1)
        return None
    
    def get_avg_response_hours(self, obj):
        if obj.avg_response_time:
            return round(obj.avg_response_time.total_seconds() / 3600, 1)
        return None


# 简化版序列化器用于列表显示
class OrderAssignmentListSerializer(serializers.ModelSerializer):
    """订单分配列表序列化器"""
    order_number = serializers.CharField(source='order.order_no', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.get_full_name', read_only=True)
    assignment_type_display = serializers.CharField(source='get_assignment_type_display', read_only=True)
    
    class Meta:
        model = OrderAssignment
        fields = [
            'id', 'order_number', 'assigned_to_name', 'assignment_type_display',
            'estimated_hours', 'actual_hours', 'assigned_at'
        ]


class OrderProgressListSerializer(serializers.ModelSerializer):
    """订单进度列表序列化器"""
    order_number = serializers.CharField(source='order.order_no', read_only=True)
    progress_type_display = serializers.CharField(source='get_progress_type_display', read_only=True)
    responsible_person_name = serializers.CharField(source='responsible_person.get_full_name', read_only=True)
    
    class Meta:
        model = OrderProgress
        fields = [
            'id', 'order_number', 'title', 'progress_type_display',
            'progress_percentage', 'responsible_person_name',
            'is_completed', 'is_delayed', 'planned_end'
        ]


class ServiceTeamListSerializer(serializers.ModelSerializer):
    """服务团队列表序列化器"""
    team_leader_name = serializers.CharField(source='team_leader.get_full_name', read_only=True)
    member_count = serializers.SerializerMethodField()
    current_workload = serializers.SerializerMethodField()
    
    class Meta:
        model = ServiceTeam
        fields = [
            'id', 'name', 'team_leader_name', 'member_count',
            'current_workload', 'max_concurrent_orders', 'is_active'
        ]
    
    def get_member_count(self, obj):
        return obj.members.filter(teammembership__is_active=True).count()
    
    def get_current_workload(self, obj):
        return obj.get_current_workload()

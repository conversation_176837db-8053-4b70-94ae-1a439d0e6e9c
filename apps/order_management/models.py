from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal
from apps.companies.models import Company
from apps.services.models import ServicePackage, Order as ServiceOrder

User = get_user_model()


class OrderStatusFlow(models.Model):
    """订单状态流转配置"""
    name = models.CharField('流程名称', max_length=100)
    service_type = models.CharField('服务类型', max_length=50)
    status_sequence = models.JSONField('状态序列', default=list, help_text='状态流转顺序')
    is_active = models.BooleanField('是否启用', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '订单状态流程'
        verbose_name_plural = '订单状态流程'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.service_type})"


class OrderAssignment(models.Model):
    """订单分配管理"""
    ASSIGNMENT_TYPES = [
        ('auto', '自动分配'),
        ('manual', '手动分配'),
        ('load_balance', '负载均衡'),
        ('skill_match', '技能匹配'),
    ]
    
    order = models.OneToOneField(ServiceOrder, on_delete=models.CASCADE, verbose_name='订单')
    assigned_to = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='assigned_orders', verbose_name='分配给'
    )
    assignment_type = models.CharField('分配方式', max_length=20, choices=ASSIGNMENT_TYPES)
    assigned_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='assignment_actions', verbose_name='分配人'
    )
    assigned_at = models.DateTimeField('分配时间', auto_now_add=True)
    assignment_reason = models.TextField('分配原因', blank=True)
    
    # 工作量统计
    estimated_hours = models.DecimalField('预估工时', max_digits=6, decimal_places=2, default=0)
    actual_hours = models.DecimalField('实际工时', max_digits=6, decimal_places=2, default=0)
    
    class Meta:
        verbose_name = '订单分配'
        verbose_name_plural = '订单分配'
        ordering = ['-assigned_at']
    
    def __str__(self):
        return f"订单 {self.order.order_number} -> {self.assigned_to.get_full_name() if self.assigned_to else '未分配'}"


class OrderProgress(models.Model):
    """订单进度跟踪"""
    PROGRESS_TYPES = [
        ('milestone', '里程碑'),
        ('task', '任务'),
        ('checkpoint', '检查点'),
        ('delivery', '交付'),
    ]
    
    order = models.ForeignKey(ServiceOrder, on_delete=models.CASCADE, related_name='progress_records', verbose_name='订单')
    progress_type = models.CharField('进度类型', max_length=20, choices=PROGRESS_TYPES)
    title = models.CharField('进度标题', max_length=200)
    description = models.TextField('进度描述')
    progress_percentage = models.IntegerField('完成百分比', default=0, help_text='0-100')
    
    # 时间记录
    planned_start = models.DateTimeField('计划开始时间', null=True, blank=True)
    planned_end = models.DateTimeField('计划完成时间', null=True, blank=True)
    actual_start = models.DateTimeField('实际开始时间', null=True, blank=True)
    actual_end = models.DateTimeField('实际完成时间', null=True, blank=True)
    
    # 负责人
    responsible_person = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        verbose_name='负责人'
    )
    
    # 状态
    is_completed = models.BooleanField('是否完成', default=False)
    is_delayed = models.BooleanField('是否延期', default=False)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '订单进度'
        verbose_name_plural = '订单进度'
        ordering = ['order', 'created_at']
    
    def __str__(self):
        return f"{self.order.order_number} - {self.title} ({self.progress_percentage}%)"
    
    def check_delay(self):
        """检查是否延期"""
        if self.planned_end and not self.is_completed:
            now = timezone.now()
            if now > self.planned_end:
                self.is_delayed = True
                self.save(update_fields=['is_delayed'])
        return self.is_delayed


class OrderQuality(models.Model):
    """订单质量管理"""
    QUALITY_LEVELS = [
        ('excellent', '优秀'),
        ('good', '良好'),
        ('average', '一般'),
        ('poor', '较差'),
        ('unacceptable', '不合格'),
    ]
    
    order = models.OneToOneField(ServiceOrder, on_delete=models.CASCADE, verbose_name='订单')
    
    # 质量评估
    quality_level = models.CharField('质量等级', max_length=20, choices=QUALITY_LEVELS, null=True, blank=True)
    quality_score = models.DecimalField('质量评分', max_digits=3, decimal_places=1, null=True, blank=True, help_text='1-10分')
    
    # 客户满意度
    customer_satisfaction = models.IntegerField('客户满意度', null=True, blank=True, help_text='1-5星')
    customer_feedback = models.TextField('客户反馈', blank=True)
    
    # 内部评估
    internal_review_score = models.DecimalField('内部评估分', max_digits=3, decimal_places=1, null=True, blank=True)
    internal_review_notes = models.TextField('内部评估备注', blank=True)
    
    # 问题记录
    has_issues = models.BooleanField('存在问题', default=False)
    issue_description = models.TextField('问题描述', blank=True)
    resolution_notes = models.TextField('解决方案', blank=True)
    
    # 评估人员
    reviewed_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        verbose_name='评估人'
    )
    reviewed_at = models.DateTimeField('评估时间', null=True, blank=True)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '订单质量'
        verbose_name_plural = '订单质量'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.order.order_number} - {self.get_quality_level_display() or '未评估'}"


class OrderTimeline(models.Model):
    """订单时间线"""
    EVENT_TYPES = [
        ('created', '订单创建'),
        ('paid', '支付完成'),
        ('assigned', '分配处理'),
        ('started', '开始处理'),
        ('progress', '进度更新'),
        ('review', '质量检查'),
        ('completed', '订单完成'),
        ('delivered', '交付客户'),
        ('feedback', '客户反馈'),
        ('issue', '问题记录'),
        ('resolved', '问题解决'),
    ]
    
    order = models.ForeignKey(ServiceOrder, on_delete=models.CASCADE, related_name='timeline', verbose_name='订单')
    event_type = models.CharField('事件类型', max_length=20, choices=EVENT_TYPES)
    title = models.CharField('事件标题', max_length=200)
    description = models.TextField('事件描述', blank=True)
    
    # 相关人员
    actor = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        verbose_name='操作人'
    )
    
    # 附加数据
    metadata = models.JSONField('附加数据', default=dict, blank=True)
    
    created_at = models.DateTimeField('发生时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '订单时间线'
        verbose_name_plural = '订单时间线'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.order.order_number} - {self.title}"


class ServiceTeam(models.Model):
    """服务团队"""
    name = models.CharField('团队名称', max_length=100)
    description = models.TextField('团队描述', blank=True)
    team_leader = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='led_teams', verbose_name='团队负责人'
    )
    members = models.ManyToManyField(
        User, through='TeamMembership', related_name='service_teams',
        verbose_name='团队成员'
    )
    
    # 团队能力
    service_types = models.JSONField('服务类型', default=list, help_text='团队可处理的服务类型')
    max_concurrent_orders = models.IntegerField('最大并发订单数', default=10)
    
    is_active = models.BooleanField('是否活跃', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '服务团队'
        verbose_name_plural = '服务团队'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.name
    
    def get_current_workload(self):
        """获取当前工作负载"""
        active_orders = ServiceOrder.objects.filter(
            orderassignment__assigned_to__in=self.members.all(),
            status__in=['pending', 'processing', 'reviewing']
        ).count()
        return active_orders


class TeamMembership(models.Model):
    """团队成员关系"""
    ROLES = [
        ('leader', '负责人'),
        ('senior', '高级成员'),
        ('junior', '初级成员'),
        ('specialist', '专家'),
    ]
    
    team = models.ForeignKey(ServiceTeam, on_delete=models.CASCADE, verbose_name='团队')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    role = models.CharField('角色', max_length=20, choices=ROLES, default='junior')
    
    # 能力评级
    skill_level = models.IntegerField('技能等级', default=1, help_text='1-5级')
    specialties = models.JSONField('专业领域', default=list)
    
    joined_at = models.DateTimeField('加入时间', auto_now_add=True)
    is_active = models.BooleanField('是否活跃', default=True)
    
    class Meta:
        verbose_name = '团队成员'
        verbose_name_plural = '团队成员'
        unique_together = ['team', 'user']
        ordering = ['-joined_at']
    
    def __str__(self):
        return f"{self.team.name} - {self.user.get_full_name()} ({self.get_role_display()})"


class OrderMetrics(models.Model):
    """订单指标统计"""
    date = models.DateField('统计日期')
    
    # 订单数量统计
    total_orders = models.IntegerField('总订单数', default=0)
    new_orders = models.IntegerField('新增订单', default=0)
    completed_orders = models.IntegerField('完成订单', default=0)
    cancelled_orders = models.IntegerField('取消订单', default=0)
    
    # 时间统计
    avg_processing_time = models.DurationField('平均处理时长', null=True, blank=True)
    avg_response_time = models.DurationField('平均响应时长', null=True, blank=True)
    
    # 质量统计
    avg_quality_score = models.DecimalField('平均质量评分', max_digits=3, decimal_places=1, null=True, blank=True)
    customer_satisfaction_rate = models.DecimalField('客户满意率', max_digits=5, decimal_places=2, default=0)
    
    # 效率统计
    on_time_delivery_rate = models.DecimalField('按时交付率', max_digits=5, decimal_places=2, default=0)
    first_time_resolution_rate = models.DecimalField('一次解决率', max_digits=5, decimal_places=2, default=0)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '订单指标'
        verbose_name_plural = '订单指标'
        unique_together = ['date']
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.date} - 订单指标"

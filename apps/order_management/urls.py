from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

router = DefaultRouter()
router.register(r'status-flows', views.OrderStatusFlowViewSet, basename='order-status-flow')
router.register(r'assignments', views.OrderAssignmentViewSet, basename='order-assignment')
router.register(r'progress', views.OrderProgressViewSet, basename='order-progress')
router.register(r'quality', views.OrderQualityViewSet, basename='order-quality')
router.register(r'timeline', views.OrderTimelineViewSet, basename='order-timeline')
router.register(r'teams', views.ServiceTeamViewSet, basename='service-team')
router.register(r'memberships', views.TeamMembershipViewSet, basename='team-membership')
router.register(r'metrics', views.OrderMetricsViewSet, basename='order-metrics')

app_name = 'order_management'

urlpatterns = [
    path('', include(router.urls)),
]

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.db.models import Count, Avg, Q
from .models import (
    OrderStatusFlow, OrderAssignment, OrderProgress, OrderQuality,
    OrderTimeline, ServiceTeam, TeamMembership, OrderMetrics
)


@admin.register(OrderStatusFlow)
class OrderStatusFlowAdmin(admin.ModelAdmin):
    """订单状态流程管理"""
    list_display = [
        'name', 'service_type', 'status_count', 'is_active', 'created_at'
    ]
    list_filter = ['service_type', 'is_active', 'created_at']
    search_fields = ['name', 'service_type']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'service_type', 'is_active')
        }),
        ('状态配置', {
            'fields': ('status_sequence',),
            'classes': ('wide',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def status_count(self, obj):
        """状态数量"""
        return len(obj.status_sequence) if obj.status_sequence else 0
    status_count.short_description = '状态数量'


@admin.register(OrderAssignment)
class OrderAssignmentAdmin(admin.ModelAdmin):
    """订单分配管理"""
    list_display = [
        'order_info', 'assigned_to', 'assignment_type_display',
        'workload_display', 'assigned_at'
    ]
    list_filter = ['assignment_type', 'assigned_at', 'assigned_to']
    search_fields = ['order__order_no', 'assigned_to__username', 'assigned_to__first_name']
    readonly_fields = ['assigned_at']
    
    fieldsets = (
        ('分配信息', {
            'fields': ('order', 'assigned_to', 'assignment_type', 'assigned_by')
        }),
        ('工作量', {
            'fields': ('estimated_hours', 'actual_hours')
        }),
        ('备注', {
            'fields': ('assignment_reason',)
        }),
        ('时间信息', {
            'fields': ('assigned_at',),
            'classes': ('collapse',)
        }),
    )
    
    def order_info(self, obj):
        """订单信息"""
        return format_html(
            '<a href="{}">{}</a><br/><small>{}</small>',
            reverse('admin:services_order_change', args=[obj.order.pk]),
            obj.order.order_no,
            obj.order.package.name if obj.order.package else '无套餐'
        )
    order_info.short_description = '订单信息'
    
    def assignment_type_display(self, obj):
        """分配方式显示"""
        type_colors = {
            'auto': '#1890ff',
            'manual': '#52c41a',
            'load_balance': '#faad14',
            'skill_match': '#722ed1',
        }
        color = type_colors.get(obj.assignment_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_assignment_type_display()
        )
    assignment_type_display.short_description = '分配方式'
    
    def workload_display(self, obj):
        """工作量显示"""
        if obj.estimated_hours and obj.actual_hours:
            variance = float(obj.actual_hours - obj.estimated_hours)
            if variance > 0:
                color = '#f5222d'
                symbol = '+'
            elif variance < 0:
                color = '#52c41a'
                symbol = ''
            else:
                color = '#1890ff'
                symbol = ''
            
            return format_html(
                '预估: {}h<br/>实际: {}h<br/><span style="color: {};">{}{}h</span>',
                obj.estimated_hours, obj.actual_hours, color, symbol, variance
            )
        elif obj.estimated_hours:
            return "预估: {}h".format(obj.estimated_hours)
        elif obj.actual_hours:
            return "实际: {}h".format(obj.actual_hours)
        return '-'
    workload_display.short_description = '工作量'


@admin.register(OrderProgress)
class OrderProgressAdmin(admin.ModelAdmin):
    """订单进度管理"""
    list_display = [
        'order_info', 'title', 'progress_type_display', 'progress_bar',
        'responsible_person', 'status_display', 'planned_end'
    ]
    list_filter = ['progress_type', 'is_completed', 'is_delayed', 'created_at']
    search_fields = ['order__order_no', 'title', 'responsible_person__username']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('order', 'progress_type', 'title', 'description')
        }),
        ('进度信息', {
            'fields': ('progress_percentage', 'responsible_person', 'is_completed', 'is_delayed')
        }),
        ('时间计划', {
            'fields': ('planned_start', 'planned_end', 'actual_start', 'actual_end')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def order_info(self, obj):
        """订单信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:services_order_change', args=[obj.order.pk]),
            obj.order.order_no
        )
    order_info.short_description = '订单'
    
    def progress_type_display(self, obj):
        """进度类型显示"""
        type_colors = {
            'milestone': '#722ed1',
            'task': '#1890ff',
            'checkpoint': '#faad14',
            'delivery': '#52c41a',
        }
        color = type_colors.get(obj.progress_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_progress_type_display()
        )
    progress_type_display.short_description = '类型'
    
    def progress_bar(self, obj):
        """进度条"""
        percentage = obj.progress_percentage
        if percentage >= 100:
            color = '#52c41a'
        elif percentage >= 80:
            color = '#1890ff'
        elif percentage >= 50:
            color = '#faad14'
        else:
            color = '#f5222d'
        
        return format_html(
            '<div style="width: 100px; background: #f0f0f0; border-radius: 4px; overflow: hidden;">'
            '<div style="width: {}%; height: 20px; background: {}; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">'
            '{}%'
            '</div></div>',
            percentage, color, percentage
        )
    progress_bar.short_description = '进度'
    
    def status_display(self, obj):
        """状态显示"""
        if obj.is_completed:
            return format_html('<span style="color: #52c41a;">✓ 已完成</span>')
        elif obj.is_delayed:
            return format_html('<span style="color: #f5222d;">⚠ 延期</span>')
        else:
            return format_html('<span style="color: #1890ff;">● 进行中</span>')
    status_display.short_description = '状态'


@admin.register(OrderQuality)
class OrderQualityAdmin(admin.ModelAdmin):
    """订单质量管理"""
    list_display = [
        'order_info', 'quality_level_display', 'quality_score',
        'customer_satisfaction_display', 'has_issues', 'reviewed_by', 'reviewed_at'
    ]
    list_filter = ['quality_level', 'customer_satisfaction', 'has_issues', 'reviewed_at']
    search_fields = ['order__order_no', 'customer_feedback', 'issue_description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('订单信息', {
            'fields': ('order',)
        }),
        ('质量评估', {
            'fields': ('quality_level', 'quality_score', 'internal_review_score', 'internal_review_notes')
        }),
        ('客户反馈', {
            'fields': ('customer_satisfaction', 'customer_feedback')
        }),
        ('问题管理', {
            'fields': ('has_issues', 'issue_description', 'resolution_notes')
        }),
        ('评估信息', {
            'fields': ('reviewed_by', 'reviewed_at')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def order_info(self, obj):
        """订单信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:services_order_change', args=[obj.order.pk]),
            obj.order.order_no
        )
    order_info.short_description = '订单'
    
    def quality_level_display(self, obj):
        """质量等级显示"""
        if not obj.quality_level:
            return format_html('<span style="color: #d9d9d9;">未评估</span>')
        
        level_colors = {
            'excellent': '#52c41a',
            'good': '#1890ff',
            'average': '#faad14',
            'poor': '#fa8c16',
            'unacceptable': '#f5222d',
        }
        color = level_colors.get(obj.quality_level, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_quality_level_display()
        )
    quality_level_display.short_description = '质量等级'
    
    def customer_satisfaction_display(self, obj):
        """客户满意度显示"""
        if not obj.customer_satisfaction:
            return '-'
        
        stars = '★' * obj.customer_satisfaction + '☆' * (5 - obj.customer_satisfaction)
        if obj.customer_satisfaction >= 4:
            color = '#52c41a'
        elif obj.customer_satisfaction >= 3:
            color = '#faad14'
        else:
            color = '#f5222d'
        
        return format_html(
            '<span style="color: {};">{}</span>',
            color, stars
        )
    customer_satisfaction_display.short_description = '客户满意度'


@admin.register(OrderTimeline)
class OrderTimelineAdmin(admin.ModelAdmin):
    """订单时间线管理"""
    list_display = [
        'order_info', 'event_type_display', 'title', 'actor', 'created_at'
    ]
    list_filter = ['event_type', 'created_at', 'actor']
    search_fields = ['order__order_no', 'title', 'description']
    readonly_fields = ['created_at']

    fieldsets = (
        ('事件信息', {
            'fields': ('order', 'event_type', 'title', 'description')
        }),
        ('相关人员', {
            'fields': ('actor',)
        }),
        ('附加数据', {
            'fields': ('metadata',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

    def order_info(self, obj):
        """订单信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:services_order_change', args=[obj.order.pk]),
            obj.order.order_no
        )
    order_info.short_description = '订单'

    def event_type_display(self, obj):
        """事件类型显示"""
        type_colors = {
            'created': '#1890ff',
            'paid': '#52c41a',
            'assigned': '#722ed1',
            'started': '#faad14',
            'progress': '#13c2c2',
            'review': '#eb2f96',
            'completed': '#52c41a',
            'delivered': '#1890ff',
            'feedback': '#fa8c16',
            'issue': '#f5222d',
            'resolved': '#52c41a',
        }
        color = type_colors.get(obj.event_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_event_type_display()
        )
    event_type_display.short_description = '事件类型'


class TeamMembershipInline(admin.TabularInline):
    """团队成员内联"""
    model = TeamMembership
    extra = 0
    fields = ['user', 'role', 'skill_level', 'specialties', 'is_active']


@admin.register(ServiceTeam)
class ServiceTeamAdmin(admin.ModelAdmin):
    """服务团队管理"""
    list_display = [
        'name', 'team_leader', 'member_count', 'workload_display',
        'service_types_display', 'is_active'
    ]
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description', 'team_leader__username']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'team_leader', 'is_active')
        }),
        ('团队能力', {
            'fields': ('service_types', 'max_concurrent_orders')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    inlines = [TeamMembershipInline]

    def member_count(self, obj):
        """成员数量"""
        return obj.members.filter(teammembership__is_active=True).count()
    member_count.short_description = '成员数量'

    def workload_display(self, obj):
        """工作负载显示"""
        current_workload = obj.get_current_workload()
        max_workload = obj.max_concurrent_orders

        if max_workload > 0:
            percentage = (current_workload / max_workload) * 100
            if percentage >= 90:
                color = '#f5222d'
            elif percentage >= 70:
                color = '#faad14'
            else:
                color = '#52c41a'
        else:
            percentage = 0
            color = '#d9d9d9'

        return format_html(
            '<span style="color: {};">{}/{} ({}%)</span>',
            color, current_workload, max_workload, int(percentage)
        )
    workload_display.short_description = '工作负载'

    def service_types_display(self, obj):
        """服务类型显示"""
        if obj.service_types:
            return ', '.join(obj.service_types[:3]) + ('...' if len(obj.service_types) > 3 else '')
        return '-'
    service_types_display.short_description = '服务类型'


@admin.register(TeamMembership)
class TeamMembershipAdmin(admin.ModelAdmin):
    """团队成员管理"""
    list_display = [
        'team', 'user', 'role_display', 'skill_level_display',
        'specialties_display', 'is_active', 'joined_at'
    ]
    list_filter = ['role', 'skill_level', 'is_active', 'joined_at']
    search_fields = ['team__name', 'user__username', 'user__first_name']
    readonly_fields = ['joined_at']

    def role_display(self, obj):
        """角色显示"""
        role_colors = {
            'leader': '#f5222d',
            'senior': '#fa8c16',
            'junior': '#1890ff',
            'specialist': '#722ed1',
        }
        color = role_colors.get(obj.role, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_role_display()
        )
    role_display.short_description = '角色'

    def skill_level_display(self, obj):
        """技能等级显示"""
        stars = '★' * obj.skill_level + '☆' * (5 - obj.skill_level)
        if obj.skill_level >= 4:
            color = '#52c41a'
        elif obj.skill_level >= 3:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {};">{}</span>',
            color, stars
        )
    skill_level_display.short_description = '技能等级'

    def specialties_display(self, obj):
        """专业领域显示"""
        if obj.specialties:
            return ', '.join(obj.specialties[:2]) + ('...' if len(obj.specialties) > 2 else '')
        return '-'
    specialties_display.short_description = '专业领域'


@admin.register(OrderMetrics)
class OrderMetricsAdmin(admin.ModelAdmin):
    """订单指标管理"""
    list_display = [
        'date', 'order_summary', 'time_metrics', 'quality_metrics',
        'efficiency_metrics'
    ]
    list_filter = ['date']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('统计日期', {
            'fields': ('date',)
        }),
        ('订单数量', {
            'fields': ('total_orders', 'new_orders', 'completed_orders', 'cancelled_orders')
        }),
        ('时间指标', {
            'fields': ('avg_processing_time', 'avg_response_time')
        }),
        ('质量指标', {
            'fields': ('avg_quality_score', 'customer_satisfaction_rate')
        }),
        ('效率指标', {
            'fields': ('on_time_delivery_rate', 'first_time_resolution_rate')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def order_summary(self, obj):
        """订单概要"""
        return format_html(
            '总数: {}<br/>新增: <span style="color: #1890ff;">{}</span><br/>'
            '完成: <span style="color: #52c41a;">{}</span><br/>'
            '取消: <span style="color: #f5222d;">{}</span>',
            obj.total_orders, obj.new_orders, obj.completed_orders, obj.cancelled_orders
        )
    order_summary.short_description = '订单统计'

    def time_metrics(self, obj):
        """时间指标"""
        processing_time = ''
        response_time = ''

        if obj.avg_processing_time:
            hours = obj.avg_processing_time.total_seconds() / 3600
            processing_time = "{:.1f}h".format(hours)

        if obj.avg_response_time:
            hours = obj.avg_response_time.total_seconds() / 3600
            response_time = "{:.1f}h".format(hours)

        return format_html(
            '处理时长: {}<br/>响应时长: {}',
            processing_time or '-', response_time or '-'
        )
    time_metrics.short_description = '时间指标'

    def quality_metrics(self, obj):
        """质量指标"""
        return format_html(
            '质量评分: {}<br/>满意率: {}%',
            obj.avg_quality_score or '-',
            obj.customer_satisfaction_rate
        )
    quality_metrics.short_description = '质量指标'

    def efficiency_metrics(self, obj):
        """效率指标"""
        return format_html(
            '按时交付: {}%<br/>一次解决: {}%',
            obj.on_time_delivery_rate,
            obj.first_time_resolution_rate
        )
    efficiency_metrics.short_description = '效率指标'

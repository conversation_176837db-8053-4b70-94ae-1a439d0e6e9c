from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from apps.companies.models import Company

User = get_user_model()


class ReportCategory(models.Model):
    """报表分类"""
    name = models.CharField('分类名称', max_length=100)
    description = models.TextField('分类描述', blank=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name='父分类')
    icon = models.CharField('图标', max_length=50, blank=True)
    sort_order = models.IntegerField('排序', default=0)
    is_active = models.BooleanField('是否启用', default=True)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '报表分类'
        verbose_name_plural = '报表分类'
        ordering = ['sort_order', 'name']
    
    def __str__(self):
        return self.name


class ReportTemplate(models.Model):
    """报表模板"""
    REPORT_TYPES = [
        ('table', '表格报表'),
        ('chart', '图表报表'),
        ('dashboard', '仪表板'),
        ('kpi', 'KPI报表'),
        ('trend', '趋势分析'),
        ('comparison', '对比分析'),
    ]
    
    CHART_TYPES = [
        ('line', '折线图'),
        ('bar', '柱状图'),
        ('pie', '饼图'),
        ('area', '面积图'),
        ('scatter', '散点图'),
        ('gauge', '仪表盘'),
        ('funnel', '漏斗图'),
        ('radar', '雷达图'),
    ]
    
    DATA_SOURCES = [
        ('companies', '企业数据'),
        ('orders', '订单数据'),
        ('invoices', '发票数据'),
        ('finance', '财务数据'),
        ('tax', '税务数据'),
        ('service', '客服数据'),
        ('users', '用户数据'),
        ('custom', '自定义数据'),
    ]
    
    name = models.CharField('模板名称', max_length=200)
    description = models.TextField('模板描述', blank=True)
    category = models.ForeignKey(ReportCategory, on_delete=models.SET_NULL, null=True, verbose_name='报表分类')
    
    # 报表配置
    report_type = models.CharField('报表类型', max_length=20, choices=REPORT_TYPES)
    chart_type = models.CharField('图表类型', max_length=20, choices=CHART_TYPES, blank=True)
    data_source = models.CharField('数据源', max_length=20, choices=DATA_SOURCES)
    
    # 查询配置
    sql_query = models.TextField('SQL查询', blank=True, help_text='自定义SQL查询语句')
    query_params = models.JSONField('查询参数', default=dict, help_text='查询参数配置')
    filters = models.JSONField('过滤条件', default=list, help_text='报表过滤条件')
    
    # 显示配置
    columns = models.JSONField('列配置', default=list, help_text='表格列配置')
    chart_config = models.JSONField('图表配置', default=dict, help_text='图表样式配置')
    layout_config = models.JSONField('布局配置', default=dict, help_text='页面布局配置')
    
    # 权限配置
    is_public = models.BooleanField('是否公开', default=False)
    allowed_roles = models.JSONField('允许角色', default=list, help_text='允许访问的角色列表')
    allowed_users = models.ManyToManyField(User, blank=True, related_name='allowed_report_templates', verbose_name='允许用户')
    
    # 缓存配置
    cache_duration = models.IntegerField('缓存时长(分钟)', default=60)
    auto_refresh = models.BooleanField('自动刷新', default=False)
    refresh_interval = models.IntegerField('刷新间隔(分钟)', default=30)
    
    is_active = models.BooleanField('是否启用', default=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_report_templates', verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '报表模板'
        verbose_name_plural = '报表模板'
        ordering = ['-updated_at']
    
    def __str__(self):
        return self.name


class ReportInstance(models.Model):
    """报表实例"""
    STATUS_CHOICES = [
        ('pending', '待生成'),
        ('generating', '生成中'),
        ('completed', '已完成'),
        ('failed', '生成失败'),
        ('expired', '已过期'),
    ]
    
    template = models.ForeignKey(ReportTemplate, on_delete=models.CASCADE, verbose_name='报表模板')
    name = models.CharField('实例名称', max_length=200)
    
    # 参数配置
    parameters = models.JSONField('参数值', default=dict, help_text='报表参数值')
    filters_applied = models.JSONField('应用的过滤条件', default=dict, help_text='实际应用的过滤条件')
    
    # 生成信息
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    generated_at = models.DateTimeField('生成时间', null=True, blank=True)
    expires_at = models.DateTimeField('过期时间', null=True, blank=True)
    
    # 数据和结果
    data = models.JSONField('报表数据', default=dict, help_text='报表生成的数据')
    metadata = models.JSONField('元数据', default=dict, help_text='报表元数据信息')
    file_path = models.CharField('文件路径', max_length=500, blank=True, help_text='导出文件路径')
    
    # 统计信息
    data_rows = models.IntegerField('数据行数', default=0)
    generation_time = models.FloatField('生成耗时(秒)', default=0)
    file_size = models.IntegerField('文件大小(字节)', default=0)
    
    # 访问控制
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='创建人')
    is_shared = models.BooleanField('是否共享', default=False)
    shared_users = models.ManyToManyField(User, blank=True, related_name='shared_reports', verbose_name='共享用户')
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '报表实例'
        verbose_name_plural = '报表实例'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.template.name} - {self.name}"
    
    def is_expired(self):
        """检查是否过期"""
        if not self.expires_at:
            return False
        return timezone.now() > self.expires_at


class Dashboard(models.Model):
    """仪表板"""
    name = models.CharField('仪表板名称', max_length=200)
    description = models.TextField('仪表板描述', blank=True)
    
    # 布局配置
    layout = models.JSONField('布局配置', default=dict, help_text='仪表板布局配置')
    widgets = models.JSONField('组件配置', default=list, help_text='仪表板组件列表')
    theme = models.CharField('主题', max_length=50, default='default')
    
    # 数据刷新
    auto_refresh = models.BooleanField('自动刷新', default=True)
    refresh_interval = models.IntegerField('刷新间隔(秒)', default=300)
    
    # 权限配置
    is_public = models.BooleanField('是否公开', default=False)
    allowed_roles = models.JSONField('允许角色', default=list)
    allowed_users = models.ManyToManyField(User, blank=True, related_name='allowed_dashboards', verbose_name='允许用户')
    
    # 个性化配置
    is_default = models.BooleanField('是否默认', default=False)
    sort_order = models.IntegerField('排序', default=0)
    
    is_active = models.BooleanField('是否启用', default=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_dashboards', verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '仪表板'
        verbose_name_plural = '仪表板'
        ordering = ['sort_order', 'name']
    
    def __str__(self):
        return self.name


class DataSource(models.Model):
    """数据源"""
    SOURCE_TYPES = [
        ('database', '数据库'),
        ('api', 'API接口'),
        ('file', '文件'),
        ('external', '外部系统'),
    ]
    
    CONNECTION_TYPES = [
        ('mysql', 'MySQL'),
        ('postgresql', 'PostgreSQL'),
        ('sqlite', 'SQLite'),
        ('oracle', 'Oracle'),
        ('sqlserver', 'SQL Server'),
        ('rest_api', 'REST API'),
        ('graphql', 'GraphQL'),
        ('csv', 'CSV文件'),
        ('excel', 'Excel文件'),
        ('json', 'JSON文件'),
    ]
    
    name = models.CharField('数据源名称', max_length=200)
    description = models.TextField('数据源描述', blank=True)
    source_type = models.CharField('数据源类型', max_length=20, choices=SOURCE_TYPES)
    connection_type = models.CharField('连接类型', max_length=20, choices=CONNECTION_TYPES)
    
    # 连接配置
    connection_config = models.JSONField('连接配置', default=dict, help_text='数据源连接配置')
    authentication = models.JSONField('认证信息', default=dict, help_text='认证配置')
    
    # 数据配置
    schema_info = models.JSONField('模式信息', default=dict, help_text='数据模式信息')
    table_mappings = models.JSONField('表映射', default=dict, help_text='表和字段映射')
    
    # 性能配置
    connection_pool_size = models.IntegerField('连接池大小', default=5)
    query_timeout = models.IntegerField('查询超时(秒)', default=30)
    cache_enabled = models.BooleanField('启用缓存', default=True)
    
    is_active = models.BooleanField('是否启用', default=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '数据源'
        verbose_name_plural = '数据源'
        ordering = ['name']
    
    def __str__(self):
        return self.name


class KPIMetric(models.Model):
    """KPI指标"""
    METRIC_TYPES = [
        ('count', '计数'),
        ('sum', '求和'),
        ('avg', '平均值'),
        ('max', '最大值'),
        ('min', '最小值'),
        ('ratio', '比率'),
        ('percentage', '百分比'),
        ('growth', '增长率'),
    ]
    
    AGGREGATION_PERIODS = [
        ('daily', '日'),
        ('weekly', '周'),
        ('monthly', '月'),
        ('quarterly', '季度'),
        ('yearly', '年'),
        ('custom', '自定义'),
    ]
    
    name = models.CharField('指标名称', max_length=200)
    description = models.TextField('指标描述', blank=True)
    category = models.ForeignKey(ReportCategory, on_delete=models.SET_NULL, null=True, verbose_name='指标分类')
    
    # 指标配置
    metric_type = models.CharField('指标类型', max_length=20, choices=METRIC_TYPES)
    data_source = models.ForeignKey(DataSource, on_delete=models.CASCADE, verbose_name='数据源')
    calculation_formula = models.TextField('计算公式', help_text='指标计算公式')
    
    # 聚合配置
    aggregation_period = models.CharField('聚合周期', max_length=20, choices=AGGREGATION_PERIODS)
    aggregation_field = models.CharField('聚合字段', max_length=100, blank=True)
    
    # 目标和阈值
    target_value = models.DecimalField('目标值', max_digits=15, decimal_places=2, null=True, blank=True)
    warning_threshold = models.DecimalField('警告阈值', max_digits=15, decimal_places=2, null=True, blank=True)
    critical_threshold = models.DecimalField('严重阈值', max_digits=15, decimal_places=2, null=True, blank=True)
    
    # 显示配置
    unit = models.CharField('单位', max_length=20, blank=True)
    decimal_places = models.IntegerField('小数位数', default=2)
    display_format = models.CharField('显示格式', max_length=50, blank=True)
    
    # 权限配置
    is_public = models.BooleanField('是否公开', default=False)
    allowed_roles = models.JSONField('允许角色', default=list)
    
    is_active = models.BooleanField('是否启用', default=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = 'KPI指标'
        verbose_name_plural = 'KPI指标'
        ordering = ['name']
    
    def __str__(self):
        return self.name


class KPIValue(models.Model):
    """KPI指标值"""
    metric = models.ForeignKey(KPIMetric, on_delete=models.CASCADE, related_name='values', verbose_name='KPI指标')
    
    # 时间维度
    period_start = models.DateTimeField('周期开始')
    period_end = models.DateTimeField('周期结束')
    period_type = models.CharField('周期类型', max_length=20)
    
    # 维度信息
    dimensions = models.JSONField('维度信息', default=dict, help_text='指标维度信息')
    
    # 指标值
    value = models.DecimalField('指标值', max_digits=15, decimal_places=4)
    target_value = models.DecimalField('目标值', max_digits=15, decimal_places=4, null=True, blank=True)
    
    # 计算信息
    calculation_details = models.JSONField('计算详情', default=dict, help_text='计算过程详情')
    data_quality_score = models.DecimalField('数据质量评分', max_digits=5, decimal_places=2, default=100)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        verbose_name = 'KPI指标值'
        verbose_name_plural = 'KPI指标值'
        ordering = ['-period_start']
    
    def __str__(self):
        return f"{self.metric.name} - {self.period_start.date()}"
    
    def get_performance_status(self):
        """获取绩效状态"""
        if not self.target_value:
            return 'no_target'
        
        achievement_rate = (self.value / self.target_value) * 100
        
        if achievement_rate >= 100:
            return 'excellent'
        elif achievement_rate >= 80:
            return 'good'
        elif achievement_rate >= 60:
            return 'warning'
        else:
            return 'poor'


class AnalyticsJob(models.Model):
    """分析任务"""
    JOB_TYPES = [
        ('report_generation', '报表生成'),
        ('data_sync', '数据同步'),
        ('kpi_calculation', 'KPI计算'),
        ('data_cleaning', '数据清洗'),
        ('model_training', '模型训练'),
        ('prediction', '预测分析'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '待执行'),
        ('running', '执行中'),
        ('completed', '已完成'),
        ('failed', '执行失败'),
        ('cancelled', '已取消'),
    ]
    
    name = models.CharField('任务名称', max_length=200)
    job_type = models.CharField('任务类型', max_length=30, choices=JOB_TYPES)
    description = models.TextField('任务描述', blank=True)
    
    # 任务配置
    config = models.JSONField('任务配置', default=dict, help_text='任务执行配置')
    parameters = models.JSONField('任务参数', default=dict, help_text='任务执行参数')
    
    # 调度配置
    is_scheduled = models.BooleanField('是否定时任务', default=False)
    schedule_expression = models.CharField('调度表达式', max_length=100, blank=True, help_text='Cron表达式')
    next_run_time = models.DateTimeField('下次执行时间', null=True, blank=True)
    
    # 执行状态
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    started_at = models.DateTimeField('开始时间', null=True, blank=True)
    completed_at = models.DateTimeField('完成时间', null=True, blank=True)
    
    # 执行结果
    result = models.JSONField('执行结果', default=dict, help_text='任务执行结果')
    error_message = models.TextField('错误信息', blank=True)
    logs = models.TextField('执行日志', blank=True)
    
    # 性能统计
    execution_time = models.FloatField('执行时间(秒)', default=0)
    memory_usage = models.IntegerField('内存使用(MB)', default=0)
    cpu_usage = models.FloatField('CPU使用率', default=0)
    
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '分析任务'
        verbose_name_plural = '分析任务'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.name


class DataQualityRule(models.Model):
    """数据质量规则"""
    RULE_TYPES = [
        ('completeness', '完整性'),
        ('accuracy', '准确性'),
        ('consistency', '一致性'),
        ('validity', '有效性'),
        ('uniqueness', '唯一性'),
        ('timeliness', '及时性'),
    ]
    
    SEVERITY_LEVELS = [
        ('low', '低'),
        ('medium', '中'),
        ('high', '高'),
        ('critical', '严重'),
    ]
    
    name = models.CharField('规则名称', max_length=200)
    description = models.TextField('规则描述', blank=True)
    rule_type = models.CharField('规则类型', max_length=20, choices=RULE_TYPES)
    
    # 规则配置
    data_source = models.ForeignKey(DataSource, on_delete=models.CASCADE, verbose_name='数据源')
    table_name = models.CharField('表名', max_length=100)
    column_name = models.CharField('字段名', max_length=100, blank=True)
    
    # 规则定义
    rule_expression = models.TextField('规则表达式', help_text='数据质量检查规则')
    expected_value = models.CharField('期望值', max_length=200, blank=True)
    threshold = models.DecimalField('阈值', max_digits=10, decimal_places=4, null=True, blank=True)
    
    # 严重程度
    severity = models.CharField('严重程度', max_length=20, choices=SEVERITY_LEVELS, default='medium')
    
    # 执行配置
    is_active = models.BooleanField('是否启用', default=True)
    check_frequency = models.CharField('检查频率', max_length=50, default='daily')
    
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '数据质量规则'
        verbose_name_plural = '数据质量规则'
        ordering = ['name']
    
    def __str__(self):
        return self.name


class DataQualityResult(models.Model):
    """数据质量检查结果"""
    rule = models.ForeignKey(DataQualityRule, on_delete=models.CASCADE, related_name='results', verbose_name='质量规则')
    
    # 检查信息
    check_time = models.DateTimeField('检查时间')
    data_period_start = models.DateTimeField('数据周期开始')
    data_period_end = models.DateTimeField('数据周期结束')
    
    # 检查结果
    passed = models.BooleanField('是否通过')
    score = models.DecimalField('质量评分', max_digits=5, decimal_places=2, default=0)
    
    # 统计信息
    total_records = models.IntegerField('总记录数', default=0)
    valid_records = models.IntegerField('有效记录数', default=0)
    invalid_records = models.IntegerField('无效记录数', default=0)
    
    # 详细结果
    details = models.JSONField('检查详情', default=dict, help_text='详细检查结果')
    sample_errors = models.JSONField('错误样本', default=list, help_text='错误数据样本')
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '数据质量结果'
        verbose_name_plural = '数据质量结果'
        ordering = ['-check_time']
    
    def __str__(self):
        return f"{self.rule.name} - {self.check_time.date()}"

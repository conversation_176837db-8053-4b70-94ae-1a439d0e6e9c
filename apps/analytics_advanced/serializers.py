from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    ReportCategory, ReportTemplate, ReportInstance, Dashboard,
    DataSource, KPIMetric, KPIValue, AnalyticsJob,
    DataQualityRule, DataQualityResult
)

User = get_user_model()


class ReportCategorySerializer(serializers.ModelSerializer):
    """报表分类序列化器"""
    parent_name = serializers.CharField(source='parent.name', read_only=True)
    
    class Meta:
        model = ReportCategory
        fields = [
            'id', 'name', 'description', 'parent', 'parent_name',
            'icon', 'sort_order', 'is_active', 'created_at', 'updated_at'
        ]


class ReportTemplateSerializer(serializers.ModelSerializer):
    """报表模板序列化器"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    report_type_display = serializers.Char<PERSON>ield(source='get_report_type_display', read_only=True)
    chart_type_display = serializers.CharField(source='get_chart_type_display', read_only=True)
    data_source_display = serializers.CharField(source='get_data_source_display', read_only=True)
    
    class Meta:
        model = ReportTemplate
        fields = [
            'id', 'name', 'description', 'category', 'category_name',
            'report_type', 'report_type_display', 'chart_type', 'chart_type_display',
            'data_source', 'data_source_display', 'sql_query', 'query_params',
            'filters', 'columns', 'chart_config', 'layout_config',
            'is_public', 'allowed_roles', 'cache_duration', 'auto_refresh',
            'refresh_interval', 'is_active', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]


class ReportInstanceSerializer(serializers.ModelSerializer):
    """报表实例序列化器"""
    template_name = serializers.CharField(source='template.name', read_only=True)
    template_type = serializers.CharField(source='template.report_type', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    is_expired = serializers.SerializerMethodField()
    
    class Meta:
        model = ReportInstance
        fields = [
            'id', 'template', 'template_name', 'template_type', 'name',
            'parameters', 'filters_applied', 'status', 'status_display',
            'generated_at', 'expires_at', 'data', 'metadata', 'file_path',
            'data_rows', 'generation_time', 'file_size', 'created_by',
            'created_by_name', 'is_shared', 'is_expired', 'created_at'
        ]
    
    def get_is_expired(self, obj):
        return obj.is_expired()


class DashboardSerializer(serializers.ModelSerializer):
    """仪表板序列化器"""
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = Dashboard
        fields = [
            'id', 'name', 'description', 'layout', 'widgets', 'theme',
            'auto_refresh', 'refresh_interval', 'is_public', 'allowed_roles',
            'is_default', 'sort_order', 'is_active', 'created_by',
            'created_by_name', 'created_at', 'updated_at'
        ]


class DataSourceSerializer(serializers.ModelSerializer):
    """数据源序列化器"""
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    source_type_display = serializers.CharField(source='get_source_type_display', read_only=True)
    connection_type_display = serializers.CharField(source='get_connection_type_display', read_only=True)
    
    class Meta:
        model = DataSource
        fields = [
            'id', 'name', 'description', 'source_type', 'source_type_display',
            'connection_type', 'connection_type_display', 'connection_config',
            'authentication', 'schema_info', 'table_mappings',
            'connection_pool_size', 'query_timeout', 'cache_enabled',
            'is_active', 'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]


class KPIMetricSerializer(serializers.ModelSerializer):
    """KPI指标序列化器"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    data_source_name = serializers.CharField(source='data_source.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    metric_type_display = serializers.CharField(source='get_metric_type_display', read_only=True)
    aggregation_period_display = serializers.CharField(source='get_aggregation_period_display', read_only=True)
    
    class Meta:
        model = KPIMetric
        fields = [
            'id', 'name', 'description', 'category', 'category_name',
            'metric_type', 'metric_type_display', 'data_source', 'data_source_name',
            'calculation_formula', 'aggregation_period', 'aggregation_period_display',
            'aggregation_field', 'target_value', 'warning_threshold',
            'critical_threshold', 'unit', 'decimal_places', 'display_format',
            'is_public', 'allowed_roles', 'is_active', 'created_by',
            'created_by_name', 'created_at', 'updated_at'
        ]


class KPIValueSerializer(serializers.ModelSerializer):
    """KPI指标值序列化器"""
    metric_name = serializers.CharField(source='metric.name', read_only=True)
    metric_unit = serializers.CharField(source='metric.unit', read_only=True)
    performance_status = serializers.SerializerMethodField()
    achievement_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = KPIValue
        fields = [
            'id', 'metric', 'metric_name', 'metric_unit', 'period_start',
            'period_end', 'period_type', 'dimensions', 'value', 'target_value',
            'calculation_details', 'data_quality_score', 'performance_status',
            'achievement_rate', 'created_at'
        ]
    
    def get_performance_status(self, obj):
        return obj.get_performance_status()
    
    def get_achievement_rate(self, obj):
        if obj.target_value and obj.target_value > 0:
            return float((obj.value / obj.target_value) * 100)
        return None


class AnalyticsJobSerializer(serializers.ModelSerializer):
    """分析任务序列化器"""
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    job_type_display = serializers.CharField(source='get_job_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    duration = serializers.SerializerMethodField()
    
    class Meta:
        model = AnalyticsJob
        fields = [
            'id', 'name', 'job_type', 'job_type_display', 'description',
            'config', 'parameters', 'is_scheduled', 'schedule_expression',
            'next_run_time', 'status', 'status_display', 'started_at',
            'completed_at', 'result', 'error_message', 'logs',
            'execution_time', 'memory_usage', 'cpu_usage', 'duration',
            'created_by', 'created_by_name', 'created_at'
        ]
    
    def get_duration(self, obj):
        if obj.started_at and obj.completed_at:
            delta = obj.completed_at - obj.started_at
            return delta.total_seconds()
        return None


class DataQualityRuleSerializer(serializers.ModelSerializer):
    """数据质量规则序列化器"""
    data_source_name = serializers.CharField(source='data_source.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    rule_type_display = serializers.CharField(source='get_rule_type_display', read_only=True)
    severity_display = serializers.CharField(source='get_severity_display', read_only=True)
    
    class Meta:
        model = DataQualityRule
        fields = [
            'id', 'name', 'description', 'rule_type', 'rule_type_display',
            'data_source', 'data_source_name', 'table_name', 'column_name',
            'rule_expression', 'expected_value', 'threshold', 'severity',
            'severity_display', 'is_active', 'check_frequency', 'created_by',
            'created_by_name', 'created_at', 'updated_at'
        ]


class DataQualityResultSerializer(serializers.ModelSerializer):
    """数据质量结果序列化器"""
    rule_name = serializers.CharField(source='rule.name', read_only=True)
    rule_type = serializers.CharField(source='rule.rule_type', read_only=True)
    rule_severity = serializers.CharField(source='rule.severity', read_only=True)
    quality_percentage = serializers.SerializerMethodField()
    
    class Meta:
        model = DataQualityResult
        fields = [
            'id', 'rule', 'rule_name', 'rule_type', 'rule_severity',
            'check_time', 'data_period_start', 'data_period_end',
            'passed', 'score', 'total_records', 'valid_records',
            'invalid_records', 'quality_percentage', 'details',
            'sample_errors', 'created_at'
        ]
    
    def get_quality_percentage(self, obj):
        if obj.total_records > 0:
            return (obj.valid_records / obj.total_records) * 100
        return 0


# 简化版序列化器用于列表显示
class ReportTemplateListSerializer(serializers.ModelSerializer):
    """报表模板列表序列化器"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    report_type_display = serializers.CharField(source='get_report_type_display', read_only=True)
    data_source_display = serializers.CharField(source='get_data_source_display', read_only=True)
    
    class Meta:
        model = ReportTemplate
        fields = [
            'id', 'name', 'description', 'category_name',
            'report_type_display', 'data_source_display',
            'is_public', 'is_active', 'updated_at'
        ]


class DashboardListSerializer(serializers.ModelSerializer):
    """仪表板列表序列化器"""
    widget_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Dashboard
        fields = [
            'id', 'name', 'description', 'theme', 'auto_refresh',
            'is_public', 'is_default', 'widget_count', 'updated_at'
        ]
    
    def get_widget_count(self, obj):
        return len(obj.widgets) if obj.widgets else 0


class KPIMetricListSerializer(serializers.ModelSerializer):
    """KPI指标列表序列化器"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    metric_type_display = serializers.CharField(source='get_metric_type_display', read_only=True)
    latest_value = serializers.SerializerMethodField()
    
    class Meta:
        model = KPIMetric
        fields = [
            'id', 'name', 'category_name', 'metric_type_display',
            'target_value', 'unit', 'latest_value', 'is_active'
        ]
    
    def get_latest_value(self, obj):
        latest = obj.values.first()
        if latest:
            return {
                'value': latest.value,
                'period': latest.period_start.date(),
                'performance_status': latest.get_performance_status()
            }
        return None


class ReportGenerationSerializer(serializers.Serializer):
    """报表生成序列化器"""
    template_id = serializers.IntegerField(help_text='报表模板ID')
    name = serializers.CharField(max_length=200, help_text='报表实例名称')
    parameters = serializers.JSONField(
        required=False,
        default=dict,
        help_text='报表参数'
    )
    filters = serializers.JSONField(
        required=False,
        default=dict,
        help_text='过滤条件'
    )
    export_format = serializers.ChoiceField(
        choices=[
            ('json', 'JSON'),
            ('excel', 'Excel'),
            ('pdf', 'PDF'),
            ('csv', 'CSV'),
        ],
        required=False,
        default='json',
        help_text='导出格式'
    )


class DashboardConfigSerializer(serializers.Serializer):
    """仪表板配置序列化器"""
    layout = serializers.JSONField(help_text='布局配置')
    widgets = serializers.ListField(
        child=serializers.JSONField(),
        help_text='组件配置列表'
    )
    theme = serializers.CharField(
        max_length=50,
        required=False,
        default='default',
        help_text='主题'
    )
    auto_refresh = serializers.BooleanField(
        required=False,
        default=True,
        help_text='是否自动刷新'
    )
    refresh_interval = serializers.IntegerField(
        required=False,
        default=300,
        help_text='刷新间隔(秒)'
    )


class KPICalculationSerializer(serializers.Serializer):
    """KPI计算序列化器"""
    metric_ids = serializers.ListField(
        child=serializers.IntegerField(),
        help_text='KPI指标ID列表'
    )
    period_start = serializers.DateTimeField(help_text='计算周期开始')
    period_end = serializers.DateTimeField(help_text='计算周期结束')
    dimensions = serializers.JSONField(
        required=False,
        default=dict,
        help_text='维度信息'
    )
    force_recalculate = serializers.BooleanField(
        required=False,
        default=False,
        help_text='是否强制重新计算'
    )

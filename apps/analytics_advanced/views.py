from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q, Sum, Count, Avg, Max, Min
from django.shortcuts import get_object_or_404
from datetime import datetime, timedelta, date
from decimal import Decimal
import json
from .models import (
    ReportCategory, ReportTemplate, ReportInstance, Dashboard,
    DataSource, KPIMetric, KPIValue, AnalyticsJob,
    DataQualityRule, DataQualityResult
)
from .serializers import (
    ReportCategorySerializer, ReportTemplateSerializer, ReportInstanceSerializer,
    DashboardSerializer, DataSourceSerializer, KPIMetricSerializer,
    KPIValueSerializer, AnalyticsJobSerializer, DataQualityRuleSerializer,
    DataQualityResultSerializer, ReportTemplateListSerializer,
    DashboardListSerializer, KPIMetricListSerializer,
    ReportGenerationSerializer, DashboardConfigSerializer, KPICalculationSerializer
)
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class ReportCategoryViewSet(viewsets.ModelViewSet):
    """报表分类ViewSet"""
    queryset = ReportCategory.objects.all()
    serializer_class = ReportCategorySerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset.filter(is_active=True).order_by('sort_order', 'name')


class ReportTemplateViewSet(viewsets.ModelViewSet):
    """报表模板ViewSet"""
    queryset = ReportTemplate.objects.all()
    serializer_class = ReportTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user
        
        # 根据用户权限过滤
        if not user.is_staff:
            # 普通用户只能看到公开的或者允许访问的模板
            queryset = queryset.filter(
                Q(is_public=True) |
                Q(allowed_users=user) |
                Q(created_by=user)
            ).distinct()
        
        # 按分类过滤
        category_id = self.request.query_params.get('category')
        if category_id:
            queryset = queryset.filter(category_id=category_id)
        
        # 按报表类型过滤
        report_type = self.request.query_params.get('report_type')
        if report_type:
            queryset = queryset.filter(report_type=report_type)
        
        # 按数据源过滤
        data_source = self.request.query_params.get('data_source')
        if data_source:
            queryset = queryset.filter(data_source=data_source)
        
        return queryset.filter(is_active=True).order_by('-updated_at')
    
    def get_serializer_class(self):
        if self.action == 'list':
            return ReportTemplateListSerializer
        return ReportTemplateSerializer
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def generate_report(self, request, pk=None):
        """生成报表"""
        template = self.get_object()
        
        serializer = ReportGenerationSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        data = serializer.validated_data
        
        # 创建报表实例
        instance = ReportInstance.objects.create(
            template=template,
            name=data['name'],
            parameters=data.get('parameters', {}),
            filters_applied=data.get('filters', {}),
            created_by=request.user,
            status='pending'
        )
        
        # 异步生成报表
        try:
            self._generate_report_data(instance)
            return Response({
                'message': '报表生成成功',
                'instance_id': instance.id,
                'data': instance.data
            })
        except Exception as e:
            instance.status = 'failed'
            instance.error_message = str(e)
            instance.save()
            return Response(
                {'error': f'报表生成失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _generate_report_data(self, instance):
        """生成报表数据"""
        start_time = timezone.now()
        instance.status = 'generating'
        instance.save()
        
        try:
            template = instance.template
            
            # 根据数据源生成数据
            if template.data_source == 'companies':
                data = self._generate_company_data(template, instance)
            elif template.data_source == 'orders':
                data = self._generate_order_data(template, instance)
            elif template.data_source == 'invoices':
                data = self._generate_invoice_data(template, instance)
            elif template.data_source == 'finance':
                data = self._generate_finance_data(template, instance)
            elif template.data_source == 'tax':
                data = self._generate_tax_data(template, instance)
            elif template.data_source == 'service':
                data = self._generate_service_data(template, instance)
            elif template.data_source == 'users':
                data = self._generate_user_data(template, instance)
            else:
                data = self._generate_custom_data(template, instance)
            
            # 更新实例状态
            instance.status = 'completed'
            instance.data = data
            instance.data_rows = len(data.get('rows', []))
            instance.generated_at = timezone.now()
            instance.generation_time = (timezone.now() - start_time).total_seconds()
            
            # 设置过期时间
            if template.cache_duration > 0:
                instance.expires_at = timezone.now() + timedelta(minutes=template.cache_duration)
            
            instance.save()
            
        except Exception as e:
            instance.status = 'failed'
            instance.error_message = str(e)
            instance.save()
            raise
    
    def _generate_company_data(self, template, instance):
        """生成企业数据"""
        from apps.companies.models import Company
        
        companies = Company.objects.all()
        
        # 应用过滤条件
        filters = instance.filters_applied
        if filters.get('status'):
            companies = companies.filter(status=filters['status'])
        
        data = {
            'columns': [
                {'key': 'name', 'title': '企业名称'},
                {'key': 'industry', 'title': '行业'},
                {'key': 'status', 'title': '状态'},
                {'key': 'created_at', 'title': '创建时间'},
            ],
            'rows': []
        }
        
        for company in companies:
            data['rows'].append({
                'name': company.name,
                'industry': company.industry or '-',
                'status': company.get_status_display() if hasattr(company, 'get_status_display') else '-',
                'created_at': company.created_at.strftime('%Y-%m-%d'),
            })
        
        return data
    
    def _generate_order_data(self, template, instance):
        """生成订单数据"""
        from apps.order_management.models import Order
        
        orders = Order.objects.all()
        
        # 应用过滤条件
        filters = instance.filters_applied
        if filters.get('status'):
            orders = orders.filter(status=filters['status'])
        if filters.get('date_from'):
            orders = orders.filter(created_at__gte=filters['date_from'])
        if filters.get('date_to'):
            orders = orders.filter(created_at__lte=filters['date_to'])
        
        data = {
            'columns': [
                {'key': 'order_number', 'title': '订单号'},
                {'key': 'company_name', 'title': '企业名称'},
                {'key': 'service_name', 'title': '服务名称'},
                {'key': 'status', 'title': '状态'},
                {'key': 'total_amount', 'title': '订单金额'},
                {'key': 'created_at', 'title': '创建时间'},
            ],
            'rows': []
        }
        
        for order in orders:
            data['rows'].append({
                'order_number': order.order_number,
                'company_name': order.company.name,
                'service_name': order.service.name,
                'status': order.get_status_display(),
                'total_amount': float(order.total_amount),
                'created_at': order.created_at.strftime('%Y-%m-%d'),
            })
        
        return data
    
    def _generate_invoice_data(self, template, instance):
        """生成发票数据"""
        from apps.invoice_management.models import Invoice
        
        invoices = Invoice.objects.all()
        
        # 应用过滤条件
        filters = instance.filters_applied
        if filters.get('direction'):
            invoices = invoices.filter(direction=filters['direction'])
        if filters.get('status'):
            invoices = invoices.filter(status=filters['status'])
        if filters.get('date_from'):
            invoices = invoices.filter(issue_date__gte=filters['date_from'])
        if filters.get('date_to'):
            invoices = invoices.filter(issue_date__lte=filters['date_to'])
        
        data = {
            'columns': [
                {'key': 'invoice_number', 'title': '发票号码'},
                {'key': 'seller_name', 'title': '销售方'},
                {'key': 'buyer_name', 'title': '购买方'},
                {'key': 'direction', 'title': '方向'},
                {'key': 'status', 'title': '状态'},
                {'key': 'total_amount', 'title': '价税合计'},
                {'key': 'issue_date', 'title': '开票日期'},
            ],
            'rows': []
        }
        
        for invoice in invoices:
            data['rows'].append({
                'invoice_number': invoice.invoice_number,
                'seller_name': invoice.seller_name,
                'buyer_name': invoice.buyer_name,
                'direction': invoice.get_direction_display(),
                'status': invoice.get_status_display(),
                'total_amount': float(invoice.total_amount),
                'issue_date': invoice.issue_date.strftime('%Y-%m-%d'),
            })
        
        return data
    
    def _generate_finance_data(self, template, instance):
        """生成财务数据"""
        # 这里可以实现具体的财务数据生成逻辑
        return {
            'columns': [
                {'key': 'period', 'title': '期间'},
                {'key': 'revenue', 'title': '收入'},
                {'key': 'cost', 'title': '成本'},
                {'key': 'profit', 'title': '利润'},
            ],
            'rows': [
                {'period': '2024-01', 'revenue': 100000, 'cost': 60000, 'profit': 40000},
                {'period': '2024-02', 'revenue': 120000, 'cost': 70000, 'profit': 50000},
                {'period': '2024-03', 'revenue': 110000, 'cost': 65000, 'profit': 45000},
            ]
        }
    
    def _generate_tax_data(self, template, instance):
        """生成税务数据"""
        from apps.tax_calendar.models import TaxEvent
        
        events = TaxEvent.objects.all()
        
        data = {
            'columns': [
                {'key': 'title', 'title': '事件标题'},
                {'key': 'event_type', 'title': '事件类型'},
                {'key': 'due_date', 'title': '截止日期'},
                {'key': 'status', 'title': '状态'},
                {'key': 'company_name', 'title': '企业名称'},
            ],
            'rows': []
        }
        
        for event in events:
            data['rows'].append({
                'title': event.title,
                'event_type': event.get_event_type_display(),
                'due_date': event.due_date.strftime('%Y-%m-%d'),
                'status': event.get_status_display(),
                'company_name': event.company.name if event.company else '-',
            })
        
        return data
    
    def _generate_service_data(self, template, instance):
        """生成客服数据"""
        from apps.customer_service.models import ServiceTicket
        
        tickets = ServiceTicket.objects.all()
        
        data = {
            'columns': [
                {'key': 'ticket_number', 'title': '工单号'},
                {'key': 'title', 'title': '标题'},
                {'key': 'priority', 'title': '优先级'},
                {'key': 'status', 'title': '状态'},
                {'key': 'created_at', 'title': '创建时间'},
            ],
            'rows': []
        }
        
        for ticket in tickets:
            data['rows'].append({
                'ticket_number': ticket.ticket_number,
                'title': ticket.title,
                'priority': ticket.get_priority_display(),
                'status': ticket.get_status_display(),
                'created_at': ticket.created_at.strftime('%Y-%m-%d'),
            })
        
        return data
    
    def _generate_user_data(self, template, instance):
        """生成用户数据"""
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        users = User.objects.all()
        
        data = {
            'columns': [
                {'key': 'username', 'title': '用户名'},
                {'key': 'email', 'title': '邮箱'},
                {'key': 'is_active', 'title': '是否激活'},
                {'key': 'date_joined', 'title': '注册时间'},
            ],
            'rows': []
        }
        
        for user in users:
            data['rows'].append({
                'username': user.username,
                'email': user.email,
                'is_active': '是' if user.is_active else '否',
                'date_joined': user.date_joined.strftime('%Y-%m-%d'),
            })
        
        return data
    
    def _generate_custom_data(self, template, instance):
        """生成自定义数据"""
        # 这里可以执行自定义SQL查询
        return {
            'columns': [
                {'key': 'item', 'title': '项目'},
                {'key': 'value', 'title': '值'},
            ],
            'rows': [
                {'item': '示例数据1', 'value': 100},
                {'item': '示例数据2', 'value': 200},
            ]
        }


class ReportInstanceViewSet(viewsets.ReadOnlyModelViewSet):
    """报表实例ViewSet"""
    queryset = ReportInstance.objects.all()
    serializer_class = ReportInstanceSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user
        
        # 根据用户权限过滤
        if not user.is_staff:
            queryset = queryset.filter(
                Q(created_by=user) |
                Q(is_shared=True, shared_users=user)
            ).distinct()
        
        # 按模板过滤
        template_id = self.request.query_params.get('template')
        if template_id:
            queryset = queryset.filter(template_id=template_id)
        
        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        return queryset.order_by('-created_at')
    
    @action(detail=True, methods=['post'])
    def share(self, request, pk=None):
        """共享报表"""
        instance = self.get_object()
        
        if instance.created_by != request.user and not request.user.is_staff:
            return Response(
                {'error': '只有报表创建者可以共享报表'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        user_ids = request.data.get('user_ids', [])
        if user_ids:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            users = User.objects.filter(id__in=user_ids)
            instance.shared_users.set(users)
            instance.is_shared = True
            instance.save()
        
        return Response({'message': '报表共享成功'})
    
    @action(detail=True, methods=['post'])
    def export(self, request, pk=None):
        """导出报表"""
        instance = self.get_object()
        export_format = request.data.get('format', 'json')
        
        if export_format == 'excel':
            # 导出Excel格式
            return self._export_excel(instance)
        elif export_format == 'csv':
            # 导出CSV格式
            return self._export_csv(instance)
        elif export_format == 'pdf':
            # 导出PDF格式
            return self._export_pdf(instance)
        else:
            # 默认JSON格式
            return Response(instance.data)
    
    def _export_excel(self, instance):
        """导出Excel"""
        # 这里可以实现Excel导出逻辑
        return Response({'message': 'Excel导出功能开发中'})
    
    def _export_csv(self, instance):
        """导出CSV"""
        # 这里可以实现CSV导出逻辑
        return Response({'message': 'CSV导出功能开发中'})
    
    def _export_pdf(self, instance):
        """导出PDF"""
        # 这里可以实现PDF导出逻辑
        return Response({'message': 'PDF导出功能开发中'})


class DashboardViewSet(viewsets.ModelViewSet):
    """仪表板ViewSet"""
    queryset = Dashboard.objects.all()
    serializer_class = DashboardSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user

        # 根据用户权限过滤
        if not user.is_staff:
            queryset = queryset.filter(
                Q(is_public=True) |
                Q(allowed_users=user) |
                Q(created_by=user)
            ).distinct()

        return queryset.filter(is_active=True).order_by('sort_order', 'name')

    def get_serializer_class(self):
        if self.action == 'list':
            return DashboardListSerializer
        return DashboardSerializer

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['get'])
    def data(self, request, pk=None):
        """获取仪表板数据"""
        dashboard = self.get_object()

        # 获取所有组件的数据
        widget_data = {}

        for widget in dashboard.widgets:
            widget_id = widget.get('id')
            widget_type = widget.get('type')

            if widget_type == 'kpi':
                widget_data[widget_id] = self._get_kpi_widget_data(widget)
            elif widget_type == 'chart':
                widget_data[widget_id] = self._get_chart_widget_data(widget)
            elif widget_type == 'table':
                widget_data[widget_id] = self._get_table_widget_data(widget)
            elif widget_type == 'metric':
                widget_data[widget_id] = self._get_metric_widget_data(widget)

        return Response({
            'dashboard': DashboardSerializer(dashboard).data,
            'widget_data': widget_data,
            'last_updated': timezone.now()
        })

    def _get_kpi_widget_data(self, widget):
        """获取KPI组件数据"""
        metric_ids = widget.get('config', {}).get('metric_ids', [])

        if not metric_ids:
            return {'error': '未配置KPI指标'}

        kpi_data = []
        for metric_id in metric_ids:
            try:
                metric = KPIMetric.objects.get(id=metric_id)
                latest_value = metric.values.first()

                if latest_value:
                    kpi_data.append({
                        'metric_id': metric.id,
                        'metric_name': metric.name,
                        'value': latest_value.value,
                        'target_value': latest_value.target_value,
                        'unit': metric.unit,
                        'performance_status': latest_value.get_performance_status(),
                        'period': latest_value.period_start.date()
                    })
            except KPIMetric.DoesNotExist:
                continue

        return {'kpis': kpi_data}

    def _get_chart_widget_data(self, widget):
        """获取图表组件数据"""
        chart_config = widget.get('config', {})
        data_source = chart_config.get('data_source')

        if data_source == 'orders':
            return self._get_order_chart_data(chart_config)
        elif data_source == 'invoices':
            return self._get_invoice_chart_data(chart_config)
        elif data_source == 'finance':
            return self._get_finance_chart_data(chart_config)
        else:
            return {'error': '不支持的数据源'}

    def _get_order_chart_data(self, config):
        """获取订单图表数据"""
        from apps.order_management.models import Order

        # 按月统计订单数量
        orders = Order.objects.all()

        # 最近12个月的数据
        months = []
        counts = []

        for i in range(12):
            month_start = timezone.now().replace(day=1) - timedelta(days=30 * i)
            month_end = month_start.replace(day=1) + timedelta(days=32)
            month_end = month_end.replace(day=1) - timedelta(days=1)

            month_orders = orders.filter(
                created_at__gte=month_start,
                created_at__lte=month_end
            ).count()

            months.insert(0, month_start.strftime('%Y-%m'))
            counts.insert(0, month_orders)

        return {
            'labels': months,
            'datasets': [{
                'label': '订单数量',
                'data': counts,
                'backgroundColor': '#1890ff'
            }]
        }

    def _get_invoice_chart_data(self, config):
        """获取发票图表数据"""
        from apps.invoice_management.models import Invoice

        # 按状态统计发票数量
        status_counts = {}
        for status_code, status_name in Invoice.STATUS_CHOICES:
            count = Invoice.objects.filter(status=status_code).count()
            status_counts[status_name] = count

        return {
            'labels': list(status_counts.keys()),
            'datasets': [{
                'label': '发票数量',
                'data': list(status_counts.values()),
                'backgroundColor': [
                    '#1890ff', '#52c41a', '#faad14', '#f5222d',
                    '#722ed1', '#fa8c16', '#13c2c2'
                ]
            }]
        }

    def _get_finance_chart_data(self, config):
        """获取财务图表数据"""
        # 模拟财务数据
        return {
            'labels': ['收入', '成本', '利润'],
            'datasets': [{
                'label': '金额(万元)',
                'data': [120, 80, 40],
                'backgroundColor': ['#52c41a', '#faad14', '#1890ff']
            }]
        }

    def _get_table_widget_data(self, widget):
        """获取表格组件数据"""
        table_config = widget.get('config', {})
        data_source = table_config.get('data_source')

        if data_source == 'recent_orders':
            return self._get_recent_orders_data()
        elif data_source == 'pending_tasks':
            return self._get_pending_tasks_data()
        else:
            return {'error': '不支持的数据源'}

    def _get_recent_orders_data(self):
        """获取最近订单数据"""
        from apps.order_management.models import Order

        orders = Order.objects.all().order_by('-created_at')[:10]

        return {
            'columns': [
                {'key': 'order_number', 'title': '订单号'},
                {'key': 'company_name', 'title': '企业'},
                {'key': 'status', 'title': '状态'},
                {'key': 'created_at', 'title': '创建时间'},
            ],
            'rows': [
                {
                    'order_number': order.order_number,
                    'company_name': order.company.name,
                    'status': order.get_status_display(),
                    'created_at': order.created_at.strftime('%Y-%m-%d'),
                }
                for order in orders
            ]
        }

    def _get_pending_tasks_data(self):
        """获取待处理任务数据"""
        from apps.tax_calendar.models import TaxEvent

        events = TaxEvent.objects.filter(
            status='pending'
        ).order_by('due_date')[:10]

        return {
            'columns': [
                {'key': 'title', 'title': '任务'},
                {'key': 'due_date', 'title': '截止日期'},
                {'key': 'priority', 'title': '优先级'},
            ],
            'rows': [
                {
                    'title': event.title,
                    'due_date': event.due_date.strftime('%Y-%m-%d'),
                    'priority': event.get_priority_display() if hasattr(event, 'get_priority_display') else '普通',
                }
                for event in events
            ]
        }

    def _get_metric_widget_data(self, widget):
        """获取指标组件数据"""
        metric_config = widget.get('config', {})
        metric_type = metric_config.get('metric_type')

        if metric_type == 'total_companies':
            from apps.companies.models import Company
            return {'value': Company.objects.count(), 'label': '企业总数'}
        elif metric_type == 'total_orders':
            from apps.order_management.models import Order
            return {'value': Order.objects.count(), 'label': '订单总数'}
        elif metric_type == 'total_invoices':
            from apps.invoice_management.models import Invoice
            return {'value': Invoice.objects.count(), 'label': '发票总数'}
        elif metric_type == 'active_users':
            from django.contrib.auth import get_user_model
            User = get_user_model()
            return {'value': User.objects.filter(is_active=True).count(), 'label': '活跃用户'}
        else:
            return {'value': 0, 'label': '未知指标'}


class DataSourceViewSet(viewsets.ModelViewSet):
    """数据源ViewSet"""
    queryset = DataSource.objects.all()
    serializer_class = DataSourceSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset.filter(is_active=True).order_by('name')

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def test_connection(self, request, pk=None):
        """测试数据源连接"""
        data_source = self.get_object()

        try:
            # 这里可以实现具体的连接测试逻辑
            success = self._test_data_source_connection(data_source)

            if success:
                return Response({'message': '连接测试成功'})
            else:
                return Response(
                    {'error': '连接测试失败'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            return Response(
                {'error': f'连接测试失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _test_data_source_connection(self, data_source):
        """测试数据源连接"""
        # 这里可以根据不同的数据源类型实现具体的连接测试
        if data_source.source_type == 'database':
            return self._test_database_connection(data_source)
        elif data_source.source_type == 'api':
            return self._test_api_connection(data_source)
        else:
            return True  # 其他类型暂时返回成功

    def _test_database_connection(self, data_source):
        """测试数据库连接"""
        # 这里可以实现数据库连接测试
        return True

    def _test_api_connection(self, data_source):
        """测试API连接"""
        # 这里可以实现API连接测试
        return True


class KPIMetricViewSet(viewsets.ModelViewSet):
    """KPI指标ViewSet"""
    queryset = KPIMetric.objects.all()
    serializer_class = KPIMetricSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user

        # 根据用户权限过滤
        if not user.is_staff:
            queryset = queryset.filter(
                Q(is_public=True) |
                Q(created_by=user)
            )

        # 按分类过滤
        category_id = self.request.query_params.get('category')
        if category_id:
            queryset = queryset.filter(category_id=category_id)

        # 按指标类型过滤
        metric_type = self.request.query_params.get('metric_type')
        if metric_type:
            queryset = queryset.filter(metric_type=metric_type)

        return queryset.filter(is_active=True).order_by('name')

    def get_serializer_class(self):
        if self.action == 'list':
            return KPIMetricListSerializer
        return KPIMetricSerializer

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def calculate(self, request, pk=None):
        """计算KPI指标值"""
        metric = self.get_object()

        serializer = KPICalculationSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data

        try:
            # 计算指标值
            value = self._calculate_kpi_value(
                metric,
                data['period_start'],
                data['period_end'],
                data.get('dimensions', {})
            )

            # 保存指标值
            kpi_value = KPIValue.objects.create(
                metric=metric,
                period_start=data['period_start'],
                period_end=data['period_end'],
                period_type=metric.aggregation_period,
                dimensions=data.get('dimensions', {}),
                value=value,
                target_value=metric.target_value,
                calculation_details={'formula': metric.calculation_formula}
            )

            return Response({
                'message': 'KPI计算成功',
                'value': value,
                'kpi_value_id': kpi_value.id
            })

        except Exception as e:
            return Response(
                {'error': f'KPI计算失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _calculate_kpi_value(self, metric, period_start, period_end, dimensions):
        """计算KPI指标值"""
        # 根据指标类型和数据源计算值
        if metric.data_source.name == 'orders':
            return self._calculate_order_kpi(metric, period_start, period_end, dimensions)
        elif metric.data_source.name == 'invoices':
            return self._calculate_invoice_kpi(metric, period_start, period_end, dimensions)
        elif metric.data_source.name == 'companies':
            return self._calculate_company_kpi(metric, period_start, period_end, dimensions)
        else:
            # 默认计算逻辑
            return Decimal('0')

    def _calculate_order_kpi(self, metric, period_start, period_end, dimensions):
        """计算订单相关KPI"""
        from apps.order_management.models import Order

        orders = Order.objects.filter(
            created_at__gte=period_start,
            created_at__lte=period_end
        )

        if metric.metric_type == 'count':
            return Decimal(str(orders.count()))
        elif metric.metric_type == 'sum':
            return orders.aggregate(total=Sum('total_amount'))['total'] or Decimal('0')
        elif metric.metric_type == 'avg':
            avg_value = orders.aggregate(avg=Avg('total_amount'))['avg']
            return Decimal(str(avg_value)) if avg_value else Decimal('0')
        else:
            return Decimal('0')

    def _calculate_invoice_kpi(self, metric, period_start, period_end, dimensions):
        """计算发票相关KPI"""
        from apps.invoice_management.models import Invoice

        invoices = Invoice.objects.filter(
            issue_date__gte=period_start.date(),
            issue_date__lte=period_end.date()
        )

        if metric.metric_type == 'count':
            return Decimal(str(invoices.count()))
        elif metric.metric_type == 'sum':
            return invoices.aggregate(total=Sum('total_amount'))['total'] or Decimal('0')
        elif metric.metric_type == 'avg':
            avg_value = invoices.aggregate(avg=Avg('total_amount'))['avg']
            return Decimal(str(avg_value)) if avg_value else Decimal('0')
        else:
            return Decimal('0')

    def _calculate_company_kpi(self, metric, period_start, period_end, dimensions):
        """计算企业相关KPI"""
        from apps.companies.models import Company

        companies = Company.objects.filter(
            created_at__gte=period_start,
            created_at__lte=period_end
        )

        if metric.metric_type == 'count':
            return Decimal(str(companies.count()))
        else:
            return Decimal('0')

    @action(detail=False, methods=['post'])
    def batch_calculate(self, request):
        """批量计算KPI"""
        serializer = KPICalculationSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data
        metric_ids = data['metric_ids']

        results = []
        errors = []

        for metric_id in metric_ids:
            try:
                metric = KPIMetric.objects.get(id=metric_id)

                # 检查是否需要重新计算
                if not data.get('force_recalculate', False):
                    existing = KPIValue.objects.filter(
                        metric=metric,
                        period_start=data['period_start'],
                        period_end=data['period_end']
                    ).first()

                    if existing:
                        results.append({
                            'metric_id': metric_id,
                            'metric_name': metric.name,
                            'value': existing.value,
                            'status': 'existing'
                        })
                        continue

                # 计算新值
                value = self._calculate_kpi_value(
                    metric,
                    data['period_start'],
                    data['period_end'],
                    data.get('dimensions', {})
                )

                # 保存指标值
                kpi_value = KPIValue.objects.create(
                    metric=metric,
                    period_start=data['period_start'],
                    period_end=data['period_end'],
                    period_type=metric.aggregation_period,
                    dimensions=data.get('dimensions', {}),
                    value=value,
                    target_value=metric.target_value,
                    calculation_details={'formula': metric.calculation_formula}
                )

                results.append({
                    'metric_id': metric_id,
                    'metric_name': metric.name,
                    'value': value,
                    'status': 'calculated'
                })

            except KPIMetric.DoesNotExist:
                errors.append(f'KPI指标 {metric_id} 不存在')
            except Exception as e:
                errors.append(f'KPI指标 {metric_id} 计算失败: {str(e)}')

        return Response({
            'message': f'批量计算完成，成功 {len(results)} 个，失败 {len(errors)} 个',
            'results': results,
            'errors': errors
        })


class KPIValueViewSet(viewsets.ReadOnlyModelViewSet):
    """KPI指标值ViewSet"""
    queryset = KPIValue.objects.all()
    serializer_class = KPIValueSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按指标过滤
        metric_id = self.request.query_params.get('metric')
        if metric_id:
            queryset = queryset.filter(metric_id=metric_id)

        # 按时间范围过滤
        period_start = self.request.query_params.get('period_start')
        if period_start:
            queryset = queryset.filter(period_start__gte=period_start)

        period_end = self.request.query_params.get('period_end')
        if period_end:
            queryset = queryset.filter(period_end__lte=period_end)

        return queryset.order_by('-period_start')

    @action(detail=False, methods=['get'])
    def trend(self, request):
        """获取KPI趋势数据"""
        metric_id = request.query_params.get('metric_id')
        if not metric_id:
            return Response(
                {'error': '请提供metric_id参数'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            metric = KPIMetric.objects.get(id=metric_id)
        except KPIMetric.DoesNotExist:
            return Response(
                {'error': 'KPI指标不存在'},
                status=status.HTTP_404_NOT_FOUND
            )

        # 获取最近12个周期的数据
        values = KPIValue.objects.filter(metric=metric).order_by('-period_start')[:12]

        trend_data = {
            'metric': KPIMetricSerializer(metric).data,
            'periods': [],
            'values': [],
            'targets': [],
            'performance_status': []
        }

        for value in reversed(values):
            trend_data['periods'].append(value.period_start.strftime('%Y-%m-%d'))
            trend_data['values'].append(float(value.value))
            trend_data['targets'].append(float(value.target_value) if value.target_value else None)
            trend_data['performance_status'].append(value.get_performance_status())

        return Response(trend_data)


class AnalyticsJobViewSet(viewsets.ModelViewSet):
    """分析任务ViewSet"""
    queryset = AnalyticsJob.objects.all()
    serializer_class = AnalyticsJobSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按任务类型过滤
        job_type = self.request.query_params.get('job_type')
        if job_type:
            queryset = queryset.filter(job_type=job_type)

        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        return queryset.order_by('-created_at')

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def run(self, request, pk=None):
        """运行分析任务"""
        job = self.get_object()

        if job.status not in ['pending', 'failed']:
            return Response(
                {'error': '任务状态不允许运行'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # 更新任务状态
            job.status = 'running'
            job.started_at = timezone.now()
            job.save()

            # 执行任务
            result = self._execute_job(job)

            # 更新任务结果
            job.status = 'completed'
            job.completed_at = timezone.now()
            job.result = result
            job.execution_time = (job.completed_at - job.started_at).total_seconds()
            job.save()

            return Response({
                'message': '任务执行成功',
                'result': result
            })

        except Exception as e:
            job.status = 'failed'
            job.error_message = str(e)
            job.completed_at = timezone.now()
            job.save()

            return Response(
                {'error': f'任务执行失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _execute_job(self, job):
        """执行分析任务"""
        if job.job_type == 'report_generation':
            return self._execute_report_generation(job)
        elif job.job_type == 'data_sync':
            return self._execute_data_sync(job)
        elif job.job_type == 'kpi_calculation':
            return self._execute_kpi_calculation(job)
        elif job.job_type == 'data_cleaning':
            return self._execute_data_cleaning(job)
        else:
            return {'message': '任务类型不支持'}

    def _execute_report_generation(self, job):
        """执行报表生成任务"""
        # 这里可以实现报表生成逻辑
        return {'message': '报表生成完成', 'reports_generated': 1}

    def _execute_data_sync(self, job):
        """执行数据同步任务"""
        # 这里可以实现数据同步逻辑
        return {'message': '数据同步完成', 'records_synced': 100}

    def _execute_kpi_calculation(self, job):
        """执行KPI计算任务"""
        # 这里可以实现KPI计算逻辑
        return {'message': 'KPI计算完成', 'kpis_calculated': 10}

    def _execute_data_cleaning(self, job):
        """执行数据清洗任务"""
        # 这里可以实现数据清洗逻辑
        return {'message': '数据清洗完成', 'records_cleaned': 50}


class DataQualityRuleViewSet(viewsets.ModelViewSet):
    """数据质量规则ViewSet"""
    queryset = DataQualityRule.objects.all()
    serializer_class = DataQualityRuleSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按规则类型过滤
        rule_type = self.request.query_params.get('rule_type')
        if rule_type:
            queryset = queryset.filter(rule_type=rule_type)

        # 按数据源过滤
        data_source_id = self.request.query_params.get('data_source')
        if data_source_id:
            queryset = queryset.filter(data_source_id=data_source_id)

        # 按严重程度过滤
        severity = self.request.query_params.get('severity')
        if severity:
            queryset = queryset.filter(severity=severity)

        return queryset.filter(is_active=True).order_by('name')

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def check(self, request, pk=None):
        """执行数据质量检查"""
        rule = self.get_object()

        try:
            # 执行质量检查
            result = self._execute_quality_check(rule)

            # 保存检查结果
            quality_result = DataQualityResult.objects.create(
                rule=rule,
                check_time=timezone.now(),
                data_period_start=result['period_start'],
                data_period_end=result['period_end'],
                passed=result['passed'],
                score=result['score'],
                total_records=result['total_records'],
                valid_records=result['valid_records'],
                invalid_records=result['invalid_records'],
                details=result['details'],
                sample_errors=result['sample_errors']
            )

            return Response({
                'message': '数据质量检查完成',
                'result_id': quality_result.id,
                'passed': result['passed'],
                'score': result['score']
            })

        except Exception as e:
            return Response(
                {'error': f'数据质量检查失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _execute_quality_check(self, rule):
        """执行数据质量检查"""
        # 模拟数据质量检查逻辑
        import random

        total_records = random.randint(100, 1000)
        invalid_records = random.randint(0, total_records // 10)
        valid_records = total_records - invalid_records

        score = (valid_records / total_records) * 100 if total_records > 0 else 0
        passed = score >= 80  # 80分以上算通过

        return {
            'period_start': timezone.now() - timedelta(days=1),
            'period_end': timezone.now(),
            'passed': passed,
            'score': Decimal(str(round(score, 2))),
            'total_records': total_records,
            'valid_records': valid_records,
            'invalid_records': invalid_records,
            'details': {
                'rule_type': rule.rule_type,
                'table_name': rule.table_name,
                'column_name': rule.column_name,
                'check_expression': rule.rule_expression
            },
            'sample_errors': [
                {'row_id': i, 'error': f'示例错误{i}'}
                for i in range(min(invalid_records, 5))
            ]
        }

    @action(detail=False, methods=['post'])
    def batch_check(self, request):
        """批量执行数据质量检查"""
        rule_ids = request.data.get('rule_ids', [])

        if not rule_ids:
            return Response(
                {'error': '请提供rule_ids参数'},
                status=status.HTTP_400_BAD_REQUEST
            )

        results = []
        errors = []

        for rule_id in rule_ids:
            try:
                rule = DataQualityRule.objects.get(id=rule_id)

                # 执行质量检查
                result = self._execute_quality_check(rule)

                # 保存检查结果
                quality_result = DataQualityResult.objects.create(
                    rule=rule,
                    check_time=timezone.now(),
                    data_period_start=result['period_start'],
                    data_period_end=result['period_end'],
                    passed=result['passed'],
                    score=result['score'],
                    total_records=result['total_records'],
                    valid_records=result['valid_records'],
                    invalid_records=result['invalid_records'],
                    details=result['details'],
                    sample_errors=result['sample_errors']
                )

                results.append({
                    'rule_id': rule_id,
                    'rule_name': rule.name,
                    'passed': result['passed'],
                    'score': result['score'],
                    'result_id': quality_result.id
                })

            except DataQualityRule.DoesNotExist:
                errors.append(f'数据质量规则 {rule_id} 不存在')
            except Exception as e:
                errors.append(f'数据质量规则 {rule_id} 检查失败: {str(e)}')

        return Response({
            'message': f'批量检查完成，成功 {len(results)} 个，失败 {len(errors)} 个',
            'results': results,
            'errors': errors
        })


class DataQualityResultViewSet(viewsets.ReadOnlyModelViewSet):
    """数据质量结果ViewSet"""
    queryset = DataQualityResult.objects.all()
    serializer_class = DataQualityResultSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按规则过滤
        rule_id = self.request.query_params.get('rule')
        if rule_id:
            queryset = queryset.filter(rule_id=rule_id)

        # 按检查结果过滤
        passed = self.request.query_params.get('passed')
        if passed is not None:
            queryset = queryset.filter(passed=passed.lower() == 'true')

        # 按时间范围过滤
        check_time_from = self.request.query_params.get('check_time_from')
        if check_time_from:
            queryset = queryset.filter(check_time__gte=check_time_from)

        check_time_to = self.request.query_params.get('check_time_to')
        if check_time_to:
            queryset = queryset.filter(check_time__lte=check_time_to)

        return queryset.order_by('-check_time')

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """获取数据质量汇总"""
        # 总体统计
        total_checks = DataQualityResult.objects.count()
        passed_checks = DataQualityResult.objects.filter(passed=True).count()
        failed_checks = total_checks - passed_checks

        pass_rate = (passed_checks / total_checks * 100) if total_checks > 0 else 0

        # 按规则类型统计
        rule_type_stats = {}
        for rule_type, rule_type_name in DataQualityRule.RULE_TYPES:
            rule_count = DataQualityResult.objects.filter(
                rule__rule_type=rule_type
            ).count()
            passed_count = DataQualityResult.objects.filter(
                rule__rule_type=rule_type,
                passed=True
            ).count()

            rule_type_stats[rule_type_name] = {
                'total': rule_count,
                'passed': passed_count,
                'failed': rule_count - passed_count,
                'pass_rate': (passed_count / rule_count * 100) if rule_count > 0 else 0
            }

        # 按严重程度统计
        severity_stats = {}
        for severity, severity_name in DataQualityRule.SEVERITY_LEVELS:
            rule_count = DataQualityResult.objects.filter(
                rule__severity=severity
            ).count()
            passed_count = DataQualityResult.objects.filter(
                rule__severity=severity,
                passed=True
            ).count()

            severity_stats[severity_name] = {
                'total': rule_count,
                'passed': passed_count,
                'failed': rule_count - passed_count,
                'pass_rate': (passed_count / rule_count * 100) if rule_count > 0 else 0
            }

        # 最近趋势
        recent_results = DataQualityResult.objects.filter(
            check_time__gte=timezone.now() - timedelta(days=30)
        ).order_by('check_time')

        trend_data = []
        for result in recent_results:
            trend_data.append({
                'date': result.check_time.date(),
                'score': float(result.score),
                'passed': result.passed
            })

        return Response({
            'overview': {
                'total_checks': total_checks,
                'passed_checks': passed_checks,
                'failed_checks': failed_checks,
                'pass_rate': round(pass_rate, 2)
            },
            'rule_type_stats': rule_type_stats,
            'severity_stats': severity_stats,
            'trend_data': trend_data
        })

    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """数据质量仪表板"""
        # 获取最新的质量评分
        latest_results = DataQualityResult.objects.order_by('-check_time')[:10]

        # 计算平均质量评分
        avg_score = DataQualityResult.objects.aggregate(
            avg_score=Avg('score')
        )['avg_score'] or 0

        # 获取问题最多的规则
        problem_rules = DataQualityRule.objects.annotate(
            failed_count=Count('results', filter=Q(results__passed=False))
        ).order_by('-failed_count')[:5]

        # 数据质量趋势
        trend_data = []
        for i in range(7):
            date = timezone.now().date() - timedelta(days=i)
            day_results = DataQualityResult.objects.filter(
                check_time__date=date
            )

            if day_results.exists():
                avg_score = day_results.aggregate(avg=Avg('score'))['avg']
                pass_rate = day_results.filter(passed=True).count() / day_results.count() * 100
            else:
                avg_score = 0
                pass_rate = 0

            trend_data.insert(0, {
                'date': date.strftime('%Y-%m-%d'),
                'avg_score': round(float(avg_score), 2),
                'pass_rate': round(pass_rate, 2)
            })

        return Response({
            'avg_score': round(float(avg_score), 2),
            'latest_results': DataQualityResultSerializer(latest_results, many=True).data,
            'problem_rules': [
                {
                    'id': rule.id,
                    'name': rule.name,
                    'rule_type': rule.get_rule_type_display(),
                    'failed_count': rule.failed_count
                }
                for rule in problem_rules
            ],
            'trend_data': trend_data
        })

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.db.models import Count, Avg
from .models import (
    ReportCategory, ReportTemplate, ReportInstance, Dashboard,
    DataSource, KPIMetric, KPIValue, AnalyticsJob,
    DataQualityRule, DataQualityResult
)


@admin.register(ReportCategory)
class ReportCategoryAdmin(admin.ModelAdmin):
    """报表分类管理"""
    list_display = ['name', 'parent', 'icon', 'sort_order', 'is_active', 'created_at']
    list_filter = ['is_active', 'parent', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'parent')
        }),
        ('显示设置', {
            'fields': ('icon', 'sort_order', 'is_active')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ReportTemplate)
class ReportTemplateAdmin(admin.ModelAdmin):
    """报表模板管理"""
    list_display = [
        'name', 'category', 'report_type_display', 'data_source_display',
        'is_public', 'cache_duration', 'is_active', 'created_by'
    ]
    list_filter = ['report_type', 'data_source', 'is_public', 'is_active', 'category']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    filter_horizontal = ['allowed_users']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'category')
        }),
        ('报表配置', {
            'fields': ('report_type', 'chart_type', 'data_source')
        }),
        ('查询配置', {
            'fields': ('sql_query', 'query_params', 'filters'),
            'classes': ('wide',)
        }),
        ('显示配置', {
            'fields': ('columns', 'chart_config', 'layout_config'),
            'classes': ('wide',)
        }),
        ('权限配置', {
            'fields': ('is_public', 'allowed_roles', 'allowed_users')
        }),
        ('缓存配置', {
            'fields': ('cache_duration', 'auto_refresh', 'refresh_interval')
        }),
        ('状态', {
            'fields': ('is_active',)
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def report_type_display(self, obj):
        """报表类型显示"""
        type_colors = {
            'table': '#1890ff',
            'chart': '#52c41a',
            'dashboard': '#722ed1',
            'kpi': '#fa8c16',
            'trend': '#13c2c2',
            'comparison': '#eb2f96',
        }
        color = type_colors.get(obj.report_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_report_type_display()
        )
    report_type_display.short_description = '报表类型'
    
    def data_source_display(self, obj):
        """数据源显示"""
        source_colors = {
            'companies': '#52c41a',
            'orders': '#1890ff',
            'invoices': '#faad14',
            'finance': '#722ed1',
            'tax': '#fa8c16',
            'service': '#13c2c2',
            'users': '#eb2f96',
            'custom': '#d9d9d9',
        }
        color = source_colors.get(obj.data_source, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_data_source_display()
        )
    data_source_display.short_description = '数据源'
    
    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(ReportInstance)
class ReportInstanceAdmin(admin.ModelAdmin):
    """报表实例管理"""
    list_display = [
        'name', 'template_info', 'status_display', 'data_rows',
        'generation_time_display', 'created_by', 'created_at'
    ]
    list_filter = ['status', 'template', 'created_at', 'is_shared']
    search_fields = ['name', 'template__name']
    readonly_fields = ['generated_at', 'expires_at', 'data_rows', 'generation_time', 'file_size', 'created_at']
    filter_horizontal = ['shared_users']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('template', 'name')
        }),
        ('参数配置', {
            'fields': ('parameters', 'filters_applied'),
            'classes': ('wide',)
        }),
        ('生成信息', {
            'fields': ('status', 'generated_at', 'expires_at')
        }),
        ('数据结果', {
            'fields': ('data', 'metadata', 'file_path'),
            'classes': ('collapse',)
        }),
        ('统计信息', {
            'fields': ('data_rows', 'generation_time', 'file_size')
        }),
        ('共享设置', {
            'fields': ('is_shared', 'shared_users')
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at'),
            'classes': ('collapse',)
        }),
    )
    
    def template_info(self, obj):
        """模板信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:analytics_advanced_reporttemplate_change', args=[obj.template.pk]),
            obj.template.name
        )
    template_info.short_description = '报表模板'
    
    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'pending': '#faad14',
            'generating': '#1890ff',
            'completed': '#52c41a',
            'failed': '#f5222d',
            'expired': '#d9d9d9',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        
        # 检查是否过期
        expired_text = ''
        if obj.is_expired():
            expired_text = ' (已过期)'
            color = '#d9d9d9'
        
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}{}',
            color, obj.get_status_display(), expired_text
        )
    status_display.short_description = '状态'
    
    def generation_time_display(self, obj):
        """生成时间显示"""
        if obj.generation_time > 0:
            return f"{obj.generation_time:.2f}秒"
        return '-'
    generation_time_display.short_description = '生成耗时'


@admin.register(Dashboard)
class DashboardAdmin(admin.ModelAdmin):
    """仪表板管理"""
    list_display = [
        'name', 'is_default', 'auto_refresh', 'refresh_interval_display',
        'is_public', 'sort_order', 'is_active', 'created_by'
    ]
    list_filter = ['is_default', 'auto_refresh', 'is_public', 'is_active']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    filter_horizontal = ['allowed_users']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description')
        }),
        ('布局配置', {
            'fields': ('layout', 'widgets', 'theme'),
            'classes': ('wide',)
        }),
        ('刷新配置', {
            'fields': ('auto_refresh', 'refresh_interval')
        }),
        ('权限配置', {
            'fields': ('is_public', 'allowed_roles', 'allowed_users')
        }),
        ('显示配置', {
            'fields': ('is_default', 'sort_order', 'is_active')
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def refresh_interval_display(self, obj):
        """刷新间隔显示"""
        if obj.auto_refresh:
            return f"{obj.refresh_interval}秒"
        return '不自动刷新'
    refresh_interval_display.short_description = '刷新间隔'
    
    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(DataSource)
class DataSourceAdmin(admin.ModelAdmin):
    """数据源管理"""
    list_display = [
        'name', 'source_type_display', 'connection_type_display',
        'connection_pool_size', 'cache_enabled', 'is_active', 'created_by'
    ]
    list_filter = ['source_type', 'connection_type', 'cache_enabled', 'is_active']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'source_type', 'connection_type')
        }),
        ('连接配置', {
            'fields': ('connection_config', 'authentication'),
            'classes': ('wide',)
        }),
        ('数据配置', {
            'fields': ('schema_info', 'table_mappings'),
            'classes': ('wide',)
        }),
        ('性能配置', {
            'fields': ('connection_pool_size', 'query_timeout', 'cache_enabled')
        }),
        ('状态', {
            'fields': ('is_active',)
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def source_type_display(self, obj):
        """数据源类型显示"""
        type_colors = {
            'database': '#1890ff',
            'api': '#52c41a',
            'file': '#faad14',
            'external': '#722ed1',
        }
        color = type_colors.get(obj.source_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_source_type_display()
        )
    source_type_display.short_description = '数据源类型'
    
    def connection_type_display(self, obj):
        """连接类型显示"""
        return obj.get_connection_type_display()
    connection_type_display.short_description = '连接类型'
    
    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(KPIMetric)
class KPIMetricAdmin(admin.ModelAdmin):
    """KPI指标管理"""
    list_display = [
        'name', 'category', 'metric_type_display', 'aggregation_period_display',
        'target_value', 'unit', 'is_public', 'is_active'
    ]
    list_filter = ['metric_type', 'aggregation_period', 'category', 'is_public', 'is_active']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'category')
        }),
        ('指标配置', {
            'fields': ('metric_type', 'data_source', 'calculation_formula')
        }),
        ('聚合配置', {
            'fields': ('aggregation_period', 'aggregation_field')
        }),
        ('目标和阈值', {
            'fields': ('target_value', 'warning_threshold', 'critical_threshold')
        }),
        ('显示配置', {
            'fields': ('unit', 'decimal_places', 'display_format')
        }),
        ('权限配置', {
            'fields': ('is_public', 'allowed_roles')
        }),
        ('状态', {
            'fields': ('is_active',)
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def metric_type_display(self, obj):
        """指标类型显示"""
        type_colors = {
            'count': '#1890ff',
            'sum': '#52c41a',
            'avg': '#faad14',
            'max': '#f5222d',
            'min': '#13c2c2',
            'ratio': '#722ed1',
            'percentage': '#eb2f96',
            'growth': '#fa8c16',
        }
        color = type_colors.get(obj.metric_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_metric_type_display()
        )
    metric_type_display.short_description = '指标类型'
    
    def aggregation_period_display(self, obj):
        """聚合周期显示"""
        return obj.get_aggregation_period_display()
    aggregation_period_display.short_description = '聚合周期'
    
    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(KPIValue)
class KPIValueAdmin(admin.ModelAdmin):
    """KPI指标值管理"""
    list_display = [
        'metric_info', 'period_display', 'value_display',
        'target_value_display', 'performance_status_display', 'data_quality_score'
    ]
    list_filter = ['metric', 'period_type', 'period_start']
    search_fields = ['metric__name']
    readonly_fields = ['created_at']
    date_hierarchy = 'period_start'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('metric',)
        }),
        ('时间维度', {
            'fields': ('period_start', 'period_end', 'period_type')
        }),
        ('维度信息', {
            'fields': ('dimensions',),
            'classes': ('wide',)
        }),
        ('指标值', {
            'fields': ('value', 'target_value')
        }),
        ('计算信息', {
            'fields': ('calculation_details', 'data_quality_score'),
            'classes': ('collapse',)
        }),
        ('创建时间', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )
    
    def metric_info(self, obj):
        """指标信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:analytics_advanced_kpimetric_change', args=[obj.metric.pk]),
            obj.metric.name
        )
    metric_info.short_description = 'KPI指标'
    
    def period_display(self, obj):
        """周期显示"""
        return f"{obj.period_start.date()} ~ {obj.period_end.date()}"
    period_display.short_description = '统计周期'
    
    def value_display(self, obj):
        """指标值显示"""
        unit = obj.metric.unit or ''
        return f"{obj.value}{unit}"
    value_display.short_description = '指标值'
    
    def target_value_display(self, obj):
        """目标值显示"""
        if obj.target_value:
            unit = obj.metric.unit or ''
            return f"{obj.target_value}{unit}"
        return '-'
    target_value_display.short_description = '目标值'
    
    def performance_status_display(self, obj):
        """绩效状态显示"""
        status = obj.get_performance_status()
        status_colors = {
            'excellent': '#52c41a',
            'good': '#1890ff',
            'warning': '#faad14',
            'poor': '#f5222d',
            'no_target': '#d9d9d9',
        }
        status_names = {
            'excellent': '优秀',
            'good': '良好',
            'warning': '警告',
            'poor': '较差',
            'no_target': '无目标',
        }
        
        color = status_colors.get(status, '#d9d9d9')
        name = status_names.get(status, '未知')
        
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, name
        )
    performance_status_display.short_description = '绩效状态'


@admin.register(AnalyticsJob)
class AnalyticsJobAdmin(admin.ModelAdmin):
    """分析任务管理"""
    list_display = [
        'name', 'job_type_display', 'status_display', 'is_scheduled',
        'execution_time_display', 'next_run_time', 'created_by'
    ]
    list_filter = ['job_type', 'status', 'is_scheduled', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['started_at', 'completed_at', 'execution_time', 'memory_usage', 'cpu_usage', 'created_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'job_type', 'description')
        }),
        ('任务配置', {
            'fields': ('config', 'parameters'),
            'classes': ('wide',)
        }),
        ('调度配置', {
            'fields': ('is_scheduled', 'schedule_expression', 'next_run_time')
        }),
        ('执行状态', {
            'fields': ('status', 'started_at', 'completed_at')
        }),
        ('执行结果', {
            'fields': ('result', 'error_message', 'logs'),
            'classes': ('collapse',)
        }),
        ('性能统计', {
            'fields': ('execution_time', 'memory_usage', 'cpu_usage'),
            'classes': ('collapse',)
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['run_jobs', 'cancel_jobs']

    def job_type_display(self, obj):
        """任务类型显示"""
        type_colors = {
            'report_generation': '#1890ff',
            'data_sync': '#52c41a',
            'kpi_calculation': '#faad14',
            'data_cleaning': '#722ed1',
            'model_training': '#fa8c16',
            'prediction': '#eb2f96',
        }
        color = type_colors.get(obj.job_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_job_type_display()
        )
    job_type_display.short_description = '任务类型'

    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'pending': '#faad14',
            'running': '#1890ff',
            'completed': '#52c41a',
            'failed': '#f5222d',
            'cancelled': '#d9d9d9',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '状态'

    def execution_time_display(self, obj):
        """执行时间显示"""
        if obj.execution_time > 0:
            if obj.execution_time < 60:
                return f"{obj.execution_time:.2f}秒"
            else:
                minutes = int(obj.execution_time // 60)
                seconds = obj.execution_time % 60
                return f"{minutes}分{seconds:.1f}秒"
        return '-'
    execution_time_display.short_description = '执行时间'

    def run_jobs(self, request, queryset):
        """运行任务"""
        updated = queryset.filter(status='pending').update(status='running')
        self.message_user(request, f"成功启动 {updated} 个任务")
    run_jobs.short_description = "运行选中的任务"

    def cancel_jobs(self, request, queryset):
        """取消任务"""
        updated = queryset.filter(status__in=['pending', 'running']).update(status='cancelled')
        self.message_user(request, f"成功取消 {updated} 个任务")
    cancel_jobs.short_description = "取消选中的任务"


@admin.register(DataQualityRule)
class DataQualityRuleAdmin(admin.ModelAdmin):
    """数据质量规则管理"""
    list_display = [
        'name', 'rule_type_display', 'data_source', 'table_name',
        'severity_display', 'check_frequency', 'is_active', 'created_by'
    ]
    list_filter = ['rule_type', 'severity', 'data_source', 'is_active']
    search_fields = ['name', 'description', 'table_name', 'column_name']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'rule_type')
        }),
        ('规则配置', {
            'fields': ('data_source', 'table_name', 'column_name')
        }),
        ('规则定义', {
            'fields': ('rule_expression', 'expected_value', 'threshold')
        }),
        ('严重程度', {
            'fields': ('severity',)
        }),
        ('执行配置', {
            'fields': ('is_active', 'check_frequency')
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def rule_type_display(self, obj):
        """规则类型显示"""
        type_colors = {
            'completeness': '#1890ff',
            'accuracy': '#52c41a',
            'consistency': '#faad14',
            'validity': '#722ed1',
            'uniqueness': '#fa8c16',
            'timeliness': '#eb2f96',
        }
        color = type_colors.get(obj.rule_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_rule_type_display()
        )
    rule_type_display.short_description = '规则类型'

    def severity_display(self, obj):
        """严重程度显示"""
        severity_colors = {
            'low': '#52c41a',
            'medium': '#faad14',
            'high': '#fa8c16',
            'critical': '#f5222d',
        }
        color = severity_colors.get(obj.severity, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_severity_display()
        )
    severity_display.short_description = '严重程度'

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(DataQualityResult)
class DataQualityResultAdmin(admin.ModelAdmin):
    """数据质量结果管理"""
    list_display = [
        'rule_info', 'check_time', 'passed_display', 'score_display',
        'records_summary', 'data_period_display'
    ]
    list_filter = ['passed', 'rule__rule_type', 'rule__severity', 'check_time']
    search_fields = ['rule__name']
    readonly_fields = ['created_at']
    date_hierarchy = 'check_time'

    fieldsets = (
        ('基本信息', {
            'fields': ('rule',)
        }),
        ('检查信息', {
            'fields': ('check_time', 'data_period_start', 'data_period_end')
        }),
        ('检查结果', {
            'fields': ('passed', 'score')
        }),
        ('统计信息', {
            'fields': ('total_records', 'valid_records', 'invalid_records')
        }),
        ('详细结果', {
            'fields': ('details', 'sample_errors'),
            'classes': ('collapse',)
        }),
        ('创建时间', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

    def rule_info(self, obj):
        """规则信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:analytics_advanced_dataqualityrule_change', args=[obj.rule.pk]),
            obj.rule.name
        )
    rule_info.short_description = '质量规则'

    def passed_display(self, obj):
        """通过状态显示"""
        if obj.passed:
            return format_html('<span style="color: #52c41a; font-weight: bold;">✓ 通过</span>')
        else:
            return format_html('<span style="color: #f5222d; font-weight: bold;">✗ 未通过</span>')
    passed_display.short_description = '检查结果'

    def score_display(self, obj):
        """评分显示"""
        score = float(obj.score)
        if score >= 90:
            color = '#52c41a'
        elif score >= 70:
            color = '#faad14'
        else:
            color = '#f5222d'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}</span>',
            color, score
        )
    score_display.short_description = '质量评分'

    def records_summary(self, obj):
        """记录汇总"""
        return format_html(
            '总数: {} | 有效: {} | 无效: {}',
            obj.total_records, obj.valid_records, obj.invalid_records
        )
    records_summary.short_description = '记录统计'

    def data_period_display(self, obj):
        """数据周期显示"""
        return f"{obj.data_period_start.date()} ~ {obj.data_period_end.date()}"
    data_period_display.short_description = '数据周期'


# 自定义管理页面标题
admin.site.site_header = '代理记账管理系统'
admin.site.site_title = '高级数据分析'
admin.site.index_title = '高级数据分析'

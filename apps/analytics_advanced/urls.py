from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from . import views

router = DefaultRouter()
router.register(r'categories', views.ReportCategoryViewSet, basename='report-category')
router.register(r'templates', views.ReportTemplateViewSet, basename='report-template')
router.register(r'instances', views.ReportInstanceViewSet, basename='report-instance')
router.register(r'dashboards', views.DashboardViewSet, basename='dashboard')
router.register(r'data-sources', views.DataSourceViewSet, basename='data-source')
router.register(r'kpi-metrics', views.KPIMetricViewSet, basename='kpi-metric')
router.register(r'kpi-values', views.KPIValueViewSet, basename='kpi-value')
router.register(r'jobs', views.AnalyticsJobViewSet, basename='analytics-job')
router.register(r'quality-rules', views.DataQualityRuleViewSet, basename='quality-rule')
router.register(r'quality-results', views.DataQualityResultViewSet, basename='quality-result')

app_name = 'analytics_advanced'

urlpatterns = [
    path('', include(router.urls)),
]

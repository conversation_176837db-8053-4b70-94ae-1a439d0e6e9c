from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from apps.companies.models import Company
from apps.finance.models import FinancialRecord

User = get_user_model()


class AuditStandard(models.Model):
    """审核标准"""
    TAXPAYER_CHOICES = [
        ('individual', '个体工商户'),
        ('small_scale', '小规模纳税人'),
        ('general', '一般纳税人'),
        ('enterprise', '企业'),
    ]
    
    name = models.CharField('标准名称', max_length=100)
    taxpayer_type = models.CharField('纳税人类型', max_length=20, choices=TAXPAYER_CHOICES)
    description = models.TextField('标准描述')
    check_items = models.JSONField('检查项目', default=list, help_text='审核检查项目列表')
    required_documents = models.JSONField('必需文档', default=list, help_text='必需的文档类型')
    is_active = models.BooleanField('是否启用', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '审核标准'
        verbose_name_plural = '审核标准'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.get_taxpayer_type_display()})"


class AuditQueue(models.Model):
    """审核队列"""
    STATUS_CHOICES = [
        ('pending', '待审核'),
        ('in_progress', '审核中'),
        ('first_review', '初审完成'),
        ('second_review', '复审完成'),
        ('approved', '审核通过'),
        ('rejected', '审核拒绝'),
        ('returned', '退回修改'),
    ]
    
    PRIORITY_CHOICES = [
        ('low', '低'),
        ('normal', '普通'),
        ('high', '高'),
        ('urgent', '紧急'),
    ]
    
    company = models.ForeignKey(Company, on_delete=models.CASCADE, verbose_name='企业')
    submission_month = models.CharField('提交月份', max_length=7, help_text='格式：2024-01')
    taxpayer_type = models.CharField('纳税人类型', max_length=20, choices=AuditStandard.TAXPAYER_CHOICES)
    status = models.CharField('审核状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    priority = models.CharField('优先级', max_length=10, choices=PRIORITY_CHOICES, default='normal')
    
    # 审核人员
    assigned_auditor = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='assigned_audits', verbose_name='指派审核员'
    )
    first_reviewer = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='first_reviews', verbose_name='初审员'
    )
    second_reviewer = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='second_reviews', verbose_name='复审员'
    )
    
    # 时间记录
    submitted_at = models.DateTimeField('提交时间', auto_now_add=True)
    assigned_at = models.DateTimeField('分配时间', null=True, blank=True)
    first_review_at = models.DateTimeField('初审时间', null=True, blank=True)
    second_review_at = models.DateTimeField('复审时间', null=True, blank=True)
    completed_at = models.DateTimeField('完成时间', null=True, blank=True)
    
    # 审核结果
    audit_notes = models.TextField('审核备注', blank=True)
    rejection_reason = models.TextField('拒绝原因', blank=True)
    return_reason = models.TextField('退回原因', blank=True)
    
    # 统计字段
    document_count = models.IntegerField('文档数量', default=0)
    processing_time = models.DurationField('处理时长', null=True, blank=True)
    
    class Meta:
        verbose_name = '审核队列'
        verbose_name_plural = '审核队列'
        ordering = ['-priority', '-submitted_at']
        indexes = [
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['company', 'submission_month']),
            models.Index(fields=['assigned_auditor', 'status']),
        ]
    
    def __str__(self):
        return f"{self.company.name} - {self.submission_month} ({self.get_status_display()})"
    
    def calculate_processing_time(self):
        """计算处理时长"""
        if self.completed_at and self.submitted_at:
            self.processing_time = self.completed_at - self.submitted_at
            self.save(update_fields=['processing_time'])
    
    def get_sla_status(self):
        """获取SLA状态"""
        if self.status in ['approved', 'rejected']:
            return 'completed'
        
        now = timezone.now()
        hours_passed = (now - self.submitted_at).total_seconds() / 3600
        
        # 根据优先级设置SLA时间
        sla_hours = {
            'urgent': 4,
            'high': 12,
            'normal': 24,
            'low': 48,
        }
        
        threshold = sla_hours.get(self.priority, 24)
        
        if hours_passed > threshold:
            return 'overdue'
        elif hours_passed > threshold * 0.8:
            return 'warning'
        else:
            return 'normal'


class AuditDocument(models.Model):
    """审核文档"""
    DOCUMENT_TYPES = [
        ('invoice', '发票'),
        ('receipt', '收据'),
        ('bank_statement', '银行流水'),
        ('contract', '合同'),
        ('voucher', '凭证'),
        ('tax_form', '税务表格'),
        ('other', '其他'),
    ]
    
    audit_queue = models.ForeignKey(AuditQueue, on_delete=models.CASCADE, related_name='documents', verbose_name='审核队列')
    document_type = models.CharField('文档类型', max_length=20, choices=DOCUMENT_TYPES)
    file_name = models.CharField('文件名', max_length=255)
    file_path = models.CharField('文件路径', max_length=500)
    file_size = models.BigIntegerField('文件大小', help_text='字节')
    upload_time = models.DateTimeField('上传时间', auto_now_add=True)
    is_verified = models.BooleanField('已验证', default=False)
    verification_notes = models.TextField('验证备注', blank=True)
    
    class Meta:
        verbose_name = '审核文档'
        verbose_name_plural = '审核文档'
        ordering = ['-upload_time']
    
    def __str__(self):
        return f"{self.file_name} ({self.get_document_type_display()})"


class AuditComment(models.Model):
    """审核意见"""
    COMMENT_TYPES = [
        ('question', '疑问'),
        ('suggestion', '建议'),
        ('error', '错误'),
        ('approval', '通过'),
        ('rejection', '拒绝'),
    ]
    
    audit_queue = models.ForeignKey(AuditQueue, on_delete=models.CASCADE, related_name='comments', verbose_name='审核队列')
    auditor = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='审核员')
    comment_type = models.CharField('意见类型', max_length=20, choices=COMMENT_TYPES)
    content = models.TextField('意见内容')
    document = models.ForeignKey(
        AuditDocument, on_delete=models.CASCADE, null=True, blank=True,
        verbose_name='相关文档'
    )
    is_resolved = models.BooleanField('已解决', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '审核意见'
        verbose_name_plural = '审核意见'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.get_comment_type_display()} - {self.content[:50]}"


class AuditTemplate(models.Model):
    """审核模板"""
    name = models.CharField('模板名称', max_length=100)
    taxpayer_type = models.CharField('纳税人类型', max_length=20, choices=AuditStandard.TAXPAYER_CHOICES)
    template_content = models.JSONField('模板内容', help_text='审核项目和标准的JSON格式')
    is_default = models.BooleanField('默认模板', default=False)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '审核模板'
        verbose_name_plural = '审核模板'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.get_taxpayer_type_display()})"


class AuditStatistics(models.Model):
    """审核统计"""
    date = models.DateField('统计日期')
    auditor = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='审核员')

    # 数量统计
    total_audits = models.IntegerField('总审核数', default=0)
    completed_audits = models.IntegerField('完成审核数', default=0)
    approved_audits = models.IntegerField('通过审核数', default=0)
    rejected_audits = models.IntegerField('拒绝审核数', default=0)
    returned_audits = models.IntegerField('退回审核数', default=0)

    # 时间统计
    avg_processing_time = models.DurationField('平均处理时长', null=True, blank=True)
    total_processing_time = models.DurationField('总处理时长', null=True, blank=True)

    # 质量统计
    accuracy_rate = models.DecimalField('准确率', max_digits=5, decimal_places=2, default=0)
    efficiency_score = models.DecimalField('效率评分', max_digits=5, decimal_places=2, default=0)

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '审核统计'
        verbose_name_plural = '审核统计'
        unique_together = ['date', 'auditor']
        ordering = ['-date']

    def __str__(self):
        return f"{self.auditor.username} - {self.date}"


class AuditWorkflow(models.Model):
    """审核工作流"""
    name = models.CharField('工作流名称', max_length=100)
    taxpayer_type = models.CharField('纳税人类型', max_length=20, choices=AuditStandard.TAXPAYER_CHOICES)
    workflow_steps = models.JSONField('工作流步骤', default=list, help_text='工作流步骤配置')
    is_active = models.BooleanField('是否启用', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '审核工作流'
        verbose_name_plural = '审核工作流'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.get_taxpayer_type_display()})"

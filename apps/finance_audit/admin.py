from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.db.models import Count, Avg, Q
from .models import (
    AuditStandard, AuditQueue, AuditDocument, AuditComment,
    AuditTemplate, AuditStatistics, AuditWorkflow
)


@admin.register(AuditStandard)
class AuditStandardAdmin(admin.ModelAdmin):
    """审核标准管理"""
    list_display = [
        'name', 'taxpayer_type_display', 'check_items_count', 
        'required_docs_count', 'is_active', 'created_at'
    ]
    list_filter = ['taxpayer_type', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'taxpayer_type', 'description', 'is_active')
        }),
        ('审核配置', {
            'fields': ('check_items', 'required_documents'),
            'classes': ('wide',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def taxpayer_type_display(self, obj):
        """纳税人类型显示"""
        return obj.get_taxpayer_type_display()
    taxpayer_type_display.short_description = '纳税人类型'
    
    def check_items_count(self, obj):
        """检查项目数量"""
        return len(obj.check_items) if obj.check_items else 0
    check_items_count.short_description = '检查项目数'
    
    def required_docs_count(self, obj):
        """必需文档数量"""
        return len(obj.required_documents) if obj.required_documents else 0
    required_docs_count.short_description = '必需文档数'


class AuditDocumentInline(admin.TabularInline):
    """审核文档内联"""
    model = AuditDocument
    extra = 0
    readonly_fields = ['file_size', 'upload_time']
    fields = ['document_type', 'file_name', 'file_path', 'file_size', 'is_verified', 'verification_notes']


class AuditCommentInline(admin.TabularInline):
    """审核意见内联"""
    model = AuditComment
    extra = 0
    readonly_fields = ['created_at']
    fields = ['auditor', 'comment_type', 'content', 'document', 'is_resolved', 'created_at']


@admin.register(AuditQueue)
class AuditQueueAdmin(admin.ModelAdmin):
    """审核队列管理"""
    list_display = [
        'company_info', 'submission_month', 'taxpayer_type_display', 
        'status_display', 'priority_display', 'sla_status_display',
        'assigned_auditor', 'processing_time_display', 'submitted_at'
    ]
    list_filter = [
        'status', 'priority', 'taxpayer_type', 'submitted_at',
        'assigned_auditor', 'first_reviewer', 'second_reviewer'
    ]
    search_fields = ['company__name', 'company__unified_social_credit_code', 'submission_month']
    readonly_fields = [
        'submitted_at', 'processing_time', 'sla_status_display',
        'document_count_display', 'audit_progress_display'
    ]
    
    fieldsets = (
        ('基本信息', {
            'fields': ('company', 'submission_month', 'taxpayer_type', 'status', 'priority')
        }),
        ('审核人员', {
            'fields': ('assigned_auditor', 'first_reviewer', 'second_reviewer')
        }),
        ('时间记录', {
            'fields': (
                'submitted_at', 'assigned_at', 'first_review_at', 
                'second_review_at', 'completed_at', 'processing_time'
            ),
            'classes': ('collapse',)
        }),
        ('审核结果', {
            'fields': ('audit_notes', 'rejection_reason', 'return_reason')
        }),
        ('统计信息', {
            'fields': ('document_count_display', 'sla_status_display', 'audit_progress_display'),
            'classes': ('collapse',)
        }),
    )
    
    inlines = [AuditDocumentInline, AuditCommentInline]
    
    actions = ['assign_to_me', 'mark_as_approved', 'mark_as_rejected', 'bulk_assign']
    
    def company_info(self, obj):
        """企业信息"""
        return format_html(
            '<a href="{}">{}</a><br/><small>{}</small>',
            reverse('admin:companies_company_change', args=[obj.company.pk]),
            obj.company.name,
            obj.company.unified_social_credit_code or '未设置'
        )
    company_info.short_description = '企业信息'
    
    def taxpayer_type_display(self, obj):
        """纳税人类型显示"""
        return obj.get_taxpayer_type_display()
    taxpayer_type_display.short_description = '纳税人类型'
    
    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            'pending': '#faad14',
            'in_progress': '#1890ff',
            'first_review': '#52c41a',
            'second_review': '#722ed1',
            'approved': '#52c41a',
            'rejected': '#f5222d',
            'returned': '#fa8c16',
        }
        color = status_colors.get(obj.status, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_status_display()
        )
    status_display.short_description = '审核状态'
    
    def priority_display(self, obj):
        """优先级显示"""
        priority_colors = {
            'urgent': '#f5222d',
            'high': '#fa8c16',
            'normal': '#1890ff',
            'low': '#52c41a',
        }
        color = priority_colors.get(obj.priority, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_priority_display()
        )
    priority_display.short_description = '优先级'
    
    def sla_status_display(self, obj):
        """SLA状态显示"""
        sla_status = obj.get_sla_status()
        status_colors = {
            'normal': '#52c41a',
            'warning': '#faad14',
            'overdue': '#f5222d',
            'completed': '#722ed1',
        }
        status_texts = {
            'normal': '正常',
            'warning': '即将超时',
            'overdue': '已超时',
            'completed': '已完成',
        }
        color = status_colors.get(sla_status, '#d9d9d9')
        text = status_texts.get(sla_status, '未知')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, text
        )
    sla_status_display.short_description = 'SLA状态'
    
    def processing_time_display(self, obj):
        """处理时长显示"""
        if obj.processing_time:
            total_seconds = int(obj.processing_time.total_seconds())
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            return "{}小时{}分钟".format(hours, minutes)
        elif obj.status not in ['approved', 'rejected']:
            # 计算当前已用时间
            now = timezone.now()
            elapsed = now - obj.submitted_at
            total_seconds = int(elapsed.total_seconds())
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            return "{}小时{}分钟 (进行中)".format(hours, minutes)
        return '-'
    processing_time_display.short_description = '处理时长'
    
    def document_count_display(self, obj):
        """文档数量显示"""
        count = obj.documents.count()
        verified_count = obj.documents.filter(is_verified=True).count()
        return "{} 个文档 ({} 已验证)".format(count, verified_count)
    document_count_display.short_description = '文档统计'
    
    def audit_progress_display(self, obj):
        """审核进度显示"""
        total_comments = obj.comments.count()
        resolved_comments = obj.comments.filter(is_resolved=True).count()
        if total_comments > 0:
            progress = (resolved_comments / total_comments) * 100
            return "{}% ({}/{} 已解决)".format(int(progress), resolved_comments, total_comments)
        return "无审核意见"
    audit_progress_display.short_description = '审核进度'
    
    def assign_to_me(self, request, queryset):
        """分配给我"""
        updated = queryset.filter(assigned_auditor__isnull=True).update(
            assigned_auditor=request.user,
            assigned_at=timezone.now(),
            status='in_progress'
        )
        self.message_user(request, "成功分配 {} 个审核任务给您".format(updated))
    assign_to_me.short_description = "分配给我"
    
    def mark_as_approved(self, request, queryset):
        """标记为通过"""
        updated = queryset.filter(status__in=['in_progress', 'first_review', 'second_review']).update(
            status='approved',
            completed_at=timezone.now()
        )
        # 计算处理时长
        for obj in queryset.filter(status='approved'):
            obj.calculate_processing_time()
        self.message_user(request, "成功标记 {} 个审核为通过".format(updated))
    mark_as_approved.short_description = "标记为通过"
    
    def mark_as_rejected(self, request, queryset):
        """标记为拒绝"""
        updated = queryset.filter(status__in=['in_progress', 'first_review', 'second_review']).update(
            status='rejected',
            completed_at=timezone.now()
        )
        # 计算处理时长
        for obj in queryset.filter(status='rejected'):
            obj.calculate_processing_time()
        self.message_user(request, "成功标记 {} 个审核为拒绝".format(updated))
    mark_as_rejected.short_description = "标记为拒绝"


@admin.register(AuditDocument)
class AuditDocumentAdmin(admin.ModelAdmin):
    """审核文档管理"""
    list_display = [
        'file_name', 'document_type_display', 'audit_queue_info',
        'file_size_display', 'is_verified', 'upload_time'
    ]
    list_filter = ['document_type', 'is_verified', 'upload_time']
    search_fields = ['file_name', 'audit_queue__company__name']
    readonly_fields = ['file_size', 'upload_time']
    
    def document_type_display(self, obj):
        """文档类型显示"""
        return obj.get_document_type_display()
    document_type_display.short_description = '文档类型'
    
    def audit_queue_info(self, obj):
        """审核队列信息"""
        return format_html(
            '<a href="{}">{} - {}</a>',
            reverse('admin:finance_audit_auditqueue_change', args=[obj.audit_queue.pk]),
            obj.audit_queue.company.name,
            obj.audit_queue.submission_month
        )
    audit_queue_info.short_description = '审核队列'
    
    def file_size_display(self, obj):
        """文件大小显示"""
        size = obj.file_size
        if size < 1024:
            return "{} B".format(size)
        elif size < 1024 * 1024:
            return "{:.1f} KB".format(size / 1024)
        else:
            return "{:.1f} MB".format(size / (1024 * 1024))
    file_size_display.short_description = '文件大小'


@admin.register(AuditComment)
class AuditCommentAdmin(admin.ModelAdmin):
    """审核意见管理"""
    list_display = [
        'audit_queue_info', 'auditor', 'comment_type_display',
        'content_preview', 'is_resolved', 'created_at'
    ]
    list_filter = ['comment_type', 'is_resolved', 'created_at', 'auditor']
    search_fields = ['content', 'audit_queue__company__name']
    readonly_fields = ['created_at']

    def audit_queue_info(self, obj):
        """审核队列信息"""
        return format_html(
            '<a href="{}">{} - {}</a>',
            reverse('admin:finance_audit_auditqueue_change', args=[obj.audit_queue.pk]),
            obj.audit_queue.company.name,
            obj.audit_queue.submission_month
        )
    audit_queue_info.short_description = '审核队列'

    def comment_type_display(self, obj):
        """意见类型显示"""
        type_colors = {
            'question': '#1890ff',
            'suggestion': '#52c41a',
            'error': '#f5222d',
            'approval': '#52c41a',
            'rejection': '#f5222d',
        }
        color = type_colors.get(obj.comment_type, '#d9d9d9')
        return format_html(
            '<span style="color: {}; font-weight: bold;">●</span> {}',
            color, obj.get_comment_type_display()
        )
    comment_type_display.short_description = '意见类型'

    def content_preview(self, obj):
        """内容预览"""
        return obj.content[:100] + '...' if len(obj.content) > 100 else obj.content
    content_preview.short_description = '意见内容'


@admin.register(AuditTemplate)
class AuditTemplateAdmin(admin.ModelAdmin):
    """审核模板管理"""
    list_display = [
        'name', 'taxpayer_type_display', 'is_default',
        'created_by', 'created_at'
    ]
    list_filter = ['taxpayer_type', 'is_default', 'created_at']
    search_fields = ['name']
    readonly_fields = ['created_at', 'updated_at']

    def taxpayer_type_display(self, obj):
        """纳税人类型显示"""
        return obj.get_taxpayer_type_display()
    taxpayer_type_display.short_description = '纳税人类型'


@admin.register(AuditStatistics)
class AuditStatisticsAdmin(admin.ModelAdmin):
    """审核统计管理"""
    list_display = [
        'auditor', 'date', 'total_audits', 'completion_rate_display',
        'approval_rate_display', 'avg_processing_time_display', 'efficiency_score'
    ]
    list_filter = ['date', 'auditor']
    search_fields = ['auditor__username', 'auditor__first_name']
    readonly_fields = ['created_at', 'updated_at']

    def completion_rate_display(self, obj):
        """完成率显示"""
        if obj.total_audits > 0:
            rate = (obj.completed_audits / obj.total_audits) * 100
            color = '#52c41a' if rate >= 90 else '#faad14' if rate >= 70 else '#f5222d'
            return format_html(
                '<span style="color: {}; font-weight: bold;">{}%</span>',
                color, "{:.1f}".format(rate)
            )
        return '-'
    completion_rate_display.short_description = '完成率'

    def approval_rate_display(self, obj):
        """通过率显示"""
        if obj.completed_audits > 0:
            rate = (obj.approved_audits / obj.completed_audits) * 100
            color = '#52c41a' if rate >= 80 else '#faad14' if rate >= 60 else '#f5222d'
            return format_html(
                '<span style="color: {}; font-weight: bold;">{}%</span>',
                color, "{:.1f}".format(rate)
            )
        return '-'
    approval_rate_display.short_description = '通过率'

    def avg_processing_time_display(self, obj):
        """平均处理时长显示"""
        if obj.avg_processing_time:
            total_seconds = int(obj.avg_processing_time.total_seconds())
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            return "{}小时{}分钟".format(hours, minutes)
        return '-'
    avg_processing_time_display.short_description = '平均处理时长'


@admin.register(AuditWorkflow)
class AuditWorkflowAdmin(admin.ModelAdmin):
    """审核工作流管理"""
    list_display = [
        'name', 'taxpayer_type_display', 'workflow_steps_count',
        'is_active', 'created_at'
    ]
    list_filter = ['taxpayer_type', 'is_active', 'created_at']
    search_fields = ['name']
    readonly_fields = ['created_at', 'updated_at']

    def taxpayer_type_display(self, obj):
        """纳税人类型显示"""
        return obj.get_taxpayer_type_display()
    taxpayer_type_display.short_description = '纳税人类型'

    def workflow_steps_count(self, obj):
        """工作流步骤数量"""
        return len(obj.workflow_steps) if obj.workflow_steps else 0
    workflow_steps_count.short_description = '步骤数量'

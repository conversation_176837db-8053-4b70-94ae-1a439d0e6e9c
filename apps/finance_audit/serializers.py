from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    AuditStandard, AuditQueue, AuditDocument, AuditComment,
    AuditTemplate, AuditStatistics, AuditWorkflow
)

User = get_user_model()


class AuditStandardSerializer(serializers.ModelSerializer):
    """审核标准序列化器"""
    taxpayer_type_display = serializers.CharField(source='get_taxpayer_type_display', read_only=True)
    check_items_count = serializers.SerializerMethodField()
    required_docs_count = serializers.SerializerMethodField()
    
    class Meta:
        model = AuditStandard
        fields = [
            'id', 'name', 'taxpayer_type', 'taxpayer_type_display',
            'description', 'check_items', 'required_documents',
            'check_items_count', 'required_docs_count',
            'is_active', 'created_at', 'updated_at'
        ]
    
    def get_check_items_count(self, obj):
        return len(obj.check_items) if obj.check_items else 0
    
    def get_required_docs_count(self, obj):
        return len(obj.required_documents) if obj.required_documents else 0


class AuditDocumentSerializer(serializers.ModelSerializer):
    """审核文档序列化器"""
    document_type_display = serializers.CharField(source='get_document_type_display', read_only=True)
    file_size_display = serializers.SerializerMethodField()
    
    class Meta:
        model = AuditDocument
        fields = [
            'id', 'audit_queue', 'document_type', 'document_type_display',
            'file_name', 'file_path', 'file_size', 'file_size_display',
            'upload_time', 'is_verified', 'verification_notes'
        ]
    
    def get_file_size_display(self, obj):
        size = obj.file_size
        if size < 1024:
            return "{} B".format(size)
        elif size < 1024 * 1024:
            return "{:.1f} KB".format(size / 1024)
        else:
            return "{:.1f} MB".format(size / (1024 * 1024))


class AuditCommentSerializer(serializers.ModelSerializer):
    """审核意见序列化器"""
    auditor_name = serializers.CharField(source='auditor.get_full_name', read_only=True)
    comment_type_display = serializers.CharField(source='get_comment_type_display', read_only=True)
    document_name = serializers.CharField(source='document.file_name', read_only=True)
    
    class Meta:
        model = AuditComment
        fields = [
            'id', 'audit_queue', 'auditor', 'auditor_name',
            'comment_type', 'comment_type_display', 'content',
            'document', 'document_name', 'is_resolved', 'created_at'
        ]


class AuditQueueSerializer(serializers.ModelSerializer):
    """审核队列序列化器"""
    company_name = serializers.CharField(source='company.name', read_only=True)
    company_credit_code = serializers.CharField(source='company.unified_social_credit_code', read_only=True)
    taxpayer_type_display = serializers.CharField(source='get_taxpayer_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    assigned_auditor_name = serializers.CharField(source='assigned_auditor.get_full_name', read_only=True)
    first_reviewer_name = serializers.CharField(source='first_reviewer.get_full_name', read_only=True)
    second_reviewer_name = serializers.CharField(source='second_reviewer.get_full_name', read_only=True)
    
    # 统计字段
    documents_count = serializers.SerializerMethodField()
    verified_documents_count = serializers.SerializerMethodField()
    comments_count = serializers.SerializerMethodField()
    resolved_comments_count = serializers.SerializerMethodField()
    processing_time_display = serializers.SerializerMethodField()
    sla_status = serializers.SerializerMethodField()
    
    # 关联数据
    documents = AuditDocumentSerializer(many=True, read_only=True)
    comments = AuditCommentSerializer(many=True, read_only=True)
    
    class Meta:
        model = AuditQueue
        fields = [
            'id', 'company', 'company_name', 'company_credit_code',
            'submission_month', 'taxpayer_type', 'taxpayer_type_display',
            'status', 'status_display', 'priority', 'priority_display',
            'assigned_auditor', 'assigned_auditor_name',
            'first_reviewer', 'first_reviewer_name',
            'second_reviewer', 'second_reviewer_name',
            'submitted_at', 'assigned_at', 'first_review_at',
            'second_review_at', 'completed_at', 'processing_time',
            'processing_time_display', 'sla_status',
            'audit_notes', 'rejection_reason', 'return_reason',
            'document_count', 'documents_count', 'verified_documents_count',
            'comments_count', 'resolved_comments_count',
            'documents', 'comments'
        ]
    
    def get_documents_count(self, obj):
        return obj.documents.count()
    
    def get_verified_documents_count(self, obj):
        return obj.documents.filter(is_verified=True).count()
    
    def get_comments_count(self, obj):
        return obj.comments.count()
    
    def get_resolved_comments_count(self, obj):
        return obj.comments.filter(is_resolved=True).count()
    
    def get_processing_time_display(self, obj):
        if obj.processing_time:
            total_seconds = int(obj.processing_time.total_seconds())
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            return "{}小时{}分钟".format(hours, minutes)
        return None
    
    def get_sla_status(self, obj):
        return obj.get_sla_status()


class AuditTemplateSerializer(serializers.ModelSerializer):
    """审核模板序列化器"""
    taxpayer_type_display = serializers.CharField(source='get_taxpayer_type_display', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = AuditTemplate
        fields = [
            'id', 'name', 'taxpayer_type', 'taxpayer_type_display',
            'template_content', 'is_default', 'created_by',
            'created_by_name', 'created_at', 'updated_at'
        ]


class AuditStatisticsSerializer(serializers.ModelSerializer):
    """审核统计序列化器"""
    auditor_name = serializers.CharField(source='auditor.get_full_name', read_only=True)
    completion_rate = serializers.SerializerMethodField()
    approval_rate = serializers.SerializerMethodField()
    rejection_rate = serializers.SerializerMethodField()
    avg_processing_time_display = serializers.SerializerMethodField()
    
    class Meta:
        model = AuditStatistics
        fields = [
            'id', 'date', 'auditor', 'auditor_name',
            'total_audits', 'completed_audits', 'approved_audits',
            'rejected_audits', 'returned_audits',
            'completion_rate', 'approval_rate', 'rejection_rate',
            'avg_processing_time', 'avg_processing_time_display',
            'total_processing_time', 'accuracy_rate', 'efficiency_score',
            'created_at', 'updated_at'
        ]
    
    def get_completion_rate(self, obj):
        if obj.total_audits > 0:
            return round((obj.completed_audits / obj.total_audits) * 100, 2)
        return 0
    
    def get_approval_rate(self, obj):
        if obj.completed_audits > 0:
            return round((obj.approved_audits / obj.completed_audits) * 100, 2)
        return 0
    
    def get_rejection_rate(self, obj):
        if obj.completed_audits > 0:
            return round((obj.rejected_audits / obj.completed_audits) * 100, 2)
        return 0
    
    def get_avg_processing_time_display(self, obj):
        if obj.avg_processing_time:
            total_seconds = int(obj.avg_processing_time.total_seconds())
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            return "{}小时{}分钟".format(hours, minutes)
        return None


class AuditWorkflowSerializer(serializers.ModelSerializer):
    """审核工作流序列化器"""
    taxpayer_type_display = serializers.CharField(source='get_taxpayer_type_display', read_only=True)
    workflow_steps_count = serializers.SerializerMethodField()
    
    class Meta:
        model = AuditWorkflow
        fields = [
            'id', 'name', 'taxpayer_type', 'taxpayer_type_display',
            'workflow_steps', 'workflow_steps_count', 'is_active',
            'created_at', 'updated_at'
        ]
    
    def get_workflow_steps_count(self, obj):
        return len(obj.workflow_steps) if obj.workflow_steps else 0


# 简化版序列化器用于列表显示
class AuditQueueListSerializer(serializers.ModelSerializer):
    """审核队列列表序列化器"""
    company_name = serializers.CharField(source='company.name', read_only=True)
    taxpayer_type_display = serializers.CharField(source='get_taxpayer_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    assigned_auditor_name = serializers.CharField(source='assigned_auditor.get_full_name', read_only=True)
    sla_status = serializers.SerializerMethodField()
    
    class Meta:
        model = AuditQueue
        fields = [
            'id', 'company_name', 'submission_month',
            'taxpayer_type_display', 'status_display', 'priority_display',
            'assigned_auditor_name', 'submitted_at', 'sla_status'
        ]
    
    def get_sla_status(self, obj):
        return obj.get_sla_status()

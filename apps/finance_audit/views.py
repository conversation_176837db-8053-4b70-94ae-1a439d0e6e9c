import logging
from django.core.exceptions import ValidationError, PermissionDenied
from django.http import JsonResponse
from rest_framework import status

logger = logging.getLogger(__name__)

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q, Count, Avg
from django.shortcuts import get_object_or_404
from .models import (
    AuditStandard, AuditQueue, AuditDocument, AuditComment,
    AuditTemplate, AuditStatistics, AuditWorkflow
)
from .serializers import (
    AuditStandardSerializer, AuditQueueSerializer, AuditDocumentSerializer,
    AuditCommentSerializer, AuditTemplateSerializer, AuditStatisticsSerializer,
    AuditWorkflowSerializer
)
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class AuditStandardViewSet(viewsets.ModelViewSet):
    """审核标准ViewSet"""
    queryset = AuditStandard.objects.all()
    serializer_class = AuditStandardSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        try:
            queryset = super().get_queryset()
            taxpayer_type = self.request.query_params.get('taxpayer_type')
            if taxpayer_type:
                queryset = queryset.filter(taxpayer_type=taxpayer_type)
            return queryset.filter(is_active=True)


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class AuditQueueViewSet(viewsets.ModelViewSet):
    """审核队列ViewSet"""
    queryset = AuditQueue.objects.all()
    serializer_class = AuditQueueSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        try:
            queryset = super().get_queryset()
            user = self.request.user
        
            # 根据用户权限过滤
            if not user.is_staff:
                # 普通用户只能看到自己企业的审核
                queryset = queryset.filter(company__owner=user)
            else:
                # 管理员可以看到所有审核
                status_filter = self.request.query_params.get('status')
                if status_filter:
                    queryset = queryset.filter(status=status_filter)
            
                assigned_to_me = self.request.query_params.get('assigned_to_me')
                if assigned_to_me == 'true':
                    queryset = queryset.filter(assigned_auditor=user)
        
            return queryset.order_by('-priority', '-submitted_at')
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def assign_to_me(self, request, pk=None):
        """分配给我"""
        try:
            audit = self.get_object()
            if audit.assigned_auditor is None:
                audit.assigned_auditor = request.user
                audit.assigned_at = timezone.now()
                audit.status = 'in_progress'
                audit.save()
                return Response({'message': '审核任务已分配给您'})
            else:
                return Response(
                    {'error': '该审核任务已被分配'},
                    status=status.HTTP_400_BAD_REQUEST
                )
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """审核通过"""
        try:
            audit = self.get_object()
            if audit.assigned_auditor != request.user:
                return Response(
                    {'error': '您没有权限审核此任务'},
                    status=status.HTTP_403_FORBIDDEN
                )
        
            audit.status = 'approved'
            audit.completed_at = timezone.now()
            audit.audit_notes = request.data.get('notes', '')
            audit.save()
            audit.calculate_processing_time()
        
            return Response({'message': '审核已通过'})
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """审核拒绝"""
        try:
            audit = self.get_object()
            if audit.assigned_auditor != request.user:
                return Response(
                    {'error': '您没有权限审核此任务'},
                    status=status.HTTP_403_FORBIDDEN
                )
        
            audit.status = 'rejected'
            audit.completed_at = timezone.now()
            audit.rejection_reason = request.data.get('reason', '')
            audit.save()
            audit.calculate_processing_time()
        
            return Response({'message': '审核已拒绝'})
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def return_for_revision(self, request, pk=None):
        """退回修改"""
        try:
            audit = self.get_object()
            if audit.assigned_auditor != request.user:
                return Response(
                    {'error': '您没有权限审核此任务'},
                    status=status.HTTP_403_FORBIDDEN
                )
        
            audit.status = 'returned'
            audit.return_reason = request.data.get('reason', '')
            audit.save()
        
            return Response({'message': '已退回修改'})
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """审核仪表板数据"""
        try:
            user = request.user
        
            # 基础统计
            total_audits = AuditQueue.objects.count()
            pending_audits = AuditQueue.objects.filter(status='pending').count()
            in_progress_audits = AuditQueue.objects.filter(status='in_progress').count()
            completed_audits = AuditQueue.objects.filter(
                status__in=['approved', 'rejected']
            ).count()
        
            # 我的审核统计
            my_audits = AuditQueue.objects.filter(assigned_auditor=user).count()
            my_pending = AuditQueue.objects.filter(
                assigned_auditor=user, 
                status='in_progress'
            ).count()
        
            # SLA统计
            overdue_audits = []
            for audit in AuditQueue.objects.filter(status__in=['pending', 'in_progress']):
                if audit.get_sla_status() == 'overdue':
                    overdue_audits.append(audit.id)
        
            # 效率统计
            avg_processing_time = AuditQueue.objects.filter(
                processing_time__isnull=False
            ).aggregate(avg_time=Avg('processing_time'))['avg_time']
        
            return Response({
                'total_audits': total_audits,
                'pending_audits': pending_audits,
                'in_progress_audits': in_progress_audits,
                'completed_audits': completed_audits,
                'my_audits': my_audits,
                'my_pending': my_pending,
                'overdue_count': len(overdue_audits),
                'avg_processing_hours': (
                    avg_processing_time.total_seconds() / 3600 
                    if avg_processing_time else 0
                ),
            })


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class AuditDocumentViewSet(viewsets.ModelViewSet):
    """审核文档ViewSet"""
    queryset = AuditDocument.objects.all()
    serializer_class = AuditDocumentSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        try:
            queryset = super().get_queryset()
            audit_queue_id = self.request.query_params.get('audit_queue')
            if audit_queue_id:
                queryset = queryset.filter(audit_queue_id=audit_queue_id)
            return queryset.order_by('-upload_time')
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def verify(self, request, pk=None):
        """验证文档"""
        try:
            document = self.get_object()
            document.is_verified = True
            document.verification_notes = request.data.get('notes', '')
            document.save()
            return Response({'message': '文档已验证'})


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class AuditCommentViewSet(viewsets.ModelViewSet):
    """审核意见ViewSet"""
    queryset = AuditComment.objects.all()
    serializer_class = AuditCommentSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        try:
            queryset = super().get_queryset()
            audit_queue_id = self.request.query_params.get('audit_queue')
            if audit_queue_id:
                queryset = queryset.filter(audit_queue_id=audit_queue_id)
            return queryset.order_by('-created_at')
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def perform_create(self, serializer):
        try:
            serializer.save(auditor=self.request.user)
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    @action(detail=True, methods=['post'])
    def resolve(self, request, pk=None):
        """解决意见"""
        try:
            comment = self.get_object()
            comment.is_resolved = True
            comment.save()
            return Response({'message': '意见已解决'})


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class AuditTemplateViewSet(viewsets.ModelViewSet):
    """审核模板ViewSet"""
    queryset = AuditTemplate.objects.all()
    serializer_class = AuditTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        try:
            queryset = super().get_queryset()
            taxpayer_type = self.request.query_params.get('taxpayer_type')
            if taxpayer_type:
                queryset = queryset.filter(taxpayer_type=taxpayer_type)
            return queryset.order_by('-is_default', '-created_at')
    
        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
    def perform_create(self, serializer):
        try:
            serializer.save(created_by=self.request.user)


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class AuditStatisticsViewSet(viewsets.ReadOnlyModelViewSet):
    """审核统计ViewSet"""
    queryset = AuditStatistics.objects.all()
    serializer_class = AuditStatisticsSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        try:
            queryset = super().get_queryset()
            auditor_id = self.request.query_params.get('auditor')
            if auditor_id:
                queryset = queryset.filter(auditor_id=auditor_id)
        
            date_from = self.request.query_params.get('date_from')
            date_to = self.request.query_params.get('date_to')
            if date_from:
                queryset = queryset.filter(date__gte=date_from)
            if date_to:
                queryset = queryset.filter(date__lte=date_to)
        
            return queryset.order_by('-date')


        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
class AuditWorkflowViewSet(viewsets.ModelViewSet):
    """审核工作流ViewSet"""
    queryset = AuditWorkflow.objects.all()
    serializer_class = AuditWorkflowSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        try:
            queryset = super().get_queryset()
            taxpayer_type = self.request.query_params.get('taxpayer_type')
            if taxpayer_type:
                queryset = queryset.filter(taxpayer_type=taxpayer_type)
            return queryset.filter(is_active=True)

        except ValidationError as e:
            logger.warning(f"验证错误: {str(e)}")
            return JsonResponse({"code": 400, "message": "数据验证失败"}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}", exc_info=True)
            return JsonResponse({"code": 500, "message": "服务器内部错误"}, status=500)
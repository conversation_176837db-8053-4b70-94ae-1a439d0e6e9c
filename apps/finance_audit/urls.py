from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from . import views

router = DefaultRouter()
router.register(r'standards', views.AuditStandardViewSet, basename='audit-standard')
router.register(r'queue', views.AuditQueueViewSet, basename='audit-queue')
router.register(r'documents', views.AuditDocumentViewSet, basename='audit-document')
router.register(r'comments', views.AuditCommentViewSet, basename='audit-comment')
router.register(r'templates', views.AuditTemplateViewSet, basename='audit-template')
router.register(r'statistics', views.AuditStatisticsViewSet, basename='audit-statistics')
router.register(r'workflows', views.AuditWorkflowViewSet, basename='audit-workflow')

app_name = 'finance_audit'

urlpatterns = [
    path('', include(router.urls)),
]

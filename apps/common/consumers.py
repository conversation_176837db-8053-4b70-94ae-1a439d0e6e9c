import json
import asyncio
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()


class CustomerServiceConsumer(AsyncWebsocketConsumer):
    """客服聊天WebSocket消费者"""
    
    async def connect(self):
        """连接处理"""
        self.user = self.scope["user"]
        
        # 检查用户是否已认证
        if self.user.is_anonymous:
            await self.close()
            return
        
        # 创建房间组名
        self.room_name = f"customer_service_{self.user.id}"
        self.room_group_name = f"chat_{self.room_name}"
        
        # 加入房间组
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # 发送欢迎消息
        await self.send(text_data=json.dumps({
            'type': 'system_message',
            'message': '欢迎使用在线客服，我们将为您提供专业的财税咨询服务。',
            'timestamp': timezone.now().isoformat()
        }))
    
    async def disconnect(self, close_code):
        """断开连接处理"""
        # 离开房间组
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        """接收消息处理"""
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type', 'chat_message')
            
            if message_type == 'chat_message':
                await self.handle_chat_message(text_data_json)
            elif message_type == 'typing':
                await self.handle_typing(text_data_json)
            elif message_type == 'file_upload':
                await self.handle_file_upload(text_data_json)
                
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': '消息格式错误'
            }))
    
    async def handle_chat_message(self, data):
        """处理聊天消息"""
        message = data.get('message', '')
        
        if not message.strip():
            return
        
        # 保存消息到数据库
        await self.save_message(message)
        
        # 广播消息到房间组
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'chat_message',
                'message': message,
                'user_id': self.user.id,
                'username': self.user.username,
                'timestamp': timezone.now().isoformat(),
                'is_customer_service': False
            }
        )
        
        # 模拟客服自动回复（实际项目中应该连接到客服系统）
        await asyncio.sleep(1)
        await self.send_auto_reply(message)
    
    async def handle_typing(self, data):
        """处理打字状态"""
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'typing_status',
                'user_id': self.user.id,
                'is_typing': data.get('is_typing', False)
            }
        )
    
    async def handle_file_upload(self, data):
        """处理文件上传"""
        file_info = data.get('file_info', {})
        
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'file_message',
                'file_info': file_info,
                'user_id': self.user.id,
                'username': self.user.username,
                'timestamp': timezone.now().isoformat()
            }
        )
    
    async def send_auto_reply(self, user_message):
        """发送自动回复"""
        # 简单的关键词匹配回复
        auto_replies = {
            '记账': '我们提供专业的代理记账服务，包括日常账务处理、财务报表编制等。您需要了解哪方面的详细信息？',
            '报税': '我们可以协助您完成各类税务申报，包括增值税、企业所得税等。请问您的企业是什么类型的纳税人？',
            '价格': '我们的服务价格根据企业规模和业务复杂度而定。小规模纳税人套餐299元/月起，一般纳税人套餐599元/月起。',
            '咨询': '感谢您的咨询！我是您的专属客服，有任何财税问题都可以随时联系我。',
            '你好': '您好！欢迎咨询我们的财税服务。请问有什么可以帮助您的吗？'
        }
        
        reply_message = '感谢您的咨询，我们的专业客服将尽快为您解答。如需紧急处理，请拨打客服热线：400-888-9999'
        
        # 检查关键词
        for keyword, reply in auto_replies.items():
            if keyword in user_message:
                reply_message = reply
                break
        
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'chat_message',
                'message': reply_message,
                'user_id': 0,  # 系统消息
                'username': '客服小助手',
                'timestamp': timezone.now().isoformat(),
                'is_customer_service': True
            }
        )
    
    # WebSocket消息处理器
    async def chat_message(self, event):
        """发送聊天消息"""
        await self.send(text_data=json.dumps({
            'type': 'chat_message',
            'message': event['message'],
            'user_id': event['user_id'],
            'username': event['username'],
            'timestamp': event['timestamp'],
            'is_customer_service': event.get('is_customer_service', False)
        }))
    
    async def typing_status(self, event):
        """发送打字状态"""
        await self.send(text_data=json.dumps({
            'type': 'typing_status',
            'user_id': event['user_id'],
            'is_typing': event['is_typing']
        }))
    
    async def file_message(self, event):
        """发送文件消息"""
        await self.send(text_data=json.dumps({
            'type': 'file_message',
            'file_info': event['file_info'],
            'user_id': event['user_id'],
            'username': event['username'],
            'timestamp': event['timestamp']
        }))
    
    @database_sync_to_async
    def save_message(self, message):
        """保存消息到数据库"""
        # 这里可以保存到聊天记录表
        # ChatMessage.objects.create(
        #     user=self.user,
        #     message=message,
        #     timestamp=timezone.now()
        # )
        pass


class NotificationConsumer(AsyncWebsocketConsumer):
    """通知推送WebSocket消费者"""
    
    async def connect(self):
        """连接处理"""
        self.user = self.scope["user"]
        
        if self.user.is_anonymous:
            await self.close()
            return
        
        # 加入用户通知组
        self.notification_group_name = f"notifications_{self.user.id}"
        
        await self.channel_layer.group_add(
            self.notification_group_name,
            self.channel_name
        )
        
        await self.accept()
    
    async def disconnect(self, close_code):
        """断开连接处理"""
        await self.channel_layer.group_discard(
            self.notification_group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        """接收消息处理"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'mark_read':
                # 标记通知为已读
                notification_id = data.get('notification_id')
                await self.mark_notification_read(notification_id)
                
        except json.JSONDecodeError:
            pass
    
    async def notification_message(self, event):
        """发送通知消息"""
        await self.send(text_data=json.dumps({
            'type': 'notification',
            'title': event['title'],
            'message': event['message'],
            'notification_type': event.get('notification_type', 'info'),
            'timestamp': event['timestamp']
        }))
    
    @database_sync_to_async
    def mark_notification_read(self, notification_id):
        """标记通知为已读"""
        # 这里可以更新通知状态
        # Notification.objects.filter(
        #     id=notification_id,
        #     user=self.user
        # ).update(is_read=True)
        pass

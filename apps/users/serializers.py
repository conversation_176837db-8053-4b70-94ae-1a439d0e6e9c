from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.utils import timezone
from .models import User, UserProfile, SMSCode
from .authentication import generate_jwt_token
import re


class UserSerializer(serializers.ModelSerializer):
    """用户序列化器"""
    
    class Meta:
        model = User
        fields = ['id', 'phone', 'nickname', 'avatar', 'gender', 'birthday', 
                 'vip_level', 'vip_expire_time', 'is_phone_verified', 'created_at']
        read_only_fields = ['id', 'vip_level', 'vip_expire_time', 'is_phone_verified', 'created_at']


class UserProfileSerializer(serializers.ModelSerializer):
    """用户资料序列化器"""
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = UserProfile
        fields = ['user', 'real_name', 'id_card', 'address', 'emergency_contact', 
                 'emergency_phone', 'notification_enabled', 'sms_enabled', 'email_enabled']


class RegisterSerializer(serializers.Serializer):
    """用户注册序列化器"""
    phone = serializers.CharField(max_length=11)
    password = serializers.CharField(write_only=True, validators=[validate_password])
    verify_code = serializers.CharField(max_length=6)
    company_name = serializers.CharField(max_length=200, required=False, allow_blank=True)
    
    def validate_phone(self, value):
        """验证手机号"""
        if not re.match(r'^1[3-9]\d{9}$', value):
            raise serializers.ValidationError('手机号格式不正确')
        
        if User.objects.filter(phone=value).exists():
            raise serializers.ValidationError('该手机号已注册')
        
        return value
    
    def validate_verify_code(self, value):
        """验证验证码"""
        phone = self.initial_data.get('phone')
        if not phone:
            raise serializers.ValidationError('请提供手机号')
        
        try:
            sms_code = SMSCode.objects.filter(
                phone=phone,
                code=value,
                code_type='register',
                is_used=False
            ).latest('created_at')
            
            if sms_code.is_expired:
                raise serializers.ValidationError('验证码已过期')
                
        except SMSCode.DoesNotExist:
            raise serializers.ValidationError('验证码无效')
        
        return value
    
    def create(self, validated_data):
        """创建用户"""
        phone = validated_data['phone']
        password = validated_data['password']
        verify_code = validated_data['verify_code']
        
        # 标记验证码为已使用
        sms_code = SMSCode.objects.filter(
            phone=phone,
            code=verify_code,
            code_type='register',
            is_used=False
        ).latest('created_at')
        sms_code.is_used = True
        sms_code.save()
        
        # 创建用户
        user = User.objects.create_user(
            username=phone,  # 使用手机号作为用户名
            phone=phone,
            password=password,
            is_phone_verified=True
        )
        
        # 创建用户资料
        UserProfile.objects.create(user=user)
        
        return user


class LoginSerializer(serializers.Serializer):
    """用户登录序列化器"""
    phone = serializers.CharField(max_length=50)  # 支持用户名和手机号
    password = serializers.CharField(write_only=True)

    def validate(self, attrs):
        """验证登录信息"""
        phone = attrs.get('phone')
        password = attrs.get('password')

        # 支持手机号或用户名登录
        if re.match(r'^1[3-9]\d{9}$', phone):
            # 手机号登录 - 直接使用手机号认证
            user = authenticate(phone=phone, password=password)
        else:
            # 用户名登录（主要用于admin等特殊账户）
            # 先通过用户名找到用户，然后获取其手机号进行认证
            from .models import User
            try:
                user_obj = User.objects.get(username=phone)
                user = authenticate(phone=user_obj.phone, password=password)
            except User.DoesNotExist:
                user = None

        if not user:
            raise serializers.ValidationError('用户名/手机号或密码错误')

        if not user.is_active:
            raise serializers.ValidationError('账户已被禁用')

        attrs['user'] = user
        return attrs


class WechatLoginSerializer(serializers.Serializer):
    """微信登录序列化器"""
    code = serializers.CharField(max_length=100)
    user_info = serializers.DictField(required=False)
    
    def validate(self, attrs):
        """验证微信登录信息"""
        code = attrs.get('code')
        user_info = attrs.get('user_info', {})
        
        # 这里应该调用微信API获取用户信息
        # 暂时模拟处理
        openid = f"mock_openid_{code}"
        unionid = f"mock_unionid_{code}"
        
        # 查找或创建用户
        try:
            user = User.objects.get(wechat_openid=openid)
        except User.DoesNotExist:
            # 创建新用户
            user = User.objects.create(
                phone=f"wx_{openid[:8]}",  # 临时手机号
                wechat_openid=openid,
                wechat_unionid=unionid,
                nickname=user_info.get('nickname', '微信用户'),
                is_phone_verified=False
            )
            UserProfile.objects.create(user=user)
        
        attrs['user'] = user
        return attrs


class SMSCodeSerializer(serializers.Serializer):
    """短信验证码序列化器"""
    phone = serializers.CharField(max_length=11)
    code_type = serializers.ChoiceField(choices=[
        ('register', '注册'),
        ('login', '登录'),
        ('reset_password', '重置密码'),
        ('bind_phone', '绑定手机'),
    ])

    def validate_phone(self, value):
        """验证手机号"""
        if not re.match(r'^1[3-9]\d{9}$', value):
            raise serializers.ValidationError('手机号格式不正确')
        return value


class ChangePasswordSerializer(serializers.Serializer):
    """修改密码序列化器"""
    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(write_only=True, validators=[validate_password])
    
    def validate_old_password(self, value):
        """验证旧密码"""
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError('旧密码错误')
        return value


class ResetPasswordSerializer(serializers.Serializer):
    """重置密码序列化器"""
    phone = serializers.CharField(max_length=11)
    verify_code = serializers.CharField(max_length=6)
    new_password = serializers.CharField(write_only=True, validators=[validate_password])
    
    def validate_phone(self, value):
        """验证手机号"""
        if not re.match(r'^1[3-9]\d{9}$', value):
            raise serializers.ValidationError('手机号格式不正确')
        
        if not User.objects.filter(phone=value).exists():
            raise serializers.ValidationError('该手机号未注册')
        
        return value
    
    def validate_verify_code(self, value):
        """验证验证码"""
        phone = self.initial_data.get('phone')
        if not phone:
            raise serializers.ValidationError('请提供手机号')
        
        try:
            sms_code = SMSCode.objects.filter(
                phone=phone,
                code=value,
                code_type='reset_password',
                is_used=False
            ).latest('created_at')
            
            if sms_code.is_expired:
                raise serializers.ValidationError('验证码已过期')
                
        except SMSCode.DoesNotExist:
            raise serializers.ValidationError('验证码无效')
        
        return value

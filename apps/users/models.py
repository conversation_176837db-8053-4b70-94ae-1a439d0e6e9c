from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone


class User(AbstractUser):
    """用户模型"""
    GENDER_CHOICES = [
        ('M', '男'),
        ('F', '女'),
        ('U', '未知'),
    ]

    VIP_LEVEL_CHOICES = [
        ('NORMAL', '普通用户'),
        ('VIP', 'VIP用户'),
        ('SVIP', '超级VIP'),
    ]
    
    phone = models.CharField('手机号', max_length=11, unique=True)
    nickname = models.CharField('昵称', max_length=50, blank=True)
    avatar = models.ImageField('头像', upload_to='avatars/', blank=True, null=True)
    gender = models.CharField('性别', max_length=1, choices=GENDER_CHOICES, default='U')
    birthday = models.DateField('生日', blank=True, null=True)
    
    # 微信相关信息
    wechat_openid = models.Char<PERSON><PERSON>('微信OpenID', max_length=100, blank=True, unique=True, null=True)
    wechat_unionid = models.CharField('微信UnionID', max_length=100, blank=True, null=True)
    wechat_session_key = models.CharField('微信SessionKey', max_length=100, blank=True)
    
    # VIP相关
    vip_level = models.CharField('VIP等级', max_length=10, choices=VIP_LEVEL_CHOICES, default='NORMAL')
    vip_expire_time = models.DateTimeField('VIP到期时间', blank=True, null=True)
    
    # 状态字段
    is_phone_verified = models.BooleanField('手机号已验证', default=False)
    last_login_ip = models.GenericIPAddressField('最后登录IP', blank=True, null=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    # 重写username字段，使用手机号作为用户名
    username = models.CharField('用户名', max_length=150, unique=True, blank=True)
    
    USERNAME_FIELD = 'phone'
    REQUIRED_FIELDS = ['username']

    class Meta:
        db_table = 'users'
        verbose_name = '用户'
        verbose_name_plural = '用户管理'
        ordering = ['-created_at']

    # 解决与Django内置User模型的冲突
    groups = models.ManyToManyField(
        'auth.Group',
        verbose_name='groups',
        blank=True,
        help_text='The groups this user belongs to.',
        related_name='custom_user_set',
        related_query_name='custom_user',
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        verbose_name='user permissions',
        blank=True,
        help_text='Specific permissions for this user.',
        related_name='custom_user_set',
        related_query_name='custom_user',
    )
    
    def __str__(self):
        return f"{self.nickname or self.phone}"
    
    def save(self, *args, **kwargs):
        if not self.username:
            self.username = self.phone
        super().save(*args, **kwargs)
    
    @property
    def is_vip(self):
        """是否为VIP用户"""
        if self.vip_level == 'NORMAL':
            return False
        if self.vip_expire_time and self.vip_expire_time < timezone.now():
            return False
        return True
    
    @property
    def vip_days_left(self):
        """VIP剩余天数"""
        if not self.is_vip or not self.vip_expire_time:
            return 0
        delta = self.vip_expire_time - timezone.now()
        return max(0, delta.days)


class UserProfile(models.Model):
    """用户扩展信息"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    real_name = models.CharField('真实姓名', max_length=50, blank=True)
    id_card = models.CharField('身份证号', max_length=18, blank=True)
    address = models.TextField('地址', blank=True)
    emergency_contact = models.CharField('紧急联系人', max_length=50, blank=True)
    emergency_phone = models.CharField('紧急联系电话', max_length=11, blank=True)
    
    # 偏好设置
    notification_enabled = models.BooleanField('接收通知', default=True)
    sms_enabled = models.BooleanField('接收短信', default=True)
    email_enabled = models.BooleanField('接收邮件', default=True)
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'user_profiles'
        verbose_name = '用户资料'
        verbose_name_plural = '用户资料'
    
    def __str__(self):
        return f"{self.user.phone}的资料"


class LoginLog(models.Model):
    """登录日志"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='login_logs')
    login_type = models.CharField('登录方式', max_length=20, choices=[
        ('password', '密码登录'),
        ('sms', '短信登录'),
        ('wechat', '微信登录'),
    ])
    ip_address = models.GenericIPAddressField('IP地址')
    user_agent = models.TextField('用户代理', blank=True)
    device_info = models.JSONField('设备信息', default=dict, blank=True)
    login_time = models.DateTimeField('登录时间', auto_now_add=True)
    is_success = models.BooleanField('是否成功', default=True)
    failure_reason = models.CharField('失败原因', max_length=100, blank=True)
    
    class Meta:
        db_table = 'login_logs'
        verbose_name = '登录日志'
        verbose_name_plural = '登录日志'
        ordering = ['-login_time']
    
    def __str__(self):
        return f"{self.user.phone} - {self.login_time}"


class SMSCode(models.Model):
    """短信验证码"""
    phone = models.CharField('手机号', max_length=11)
    code = models.CharField('验证码', max_length=6)
    code_type = models.CharField('验证码类型', max_length=20, choices=[
        ('register', '注册'),
        ('login', '登录'),
        ('reset_password', '重置密码'),
        ('bind_phone', '绑定手机'),
    ])
    is_used = models.BooleanField('是否已使用', default=False)
    expire_time = models.DateTimeField('过期时间')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        db_table = 'sms_codes'
        verbose_name = '短信验证码'
        verbose_name_plural = '短信验证码'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.phone} - {self.code}"
    
    @property
    def is_expired(self):
        """是否已过期"""
        return timezone.now() > self.expire_time

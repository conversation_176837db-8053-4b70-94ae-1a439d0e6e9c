from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from .models import User, UserProfile, LoginLog, SMSCode


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """用户管理"""
    list_display = ['phone', 'nickname', 'avatar_preview', 'wechat_info', 'vip_level', 'is_phone_verified', 'is_active', 'created_at']
    list_filter = ['vip_level', 'is_phone_verified', 'is_active', 'created_at']
    search_fields = ['phone', 'nickname', 'username']
    ordering = ['-created_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('phone', 'username', 'nickname', 'avatar', 'gender', 'birthday')
        }),
        ('微信信息', {
            'fields': ('wechat_openid', 'wechat_unionid'),
            'classes': ('collapse',)
        }),
        ('VIP信息', {
            'fields': ('vip_level', 'vip_expire_time')
        }),
        ('状态信息', {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'is_phone_verified')
        }),
        ('时间信息', {
            'fields': ('last_login', 'date_joined', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['created_at', 'updated_at', 'date_joined']

    def avatar_preview(self, obj):
        """头像预览"""
        # 优先显示微信头像
        if hasattr(obj, 'wechat_user') and obj.wechat_user.avatar_url:
            return format_html(
                '<img src="{}" style="width: 40px; height: 40px; border-radius: 50%;" />',
                obj.wechat_user.avatar_url
            )
        # 其次显示用户上传的头像
        elif obj.avatar:
            return format_html(
                '<img src="{}" style="width: 40px; height: 40px; border-radius: 50%;" />',
                obj.avatar.url if hasattr(obj.avatar, 'url') else obj.avatar
            )
        return '无头像'
    avatar_preview.short_description = '头像'

    def wechat_info(self, obj):
        """微信信息"""
        if hasattr(obj, 'wechat_user'):
            wechat_user = obj.wechat_user
            return format_html(
                '<div style="font-size: 12px;">'
                '<div><strong>昵称:</strong> {}</div>'
                '<div><strong>性别:</strong> {}</div>'
                '<div><strong>地区:</strong> {}</div>'
                '</div>',
                wechat_user.nickname or '未设置',
                wechat_user.gender_display,
                f"{wechat_user.province} {wechat_user.city}".strip() or '未知'
            )
        return '未绑定微信'
    wechat_info.short_description = '微信信息'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('profile').prefetch_related('wechat_user')


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """用户资料管理"""
    list_display = ['user', 'real_name', 'notification_enabled', 'created_at']
    list_filter = ['notification_enabled', 'sms_enabled', 'email_enabled']
    search_fields = ['user__phone', 'real_name', 'id_card']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'real_name', 'id_card', 'address')
        }),
        ('紧急联系人', {
            'fields': ('emergency_contact', 'emergency_phone')
        }),
        ('通知设置', {
            'fields': ('notification_enabled', 'sms_enabled', 'email_enabled')
        }),
    )


@admin.register(LoginLog)
class LoginLogAdmin(admin.ModelAdmin):
    """登录日志管理"""
    list_display = ['user', 'login_type', 'ip_address', 'is_success', 'login_time']
    list_filter = ['login_type', 'is_success', 'login_time']
    search_fields = ['user__phone', 'ip_address']
    readonly_fields = ['user', 'login_type', 'ip_address', 'user_agent', 'device_info', 
                      'login_time', 'is_success', 'failure_reason']
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False


@admin.register(SMSCode)
class SMSCodeAdmin(admin.ModelAdmin):
    """短信验证码管理"""
    list_display = ['phone', 'code', 'code_type', 'is_used', 'expire_time', 'created_at']
    list_filter = ['code_type', 'is_used', 'created_at']
    search_fields = ['phone', 'code']
    readonly_fields = ['phone', 'code', 'code_type', 'is_used', 'expire_time', 'created_at']
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False

from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

router = DefaultRouter()
router.register(r'users', views.UserViewSet, basename='user')

urlpatterns = [
    # 认证相关
    path('register/', views.RegisterView.as_view(), name='register'),
    path('login/', views.LoginView.as_view(), name='login'),
    path('logout/', views.LogoutView.as_view(), name='logout'),
    path('wechat-login/', views.WechatLoginView.as_view(), name='wechat_login'),
    path('send-sms/', views.SendSMSView.as_view(), name='send_sms'),
    path('verify-sms/', views.VerifySMSView.as_view(), name='verify_sms'),
    path('reset-password/', views.ResetPasswordView.as_view(), name='reset_password'),
    
    # 用户信息
    path('profile/', views.UserProfileView.as_view(), name='user_profile'),
    path('update-profile/', views.UpdateProfileView.as_view(), name='update_profile'),
    path('change-password/', views.ChangePasswordView.as_view(), name='change_password'),
    
    # 用户统计
    path('statistics/', views.get_user_statistics, name='user_statistics'),

    # 其他路由
    path('', include(router.urls)),
]

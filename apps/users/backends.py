from django.contrib.auth.backends import ModelBackend
from django.contrib.auth import get_user_model
from django.db.models import Q

User = get_user_model()


class PhoneOrUsernameBackend(ModelBackend):
    """
    自定义认证后端，支持手机号或用户名登录
    """
    
    def authenticate(self, request, username=None, password=None, **kwargs):
        if username is None or password is None:
            return None
        
        try:
            # 尝试用手机号或用户名查找用户
            user = User.objects.get(
                Q(phone=username) | Q(username=username)
            )
        except User.DoesNotExist:
            # 如果用户不存在，运行默认密码哈希器以防止时序攻击
            User().set_password(password)
            return None
        
        # 检查密码
        if user.check_password(password) and self.user_can_authenticate(user):
            return user
        
        return None
    
    def user_can_authenticate(self, user):
        """
        检查用户是否可以认证
        """
        is_active = getattr(user, 'is_active', None)
        return is_active or is_active is None

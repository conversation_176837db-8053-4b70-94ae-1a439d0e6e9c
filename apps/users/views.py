from rest_framework import status, viewsets, permissions
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated, AllowAny
from django.contrib.auth import login, logout
from django.utils import timezone
from datetime import <PERSON>el<PERSON>
import random
import string

from .models import User, UserProfile, SMSCode, LoginLog
from .serializers import (
    UserSerializer, UserProfileSerializer, RegisterSerializer,
    LoginSerializer, WechatLoginSerializer, SMSCodeSerializer,
    ChangePasswordSerializer, ResetPasswordSerializer
)
from .authentication import generate_jwt_token
from utils.api_response import APIResponse, api_response_decorator
from utils.api_response import success_response, error_response, validation_error_response


class RegisterView(APIView):
    """用户注册"""
    permission_classes = [AllowAny]
    
    def post(self, request):
        serializer = RegisterSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            
            # 记录登录日志
            LoginLog.objects.create(
                user=user,
                login_type='password',
                ip_address=self.get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                is_success=True
            )
            
            # 生成token
            token = generate_jwt_token(user)
            
            return Response({
                'code': 200,
                'message': '注册成功',
                'data': {
                    'user_id': user.id,
                    'phone': user.phone,
                    'token': token,
                    'user_info': UserSerializer(user).data
                }
            }, status=status.HTTP_201_CREATED)
        
        return Response({
            'code': 400,
            'message': '注册失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class LoginView(APIView):
    """用户登录"""
    permission_classes = [AllowAny]
    
    def post(self, request):
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']
            
            # 记录登录日志
            LoginLog.objects.create(
                user=user,
                login_type='password',
                ip_address=self.get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                is_success=True
            )
            
            # 更新最后登录信息
            user.last_login = timezone.now()
            user.last_login_ip = self.get_client_ip(request)
            user.save(update_fields=['last_login', 'last_login_ip'])
            
            # 生成token
            token = generate_jwt_token(user)
            
            return Response({
                'code': 200,
                'message': '登录成功',
                'data': {
                    'user_id': user.id,
                    'phone': user.phone,
                    'token': token,
                    'user_info': UserSerializer(user).data
                }
            })
        
        return Response({
            'code': 400,
            'message': '登录失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class WechatLoginView(APIView):
    """微信登录"""
    permission_classes = [AllowAny]
    
    def post(self, request):
        serializer = WechatLoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']
            
            # 记录登录日志
            LoginLog.objects.create(
                user=user,
                login_type='wechat',
                ip_address=self.get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                is_success=True
            )
            
            # 更新最后登录信息
            user.last_login = timezone.now()
            user.last_login_ip = self.get_client_ip(request)
            user.save(update_fields=['last_login', 'last_login_ip'])
            
            # 生成token
            token = generate_jwt_token(user)
            
            return Response({
                'code': 200,
                'message': '登录成功',
                'data': {
                    'user_id': user.id,
                    'phone': user.phone,
                    'token': token,
                    'user_info': UserSerializer(user).data
                }
            })
        
        return Response({
            'code': 400,
            'message': '登录失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class LogoutView(APIView):
    """用户登出"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        # 这里可以将token加入黑名单
        return success_response(data=data, message="操作成功")


class SendSMSView(APIView):
    """发送短信验证码"""
    permission_classes = [AllowAny]
    
    def post(self, request):
        serializer = SMSCodeSerializer(data=request.data)
        if serializer.is_valid():
            phone = serializer.validated_data['phone']
            code_type = serializer.validated_data['code_type']
            
            # 检查发送频率限制
            recent_sms = SMSCode.objects.filter(
                phone=phone,
                created_at__gte=timezone.now() - timedelta(minutes=1)
            ).exists()
            
            if recent_sms:
                return Response({
                    'code': 400,
                    'message': '发送过于频繁，请稍后再试'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 生成验证码
            code = ''.join(random.choices(string.digits, k=6))
            
            # 保存验证码
            SMSCode.objects.create(
                phone=phone,
                code=code,
                code_type=code_type,
                expire_time=timezone.now() + timedelta(minutes=5)
            )
            
            # 这里应该调用短信服务发送验证码
            # 暂时模拟发送成功
            
            return Response({
                'code': 200,
                'message': '验证码发送成功',
                'data': {
                    'code': code  # 开发环境返回验证码，生产环境不返回
                }
            })
        
        return Response({
            'code': 400,
            'message': '发送失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class VerifySMSView(APIView):
    """验证短信验证码"""
    permission_classes = [AllowAny]
    
    def post(self, request):
        phone = request.data.get('phone')
        code = request.data.get('code')
        code_type = request.data.get('code_type')
        
        try:
            sms_code = SMSCode.objects.filter(
                phone=phone,
                code=code,
                code_type=code_type,
                is_used=False
            ).latest('created_at')
            
            if sms_code.is_expired:
                return Response({
                    'code': 400,
                    'message': '验证码已过期'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 标记为已使用
            sms_code.is_used = True
            sms_code.save()
            
            return success_response(data=data, message="操作成功")
            
        except SMSCode.DoesNotExist:
            return Response({
                'code': 400,
                'message': '验证码无效'
            }, status=status.HTTP_400_BAD_REQUEST)


class UserProfileView(APIView):
    """获取用户资料"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        try:
            profile = UserProfile.objects.get(user=request.user)
            serializer = UserProfileSerializer(profile)
            return success_response(data=data, message="操作成功")
        except UserProfile.DoesNotExist:
            # 如果没有资料，创建一个
            profile = UserProfile.objects.create(user=request.user)
            serializer = UserProfileSerializer(profile)
            return success_response(data=data, message="操作成功")


class UpdateProfileView(APIView):
    """更新用户资料"""
    permission_classes = [IsAuthenticated]
    
    def put(self, request):
        try:
            profile = UserProfile.objects.get(user=request.user)
        except UserProfile.DoesNotExist:
            profile = UserProfile.objects.create(user=request.user)
        
        # 更新用户基本信息
        user_data = {}
        if 'nickname' in request.data:
            user_data['nickname'] = request.data['nickname']
        if 'gender' in request.data:
            user_data['gender'] = request.data['gender']
        if 'birthday' in request.data:
            user_data['birthday'] = request.data['birthday']
        
        if user_data:
            user_serializer = UserSerializer(request.user, data=user_data, partial=True)
            if user_serializer.is_valid():
                user_serializer.save()
        
        # 更新用户资料
        profile_serializer = UserProfileSerializer(profile, data=request.data, partial=True)
        if profile_serializer.is_valid():
            profile_serializer.save()
            return success_response(data=data, message="操作成功")
        
        return Response({
            'code': 400,
            'message': '更新失败',
            'errors': profile_serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class ChangePasswordView(APIView):
    """修改密码"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        serializer = ChangePasswordSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            user = request.user
            user.set_password(serializer.validated_data['new_password'])
            user.save()
            
            return success_response(data=data, message="操作成功")
        
        return Response({
            'code': 400,
            'message': '修改失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class ResetPasswordView(APIView):
    """重置密码"""
    permission_classes = [AllowAny]
    
    def post(self, request):
        serializer = ResetPasswordSerializer(data=request.data)
        if serializer.is_valid():
            phone = serializer.validated_data['phone']
            verify_code = serializer.validated_data['verify_code']
            new_password = serializer.validated_data['new_password']
            
            # 标记验证码为已使用
            sms_code = SMSCode.objects.filter(
                phone=phone,
                code=verify_code,
                code_type='reset_password',
                is_used=False
            ).latest('created_at')
            sms_code.is_used = True
            sms_code.save()
            
            # 重置密码
            user = User.objects.get(phone=phone)
            user.set_password(new_password)
            user.save()
            
            return success_response(data=data, message="操作成功")
        
        return Response({
            'code': 400,
            'message': '重置失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class UserViewSet(viewsets.ModelViewSet):
    """用户管理ViewSet"""
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        # 普通用户只能查看自己的信息
        if not self.request.user.is_staff:
            return User.objects.filter(id=self.request.user.id)
        return super().get_queryset()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_statistics(request):
    """
    获取用户统计数据 - 支持个人中心页面
    """
    try:
        user = request.user

        # 获取用户的企业
        company = user.companies.first()

        # 基础统计
        stats = {
            'pending_tax_records': 0,
            'completed_tax_records': 0,
            'total_renewal_amount': 0,
            'current_month_submissions': 0,
            'total_financial_records': 0,
            'pending_financial_records': 0
        }

        if company:
            from apps.finance.models import FinancialRecord, TaxRecord
            from datetime import datetime

            current_year = datetime.now().year
            current_month = datetime.now().month

            # 财务记录统计
            stats['total_financial_records'] = FinancialRecord.objects.filter(
                company=company
            ).count()

            stats['pending_financial_records'] = FinancialRecord.objects.filter(
                company=company,
                status='pending'
            ).count()

            stats['current_month_submissions'] = FinancialRecord.objects.filter(
                company=company,
                year=current_year,
                month=current_month
            ).count()

            # 税务记录统计
            stats['pending_tax_records'] = TaxRecord.objects.filter(
                company=company,
                status='pending'
            ).count()

            stats['completed_tax_records'] = TaxRecord.objects.filter(
                company=company,
                status='completed'
            ).count()

            # 续费统计（模拟数据）
            stats['total_renewal_amount'] = 2394.00

        return APIResponse.success(data=stats)

    except Exception as e:
        return APIResponse.error(message=f'获取统计数据失败: {str(e)}')

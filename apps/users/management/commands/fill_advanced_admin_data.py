from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal
import random
from datetime import datetime, timedelta

User = get_user_model()

class Command(BaseCommand):
    help = '为高级后台管理模块补充完整的演示数据'

    def handle(self, *args, **options):
        self.stdout.write('开始补充高级后台管理模块演示数据...')
        
        # 财务审核模块
        self.create_finance_audit_data()
        
        # 订单管理模块
        self.create_order_management_data()
        
        # 税务日历模块
        self.create_tax_calendar_data()
        
        # 智能客服模块
        self.create_customer_service_data()
        
        # 发票管理模块
        self.create_invoice_management_data()
        
        # 高级数据分析模块
        self.create_analytics_advanced_data()
        
        # 第三方集成模块
        self.create_third_party_integration_data()
        
        # AI智能助手模块
        self.create_ai_assistant_data()
        
        # 智能数据分析助手模块
        self.create_smart_analytics_data()
        
        self.stdout.write(self.style.SUCCESS('高级后台管理模块演示数据补充完成！'))

    def create_finance_audit_data(self):
        self.stdout.write('创建财务审核演示数据...')
        
        try:
            from apps.finance_audit.models import (
                AuditQueue, AuditStandard, AuditDocument, 
                AuditComment, AuditTemplate, AuditStatistics, AuditWorkflow
            )
            from apps.companies.models import Company
            
            companies = Company.objects.all()[:5]
            
            # 创建审核标准
            standards = [
                {'name': '小规模纳税人审核标准', 'description': '适用于小规模纳税人的财务审核标准', 'is_active': True},
                {'name': '一般纳税人审核标准', 'description': '适用于一般纳税人的财务审核标准', 'is_active': True},
                {'name': '高新技术企业审核标准', 'description': '适用于高新技术企业的特殊审核标准', 'is_active': True},
            ]
            
            for standard_data in standards:
                standard, created = AuditStandard.objects.get_or_create(
                    name=standard_data['name'],
                    defaults=standard_data
                )
                if created:
                    self.stdout.write(f'  创建审核标准: {standard.name}')
            
            # 创建审核队列
            for company in companies:
                for i in range(random.randint(2, 5)):
                    queue_data = {
                        'company': company,
                        'audit_type': random.choice(['monthly', 'quarterly', 'annual']),
                        'priority': random.choice(['low', 'medium', 'high']),
                        'status': random.choice(['pending', 'in_progress', 'completed', 'rejected']),
                        'assigned_to': None,
                        'due_date': timezone.now().date() + timedelta(days=random.randint(1, 30)),
                        'description': f'{company.name}的{random.choice(["月度", "季度", "年度"])}财务审核'
                    }
                    
                    queue = AuditQueue.objects.create(**queue_data)
                    if i == 0:
                        self.stdout.write(f'  创建审核队列: {queue.company.name} - {queue.audit_type}')
            
        except ImportError:
            self.stdout.write('  财务审核模型不存在，跳过...')

    def create_order_management_data(self):
        self.stdout.write('创建订单管理演示数据...')
        
        try:
            from apps.order_management.models import (
                OrderAssignment, OrderProgress, OrderQuality,
                OrderTimeline, ServiceTeam, TeamMembership, OrderStatusFlow, OrderMetrics
            )
            from apps.services.models import Order
            
            orders = Order.objects.all()[:10]
            
            # 创建服务团队
            teams_data = [
                {'name': '财务审核团队', 'description': '专业的财务审核服务团队', 'is_active': True},
                {'name': '税务申报团队', 'description': '专业的税务申报服务团队', 'is_active': True},
                {'name': '代理记账团队', 'description': '专业的代理记账服务团队', 'is_active': True},
            ]
            
            for team_data in teams_data:
                team, created = ServiceTeam.objects.get_or_create(
                    name=team_data['name'],
                    defaults=team_data
                )
                if created:
                    self.stdout.write(f'  创建服务团队: {team.name}')
            
            # 创建订单分配
            for order in orders:
                assignment_data = {
                    'order': order,
                    'assigned_to': None,
                    'assigned_by': None,
                    'assignment_date': timezone.now(),
                    'status': random.choice(['assigned', 'in_progress', 'completed']),
                    'notes': f'订单{order.order_no}的服务分配'
                }
                
                assignment = OrderAssignment.objects.create(**assignment_data)
                if order == orders[0]:
                    self.stdout.write(f'  创建订单分配: {assignment.order.order_no}')
            
        except ImportError:
            self.stdout.write('  订单管理模型不存在，跳过...')

    def create_tax_calendar_data(self):
        self.stdout.write('创建税务日历演示数据...')
        
        try:
            from apps.tax_calendar.models import (
                TaxEvent, TaxEventType, TaxEventTemplate,
                TaxReminder, TaxPolicy, TaxCalendarSettings
            )
            
            # 创建税务事件类型
            event_types_data = [
                {'name': '增值税申报', 'description': '增值税纳税申报', 'color': '#ff6b6b'},
                {'name': '企业所得税申报', 'description': '企业所得税纳税申报', 'color': '#4ecdc4'},
                {'name': '个人所得税申报', 'description': '个人所得税纳税申报', 'color': '#45b7d1'},
                {'name': '印花税申报', 'description': '印花税纳税申报', 'color': '#f9ca24'},
            ]
            
            for type_data in event_types_data:
                event_type, created = TaxEventType.objects.get_or_create(
                    name=type_data['name'],
                    defaults=type_data
                )
                if created:
                    self.stdout.write(f'  创建税务事件类型: {event_type.name}')
            
            # 创建税务政策
            policies_data = [
                {
                    'title': '小规模纳税人减税政策',
                    'content': '小规模纳税人月销售额15万元以下免征增值税',
                    'policy_type': 'tax_reduction',
                    'effective_date': timezone.now().date(),
                    'is_active': True
                },
                {
                    'title': '研发费用加计扣除政策',
                    'content': '企业研发费用可按175%在税前扣除',
                    'policy_type': 'deduction',
                    'effective_date': timezone.now().date(),
                    'is_active': True
                }
            ]
            
            for policy_data in policies_data:
                policy, created = TaxPolicy.objects.get_or_create(
                    title=policy_data['title'],
                    defaults=policy_data
                )
                if created:
                    self.stdout.write(f'  创建税务政策: {policy.title}')
            
        except ImportError:
            self.stdout.write('  税务日历模型不存在，跳过...')

    def create_customer_service_data(self):
        self.stdout.write('创建智能客服演示数据...')
        
        try:
            from apps.customer_service.models import (
                ServiceTicket, TicketMessage, KnowledgeArticle,
                KnowledgeCategory, QuickReply, ServiceAgent, ServiceCategory, ServiceMetrics, CustomerFeedback
            )
            
            # 创建知识库分类
            categories_data = [
                {'name': '财务问题', 'description': '财务相关问题解答', 'is_active': True},
                {'name': '税务问题', 'description': '税务相关问题解答', 'is_active': True},
                {'name': '系统使用', 'description': '系统使用相关问题', 'is_active': True},
            ]
            
            for category_data in categories_data:
                category, created = KnowledgeCategory.objects.get_or_create(
                    name=category_data['name'],
                    defaults=category_data
                )
                if created:
                    self.stdout.write(f'  创建知识库分类: {category.name}')
            
            # 创建知识库文章
            categories = KnowledgeCategory.objects.all()
            for category in categories:
                for i in range(3):
                    article_data = {
                        'title': f'{category.name}常见问题{i+1}',
                        'content': f'这是关于{category.name}的详细解答内容...',
                        'category': category,
                        'is_published': True,
                        'view_count': random.randint(10, 1000),
                        'helpful_count': random.randint(5, 100)
                    }
                    
                    article = KnowledgeArticle.objects.create(**article_data)
                    if i == 0:
                        self.stdout.write(f'  创建知识库文章: {article.title}')
            
            # 创建快捷回复
            quick_replies_data = [
                {'title': '欢迎语', 'content': '您好！欢迎使用财税通智能客服，我是您的专属助手，有什么可以帮助您的吗？'},
                {'title': '转人工', 'content': '正在为您转接人工客服，请稍候...'},
                {'title': '感谢语', 'content': '感谢您的咨询，如果还有其他问题，随时联系我们！'},
            ]
            
            for reply_data in quick_replies_data:
                reply, created = QuickReply.objects.get_or_create(
                    title=reply_data['title'],
                    defaults=reply_data
                )
                if created:
                    self.stdout.write(f'  创建快捷回复: {reply.title}')
            
        except ImportError:
            self.stdout.write('  智能客服模型不存在，跳过...')

    def create_invoice_management_data(self):
        self.stdout.write('创建发票管理演示数据...')
        
        try:
            from apps.invoice_management.models import (
                Invoice, InvoiceType, InvoiceItem, InvoiceAuthentication,
                InvoiceDeduction, InvoiceTemplate, InvoiceValidation, InvoiceStatistics, InvoiceReminder
            )
            from apps.companies.models import Company
            
            companies = Company.objects.all()[:5]
            
            # 创建发票类型
            types_data = [
                {'name': '增值税专用发票', 'code': 'VAT_SPECIAL', 'description': '增值税专用发票'},
                {'name': '增值税普通发票', 'code': 'VAT_ORDINARY', 'description': '增值税普通发票'},
                {'name': '电子发票', 'code': 'ELECTRONIC', 'description': '电子发票'},
            ]
            
            for type_data in types_data:
                invoice_type, created = InvoiceType.objects.get_or_create(
                    code=type_data['code'],
                    defaults=type_data
                )
                if created:
                    self.stdout.write(f'  创建发票类型: {invoice_type.name}')
            
            # 创建发票记录
            invoice_types = InvoiceType.objects.all()
            for company in companies:
                for i in range(random.randint(3, 8)):
                    invoice_data = {
                        'company': company,
                        'invoice_type': random.choice(invoice_types),
                        'invoice_number': f'INV{random.randint(100000, 999999)}',
                        'invoice_date': timezone.now().date() - timedelta(days=random.randint(1, 90)),
                        'amount': Decimal(str(random.randint(1000, 50000))),
                        'tax_amount': Decimal(str(random.randint(100, 5000))),
                        'status': random.choice(['draft', 'issued', 'authenticated', 'deducted']),
                        'buyer_name': f'客户{random.randint(1, 100)}',
                        'seller_name': company.name,
                        'description': f'{company.name}开具的发票'
                    }
                    
                    invoice = Invoice.objects.create(**invoice_data)
                    if i == 0:
                        self.stdout.write(f'  创建发票: {invoice.invoice_number}')
            
        except ImportError:
            self.stdout.write('  发票管理模型不存在，跳过...')

    def create_analytics_advanced_data(self):
        self.stdout.write('创建高级数据分析演示数据...')
        
        try:
            from apps.analytics_advanced.models import (
                ReportCategory, ReportTemplate, ReportInstance,
                Dashboard, DataSource, KPIMetric, KPIValue, AnalyticsJob, DataQualityRule, DataQualityResult
            )
            
            # 创建报表分类
            categories_data = [
                {'name': '财务报表', 'description': '财务相关报表', 'is_active': True},
                {'name': '税务报表', 'description': '税务相关报表', 'is_active': True},
                {'name': '经营报表', 'description': '经营分析报表', 'is_active': True},
            ]
            
            for category_data in categories_data:
                category, created = ReportCategory.objects.get_or_create(
                    name=category_data['name'],
                    defaults=category_data
                )
                if created:
                    self.stdout.write(f'  创建报表分类: {category.name}')
            
            # 创建KPI指标
            kpi_data = [
                {'name': '月度营业收入', 'description': '每月营业收入统计', 'unit': '元', 'target_value': 1000000},
                {'name': '客户满意度', 'description': '客户满意度评分', 'unit': '分', 'target_value': 95},
                {'name': '订单完成率', 'description': '订单按时完成率', 'unit': '%', 'target_value': 98},
            ]
            
            for kpi in kpi_data:
                metric, created = KPIMetric.objects.get_or_create(
                    name=kpi['name'],
                    defaults=kpi
                )
                if created:
                    self.stdout.write(f'  创建KPI指标: {metric.name}')
            
        except ImportError:
            self.stdout.write('  高级数据分析模型不存在，跳过...')

    def create_third_party_integration_data(self):
        self.stdout.write('创建第三方集成演示数据...')
        
        try:
            from apps.third_party_integration.models import (
                ThirdPartyProvider, APIRequest, TaxBureauInterface,
                BankInterface, OCRService, ESignatureService, IntegrationTask, IntegrationLog, IntegrationConfig
            )
            
            # 创建第三方服务提供商
            providers_data = [
                {'name': '国家税务总局', 'provider_type': 'tax_bureau', 'is_active': True, 'description': '税务局官方接口'},
                {'name': '中国银行', 'provider_type': 'bank', 'is_active': True, 'description': '银行接口服务'},
                {'name': '百度OCR', 'provider_type': 'ocr', 'is_active': True, 'description': 'OCR识别服务'},
                {'name': '法大大电子签', 'provider_type': 'esignature', 'is_active': True, 'description': '电子签章服务'},
            ]
            
            for provider_data in providers_data:
                provider, created = ThirdPartyProvider.objects.get_or_create(
                    name=provider_data['name'],
                    defaults=provider_data
                )
                if created:
                    self.stdout.write(f'  创建第三方服务提供商: {provider.name}')
            
        except ImportError:
            self.stdout.write('  第三方集成模型不存在，跳过...')

    def create_ai_assistant_data(self):
        self.stdout.write('创建AI智能助手演示数据...')
        
        try:
            from apps.ai_assistant.models import (
                KnowledgeBase, ConversationSession, ConversationMessage,
                AIModel, IntentPattern, AIAssistantConfig, AIAssistantLog
            )
            
            # 创建知识库
            knowledge_data = [
                {'title': '财务基础知识', 'content': '财务基础知识库内容...', 'category': 'finance', 'is_active': True},
                {'title': '税务政策解读', 'content': '税务政策解读内容...', 'category': 'tax', 'is_active': True},
                {'title': '系统操作指南', 'content': '系统操作指南内容...', 'category': 'system', 'is_active': True},
            ]
            
            for kb_data in knowledge_data:
                kb, created = KnowledgeBase.objects.get_or_create(
                    title=kb_data['title'],
                    defaults=kb_data
                )
                if created:
                    self.stdout.write(f'  创建知识库: {kb.title}')
            
            # 创建AI模型
            models_data = [
                {'name': 'GPT-3.5-Turbo', 'model_type': 'language_model', 'version': '1.0', 'is_active': True},
                {'name': '财税专用模型', 'model_type': 'domain_specific', 'version': '1.0', 'is_active': True},
            ]
            
            for model_data in models_data:
                model, created = AIModel.objects.get_or_create(
                    name=model_data['name'],
                    defaults=model_data
                )
                if created:
                    self.stdout.write(f'  创建AI模型: {model.name}')
            
        except ImportError:
            self.stdout.write('  AI智能助手模型不存在，跳过...')

    def create_smart_analytics_data(self):
        self.stdout.write('创建智能数据分析助手演示数据...')
        
        try:
            from apps.smart_analytics.models import (
                SmartDataSource, AnalysisModel, AnalysisTask,
                DataInsight, PredictionResult, AnomalyDetection, SmartReport, SmartAnalyticsConfig
            )
            
            # 创建数据源
            datasources_data = [
                {'name': '财务数据源', 'source_type': 'database', 'connection_string': 'postgresql://...', 'is_active': True},
                {'name': '订单数据源', 'source_type': 'api', 'connection_string': 'https://api.example.com', 'is_active': True},
                {'name': '用户行为数据源', 'source_type': 'file', 'connection_string': '/data/user_behavior.csv', 'is_active': True},
            ]
            
            for ds_data in datasources_data:
                datasource, created = SmartDataSource.objects.get_or_create(
                    name=ds_data['name'],
                    defaults=ds_data
                )
                if created:
                    self.stdout.write(f'  创建数据源: {datasource.name}')
            
            # 创建分析模型
            models_data = [
                {'name': '收入预测模型', 'model_type': 'regression', 'algorithm': 'linear_regression', 'is_active': True},
                {'name': '客户流失预测', 'model_type': 'classification', 'algorithm': 'random_forest', 'is_active': True},
                {'name': '异常检测模型', 'model_type': 'anomaly_detection', 'algorithm': 'isolation_forest', 'is_active': True},
            ]
            
            for model_data in models_data:
                model, created = AnalysisModel.objects.get_or_create(
                    name=model_data['name'],
                    defaults=model_data
                )
                if created:
                    self.stdout.write(f'  创建分析模型: {model.name}')
            
        except ImportError:
            self.stdout.write('  智能数据分析助手模型不存在，跳过...')

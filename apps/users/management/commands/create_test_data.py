from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal
import random
from datetime import datetime, timedelta

from apps.users.models import User
from apps.companies.models import Company
from apps.services.models import ServicePackage
from apps.finance.models import FinancialRecord, SimpleFinancialRecord
from apps.notifications.models import Message, MessageRecipient, Notification
from apps.payments.models import Payment
from apps.files.models import FileUpload
from apps.wechat.models import WechatUser, WechatConfig

class Command(BaseCommand):
    help = '创建测试数据供后台管理系统体验'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='清除现有测试数据',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.clear_test_data()
            return

        self.stdout.write('开始创建测试数据...')
        
        # 创建测试用户
        self.create_test_users()
        
        # 创建测试企业
        self.create_test_companies()
        
        # 创建财务记录
        self.create_finance_records()
        
        # 创建通知
        self.create_notifications()
        
        # 创建支付记录
        self.create_payments()
        
        # 创建文件记录
        self.create_files()
        
        # 创建微信配置
        self.create_wechat_config()
        
        self.stdout.write(self.style.SUCCESS('测试数据创建完成！'))

    def clear_test_data(self):
        self.stdout.write('清除测试数据...')
        
        # 清除非管理员用户
        User.objects.filter(is_superuser=False).delete()
        Company.objects.all().delete()
        FinancialRecord.objects.all().delete()
        SimpleFinancialRecord.objects.all().delete()
        Message.objects.all().delete()
        MessageRecipient.objects.all().delete()
        Notification.objects.all().delete()
        Payment.objects.all().delete()
        FileUpload.objects.all().delete()
        WechatUser.objects.all().delete()
        
        self.stdout.write(self.style.SUCCESS('测试数据清除完成！'))

    def create_test_users(self):
        self.stdout.write('创建测试用户...')
        
        # 创建普通用户
        users_data = [
            {
                'username': 'test_user1',
                'email': '<EMAIL>',
                'phone': '13800138001',
                'first_name': '张',
                'last_name': '三',
                'is_active': True
            },
            {
                'username': 'test_user2', 
                'email': '<EMAIL>',
                'phone': '13800138002',
                'first_name': '李',
                'last_name': '四',
                'is_active': True
            },
            {
                'username': 'test_user3',
                'email': '<EMAIL>', 
                'phone': '13800138003',
                'first_name': '王',
                'last_name': '五',
                'is_active': False
            }
        ]
        
        for user_data in users_data:
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults=user_data
            )
            if created:
                user.set_password('123456')
                user.save()
                self.stdout.write(f'  创建用户: {user.username}')

    def create_test_companies(self):
        self.stdout.write('创建测试企业...')
        
        users = User.objects.filter(is_superuser=False)[:3]
        
        companies_data = [
            {
                'name': '北京科技有限公司',
                'credit_code': '91110000123456789A',
                'company_type': 'limited',
                'legal_person': '张三',
                'address': '北京市朝阳区科技园区1号',
                'phone': '010-12345678',
                'status': 'active'
            },
            {
                'name': '上海贸易有限公司',
                'credit_code': '91310000987654321B',
                'company_type': 'limited',
                'legal_person': '李四',
                'address': '上海市浦东新区金融街2号',
                'phone': '021-87654321',
                'status': 'active'
            },
            {
                'name': '深圳创新科技公司',
                'credit_code': '91440300555666777C',
                'company_type': 'limited',
                'legal_person': '王五',
                'address': '深圳市南山区高新园3号',
                'phone': '0755-88888888',
                'status': 'pending'
            }
        ]
        
        for i, company_data in enumerate(companies_data):
            if i < len(users):
                company_data['owner'] = users[i]
            
            company, created = Company.objects.get_or_create(
                credit_code=company_data['credit_code'],
                defaults=company_data
            )
            if created:
                self.stdout.write(f'  创建企业: {company.name}')

    def create_finance_records(self):
        self.stdout.write('创建财务记录...')

        companies = Company.objects.all()
        users = User.objects.filter(is_superuser=False)

        for i, company in enumerate(companies):
            user = users[i] if i < len(users) else users[0]

            # 创建财务记录
            record_data = {
                'company': company,
                'user': user,
                'year': 2024,
                'month': random.randint(1, 12),
                'main_business_income': Decimal(str(random.randint(50000, 200000))),
                'other_business_income': Decimal(str(random.randint(5000, 20000))),
                'main_business_cost': Decimal(str(random.randint(30000, 120000))),
                'management_expense': Decimal(str(random.randint(10000, 50000))),
                'sales_expense': Decimal(str(random.randint(5000, 30000))),
                'vat_payable': Decimal(str(random.randint(3000, 15000))),
                'income_tax_payable': Decimal(str(random.randint(2000, 10000))),
                'status': random.choice(['submitted', 'processing', 'completed'])
            }

            record, created = FinancialRecord.objects.get_or_create(
                company=company,
                year=record_data['year'],
                month=record_data['month'],
                defaults=record_data
            )

            if created:
                self.stdout.write(f'  创建财务记录: {record}')

            # 创建简单财务记录
            for j in range(3):
                simple_record_data = {
                    'user': user,
                    'company': company,
                    'record_type': random.choice(['income', 'expense']),
                    'transaction_date': timezone.now().date() - timedelta(days=random.randint(1, 30)),
                    'amount': Decimal(str(random.randint(1000, 50000))),
                    'description': f'{random.choice(["销售收入", "办公费用", "差旅费", "设备采购", "服务费"])}',
                    'category': random.choice(['sales', 'office', 'travel', 'equipment', 'service']),
                    'payment_method': random.choice(['cash', 'bank_transfer', 'alipay', 'wechat']),
                    'status': random.choice(['pending', 'approved', 'rejected'])
                }

                simple_record = SimpleFinancialRecord.objects.create(**simple_record_data)
                if j == 0:  # 只显示第一条记录
                    self.stdout.write(f'  创建简单财务记录: {simple_record.description}')

    def create_notifications(self):
        self.stdout.write('创建通知消息...')
        
        users = User.objects.filter(is_superuser=False)
        
        notifications_data = [
            {
                'title': '税务申报提醒',
                'message': '您的企业需要在本月15日前完成增值税申报',
                'notification_type': 'tax_reminder',
                'is_read': False
            },
            {
                'title': '财务审核完成',
                'message': '您提交的财务资料已审核通过',
                'notification_type': 'finance_approved',
                'is_read': True
            },
            {
                'title': '服务到期提醒',
                'message': '您的代理记账服务将在7天后到期，请及时续费',
                'notification_type': 'service_expiry',
                'is_read': False
            }
        ]
        
        for user in users:
            for notif_data in notifications_data:
                notif_data['user'] = user
                notification = Notification.objects.create(**notif_data)
                if user == users[0]:  # 只显示第一个用户的通知
                    self.stdout.write(f'  创建通知: {notification.title}')

    def create_payments(self):
        self.stdout.write('创建支付记录...')
        
        users = User.objects.filter(is_superuser=False)
        
        for user in users:
            payment_data = {
                'user': user,
                'amount': Decimal(str(random.randint(199, 999))),
                'payment_method': random.choice(['wechat', 'alipay', 'bank']),
                'status': random.choice(['pending', 'completed', 'failed']),
                'order_no': f'ORDER{random.randint(100000, 999999)}',
                'description': '代理记账服务费用'
            }
            
            payment = Payment.objects.create(**payment_data)
            if user == users[0]:  # 只显示第一个用户的支付
                self.stdout.write(f'  创建支付记录: {payment.order_no}')

    def create_files(self):
        self.stdout.write('创建文件记录...')
        
        users = User.objects.filter(is_superuser=False)
        
        file_types = [
            ('营业执照.pdf', 'license'),
            ('财务报表.xlsx', 'financial'),
            ('发票.pdf', 'invoice'),
            ('合同.pdf', 'contract')
        ]
        
        for user in users:
            for filename, file_type in file_types:
                file_data = {
                    'user': user,
                    'filename': filename,
                    'file_type': file_type,
                    'file_size': random.randint(100000, 5000000),
                    'file_path': f'/uploads/{user.username}/{filename}',
                    'status': random.choice(['uploaded', 'processing', 'completed'])
                }
                
                file_obj = FileUpload.objects.create(**file_data)
                if user == users[0]:  # 只显示第一个用户的文件
                    self.stdout.write(f'  创建文件记录: {file_obj.filename}')

    def create_wechat_config(self):
        self.stdout.write('创建微信配置...')
        
        # 创建微信小程序配置
        config, created = WechatConfig.objects.get_or_create(
            name='代理记账专家',
            defaults={
                'app_id': 'wx1234567890abcdef',
                'app_secret': 'your_app_secret_here',
                'enable_user_auth': True,
                'enable_payment': False,
                'enable_message': False
            }
        )
        
        if created:
            self.stdout.write(f'  创建微信配置: {config.name}')
        
        # 创建一些微信用户
        users = User.objects.filter(is_superuser=False)[:2]
        
        for i, user in enumerate(users):
            wechat_user_data = {
                'user': user,
                'openid': f'openid_{i+1}_test',
                'unionid': f'unionid_{i+1}_test',
                'nickname': f'微信用户{i+1}',
                'avatar_url': f'https://example.com/avatar{i+1}.jpg',
                'gender': random.choice([1, 2]),
                'city': random.choice(['北京', '上海', '深圳']),
                'province': random.choice(['北京', '上海', '广东']),
                'country': '中国'
            }
            
            wechat_user, created = WechatUser.objects.get_or_create(
                openid=wechat_user_data['openid'],
                defaults=wechat_user_data
            )
            
            if created:
                self.stdout.write(f'  创建微信用户: {wechat_user.nickname}')

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal
import random
from datetime import datetime, timedelta

User = get_user_model()

class Command(BaseCommand):
    help = '为后台管理系统补充完整的测试数据'

    def handle(self, *args, **options):
        self.stdout.write('开始补充后台管理系统测试数据...')
        
        # 创建服务套餐和订单
        self.create_service_packages()
        
        # 创建优惠券
        self.create_coupons()

        # 创建订单
        self.create_orders()

        # 创建支付记录
        self.create_payments()
        
        # 创建文件上传记录
        self.create_file_uploads()
        
        # 创建帮助文章
        self.create_help_articles()
        
        # 创建访问日志
        self.create_access_logs()
        
        self.stdout.write(self.style.SUCCESS('后台管理系统测试数据补充完成！'))

    def create_service_packages(self):
        self.stdout.write('创建服务套餐和订单...')
        
        try:
            from apps.services.models import ServicePackage
            from apps.companies.models import Company
            
            # 创建服务套餐
            packages_data = [
                {
                    'name': '小规模纳税人基础版',
                    'package_type': 'small_scale',
                    'description': '适合初创企业和小微企业的基础财税服务',
                    'monthly_price': Decimal('299.00'),
                    'yearly_price': Decimal('2990.00'),
                    'max_transactions': 100,
                    'features': ['月度记账', '报税申报', '财务报表', '专属客服'],
                    'includes_bookkeeping': True,
                    'includes_tax_filing': True,
                    'includes_financial_reports': True,
                    'status': 'active',
                    'sort_order': 1
                },
                {
                    'name': '一般纳税人标准版',
                    'package_type': 'general',
                    'description': '适合中小企业的全面财税服务',
                    'monthly_price': Decimal('599.00'),
                    'yearly_price': Decimal('5990.00'),
                    'max_transactions': 500,
                    'features': ['月度记账', '报税申报', '财务报表', '税务筹划', '专属客服'],
                    'includes_bookkeeping': True,
                    'includes_tax_filing': True,
                    'includes_financial_reports': True,
                    'includes_tax_planning': True,
                    'status': 'active',
                    'sort_order': 2
                },
                {
                    'name': '企业高级版',
                    'package_type': 'general',
                    'description': '适合大中型企业的专业财税服务',
                    'monthly_price': Decimal('1299.00'),
                    'yearly_price': Decimal('12990.00'),
                    'max_transactions': 2000,
                    'features': ['月度记账', '报税申报', '财务报表', '税务筹划', '财务分析', '专属会计师'],
                    'includes_bookkeeping': True,
                    'includes_tax_filing': True,
                    'includes_financial_reports': True,
                    'includes_tax_planning': True,
                    'includes_consultation': True,
                    'status': 'active',
                    'is_recommended': True,
                    'sort_order': 3
                }
            ]
            
            for pkg_data in packages_data:
                package, created = ServicePackage.objects.get_or_create(
                    name=pkg_data['name'],
                    defaults=pkg_data
                )
                if created:
                    self.stdout.write(f'  创建服务套餐: {package.name}')
                    
        except ImportError:
            self.stdout.write('  服务套餐模型不存在，跳过...')

    def create_coupons(self):
        self.stdout.write('创建优惠券...')
        
        try:
            from apps.services.models import Coupon, UserCoupon
            
            # 创建优惠券
            coupons_data = [
                {
                    'name': '新用户专享券',
                    'code': 'NEW2024',
                    'coupon_type': 'amount',
                    'discount_amount': Decimal('100.00'),
                    'min_amount': Decimal('299.00'),
                    'usage_limit': 1000,
                    'used_count': 156,
                    'start_date': timezone.now(),
                    'end_date': timezone.now() + timedelta(days=90),
                    'status': 'active'
                },
                {
                    'name': '春节特惠券',
                    'code': 'SPRING2024',
                    'coupon_type': 'discount',
                    'discount_rate': Decimal('0.80'),
                    'min_amount': Decimal('500.00'),
                    'usage_limit': 500,
                    'used_count': 89,
                    'start_date': timezone.now() - timedelta(days=30),
                    'end_date': timezone.now() + timedelta(days=30),
                    'status': 'active'
                },
                {
                    'name': '企业专享券',
                    'code': 'ENTERPRISE2024',
                    'coupon_type': 'amount',
                    'discount_amount': Decimal('300.00'),
                    'min_amount': Decimal('1000.00'),
                    'usage_limit': 200,
                    'used_count': 23,
                    'start_date': timezone.now(),
                    'end_date': timezone.now() + timedelta(days=180),
                    'status': 'active'
                }
            ]
            
            users = User.objects.filter(is_superuser=False)[:5]
            
            for coupon_data in coupons_data:
                coupon, created = Coupon.objects.get_or_create(
                    code=coupon_data['code'],
                    defaults=coupon_data
                )
                if created:
                    self.stdout.write(f'  创建优惠券: {coupon.name}')
                    
                    # 为用户分配优惠券
                    for user in users:
                        user_coupon_data = {
                            'user': user,
                            'coupon': coupon,
                            'status': random.choice(['unused', 'used', 'expired']),
                            'obtained_at': timezone.now() - timedelta(days=random.randint(1, 30))
                        }

                        if user_coupon_data['status'] == 'used':
                            user_coupon_data['used_at'] = timezone.now() - timedelta(days=random.randint(1, 15))
                        
                        UserCoupon.objects.get_or_create(
                            user=user,
                            coupon=coupon,
                            defaults=user_coupon_data
                        )
                        
        except ImportError:
            self.stdout.write('  优惠券模型不存在，跳过...')

    def create_orders(self):
        self.stdout.write('创建订单数据...')

        try:
            from apps.services.models import Order, ServicePackage
            from apps.companies.models import Company

            packages = ServicePackage.objects.all()
            companies = Company.objects.all()
            users = User.objects.filter(is_superuser=False)[:5]

            if not packages.exists() or not companies.exists():
                self.stdout.write('  缺少套餐或企业数据，跳过订单创建...')
                return

            for i, user in enumerate(users):
                company = companies[i % companies.count()]
                package = packages[i % packages.count()]

                # 创建订单
                order_data = {
                    'user': user,
                    'company': company,
                    'package': package,
                    'package_name': package.name,
                    'payment_period': random.choice(['monthly', 'yearly']),
                    'months': random.choice([1, 12]),
                    'original_price': package.monthly_price if random.choice([True, False]) else package.yearly_price or package.monthly_price * 12,
                    'discount_amount': Decimal(str(random.randint(0, 100))),
                    'status': random.choice(['pending', 'paid', 'active', 'expired']),
                    'is_first_order': random.choice([True, False])
                }

                order_data['final_price'] = order_data['original_price'] - order_data['discount_amount']

                if order_data['status'] in ['paid', 'active']:
                    order_data['paid_at'] = timezone.now() - timedelta(days=random.randint(1, 30))
                    order_data['payment_method'] = random.choice(['wechat', 'alipay'])
                    order_data['transaction_id'] = f'TXN{random.randint(**********, **********)}'
                    order_data['service_start_date'] = timezone.now().date() - timedelta(days=random.randint(1, 30))
                    order_data['service_end_date'] = order_data['service_start_date'] + timedelta(days=order_data['months'] * 30)

                order = Order.objects.create(**order_data)
                if i == 0:  # 只显示第一条记录
                    self.stdout.write(f'  创建订单: {order.order_no} - {order.final_price}元')

        except ImportError:
            self.stdout.write('  订单模型不存在，跳过...')

    def create_payments(self):
        self.stdout.write('创建支付记录...')

        try:
            from apps.payments.models import Payment
            from apps.services.models import Order

            orders = Order.objects.all()[:10]  # 取前10个订单

            for order in orders:
                payment_data = {
                    'order': order,
                    'user': order.user,
                    'amount': order.final_price,
                    'payment_method': random.choice(['wechat', 'alipay', 'bank_transfer']),
                    'status': random.choice(['pending', 'success', 'failed']),
                    'third_party_order_no': f'TXN{random.randint(**********, **********)}',
                    'paid_at': timezone.now() - timedelta(days=random.randint(1, 60)) if random.choice([True, False]) else None
                }

                payment = Payment.objects.create(**payment_data)
                if order == orders[0]:  # 只显示第一条记录
                    self.stdout.write(f'  创建支付记录: {payment.payment_no} - {payment.amount}元')

        except ImportError:
            self.stdout.write('  支付模型不存在，跳过...')

    def create_file_uploads(self):
        self.stdout.write('创建文件上传记录...')
        
        try:
            from apps.files.models import FileUpload
            
            users = User.objects.filter(is_superuser=False)[:5]
            
            file_types = [
                ('营业执照.pdf', 'license', 'application/pdf'),
                ('财务报表.xlsx', 'financial', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
                ('发票.pdf', 'invoice', 'application/pdf'),
                ('合同.pdf', 'contract', 'application/pdf'),
                ('银行流水.xlsx', 'bank_statement', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            ]
            
            for user in users:
                for filename, file_type, mime_type in file_types:
                    file_data = {
                        'user': user,
                        'filename': filename,
                        'file_type': file_type,
                        'file_size': random.randint(100000, 5000000),
                        'mime_type': mime_type,
                        'file_path': f'/uploads/{user.username}/{filename}',
                        'status': random.choice(['uploaded', 'processing', 'completed', 'failed']),
                        'description': f'{user.username}上传的{filename}'
                    }
                    
                    file_obj = FileUpload.objects.create(**file_data)
                    if filename == '营业执照.pdf':  # 只显示第一个文件
                        self.stdout.write(f'  创建文件记录: {file_obj.filename}')
                        
        except ImportError:
            self.stdout.write('  文件模型不存在，跳过...')

    def create_help_articles(self):
        self.stdout.write('创建帮助文章...')
        
        try:
            from apps.support.models import HelpArticle, HelpCategory
            
            # 创建帮助分类
            categories_data = [
                {'name': '新手指南', 'description': '新用户使用指南', 'sort_order': 1},
                {'name': '财务记账', 'description': '财务记账相关问题', 'sort_order': 2},
                {'name': '税务申报', 'description': '税务申报相关问题', 'sort_order': 3},
                {'name': '常见问题', 'description': '用户常见问题解答', 'sort_order': 4}
            ]
            
            for cat_data in categories_data:
                category, created = HelpCategory.objects.get_or_create(
                    name=cat_data['name'],
                    defaults=cat_data
                )
                if created:
                    self.stdout.write(f'  创建帮助分类: {category.name}')
            
            # 创建帮助文章
            articles_data = [
                {
                    'title': '如何注册和登录系统',
                    'content': '本文将详细介绍如何注册账户和登录系统...',
                    'summary': '新用户注册和登录指南',
                    'category': HelpCategory.objects.get(name='新手指南'),
                    'status': 'published',
                    'view_count': random.randint(100, 1000)
                },
                {
                    'title': '如何添加财务记录',
                    'content': '本文将介绍如何正确添加和管理财务记录...',
                    'summary': '财务记录添加和管理教程',
                    'category': HelpCategory.objects.get(name='财务记账'),
                    'status': 'published',
                    'view_count': random.randint(100, 1000)
                },
                {
                    'title': '税务申报流程详解',
                    'content': '本文将详细说明税务申报的完整流程...',
                    'summary': '完整的税务申报操作指南',
                    'category': HelpCategory.objects.get(name='税务申报'),
                    'status': 'published',
                    'view_count': random.randint(100, 1000)
                }
            ]
            
            for article_data in articles_data:
                article, created = HelpArticle.objects.get_or_create(
                    title=article_data['title'],
                    defaults=article_data
                )
                if created:
                    self.stdout.write(f'  创建帮助文章: {article.title}')
                    
        except ImportError:
            self.stdout.write('  帮助文章模型不存在，跳过...')

    def create_access_logs(self):
        self.stdout.write('创建访问日志...')
        
        try:
            from apps.users.models import AccessLog
            
            users = User.objects.all()[:5]
            
            for user in users:
                for i in range(random.randint(5, 15)):
                    log_data = {
                        'user': user,
                        'ip_address': f'192.168.1.{random.randint(1, 255)}',
                        'user_agent': random.choice([
                            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15'
                        ]),
                        'path': random.choice(['/dashboard/', '/finance/records/', '/orders/', '/profile/']),
                        'method': 'GET',
                        'status_code': random.choice([200, 200, 200, 404, 500]),
                        'created_at': timezone.now() - timedelta(days=random.randint(1, 30))
                    }
                    
                    AccessLog.objects.create(**log_data)
                    
                if user == users[0]:  # 只显示第一个用户的日志
                    self.stdout.write(f'  创建访问日志: {user.username} - 15条记录')
                    
        except ImportError:
            self.stdout.write('  访问日志模型不存在，跳过...')

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal
import random
from datetime import datetime, timedelta

User = get_user_model()

class Command(BaseCommand):
    help = '创建简单测试数据供后台管理系统体验'

    def handle(self, *args, **options):
        self.stdout.write('开始创建简单测试数据...')
        
        # 创建测试用户
        self.create_test_users()
        
        # 创建企业数据
        self.create_companies()
        
        # 创建财务记录
        self.create_finance_records()
        
        # 创建微信配置
        self.create_wechat_config()
        
        self.stdout.write(self.style.SUCCESS('简单测试数据创建完成！'))

    def create_test_users(self):
        self.stdout.write('创建测试用户...')
        
        # 创建普通用户
        users_data = [
            {
                'username': 'test_user1',
                'email': '<EMAIL>',
                'phone': '13800138001',
                'first_name': '张三',
                'is_active': True
            },
            {
                'username': 'test_user2',
                'email': '<EMAIL>',
                'phone': '13800138002',
                'first_name': '李四',
                'is_active': True
            },
            {
                'username': 'test_user3',
                'email': '<EMAIL>',
                'phone': '13800138003',
                'first_name': '王五',
                'is_active': False
            }
        ]
        
        for user_data in users_data:
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults=user_data
            )
            if created:
                user.set_password('123456')
                user.save()
                self.stdout.write(f'  创建用户: {user.username}')

    def create_companies(self):
        self.stdout.write('创建企业数据...')
        
        try:
            from apps.companies.models import Company
            
            users = User.objects.filter(is_superuser=False)[:3]
            
            companies_data = [
                {
                    'name': '北京科技有限公司',
                    'credit_code': '91110000123456789A',
                    'company_type': 'limited',
                    'legal_person': '张三',
                    'address': '北京市朝阳区科技园区1号',
                    'phone': '010-12345678',
                    'status': 'active'
                },
                {
                    'name': '上海贸易有限公司',
                    'credit_code': '91310000987654321B',
                    'company_type': 'limited',
                    'legal_person': '李四',
                    'address': '上海市浦东新区金融街2号',
                    'phone': '021-87654321',
                    'status': 'active'
                }
            ]
            
            for i, company_data in enumerate(companies_data):
                if i < len(users):
                    company_data['owner'] = users[i]
                
                company, created = Company.objects.get_or_create(
                    credit_code=company_data['credit_code'],
                    defaults=company_data
                )
                if created:
                    self.stdout.write(f'  创建企业: {company.name}')
                    
        except ImportError:
            self.stdout.write('  企业模型不存在，跳过...')

    def create_finance_records(self):
        self.stdout.write('创建财务记录...')
        
        try:
            from apps.finance.models import SimpleFinancialRecord
            from apps.companies.models import Company
            
            companies = Company.objects.all()
            users = User.objects.filter(is_superuser=False)
            
            if not companies.exists():
                self.stdout.write('  没有企业数据，跳过财务记录创建...')
                return
            
            for i, company in enumerate(companies[:2]):  # 只为前两个企业创建记录
                user = users[i] if i < len(users) else users[0]
                
                # 创建简单财务记录
                for j in range(3):
                    record_data = {
                        'user': user,
                        'company': company,
                        'record_type': random.choice(['income', 'expense']),
                        'transaction_date': timezone.now().date() - timedelta(days=random.randint(1, 30)),
                        'amount': Decimal(str(random.randint(1000, 50000))),
                        'description': f'{random.choice(["销售收入", "办公费用", "差旅费", "设备采购", "服务费"])}',
                        'category': random.choice(['sales', 'office', 'travel', 'equipment', 'service']),
                        'payment_method': random.choice(['cash', 'bank_transfer', 'alipay', 'wechat']),
                        'status': random.choice(['pending', 'approved', 'rejected'])
                    }
                    
                    record = SimpleFinancialRecord.objects.create(**record_data)
                    if j == 0:  # 只显示第一条记录
                        self.stdout.write(f'  创建财务记录: {record.description}')
                        
        except ImportError:
            self.stdout.write('  财务模型不存在，跳过...')

    def create_wechat_config(self):
        self.stdout.write('创建微信配置...')
        
        try:
            from apps.wechat.models import WechatConfig, WechatUser
            
            # 创建微信小程序配置
            config, created = WechatConfig.objects.get_or_create(
                name='代理记账专家',
                defaults={
                    'app_id': 'wx1234567890abcdef',
                    'app_secret': 'your_app_secret_here',
                    'enable_user_auth': True,
                    'enable_payment': False,
                    'enable_message': False
                }
            )
            
            if created:
                self.stdout.write(f'  创建微信配置: {config.name}')
            
            # 创建一些微信用户
            users = User.objects.filter(is_superuser=False)[:2]
            
            for i, user in enumerate(users):
                wechat_user_data = {
                    'user': user,
                    'openid': f'openid_{i+1}_test',
                    'unionid': f'unionid_{i+1}_test',
                    'nickname': f'微信用户{i+1}',
                    'avatar_url': f'https://example.com/avatar{i+1}.jpg',
                    'gender': random.choice([1, 2]),
                    'city': random.choice(['北京', '上海', '深圳']),
                    'province': random.choice(['北京', '上海', '广东']),
                    'country': '中国'
                }
                
                wechat_user, created = WechatUser.objects.get_or_create(
                    openid=wechat_user_data['openid'],
                    defaults=wechat_user_data
                )
                
                if created:
                    self.stdout.write(f'  创建微信用户: {wechat_user.nickname}')
                    
        except ImportError:
            self.stdout.write('  微信模型不存在，跳过...')

# 开放API平台
import asyncio
import json
import logging
import hashlib
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
import aioredis
import jwt
from fastapi import FastAPI, HTTPException, Depends, Request, Response
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import httpx

logger = logging.getLogger(__name__)

@dataclass
class APIKey:
    """API密钥"""
    key_id: str
    api_key: str
    secret_key: str
    developer_id: str
    app_name: str
    permissions: List[str]
    rate_limit: Dict[str, int]  # requests per minute/hour/day
    quota: Dict[str, int]  # monthly quotas
    status: str  # active, suspended, revoked
    created_at: datetime
    expires_at: Optional[datetime]
    last_used: Optional[datetime]
    usage_stats: Dict[str, int]
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class Developer:
    """开发者"""
    developer_id: str
    username: str
    email: str
    company: str
    tier: str  # free, basic, premium, enterprise
    status: str  # active, suspended, banned
    api_keys: List[str]
    applications: List[str]
    created_at: datetime
    verified_at: Optional[datetime]
    profile: Dict[str, Any]
    billing_info: Optional[Dict[str, Any]] = None

@dataclass
class APIEndpoint:
    """API端点"""
    endpoint_id: str
    path: str
    method: str
    category: str
    description: str
    parameters: List[Dict[str, Any]]
    response_schema: Dict[str, Any]
    rate_limit: Dict[str, int]
    required_permissions: List[str]
    version: str
    status: str  # active, deprecated, beta
    documentation: str
    examples: List[Dict[str, Any]]

@dataclass
class APIRequest:
    """API请求记录"""
    request_id: str
    api_key: str
    endpoint: str
    method: str
    timestamp: datetime
    ip_address: str
    user_agent: str
    parameters: Dict[str, Any]
    response_code: int
    response_time: float
    data_size: int
    error_message: Optional[str] = None

class OpenAPIPlatform:
    """开放API平台"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.redis_client = None
        self.app = FastAPI(
            title="代理记账开放API平台",
            description="为第三方开发者提供财务数据和服务的开放API",
            version="1.0.0"
        )
        
        # 开发者管理
        self.developers = {}
        self.api_keys = {}
        
        # API端点
        self.endpoints = {}
        
        # 请求记录
        self.request_logs = []
        
        # 限流器
        self.rate_limiter = RateLimiter()
        
        # 认证器
        self.authenticator = APIAuthenticator()
        
        # 监控器
        self.monitor = APIMonitor()
        
        # 文档生成器
        self.doc_generator = DocumentationGenerator()
        
        # 初始化
        asyncio.create_task(self._initialize())
    
    async def _initialize(self):
        """初始化开放API平台"""
        try:
            # Redis连接
            redis_url = self.config.get('redis_url', 'redis://redis:6379')
            self.redis_client = await aioredis.from_url(redis_url)
            
            # 初始化组件
            await self.rate_limiter.initialize(self.config, self.redis_client)
            await self.authenticator.initialize(self.config, self.redis_client)
            await self.monitor.initialize(self.config, self.redis_client)
            await self.doc_generator.initialize(self.config)
            
            # 设置中间件
            self._setup_middleware()
            
            # 注册API端点
            await self._register_api_endpoints()
            
            # 加载开发者数据
            await self._load_developers()
            
            logger.info("Open API platform initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize open API platform: {e}")
            raise
    
    def _setup_middleware(self):
        """设置中间件"""
        # CORS中间件
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=self.config.get('allowed_origins', ['*']),
            allow_credentials=True,
            allow_methods=['*'],
            allow_headers=['*']
        )
        
        # 可信主机中间件
        self.app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=self.config.get('allowed_hosts', ['*'])
        )
        
        # 自定义中间件
        @self.app.middleware("http")
        async def api_middleware(request: Request, call_next):
            start_time = time.time()
            
            # 请求预处理
            request_id = str(uuid.uuid4())
            request.state.request_id = request_id
            
            # 执行请求
            response = await call_next(request)
            
            # 请求后处理
            process_time = time.time() - start_time
            response.headers["X-Process-Time"] = str(process_time)
            response.headers["X-Request-ID"] = request_id
            
            # 记录请求日志
            await self._log_api_request(request, response, process_time)
            
            return response
    
    async def _register_api_endpoints(self):
        """注册API端点"""
        # 财务数据API
        financial_endpoints = [
            APIEndpoint(
                endpoint_id="get_transactions",
                path="/api/v1/transactions",
                method="GET",
                category="financial_data",
                description="获取交易记录",
                parameters=[
                    {"name": "start_date", "type": "string", "required": False},
                    {"name": "end_date", "type": "string", "required": False},
                    {"name": "category", "type": "string", "required": False},
                    {"name": "limit", "type": "integer", "required": False, "default": 100}
                ],
                response_schema={
                    "type": "object",
                    "properties": {
                        "transactions": {"type": "array"},
                        "total": {"type": "integer"},
                        "page": {"type": "integer"}
                    }
                },
                rate_limit={"per_minute": 60, "per_hour": 1000},
                required_permissions=["read:transactions"],
                version="1.0",
                status="active",
                documentation="获取用户的交易记录数据",
                examples=[
                    {
                        "request": "/api/v1/transactions?start_date=2024-01-01&limit=10",
                        "response": {"transactions": [], "total": 0, "page": 1}
                    }
                ]
            ),
            APIEndpoint(
                endpoint_id="create_transaction",
                path="/api/v1/transactions",
                method="POST",
                category="financial_data",
                description="创建交易记录",
                parameters=[
                    {"name": "amount", "type": "number", "required": True},
                    {"name": "category", "type": "string", "required": True},
                    {"name": "description", "type": "string", "required": False},
                    {"name": "date", "type": "string", "required": False}
                ],
                response_schema={
                    "type": "object",
                    "properties": {
                        "transaction_id": {"type": "string"},
                        "status": {"type": "string"}
                    }
                },
                rate_limit={"per_minute": 30, "per_hour": 500},
                required_permissions=["write:transactions"],
                version="1.0",
                status="active",
                documentation="创建新的交易记录",
                examples=[
                    {
                        "request": {
                            "amount": 100.50,
                            "category": "餐饮",
                            "description": "午餐"
                        },
                        "response": {
                            "transaction_id": "txn_123456",
                            "status": "created"
                        }
                    }
                ]
            )
        ]
        
        # 报表API
        report_endpoints = [
            APIEndpoint(
                endpoint_id="get_financial_report",
                path="/api/v1/reports/financial",
                method="GET",
                category="reports",
                description="获取财务报表",
                parameters=[
                    {"name": "type", "type": "string", "required": True, "enum": ["income", "expense", "balance"]},
                    {"name": "period", "type": "string", "required": True, "enum": ["monthly", "quarterly", "yearly"]},
                    {"name": "year", "type": "integer", "required": True},
                    {"name": "month", "type": "integer", "required": False}
                ],
                response_schema={
                    "type": "object",
                    "properties": {
                        "report_type": {"type": "string"},
                        "period": {"type": "string"},
                        "data": {"type": "object"},
                        "generated_at": {"type": "string"}
                    }
                },
                rate_limit={"per_minute": 20, "per_hour": 200},
                required_permissions=["read:reports"],
                version="1.0",
                status="active",
                documentation="生成和获取各类财务报表",
                examples=[]
            )
        ]
        
        # 用户API
        user_endpoints = [
            APIEndpoint(
                endpoint_id="get_user_profile",
                path="/api/v1/user/profile",
                method="GET",
                category="user_data",
                description="获取用户资料",
                parameters=[],
                response_schema={
                    "type": "object",
                    "properties": {
                        "user_id": {"type": "string"},
                        "username": {"type": "string"},
                        "email": {"type": "string"},
                        "profile": {"type": "object"}
                    }
                },
                rate_limit={"per_minute": 30, "per_hour": 300},
                required_permissions=["read:profile"],
                version="1.0",
                status="active",
                documentation="获取当前用户的基本资料信息",
                examples=[]
            )
        ]
        
        # 注册所有端点
        all_endpoints = financial_endpoints + report_endpoints + user_endpoints
        
        for endpoint in all_endpoints:
            self.endpoints[endpoint.endpoint_id] = endpoint
            await self._register_fastapi_route(endpoint)
    
    async def _register_fastapi_route(self, endpoint: APIEndpoint):
        """注册FastAPI路由"""
        async def route_handler(request: Request):
            try:
                # 认证和授权
                api_key = await self.authenticator.authenticate_request(request)
                await self.authenticator.authorize_request(api_key, endpoint.required_permissions)
                
                # 限流检查
                await self.rate_limiter.check_rate_limit(api_key, endpoint.rate_limit)
                
                # 执行业务逻辑
                result = await self._execute_endpoint_logic(endpoint, request, api_key)
                
                return JSONResponse(content=result)
                
            except HTTPException as e:
                raise e
            except Exception as e:
                logger.error(f"API endpoint error: {e}")
                raise HTTPException(status_code=500, detail="Internal server error")
        
        # 动态添加路由
        if endpoint.method == "GET":
            self.app.get(endpoint.path)(route_handler)
        elif endpoint.method == "POST":
            self.app.post(endpoint.path)(route_handler)
        elif endpoint.method == "PUT":
            self.app.put(endpoint.path)(route_handler)
        elif endpoint.method == "DELETE":
            self.app.delete(endpoint.path)(route_handler)
    
    async def _execute_endpoint_logic(self, endpoint: APIEndpoint, request: Request, api_key: str) -> Dict[str, Any]:
        """执行端点业务逻辑"""
        if endpoint.endpoint_id == "get_transactions":
            return await self._handle_get_transactions(request, api_key)
        elif endpoint.endpoint_id == "create_transaction":
            return await self._handle_create_transaction(request, api_key)
        elif endpoint.endpoint_id == "get_financial_report":
            return await self._handle_get_financial_report(request, api_key)
        elif endpoint.endpoint_id == "get_user_profile":
            return await self._handle_get_user_profile(request, api_key)
        else:
            raise HTTPException(status_code=404, detail="Endpoint not implemented")
    
    async def _handle_get_transactions(self, request: Request, api_key: str) -> Dict[str, Any]:
        """处理获取交易记录请求"""
        # 获取查询参数
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        category = request.query_params.get('category')
        limit = int(request.query_params.get('limit', 100))
        
        # 这里应该调用实际的业务逻辑
        # 目前返回模拟数据
        transactions = [
            {
                "id": "txn_001",
                "amount": 100.50,
                "category": "餐饮",
                "description": "午餐",
                "date": "2024-01-15",
                "type": "expense"
            },
            {
                "id": "txn_002",
                "amount": 5000.00,
                "category": "工资",
                "description": "月薪",
                "date": "2024-01-01",
                "type": "income"
            }
        ]
        
        return {
            "transactions": transactions[:limit],
            "total": len(transactions),
            "page": 1,
            "limit": limit
        }
    
    async def _handle_create_transaction(self, request: Request, api_key: str) -> Dict[str, Any]:
        """处理创建交易记录请求"""
        # 获取请求体
        body = await request.json()
        
        # 验证必需参数
        if 'amount' not in body or 'category' not in body:
            raise HTTPException(status_code=400, detail="Missing required parameters")
        
        # 这里应该调用实际的业务逻辑创建交易记录
        transaction_id = f"txn_{int(time.time())}"
        
        return {
            "transaction_id": transaction_id,
            "status": "created",
            "message": "Transaction created successfully"
        }
    
    async def _handle_get_financial_report(self, request: Request, api_key: str) -> Dict[str, Any]:
        """处理获取财务报表请求"""
        report_type = request.query_params.get('type')
        period = request.query_params.get('period')
        year = request.query_params.get('year')
        month = request.query_params.get('month')
        
        # 这里应该调用实际的报表生成逻辑
        report_data = {
            "summary": {
                "total_income": 10000.00,
                "total_expense": 6000.00,
                "net_income": 4000.00
            },
            "categories": [
                {"name": "餐饮", "amount": 2000.00},
                {"name": "交通", "amount": 1000.00},
                {"name": "购物", "amount": 3000.00}
            ]
        }
        
        return {
            "report_type": report_type,
            "period": period,
            "year": year,
            "month": month,
            "data": report_data,
            "generated_at": datetime.utcnow().isoformat()
        }
    
    async def _handle_get_user_profile(self, request: Request, api_key: str) -> Dict[str, Any]:
        """处理获取用户资料请求"""
        # 根据API密钥获取用户信息
        api_key_info = self.api_keys.get(api_key)
        if not api_key_info:
            raise HTTPException(status_code=401, detail="Invalid API key")
        
        developer = self.developers.get(api_key_info.developer_id)
        if not developer:
            raise HTTPException(status_code=404, detail="Developer not found")
        
        return {
            "user_id": developer.developer_id,
            "username": developer.username,
            "email": developer.email,
            "company": developer.company,
            "tier": developer.tier,
            "profile": developer.profile
        }
    
    async def register_developer(self, developer_info: Dict[str, Any]) -> Developer:
        """注册开发者"""
        try:
            developer_id = str(uuid.uuid4())
            
            developer = Developer(
                developer_id=developer_id,
                username=developer_info['username'],
                email=developer_info['email'],
                company=developer_info.get('company', ''),
                tier='free',
                status='active',
                api_keys=[],
                applications=[],
                created_at=datetime.utcnow(),
                profile=developer_info.get('profile', {})
            )
            
            # 保存开发者信息
            self.developers[developer_id] = developer
            await self._save_developer(developer)
            
            # 生成默认API密钥
            api_key = await self.generate_api_key(developer_id, "默认应用")
            
            logger.info(f"Developer registered: {developer_id}")
            return developer
            
        except Exception as e:
            logger.error(f"Failed to register developer: {e}")
            raise
    
    async def generate_api_key(self, developer_id: str, app_name: str, 
                             permissions: List[str] = None) -> APIKey:
        """生成API密钥"""
        try:
            key_id = str(uuid.uuid4())
            api_key = self._generate_api_key_string()
            secret_key = self._generate_secret_key_string()
            
            # 默认权限
            if permissions is None:
                permissions = ['read:transactions', 'read:reports', 'read:profile']
            
            # 默认限流配置
            rate_limit = {
                'per_minute': 60,
                'per_hour': 1000,
                'per_day': 10000
            }
            
            # 默认配额
            quota = {
                'monthly_requests': 100000,
                'monthly_data_mb': 1000
            }
            
            api_key_obj = APIKey(
                key_id=key_id,
                api_key=api_key,
                secret_key=secret_key,
                developer_id=developer_id,
                app_name=app_name,
                permissions=permissions,
                rate_limit=rate_limit,
                quota=quota,
                status='active',
                created_at=datetime.utcnow(),
                expires_at=datetime.utcnow() + timedelta(days=365),
                usage_stats={}
            )
            
            # 保存API密钥
            self.api_keys[api_key] = api_key_obj
            await self._save_api_key(api_key_obj)
            
            # 更新开发者信息
            developer = self.developers[developer_id]
            developer.api_keys.append(key_id)
            await self._save_developer(developer)
            
            logger.info(f"API key generated: {key_id}")
            return api_key_obj
            
        except Exception as e:
            logger.error(f"Failed to generate API key: {e}")
            raise
    
    def _generate_api_key_string(self) -> str:
        """生成API密钥字符串"""
        return f"ak_{hashlib.sha256(str(uuid.uuid4()).encode()).hexdigest()[:32]}"
    
    def _generate_secret_key_string(self) -> str:
        """生成密钥字符串"""
        return f"sk_{hashlib.sha256(str(uuid.uuid4()).encode()).hexdigest()[:32]}"
    
    async def get_api_documentation(self) -> Dict[str, Any]:
        """获取API文档"""
        return await self.doc_generator.generate_documentation(self.endpoints)
    
    async def get_platform_statistics(self) -> Dict[str, Any]:
        """获取平台统计信息"""
        return await self.monitor.get_platform_statistics()

# 限流器
class RateLimiter:
    """API限流器"""
    
    async def initialize(self, config: Dict[str, Any], redis_client):
        self.config = config
        self.redis_client = redis_client
    
    async def check_rate_limit(self, api_key: str, limits: Dict[str, int]):
        """检查限流"""
        current_time = int(time.time())
        
        for period, limit in limits.items():
            if period == 'per_minute':
                window = 60
            elif period == 'per_hour':
                window = 3600
            elif period == 'per_day':
                window = 86400
            else:
                continue
            
            key = f"rate_limit:{api_key}:{period}:{current_time // window}"
            
            # 获取当前计数
            current_count = await self.redis_client.get(key)
            current_count = int(current_count) if current_count else 0
            
            if current_count >= limit:
                raise HTTPException(
                    status_code=429,
                    detail=f"Rate limit exceeded: {limit} requests {period}"
                )
            
            # 增加计数
            await self.redis_client.incr(key)
            await self.redis_client.expire(key, window)

# API认证器
class APIAuthenticator:
    """API认证器"""
    
    async def initialize(self, config: Dict[str, Any], redis_client):
        self.config = config
        self.redis_client = redis_client
    
    async def authenticate_request(self, request: Request) -> str:
        """认证请求"""
        # 从Header获取API密钥
        api_key = request.headers.get('X-API-Key')
        if not api_key:
            raise HTTPException(status_code=401, detail="Missing API key")
        
        # 验证API密钥
        if not await self._validate_api_key(api_key):
            raise HTTPException(status_code=401, detail="Invalid API key")
        
        return api_key
    
    async def _validate_api_key(self, api_key: str) -> bool:
        """验证API密钥"""
        # 这里应该从数据库或缓存中验证API密钥
        return True  # 简化实现
    
    async def authorize_request(self, api_key: str, required_permissions: List[str]):
        """授权请求"""
        # 检查API密钥是否有所需权限
        # 这里应该实现实际的权限检查逻辑
        pass

# API监控器
class APIMonitor:
    """API监控器"""
    
    async def initialize(self, config: Dict[str, Any], redis_client):
        self.config = config
        self.redis_client = redis_client
    
    async def get_platform_statistics(self) -> Dict[str, Any]:
        """获取平台统计信息"""
        return {
            "total_developers": 0,
            "total_api_keys": 0,
            "total_requests_today": 0,
            "average_response_time": 0,
            "error_rate": 0
        }

# 文档生成器
class DocumentationGenerator:
    """API文档生成器"""
    
    async def initialize(self, config: Dict[str, Any]):
        self.config = config
    
    async def generate_documentation(self, endpoints: Dict[str, APIEndpoint]) -> Dict[str, Any]:
        """生成API文档"""
        docs = {
            "title": "代理记账开放API文档",
            "version": "1.0.0",
            "description": "为第三方开发者提供的财务数据和服务API",
            "base_url": "https://api.dailijizhang.com",
            "authentication": {
                "type": "API Key",
                "header": "X-API-Key",
                "description": "在请求头中包含您的API密钥"
            },
            "endpoints": {}
        }
        
        for endpoint_id, endpoint in endpoints.items():
            docs["endpoints"][endpoint_id] = {
                "path": endpoint.path,
                "method": endpoint.method,
                "category": endpoint.category,
                "description": endpoint.description,
                "parameters": endpoint.parameters,
                "response_schema": endpoint.response_schema,
                "rate_limit": endpoint.rate_limit,
                "required_permissions": endpoint.required_permissions,
                "examples": endpoint.examples
            }
        
        return docs

# 全局开放API平台
open_api_platform = None

def initialize_open_api_platform(config: Dict[str, Any]):
    """初始化开放API平台"""
    global open_api_platform
    open_api_platform = OpenAPIPlatform(config)
    return open_api_platform

# 预测分析系统
import asyncio
import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
import tensorflow as tf
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.model_selection import train_test_split
import joblib
import aioredis
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class PredictionRequest:
    """预测请求"""
    request_id: str
    user_id: str
    prediction_type: str
    target_metric: str
    time_horizon: int  # 预测天数
    input_data: Dict[str, Any]
    created_at: datetime

@dataclass
class PredictionResult:
    """预测结果"""
    prediction_id: str
    request_id: str
    prediction_type: str
    target_metric: str
    predicted_values: List[float]
    confidence_intervals: List[Tuple[float, float]]
    accuracy_metrics: Dict[str, float]
    feature_importance: Dict[str, float]
    model_used: str
    created_at: datetime
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class ModelPerformance:
    """模型性能"""
    model_name: str
    model_type: str
    accuracy_score: float
    mae: float
    mse: float
    r2_score: float
    training_date: datetime
    validation_samples: int

class PredictiveAnalytics:
    """预测分析系统"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.redis_client = None
        
        # 预测模型
        self.models = {}
        self.scalers = {}
        self.model_performance = {}
        
        # 预测历史
        self.prediction_history = []
        
        # 特征工程
        self.feature_extractors = {}
        
        # 模型配置
        self.model_configs = {
            'cash_flow': {
                'features': ['amount', 'category_encoded', 'day_of_week', 'day_of_month', 'month'],
                'target': 'amount',
                'model_types': ['lstm', 'random_forest', 'gradient_boosting']
            },
            'expense_category': {
                'features': ['amount', 'day_of_week', 'month', 'historical_avg'],
                'target': 'category_encoded',
                'model_types': ['random_forest', 'gradient_boosting']
            },
            'budget_utilization': {
                'features': ['current_spending', 'days_remaining', 'historical_pattern'],
                'target': 'budget_utilization',
                'model_types': ['linear_regression', 'random_forest']
            },
            'financial_health': {
                'features': ['income', 'expense', 'savings_rate', 'debt_ratio'],
                'target': 'health_score',
                'model_types': ['gradient_boosting', 'neural_network']
            }
        }
        
        # 初始化
        asyncio.create_task(self._initialize())
    
    async def _initialize(self):
        """初始化预测分析系统"""
        try:
            # Redis连接
            redis_url = self.config.get('redis_url', 'redis://redis:6379')
            self.redis_client = await aioredis.from_url(redis_url)
            
            # 初始化模型
            await self._initialize_models()
            
            # 加载预训练模型
            await self._load_pretrained_models()
            
            logger.info("Predictive analytics system initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize predictive analytics: {e}")
            raise
    
    async def _initialize_models(self):
        """初始化预测模型"""
        try:
            for prediction_type, config in self.model_configs.items():
                self.models[prediction_type] = {}
                self.scalers[prediction_type] = {}
                
                for model_type in config['model_types']:
                    if model_type == 'lstm':
                        self.models[prediction_type][model_type] = self._create_lstm_model()
                    elif model_type == 'random_forest':
                        self.models[prediction_type][model_type] = RandomForestRegressor(
                            n_estimators=100,
                            random_state=42
                        )
                    elif model_type == 'gradient_boosting':
                        self.models[prediction_type][model_type] = GradientBoostingRegressor(
                            n_estimators=100,
                            random_state=42
                        )
                    elif model_type == 'linear_regression':
                        self.models[prediction_type][model_type] = LinearRegression()
                    elif model_type == 'neural_network':
                        self.models[prediction_type][model_type] = self._create_neural_network()
                    
                    # 初始化对应的标准化器
                    self.scalers[prediction_type][model_type] = StandardScaler()
            
            logger.info("Prediction models initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize models: {e}")
    
    def _create_lstm_model(self) -> tf.keras.Model:
        """创建LSTM模型"""
        model = tf.keras.Sequential([
            tf.keras.layers.LSTM(50, return_sequences=True, input_shape=(60, 1)),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.LSTM(50, return_sequences=True),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.LSTM(50),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.Dense(25),
            tf.keras.layers.Dense(1)
        ])
        
        model.compile(
            optimizer='adam',
            loss='mean_squared_error',
            metrics=['mae']
        )
        
        return model
    
    def _create_neural_network(self) -> tf.keras.Model:
        """创建神经网络模型"""
        model = tf.keras.Sequential([
            tf.keras.layers.Dense(128, activation='relu', input_shape=(10,)),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(64, activation='relu'),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(32, activation='relu'),
            tf.keras.layers.Dense(1, activation='sigmoid')
        ])
        
        model.compile(
            optimizer='adam',
            loss='binary_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    async def _load_pretrained_models(self):
        """加载预训练模型"""
        try:
            models_dir = Path(self.config.get('models_dir', 'models'))
            models_dir.mkdir(exist_ok=True)
            
            for prediction_type in self.model_configs.keys():
                model_path = models_dir / f"{prediction_type}_model.joblib"
                scaler_path = models_dir / f"{prediction_type}_scaler.joblib"
                
                if model_path.exists() and scaler_path.exists():
                    try:
                        # 加载sklearn模型
                        model = joblib.load(model_path)
                        scaler = joblib.load(scaler_path)
                        
                        # 更新模型字典
                        if 'random_forest' in self.models[prediction_type]:
                            self.models[prediction_type]['random_forest'] = model
                            self.scalers[prediction_type]['random_forest'] = scaler
                        
                        logger.info(f"Loaded pretrained model for {prediction_type}")
                        
                    except Exception as e:
                        logger.warning(f"Failed to load pretrained model for {prediction_type}: {e}")
            
        except Exception as e:
            logger.error(f"Failed to load pretrained models: {e}")
    
    async def predict_cash_flow(self, user_id: str, days_ahead: int = 30, 
                               historical_data: List[Dict] = None) -> PredictionResult:
        """预测现金流"""
        try:
            request_id = f"cashflow_{int(datetime.utcnow().timestamp())}"
            
            # 获取历史数据
            if not historical_data:
                historical_data = await self._get_user_transaction_data(user_id)
            
            if len(historical_data) < 30:
                raise ValueError("Insufficient historical data for cash flow prediction")
            
            # 数据预处理
            df = pd.DataFrame(historical_data)
            features, target = await self._prepare_cash_flow_features(df)
            
            # 选择最佳模型
            best_model, model_name = await self._select_best_model('cash_flow', features, target)
            
            # 生成预测
            predictions = await self._generate_cash_flow_predictions(
                best_model, features, days_ahead, model_name
            )
            
            # 计算置信区间
            confidence_intervals = await self._calculate_confidence_intervals(
                predictions, model_name
            )
            
            # 计算特征重要性
            feature_importance = await self._calculate_feature_importance(
                best_model, features.columns, model_name
            )
            
            result = PredictionResult(
                prediction_id=f"pred_{int(datetime.utcnow().timestamp())}",
                request_id=request_id,
                prediction_type="cash_flow",
                target_metric="daily_cash_flow",
                predicted_values=predictions.tolist(),
                confidence_intervals=confidence_intervals,
                accuracy_metrics=await self._calculate_accuracy_metrics(best_model, features, target),
                feature_importance=feature_importance,
                model_used=model_name,
                created_at=datetime.utcnow(),
                metadata={
                    'user_id': user_id,
                    'prediction_horizon': days_ahead,
                    'data_points_used': len(historical_data)
                }
            )
            
            # 保存预测结果
            await self._save_prediction_result(result)
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to predict cash flow: {e}")
            raise
    
    async def predict_expense_category(self, user_id: str, amount: float, 
                                     context: Dict[str, Any] = None) -> PredictionResult:
        """预测支出类别"""
        try:
            request_id = f"category_{int(datetime.utcnow().timestamp())}"
            
            # 获取历史数据
            historical_data = await self._get_user_transaction_data(user_id)
            
            if len(historical_data) < 50:
                raise ValueError("Insufficient historical data for category prediction")
            
            # 数据预处理
            df = pd.DataFrame(historical_data)
            features, target = await self._prepare_category_features(df, amount, context)
            
            # 选择最佳模型
            best_model, model_name = await self._select_best_model('expense_category', features, target)
            
            # 生成预测
            category_probabilities = await self._predict_expense_category(
                best_model, features.iloc[-1:], model_name
            )
            
            result = PredictionResult(
                prediction_id=f"pred_{int(datetime.utcnow().timestamp())}",
                request_id=request_id,
                prediction_type="expense_category",
                target_metric="category_probability",
                predicted_values=list(category_probabilities.values()),
                confidence_intervals=[],
                accuracy_metrics=await self._calculate_accuracy_metrics(best_model, features, target),
                feature_importance=await self._calculate_feature_importance(
                    best_model, features.columns, model_name
                ),
                model_used=model_name,
                created_at=datetime.utcnow(),
                metadata={
                    'user_id': user_id,
                    'amount': amount,
                    'context': context,
                    'category_probabilities': category_probabilities
                }
            )
            
            await self._save_prediction_result(result)
            return result
            
        except Exception as e:
            logger.error(f"Failed to predict expense category: {e}")
            raise
    
    async def predict_budget_utilization(self, user_id: str, budget_data: Dict[str, Any]) -> PredictionResult:
        """预测预算使用情况"""
        try:
            request_id = f"budget_{int(datetime.utcnow().timestamp())}"
            
            # 获取历史预算数据
            historical_data = await self._get_user_budget_data(user_id)
            
            # 数据预处理
            features = await self._prepare_budget_features(budget_data, historical_data)
            
            # 选择模型
            model = self.models['budget_utilization']['random_forest']
            scaler = self.scalers['budget_utilization']['random_forest']
            
            # 标准化特征
            features_scaled = scaler.transform(features)
            
            # 生成预测
            utilization_prediction = model.predict(features_scaled)[0]
            
            # 计算置信区间
            confidence_interval = await self._calculate_budget_confidence_interval(
                model, features_scaled, utilization_prediction
            )
            
            result = PredictionResult(
                prediction_id=f"pred_{int(datetime.utcnow().timestamp())}",
                request_id=request_id,
                prediction_type="budget_utilization",
                target_metric="utilization_rate",
                predicted_values=[utilization_prediction],
                confidence_intervals=[confidence_interval],
                accuracy_metrics={'r2_score': 0.85},  # 示例值
                feature_importance=await self._calculate_feature_importance(
                    model, features.columns, 'random_forest'
                ),
                model_used='random_forest',
                created_at=datetime.utcnow(),
                metadata={
                    'user_id': user_id,
                    'budget_data': budget_data
                }
            )
            
            await self._save_prediction_result(result)
            return result
            
        except Exception as e:
            logger.error(f"Failed to predict budget utilization: {e}")
            raise
    
    async def predict_financial_health(self, user_id: str, financial_data: Dict[str, Any]) -> PredictionResult:
        """预测财务健康度"""
        try:
            request_id = f"health_{int(datetime.utcnow().timestamp())}"
            
            # 准备特征
            features = await self._prepare_financial_health_features(financial_data)
            
            # 使用神经网络模型
            model = self.models['financial_health']['neural_network']
            scaler = self.scalers['financial_health']['neural_network']
            
            # 标准化特征
            features_scaled = scaler.transform(features)
            
            # 生成预测
            health_score = model.predict(features_scaled)[0][0]
            
            # 生成健康度解释
            health_explanation = await self._generate_health_explanation(health_score, financial_data)
            
            result = PredictionResult(
                prediction_id=f"pred_{int(datetime.utcnow().timestamp())}",
                request_id=request_id,
                prediction_type="financial_health",
                target_metric="health_score",
                predicted_values=[health_score],
                confidence_intervals=[(health_score - 0.1, health_score + 0.1)],
                accuracy_metrics={'accuracy': 0.88},  # 示例值
                feature_importance=await self._calculate_neural_network_importance(
                    model, features_scaled
                ),
                model_used='neural_network',
                created_at=datetime.utcnow(),
                metadata={
                    'user_id': user_id,
                    'financial_data': financial_data,
                    'health_explanation': health_explanation
                }
            )
            
            await self._save_prediction_result(result)
            return result
            
        except Exception as e:
            logger.error(f"Failed to predict financial health: {e}")
            raise
    
    async def _get_user_transaction_data(self, user_id: str) -> List[Dict]:
        """获取用户交易数据"""
        try:
            # 从Redis获取缓存数据
            cached_data = await self.redis_client.get(f"user_transactions:{user_id}")
            if cached_data:
                return json.loads(cached_data)
            
            # 这里应该从数据库获取实际数据
            # 示例数据
            sample_data = []
            base_date = datetime.utcnow() - timedelta(days=90)
            
            for i in range(90):
                date = base_date + timedelta(days=i)
                # 模拟交易数据
                sample_data.append({
                    'date': date.strftime('%Y-%m-%d'),
                    'amount': np.random.normal(100, 50),
                    'category': np.random.choice(['餐饮', '交通', '购物', '娱乐', '医疗']),
                    'type': np.random.choice(['income', 'expense'], p=[0.3, 0.7])
                })
            
            # 缓存数据
            await self.redis_client.setex(
                f"user_transactions:{user_id}",
                3600,  # 1小时缓存
                json.dumps(sample_data, default=str)
            )
            
            return sample_data
            
        except Exception as e:
            logger.error(f"Failed to get user transaction data: {e}")
            return []
    
    async def _get_user_budget_data(self, user_id: str) -> List[Dict]:
        """获取用户预算数据"""
        try:
            # 示例预算数据
            return [
                {
                    'month': '2024-01',
                    'category': '餐饮',
                    'budget': 2000,
                    'spent': 1800,
                    'utilization': 0.9
                },
                {
                    'month': '2024-02',
                    'category': '餐饮',
                    'budget': 2000,
                    'spent': 1650,
                    'utilization': 0.825
                }
            ]
            
        except Exception as e:
            logger.error(f"Failed to get user budget data: {e}")
            return []

# 智能客服系统
import asyncio
import json
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import aioredis
import openai
from transformers import pipeline
import torch

logger = logging.getLogger(__name__)

class TicketStatus(Enum):
    """工单状态"""
    OPEN = "open"
    IN_PROGRESS = "in_progress"
    PENDING = "pending"
    RESOLVED = "resolved"
    CLOSED = "closed"

class TicketPriority(Enum):
    """工单优先级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

class ConversationStatus(Enum):
    """对话状态"""
    ACTIVE = "active"
    WAITING = "waiting"
    TRANSFERRED = "transferred"
    ENDED = "ended"

@dataclass
class CustomerTicket:
    """客服工单"""
    ticket_id: str
    user_id: str
    title: str
    description: str
    category: str
    priority: TicketPriority
    status: TicketStatus
    created_at: datetime
    updated_at: datetime
    assigned_agent: Optional[str] = None
    resolution: Optional[str] = None
    satisfaction_score: Optional[int] = None
    tags: List[str] = None
    attachments: List[str] = None

@dataclass
class ConversationSession:
    """对话会话"""
    session_id: str
    user_id: str
    channel: str  # chat, voice, email
    status: ConversationStatus
    created_at: datetime
    updated_at: datetime
    messages: List[Dict[str, Any]]
    context: Dict[str, Any]
    assigned_agent: Optional[str] = None
    satisfaction_rating: Optional[int] = None

@dataclass
class KnowledgeBaseEntry:
    """知识库条目"""
    entry_id: str
    title: str
    content: str
    category: str
    tags: List[str]
    created_at: datetime
    updated_at: datetime
    view_count: int = 0
    helpful_count: int = 0
    not_helpful_count: int = 0

@dataclass
class AutoResponse:
    """自动回复"""
    response_id: str
    trigger_patterns: List[str]
    response_text: str
    confidence_threshold: float
    category: str
    enabled: bool = True

class IntelligentCustomerService:
    """智能客服系统"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.redis_client = None

        # AI模型和工具
        self.models = {}
        self.pipelines = {}

        # 知识库
        self.knowledge_base = {}
        self.auto_responses = {}
        self.faq_database = {}

        # 会话管理
        self.active_sessions = {}
        self.ticket_queue = {}

        # 智能路由
        self.routing_rules = {}
        self.agent_skills = {}

        # 初始化
        asyncio.create_task(self._initialize())

    async def _initialize(self):
        """初始化智能客服系统"""
        try:
            # Redis连接
            redis_url = self.config.get('redis_url', 'redis://redis:6379')
            self.redis_client = await aioredis.from_url(redis_url)

            # 初始化AI模型
            await self._initialize_ai_models()

            # 加载知识库
            await self._load_knowledge_base()

            # 初始化自动回复
            await self._initialize_auto_responses()

            # 加载路由规则
            await self._load_routing_rules()

            logger.info("Intelligent customer service initialized")

        except Exception as e:
            logger.error(f"Failed to initialize intelligent customer service: {e}")
            raise

    async def _initialize_ai_models(self):
        """初始化AI模型"""
        try:
            # 意图分类模型
            self.pipelines['intent_classification'] = pipeline(
                "text-classification",
                model="microsoft/DialoGPT-medium"
            )

            # 情感分析模型
            self.pipelines['sentiment_analysis'] = pipeline(
                "sentiment-analysis",
                model="cardiffnlp/twitter-roberta-base-sentiment-latest"
            )

            # 文本相似度模型
            self.pipelines['similarity'] = pipeline(
                "feature-extraction",
                model="sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
            )

            # OpenAI集成
            if self.config.get('openai_api_key'):
                openai.api_key = self.config['openai_api_key']

            logger.info("AI models for customer service initialized")

        except Exception as e:
            logger.error(f"Failed to initialize AI models: {e}")

    async def _load_knowledge_base(self):
        """加载知识库"""
        try:
            # 默认知识库条目
            default_entries = [
                KnowledgeBaseEntry(
                    entry_id="kb_001",
                    title="如何添加交易记录",
                    content="您可以通过以下方式添加交易记录：1. 点击首页的'记一笔'按钮 2. 选择收入或支出 3. 输入金额和类别 4. 添加备注（可选）5. 点击保存",
                    category="基础操作",
                    tags=["交易", "记录", "添加"],
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                ),
                KnowledgeBaseEntry(
                    entry_id="kb_002",
                    title="如何查看财务报表",
                    content="查看财务报表的步骤：1. 进入'报表'页面 2. 选择报表类型（收支统计、类别分析等）3. 设置时间范围 4. 点击生成报表 5. 可以导出PDF或Excel格式",
                    category="报表功能",
                    tags=["报表", "统计", "导出"],
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                ),
                KnowledgeBaseEntry(
                    entry_id="kb_003",
                    title="忘记密码怎么办",
                    content="如果忘记密码，请按以下步骤重置：1. 在登录页面点击'忘记密码' 2. 输入注册时的手机号或邮箱 3. 获取验证码 4. 设置新密码 5. 重新登录",
                    category="账户管理",
                    tags=["密码", "重置", "登录"],
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                ),
                KnowledgeBaseEntry(
                    entry_id="kb_004",
                    title="数据安全保障",
                    content="我们采用多重安全措施保护您的数据：1. 银行级加密传输 2. 数据本地加密存储 3. 定期安全备份 4. 严格的访问控制 5. 符合国家数据安全法规",
                    category="安全保障",
                    tags=["安全", "加密", "隐私"],
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
            ]

            for entry in default_entries:
                self.knowledge_base[entry.entry_id] = entry

            logger.info(f"Loaded {len(self.knowledge_base)} knowledge base entries")

        except Exception as e:
            logger.error(f"Failed to load knowledge base: {e}")

    async def _initialize_auto_responses(self):
        """初始化自动回复"""
        try:
            # 默认自动回复规则
            default_responses = [
                AutoResponse(
                    response_id="auto_001",
                    trigger_patterns=["你好", "您好", "hello", "hi"],
                    response_text="您好！我是智能客服助手，很高兴为您服务。请问有什么可以帮助您的吗？",
                    confidence_threshold=0.8,
                    category="问候"
                ),
                AutoResponse(
                    response_id="auto_002",
                    trigger_patterns=["谢谢", "感谢", "thank you"],
                    response_text="不客气！如果还有其他问题，随时可以咨询我。祝您使用愉快！",
                    confidence_threshold=0.8,
                    category="感谢"
                ),
                AutoResponse(
                    response_id="auto_003",
                    trigger_patterns=["再见", "拜拜", "bye"],
                    response_text="再见！感谢您的咨询，期待下次为您服务。",
                    confidence_threshold=0.8,
                    category="告别"
                ),
                AutoResponse(
                    response_id="auto_004",
                    trigger_patterns=["怎么", "如何", "怎样"],
                    response_text="我来帮您查找相关的操作指南。请稍等片刻...",
                    confidence_threshold=0.6,
                    category="操作询问"
                )
            ]

            for response in default_responses:
                self.auto_responses[response.response_id] = response

            logger.info(f"Initialized {len(self.auto_responses)} auto responses")

        except Exception as e:
            logger.error(f"Failed to initialize auto responses: {e}")

    async def _load_routing_rules(self):
        """加载路由规则"""
        try:
            # 智能路由规则
            self.routing_rules = {
                'technical_issues': {
                    'keywords': ['bug', '错误', '崩溃', '无法', '不能'],
                    'priority': TicketPriority.HIGH,
                    'required_skills': ['technical_support']
                },
                'account_issues': {
                    'keywords': ['账户', '登录', '密码', '注册'],
                    'priority': TicketPriority.MEDIUM,
                    'required_skills': ['account_management']
                },
                'billing_issues': {
                    'keywords': ['费用', '收费', '退款', '发票'],
                    'priority': TicketPriority.HIGH,
                    'required_skills': ['billing_support']
                },
                'general_inquiry': {
                    'keywords': ['如何', '怎么', '功能', '使用'],
                    'priority': TicketPriority.LOW,
                    'required_skills': ['general_support']
                }
            }

            # 客服技能配置
            self.agent_skills = {
                'agent_001': ['technical_support', 'general_support'],
                'agent_002': ['account_management', 'billing_support'],
                'agent_003': ['general_support'],
                'ai_assistant': ['general_support', 'technical_support', 'account_management']
            }

            logger.info("Routing rules loaded")

        except Exception as e:
            logger.error(f"Failed to load routing rules: {e}")

    async def start_conversation(self, user_id: str, channel: str = "chat",
                                initial_message: str = "") -> ConversationSession:
        """开始对话会话"""
        try:
            session_id = str(uuid.uuid4())

            session = ConversationSession(
                session_id=session_id,
                user_id=user_id,
                channel=channel,
                status=ConversationStatus.ACTIVE,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                messages=[],
                context={}
            )

            # 添加初始消息
            if initial_message:
                await self._add_message(session, "user", initial_message)

                # 生成自动回复
                response = await self._generate_response(session, initial_message)
                await self._add_message(session, "assistant", response)
            else:
                # 发送欢迎消息
                welcome_message = "您好！我是智能客服助手，很高兴为您服务。请问有什么可以帮助您的吗？"
                await self._add_message(session, "assistant", welcome_message)

            # 保存会话
            self.active_sessions[session_id] = session
            await self._save_session(session)

            return session

        except Exception as e:
            logger.error(f"Failed to start conversation: {e}")
            raise

    async def process_message(self, session_id: str, message: str) -> Dict[str, Any]:
        """处理用户消息"""
        try:
            session = await self._get_session(session_id)
            if not session:
                raise ValueError(f"Session {session_id} not found")

            # 添加用户消息
            await self._add_message(session, "user", message)

            # 分析消息意图和情感
            intent = await self._analyze_intent(message)
            sentiment = await self._analyze_sentiment(message)

            # 更新会话上下文
            session.context.update({
                'last_intent': intent,
                'last_sentiment': sentiment,
                'message_count': len(session.messages)
            })

            # 检查是否需要转人工
            if await self._should_transfer_to_human(session, message, sentiment):
                return await self._transfer_to_human(session, message)

            # 生成AI回复
            response = await self._generate_response(session, message)
            await self._add_message(session, "assistant", response)

            # 更新会话
            session.updated_at = datetime.utcnow()
            await self._save_session(session)

            return {
                'session_id': session_id,
                'response': response,
                'intent': intent,
                'sentiment': sentiment,
                'status': session.status.value,
                'requires_human': False
            }

        except Exception as e:
            logger.error(f"Failed to process message: {e}")
            raise

    async def _add_message(self, session: ConversationSession, role: str, content: str):
        """添加消息到会话"""
        message = {
            'id': str(uuid.uuid4()),
            'role': role,
            'content': content,
            'timestamp': datetime.utcnow().isoformat(),
            'metadata': {}
        }
        session.messages.append(message)

    async def _analyze_intent(self, message: str) -> str:
        """分析消息意图"""
        try:
            # 基于关键词的简单意图识别
            message_lower = message.lower()

            for category, rules in self.routing_rules.items():
                for keyword in rules['keywords']:
                    if keyword in message_lower:
                        return category

            # 使用AI模型进行意图分类
            if 'intent_classification' in self.pipelines:
                result = self.pipelines['intent_classification'](message)
                if result and len(result) > 0:
                    return result[0]['label']

            return 'general_inquiry'

        except Exception as e:
            logger.error(f"Failed to analyze intent: {e}")
            return 'unknown'

    async def _analyze_sentiment(self, message: str) -> Dict[str, Any]:
        """分析消息情感"""
        try:
            if 'sentiment_analysis' in self.pipelines:
                result = self.pipelines['sentiment_analysis'](message)
                if result and len(result) > 0:
                    return {
                        'label': result[0]['label'],
                        'score': result[0]['score']
                    }

            return {'label': 'NEUTRAL', 'score': 0.5}

        except Exception as e:
            logger.error(f"Failed to analyze sentiment: {e}")
            return {'label': 'NEUTRAL', 'score': 0.5}

    async def _should_transfer_to_human(self, session: ConversationSession,
                                      message: str, sentiment: Dict[str, Any]) -> bool:
        """判断是否需要转人工"""
        try:
            # 负面情感且分数较高
            if sentiment['label'] == 'NEGATIVE' and sentiment['score'] > 0.8:
                return True

            # 连续多次无法解决的问题
            if len(session.messages) > 10:
                recent_messages = session.messages[-6:]
                user_messages = [msg for msg in recent_messages if msg['role'] == 'user']
                if len(user_messages) >= 3:
                    # 检查是否包含投诉关键词
                    complaint_keywords = ['投诉', '不满', '差评', '退款', '解决不了']
                    for msg in user_messages:
                        if any(keyword in msg['content'] for keyword in complaint_keywords):
                            return True

            # 明确要求人工服务
            human_request_keywords = ['人工', '客服', '转接', '真人']
            if any(keyword in message for keyword in human_request_keywords):
                return True

            return False

        except Exception as e:
            logger.error(f"Failed to check transfer requirement: {e}")
            return False

    async def _transfer_to_human(self, session: ConversationSession, message: str) -> Dict[str, Any]:
        """转接人工客服"""
        try:
            # 创建工单
            ticket = await self._create_ticket_from_session(session, message)

            # 更新会话状态
            session.status = ConversationStatus.TRANSFERRED
            session.updated_at = datetime.utcnow()

            # 分配客服
            assigned_agent = await self._assign_agent(ticket)
            session.assigned_agent = assigned_agent

            await self._save_session(session)

            response = f"我已为您转接人工客服，工单号：{ticket.ticket_id}。客服人员将尽快为您处理，请稍候。"
            await self._add_message(session, "assistant", response)

            return {
                'session_id': session.session_id,
                'response': response,
                'status': session.status.value,
                'requires_human': True,
                'ticket_id': ticket.ticket_id,
                'assigned_agent': assigned_agent
            }

        except Exception as e:
            logger.error(f"Failed to transfer to human: {e}")
            raise

    async def _generate_response(self, session: ConversationSession, message: str) -> str:
        """生成AI回复"""
        try:
            # 检查自动回复规则
            auto_response = await self._check_auto_responses(message)
            if auto_response:
                return auto_response

            # 搜索知识库
            kb_response = await self._search_knowledge_base(message)
            if kb_response:
                return kb_response

            # 使用GPT生成回复
            if self.config.get('openai_api_key'):
                gpt_response = await self._generate_gpt_response(session, message)
                if gpt_response:
                    return gpt_response

            # 默认回复
            return "抱歉，我暂时无法理解您的问题。您可以尝试重新描述，或者我为您转接人工客服。"

        except Exception as e:
            logger.error(f"Failed to generate response: {e}")
            return "系统暂时繁忙，请稍后再试或联系人工客服。"

    async def _check_auto_responses(self, message: str) -> Optional[str]:
        """检查自动回复规则"""
        try:
            message_lower = message.lower()

            for response in self.auto_responses.values():
                if not response.enabled:
                    continue

                for pattern in response.trigger_patterns:
                    if pattern in message_lower:
                        return response.response_text

            return None

        except Exception as e:
            logger.error(f"Failed to check auto responses: {e}")
            return None

    async def _search_knowledge_base(self, message: str) -> Optional[str]:
        """搜索知识库"""
        try:
            best_match = None
            best_score = 0

            for entry in self.knowledge_base.values():
                # 简单的关键词匹配
                score = 0
                message_words = message.lower().split()

                # 检查标题匹配
                for word in message_words:
                    if word in entry.title.lower():
                        score += 2

                # 检查标签匹配
                for tag in entry.tags:
                    if tag in message.lower():
                        score += 1

                # 检查内容匹配
                for word in message_words:
                    if word in entry.content.lower():
                        score += 0.5

                if score > best_score and score > 1:
                    best_score = score
                    best_match = entry

            if best_match:
                # 更新查看次数
                best_match.view_count += 1
                return f"根据您的问题，我找到了相关信息：\n\n{best_match.title}\n\n{best_match.content}\n\n这个回答对您有帮助吗？"

            return None

        except Exception as e:
            logger.error(f"Failed to search knowledge base: {e}")
            return None

    async def _generate_gpt_response(self, session: ConversationSession, message: str) -> Optional[str]:
        """使用GPT生成回复"""
        try:
            # 构建对话历史
            conversation_history = []
            for msg in session.messages[-10:]:  # 最近10条消息
                role = "user" if msg['role'] == "user" else "assistant"
                conversation_history.append({
                    "role": role,
                    "content": msg['content']
                })

            # 添加当前消息
            conversation_history.append({
                "role": "user",
                "content": message
            })

            # 系统提示
            system_prompt = """
            你是一个专业的财务软件客服助手，专门为代理记账系统的用户提供帮助。
            请遵循以下原则：
            1. 友好、专业、耐心
            2. 提供准确、实用的信息
            3. 如果不确定答案，建议用户联系人工客服
            4. 回复要简洁明了，不超过200字
            5. 重点关注财务记账、报表、预算等功能
            """

            response = await openai.ChatCompletion.acreate(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": system_prompt},
                    *conversation_history
                ],
                max_tokens=300,
                temperature=0.7
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"Failed to generate GPT response: {e}")
            return None

    async def _create_ticket_from_session(self, session: ConversationSession, message: str) -> CustomerTicket:
        """从会话创建工单"""
        try:
            ticket_id = f"TK{int(datetime.utcnow().timestamp())}"

            # 分析问题类别
            intent = await self._analyze_intent(message)
            category = self._map_intent_to_category(intent)

            # 确定优先级
            priority = self.routing_rules.get(intent, {}).get('priority', TicketPriority.MEDIUM)

            # 生成工单标题
            title = await self._generate_ticket_title(session, message)

            # 整理问题描述
            description = self._compile_session_description(session)

            ticket = CustomerTicket(
                ticket_id=ticket_id,
                user_id=session.user_id,
                title=title,
                description=description,
                category=category,
                priority=priority,
                status=TicketStatus.OPEN,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                tags=[intent, session.channel]
            )

            # 保存工单
            await self._save_ticket(ticket)
            self.ticket_queue[ticket_id] = ticket

            return ticket

        except Exception as e:
            logger.error(f"Failed to create ticket from session: {e}")
            raise

    def _map_intent_to_category(self, intent: str) -> str:
        """映射意图到工单类别"""
        mapping = {
            'technical_issues': '技术问题',
            'account_issues': '账户问题',
            'billing_issues': '计费问题',
            'general_inquiry': '一般咨询'
        }
        return mapping.get(intent, '其他')

    async def _generate_ticket_title(self, session: ConversationSession, message: str) -> str:
        """生成工单标题"""
        try:
            # 使用最近的用户消息生成标题
            user_messages = [msg['content'] for msg in session.messages if msg['role'] == 'user']
            if user_messages:
                first_message = user_messages[0]
                # 简单截取前50个字符作为标题
                title = first_message[:50]
                if len(first_message) > 50:
                    title += "..."
                return title

            return "用户咨询"

        except Exception as e:
            logger.error(f"Failed to generate ticket title: {e}")
            return "用户咨询"

    def _compile_session_description(self, session: ConversationSession) -> str:
        """整理会话描述"""
        try:
            description_parts = [
                f"会话ID: {session.session_id}",
                f"渠道: {session.channel}",
                f"开始时间: {session.created_at.strftime('%Y-%m-%d %H:%M:%S')}",
                "",
                "对话记录:"
            ]

            for msg in session.messages:
                role = "用户" if msg['role'] == 'user' else "助手"
                timestamp = msg['timestamp']
                content = msg['content']
                description_parts.append(f"[{timestamp}] {role}: {content}")

            return "\n".join(description_parts)

        except Exception as e:
            logger.error(f"Failed to compile session description: {e}")
            return "会话记录整理失败"

    async def _assign_agent(self, ticket: CustomerTicket) -> Optional[str]:
        """分配客服代理"""
        try:
            # 获取所需技能
            required_skills = self.routing_rules.get(
                ticket.category.lower().replace(' ', '_'), {}
            ).get('required_skills', ['general_support'])

            # 查找具备相应技能的客服
            available_agents = []
            for agent_id, skills in self.agent_skills.items():
                if any(skill in skills for skill in required_skills):
                    available_agents.append(agent_id)

            if available_agents:
                # 简单的轮询分配
                assigned_agent = available_agents[0]
                ticket.assigned_agent = assigned_agent
                await self._save_ticket(ticket)
                return assigned_agent

            return None

        except Exception as e:
            logger.error(f"Failed to assign agent: {e}")
            return None

    async def _get_session(self, session_id: str) -> Optional[ConversationSession]:
        """获取会话"""
        try:
            # 从内存获取
            if session_id in self.active_sessions:
                return self.active_sessions[session_id]

            # 从Redis获取
            session_data = await self.redis_client.get(f"session:{session_id}")
            if session_data:
                session_dict = json.loads(session_data)
                session_dict['created_at'] = datetime.fromisoformat(session_dict['created_at'])
                session_dict['updated_at'] = datetime.fromisoformat(session_dict['updated_at'])
                session_dict['status'] = ConversationStatus(session_dict['status'])

                session = ConversationSession(**session_dict)
                self.active_sessions[session_id] = session
                return session

            return None

        except Exception as e:
            logger.error(f"Failed to get session: {e}")
            return None

    async def _save_session(self, session: ConversationSession):
        """保存会话"""
        try:
            session_data = asdict(session)
            session_data['created_at'] = session.created_at.isoformat()
            session_data['updated_at'] = session.updated_at.isoformat()
            session_data['status'] = session.status.value

            await self.redis_client.setex(
                f"session:{session.session_id}",
                3600,  # 1小时过期
                json.dumps(session_data, default=str)
            )

        except Exception as e:
            logger.error(f"Failed to save session: {e}")

    async def _save_ticket(self, ticket: CustomerTicket):
        """保存工单"""
        try:
            ticket_data = asdict(ticket)
            ticket_data['created_at'] = ticket.created_at.isoformat()
            ticket_data['updated_at'] = ticket.updated_at.isoformat()
            ticket_data['priority'] = ticket.priority.value
            ticket_data['status'] = ticket.status.value

            await self.redis_client.hset(
                'customer_tickets',
                ticket.ticket_id,
                json.dumps(ticket_data, default=str)
            )

        except Exception as e:
            logger.error(f"Failed to save ticket: {e}")

    async def get_session_summary(self, session_id: str) -> Dict[str, Any]:
        """获取会话摘要"""
        try:
            session = await self._get_session(session_id)
            if not session:
                return {'error': 'Session not found'}

            user_messages = [msg for msg in session.messages if msg['role'] == 'user']
            assistant_messages = [msg for msg in session.messages if msg['role'] == 'assistant']

            return {
                'session_id': session_id,
                'user_id': session.user_id,
                'status': session.status.value,
                'channel': session.channel,
                'created_at': session.created_at.isoformat(),
                'updated_at': session.updated_at.isoformat(),
                'message_count': len(session.messages),
                'user_message_count': len(user_messages),
                'assistant_message_count': len(assistant_messages),
                'assigned_agent': session.assigned_agent,
                'context': session.context
            }

        except Exception as e:
            logger.error(f"Failed to get session summary: {e}")
            return {'error': str(e)}

    async def rate_conversation(self, session_id: str, rating: int, feedback: str = "") -> bool:
        """评价对话"""
        try:
            session = await self._get_session(session_id)
            if not session:
                return False

            session.satisfaction_rating = rating
            session.context['feedback'] = feedback
            session.updated_at = datetime.utcnow()

            await self._save_session(session)

            # 记录评价统计
            await self.redis_client.lpush(
                'satisfaction_ratings',
                json.dumps({
                    'session_id': session_id,
                    'rating': rating,
                    'feedback': feedback,
                    'timestamp': datetime.utcnow().isoformat()
                })
            )

            return True

        except Exception as e:
            logger.error(f"Failed to rate conversation: {e}")
            return False

    async def get_customer_service_stats(self) -> Dict[str, Any]:
        """获取客服统计数据"""
        try:
            # 活跃会话统计
            active_sessions_count = len(self.active_sessions)

            # 工单统计
            total_tickets = len(self.ticket_queue)
            open_tickets = sum(1 for ticket in self.ticket_queue.values()
                             if ticket.status == TicketStatus.OPEN)

            # 满意度统计
            ratings_data = await self.redis_client.lrange('satisfaction_ratings', 0, -1)
            ratings = []
            for data in ratings_data:
                try:
                    rating_info = json.loads(data)
                    ratings.append(rating_info['rating'])
                except:
                    continue

            avg_rating = sum(ratings) / len(ratings) if ratings else 0

            # 知识库使用统计
            kb_views = sum(entry.view_count for entry in self.knowledge_base.values())

            return {
                'active_sessions': active_sessions_count,
                'total_tickets': total_tickets,
                'open_tickets': open_tickets,
                'average_satisfaction': round(avg_rating, 2),
                'total_ratings': len(ratings),
                'knowledge_base_views': kb_views,
                'auto_responses_count': len(self.auto_responses),
                'knowledge_base_entries': len(self.knowledge_base)
            }

        except Exception as e:
            logger.error(f"Failed to get customer service stats: {e}")
            return {'error': str(e)}

    async def add_knowledge_base_entry(self, title: str, content: str,
                                     category: str, tags: List[str]) -> str:
        """添加知识库条目"""
        try:
            entry_id = f"kb_{int(datetime.utcnow().timestamp())}"

            entry = KnowledgeBaseEntry(
                entry_id=entry_id,
                title=title,
                content=content,
                category=category,
                tags=tags,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )

            self.knowledge_base[entry_id] = entry

            # 保存到Redis
            entry_data = asdict(entry)
            entry_data['created_at'] = entry.created_at.isoformat()
            entry_data['updated_at'] = entry.updated_at.isoformat()

            await self.redis_client.hset(
                'knowledge_base',
                entry_id,
                json.dumps(entry_data, default=str)
            )

            return entry_id

        except Exception as e:
            logger.error(f"Failed to add knowledge base entry: {e}")
            raise

    async def update_auto_response(self, response_id: str, **kwargs) -> bool:
        """更新自动回复"""
        try:
            if response_id not in self.auto_responses:
                return False

            response = self.auto_responses[response_id]

            for key, value in kwargs.items():
                if hasattr(response, key):
                    setattr(response, key, value)

            # 保存到Redis
            response_data = asdict(response)
            await self.redis_client.hset(
                'auto_responses',
                response_id,
                json.dumps(response_data, default=str)
            )

            return True

        except Exception as e:
            logger.error(f"Failed to update auto response: {e}")
            return False

# 全局智能客服实例
intelligent_customer_service = None

def initialize_intelligent_customer_service(config: Dict[str, Any]):
    """初始化智能客服系统"""
    global intelligent_customer_service
    intelligent_customer_service = IntelligentCustomerService(config)
    return intelligent_customer_service
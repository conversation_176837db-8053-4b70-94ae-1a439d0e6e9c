# 自然语言处理服务
import asyncio
import json
import logging
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
import jieba
import jieba.posseg as pseg
from transformers import pipeline, AutoTokenizer, AutoModel
import torch
import openai
import aioredis
from fuzzywuzzy import fuzz, process
import spacy

logger = logging.getLogger(__name__)

@dataclass
class NLPResult:
    """NLP处理结果"""
    request_id: str
    input_text: str
    processed_text: str
    intent: str
    entities: List[Dict[str, Any]]
    sentiment: Dict[str, float]
    confidence_score: float
    response_text: str
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class ConversationContext:
    """对话上下文"""
    session_id: str
    user_id: str
    conversation_history: List[Dict[str, Any]]
    current_intent: str
    extracted_entities: Dict[str, Any]
    context_variables: Dict[str, Any]
    last_updated: datetime

@dataclass
class FinancialEntity:
    """财务实体"""
    entity_type: str  # amount, date, category, account, etc.
    value: str
    normalized_value: Any
    confidence: float
    start_pos: int
    end_pos: int

class NLPService:
    """自然语言处理服务"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.redis_client = None
        
        # NLP模型和工具
        self.tokenizer = None
        self.models = {}
        self.pipelines = {}
        
        # 意图识别
        self.intent_patterns = {}
        self.entity_patterns = {}
        
        # 对话管理
        self.conversation_contexts = {}
        
        # 财务领域知识库
        self.financial_vocabulary = {}
        self.category_mappings = {}
        
        # 初始化
        asyncio.create_task(self._initialize())
    
    async def _initialize(self):
        """初始化NLP服务"""
        try:
            # Redis连接
            redis_url = self.config.get('redis_url', 'redis://redis:6379')
            self.redis_client = await aioredis.from_url(redis_url)
            
            # 初始化中文NLP工具
            await self._initialize_chinese_nlp()
            
            # 初始化预训练模型
            await self._initialize_pretrained_models()
            
            # 加载财务领域知识
            await self._load_financial_knowledge()
            
            # 初始化意图识别
            await self._initialize_intent_recognition()
            
            logger.info("NLP service initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize NLP service: {e}")
            raise
    
    async def _initialize_chinese_nlp(self):
        """初始化中文NLP工具"""
        try:
            # 加载jieba分词
            jieba.load_userdict(self.config.get('user_dict_path', 'data/financial_dict.txt'))
            
            # 加载spaCy中文模型（如果可用）
            try:
                self.models['spacy_zh'] = spacy.load("zh_core_web_sm")
            except OSError:
                logger.warning("Chinese spaCy model not found, using basic tokenization")
                self.models['spacy_zh'] = None
            
            logger.info("Chinese NLP tools initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Chinese NLP: {e}")
    
    async def _initialize_pretrained_models(self):
        """初始化预训练模型"""
        try:
            # 情感分析
            self.pipelines['sentiment'] = pipeline(
                "sentiment-analysis",
                model="uer/roberta-base-finetuned-chinanews-chinese"
            )
            
            # 文本分类
            self.pipelines['classification'] = pipeline(
                "text-classification",
                model="uer/chinese_roberta_L-12_H-768"
            )
            
            # 命名实体识别
            self.pipelines['ner'] = pipeline(
                "ner",
                model="ckiplab/bert-base-chinese-ner",
                aggregation_strategy="simple"
            )
            
            # 问答系统
            self.pipelines['qa'] = pipeline(
                "question-answering",
                model="uer/chinese-roberta-base-finetuned-dureader-robust"
            )
            
            # OpenAI集成
            if self.config.get('openai_api_key'):
                openai.api_key = self.config['openai_api_key']
            
            logger.info("Pretrained models initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize pretrained models: {e}")
    
    async def _load_financial_knowledge(self):
        """加载财务领域知识"""
        try:
            # 财务词汇表
            self.financial_vocabulary = {
                'amounts': ['元', '万', '千', '百', '块', '毛', '分'],
                'time_expressions': ['今天', '昨天', '明天', '本月', '上月', '下月', '今年', '去年'],
                'transaction_types': ['收入', '支出', '转账', '投资', '借款', '还款'],
                'categories': {
                    '餐饮': ['吃饭', '用餐', '餐厅', '外卖', '食物'],
                    '交通': ['打车', '地铁', '公交', '油费', '停车'],
                    '购物': ['买', '购买', '商场', '超市', '网购'],
                    '娱乐': ['电影', '游戏', '旅游', '娱乐'],
                    '医疗': ['看病', '医院', '药费', '体检'],
                    '教育': ['学费', '培训', '书籍', '课程'],
                    '住房': ['房租', '水电', '物业', '装修'],
                    '通讯': ['话费', '网费', '流量']
                }
            }
            
            # 类别映射
            self.category_mappings = {}
            for category, keywords in self.financial_vocabulary['categories'].items():
                for keyword in keywords:
                    self.category_mappings[keyword] = category
            
            logger.info("Financial knowledge loaded")
            
        except Exception as e:
            logger.error(f"Failed to load financial knowledge: {e}")
    
    async def _initialize_intent_recognition(self):
        """初始化意图识别"""
        try:
            # 意图模式
            self.intent_patterns = {
                'add_transaction': [
                    r'记录.*(\d+).*元',
                    r'添加.*交易',
                    r'花了.*(\d+)',
                    r'收入.*(\d+)',
                    r'支出.*(\d+)'
                ],
                'query_balance': [
                    r'余额.*多少',
                    r'账户.*余额',
                    r'还有.*钱',
                    r'查看.*余额'
                ],
                'query_transactions': [
                    r'查看.*交易',
                    r'最近.*消费',
                    r'交易.*记录',
                    r'流水.*查询'
                ],
                'generate_report': [
                    r'生成.*报表',
                    r'财务.*报告',
                    r'统计.*分析',
                    r'月度.*总结'
                ],
                'budget_management': [
                    r'预算.*设置',
                    r'控制.*支出',
                    r'预算.*查询',
                    r'支出.*限制'
                ],
                'financial_advice': [
                    r'理财.*建议',
                    r'投资.*建议',
                    r'财务.*规划',
                    r'如何.*理财'
                ]
            }
            
            # 实体识别模式
            self.entity_patterns = {
                'amount': r'(\d+(?:\.\d+)?)\s*(?:元|万|千|块)',
                'date': r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})|今天|昨天|明天',
                'category': '|'.join(self.category_mappings.keys()),
                'account': r'(储蓄卡|信用卡|现金|支付宝|微信)'
            }
            
            logger.info("Intent recognition initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize intent recognition: {e}")
    
    async def process_natural_language(self, text: str, user_id: str, session_id: str) -> NLPResult:
        """处理自然语言输入"""
        try:
            request_id = f"nlp_{int(datetime.utcnow().timestamp())}"
            
            # 文本预处理
            processed_text = await self._preprocess_text(text)
            
            # 意图识别
            intent = await self._recognize_intent(processed_text)
            
            # 实体提取
            entities = await self._extract_entities(processed_text)
            
            # 情感分析
            sentiment = await self._analyze_sentiment(processed_text)
            
            # 获取对话上下文
            context = await self._get_conversation_context(session_id, user_id)
            
            # 生成响应
            response_text, confidence = await self._generate_response(
                processed_text, intent, entities, context
            )
            
            # 更新对话上下文
            await self._update_conversation_context(
                session_id, user_id, text, intent, entities, response_text
            )
            
            result = NLPResult(
                request_id=request_id,
                input_text=text,
                processed_text=processed_text,
                intent=intent,
                entities=entities,
                sentiment=sentiment,
                confidence_score=confidence,
                response_text=response_text,
                metadata={
                    'user_id': user_id,
                    'session_id': session_id,
                    'processing_time': datetime.utcnow().isoformat()
                }
            )
            
            # 保存处理结果
            await self._save_nlp_result(result)
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to process natural language: {e}")
            raise
    
    async def _preprocess_text(self, text: str) -> str:
        """文本预处理"""
        try:
            # 清理文本
            text = re.sub(r'[^\w\s\u4e00-\u9fff]', '', text)
            text = text.strip()
            
            # 分词
            words = jieba.lcut(text)
            
            # 去除停用词
            stopwords = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个'}
            words = [word for word in words if word not in stopwords and len(word) > 1]
            
            return ' '.join(words)
            
        except Exception as e:
            logger.error(f"Failed to preprocess text: {e}")
            return text
    
    async def _recognize_intent(self, text: str) -> str:
        """识别用户意图"""
        try:
            # 基于规则的意图识别
            for intent, patterns in self.intent_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, text):
                        return intent
            
            # 基于模型的意图识别
            if 'classification' in self.pipelines:
                result = self.pipelines['classification'](text)
                if result and len(result) > 0:
                    # 映射模型输出到我们的意图
                    model_label = result[0]['label']
                    mapped_intent = self._map_model_intent(model_label)
                    if mapped_intent:
                        return mapped_intent
            
            # 默认意图
            return 'general_inquiry'
            
        except Exception as e:
            logger.error(f"Failed to recognize intent: {e}")
            return 'unknown'
    
    def _map_model_intent(self, model_label: str) -> Optional[str]:
        """映射模型输出到业务意图"""
        mapping = {
            'POSITIVE': 'general_inquiry',
            'NEGATIVE': 'complaint',
            'NEUTRAL': 'general_inquiry'
        }
        return mapping.get(model_label)
    
    async def _extract_entities(self, text: str) -> List[Dict[str, Any]]:
        """提取实体"""
        entities = []
        
        try:
            # 基于规则的实体提取
            for entity_type, pattern in self.entity_patterns.items():
                matches = re.finditer(pattern, text)
                for match in matches:
                    entity = {
                        'type': entity_type,
                        'value': match.group(),
                        'start': match.start(),
                        'end': match.end(),
                        'confidence': 0.9
                    }
                    
                    # 标准化实体值
                    if entity_type == 'amount':
                        entity['normalized_value'] = self._normalize_amount(match.group())
                    elif entity_type == 'date':
                        entity['normalized_value'] = self._normalize_date(match.group())
                    elif entity_type == 'category':
                        entity['normalized_value'] = self.category_mappings.get(match.group(), match.group())
                    
                    entities.append(entity)
            
            # 基于模型的实体提取
            if 'ner' in self.pipelines:
                ner_results = self.pipelines['ner'](text)
                for result in ner_results:
                    entity = {
                        'type': result['entity_group'],
                        'value': result['word'],
                        'start': result['start'],
                        'end': result['end'],
                        'confidence': result['score'],
                        'normalized_value': result['word']
                    }
                    entities.append(entity)
            
        except Exception as e:
            logger.error(f"Failed to extract entities: {e}")
        
        return entities
    
    def _normalize_amount(self, amount_text: str) -> float:
        """标准化金额"""
        try:
            # 提取数字
            numbers = re.findall(r'\d+(?:\.\d+)?', amount_text)
            if not numbers:
                return 0.0
            
            amount = float(numbers[0])
            
            # 处理单位
            if '万' in amount_text:
                amount *= 10000
            elif '千' in amount_text:
                amount *= 1000
            elif '百' in amount_text:
                amount *= 100
            
            return amount
            
        except Exception:
            return 0.0
    
    def _normalize_date(self, date_text: str) -> str:
        """标准化日期"""
        try:
            today = datetime.now()
            
            if date_text == '今天':
                return today.strftime('%Y-%m-%d')
            elif date_text == '昨天':
                return (today - timedelta(days=1)).strftime('%Y-%m-%d')
            elif date_text == '明天':
                return (today + timedelta(days=1)).strftime('%Y-%m-%d')
            else:
                # 尝试解析具体日期
                date_match = re.match(r'(\d{4})[-/](\d{1,2})[-/](\d{1,2})', date_text)
                if date_match:
                    year, month, day = date_match.groups()
                    return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
            
            return date_text
            
        except Exception:
            return date_text
    
    async def _analyze_sentiment(self, text: str) -> Dict[str, float]:
        """分析情感"""
        try:
            if 'sentiment' in self.pipelines:
                result = self.pipelines['sentiment'](text)
                if result and len(result) > 0:
                    return {
                        'label': result[0]['label'],
                        'score': result[0]['score']
                    }
            
            return {'label': 'NEUTRAL', 'score': 0.5}
            
        except Exception as e:
            logger.error(f"Failed to analyze sentiment: {e}")
            return {'label': 'NEUTRAL', 'score': 0.5}
    
    async def _get_conversation_context(self, session_id: str, user_id: str) -> ConversationContext:
        """获取对话上下文"""
        try:
            # 从内存获取
            if session_id in self.conversation_contexts:
                return self.conversation_contexts[session_id]
            
            # 从Redis获取
            context_data = await self.redis_client.get(f"conversation:{session_id}")
            if context_data:
                context_dict = json.loads(context_data)
                context_dict['last_updated'] = datetime.fromisoformat(context_dict['last_updated'])
                return ConversationContext(**context_dict)
            
            # 创建新的上下文
            context = ConversationContext(
                session_id=session_id,
                user_id=user_id,
                conversation_history=[],
                current_intent='',
                extracted_entities={},
                context_variables={},
                last_updated=datetime.utcnow()
            )
            
            self.conversation_contexts[session_id] = context
            return context
            
        except Exception as e:
            logger.error(f"Failed to get conversation context: {e}")
            return ConversationContext(
                session_id=session_id,
                user_id=user_id,
                conversation_history=[],
                current_intent='',
                extracted_entities={},
                context_variables={},
                last_updated=datetime.utcnow()
            )
    
    async def _generate_response(self, text: str, intent: str, entities: List[Dict], 
                               context: ConversationContext) -> Tuple[str, float]:
        """生成响应"""
        try:
            confidence = 0.8
            
            # 基于意图生成响应
            if intent == 'add_transaction':
                response = await self._handle_add_transaction(entities, context)
            elif intent == 'query_balance':
                response = await self._handle_query_balance(context)
            elif intent == 'query_transactions':
                response = await self._handle_query_transactions(entities, context)
            elif intent == 'generate_report':
                response = await self._handle_generate_report(entities, context)
            elif intent == 'budget_management':
                response = await self._handle_budget_management(entities, context)
            elif intent == 'financial_advice':
                response = await self._handle_financial_advice(text, context)
            else:
                response = await self._handle_general_inquiry(text, context)
                confidence = 0.6
            
            return response, confidence
            
        except Exception as e:
            logger.error(f"Failed to generate response: {e}")
            return "抱歉，我暂时无法理解您的问题，请重新描述。", 0.3
    
    async def _handle_add_transaction(self, entities: List[Dict], context: ConversationContext) -> str:
        """处理添加交易请求"""
        amount = None
        category = None
        date = None
        
        for entity in entities:
            if entity['type'] == 'amount':
                amount = entity.get('normalized_value')
            elif entity['type'] == 'category':
                category = entity.get('normalized_value')
            elif entity['type'] == 'date':
                date = entity.get('normalized_value')
        
        if amount:
            response = f"好的，我帮您记录一笔 {amount} 元的交易"
            if category:
                response += f"，类别是 {category}"
            if date:
                response += f"，日期是 {date}"
            response += "。请确认是否正确？"
        else:
            response = "请告诉我交易金额，比如：记录100元餐饮支出"
        
        return response
    
    async def _handle_query_balance(self, context: ConversationContext) -> str:
        """处理余额查询"""
        # 这里应该调用实际的余额查询服务
        return "您当前的账户余额是 5,280.50 元。如需查看详细信息，请说'查看账户详情'。"
    
    async def _handle_query_transactions(self, entities: List[Dict], context: ConversationContext) -> str:
        """处理交易查询"""
        # 这里应该调用实际的交易查询服务
        return "您最近的交易记录：\n1. 今天 - 餐饮支出 85元\n2. 昨天 - 交通支出 12元\n3. 前天 - 购物支出 268元"
    
    async def _handle_generate_report(self, entities: List[Dict], context: ConversationContext) -> str:
        """处理报表生成"""
        return "正在为您生成财务报表，请稍候。报表将包含收支统计、类别分析和趋势图表。"
    
    async def _handle_budget_management(self, entities: List[Dict], context: ConversationContext) -> str:
        """处理预算管理"""
        return "您的本月预算使用情况：\n- 餐饮：已用 65% (1,300/2,000元)\n- 交通：已用 40% (200/500元)\n- 购物：已用 80% (800/1,000元)"
    
    async def _handle_financial_advice(self, text: str, context: ConversationContext) -> str:
        """处理理财建议"""
        if self.config.get('openai_api_key'):
            try:
                prompt = f"作为专业理财顾问，针对用户问题：{text}，提供简洁实用的理财建议。"
                
                response = await openai.ChatCompletion.acreate(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": "你是一个专业的理财顾问，提供实用的理财建议。"},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=300,
                    temperature=0.7
                )
                
                return response.choices[0].message.content.strip()
                
            except Exception as e:
                logger.error(f"Failed to get AI advice: {e}")
        
        return "建议您：1. 制定合理的预算计划 2. 控制不必要的支出 3. 考虑稳健的投资理财产品 4. 建立应急资金储备"
    
    async def _handle_general_inquiry(self, text: str, context: ConversationContext) -> str:
        """处理一般询问"""
        # 使用问答模型
        if 'qa' in self.pipelines:
            try:
                # 构建财务知识库上下文
                financial_context = """
                代理记账系统可以帮您：
                1. 记录和管理日常收支
                2. 生成各类财务报表
                3. 进行预算管理和控制
                4. 提供财务分析和建议
                5. 自动分类交易记录
                """
                
                result = self.pipelines['qa'](
                    question=text,
                    context=financial_context
                )
                
                if result['score'] > 0.5:
                    return result['answer']
                    
            except Exception as e:
                logger.error(f"Failed to use QA model: {e}")
        
        return "我是您的财务助手，可以帮您记录交易、查询余额、生成报表等。请告诉我您需要什么帮助？"
    
    async def _update_conversation_context(self, session_id: str, user_id: str, 
                                         user_input: str, intent: str, entities: List[Dict], 
                                         response: str):
        """更新对话上下文"""
        try:
            context = self.conversation_contexts.get(session_id)
            if not context:
                return
            
            # 添加到对话历史
            context.conversation_history.append({
                'timestamp': datetime.utcnow().isoformat(),
                'user_input': user_input,
                'intent': intent,
                'entities': entities,
                'response': response
            })
            
            # 限制历史记录长度
            if len(context.conversation_history) > 20:
                context.conversation_history = context.conversation_history[-10:]
            
            # 更新当前意图和实体
            context.current_intent = intent
            for entity in entities:
                context.extracted_entities[entity['type']] = entity
            
            context.last_updated = datetime.utcnow()
            
            # 保存到Redis
            context_data = asdict(context)
            context_data['last_updated'] = context.last_updated.isoformat()
            
            await self.redis_client.setex(
                f"conversation:{session_id}",
                3600,  # 1小时过期
                json.dumps(context_data, default=str)
            )
            
        except Exception as e:
            logger.error(f"Failed to update conversation context: {e}")
    
    async def _save_nlp_result(self, result: NLPResult):
        """保存NLP处理结果"""
        try:
            result_data = asdict(result)
            
            await self.redis_client.lpush(
                'nlp_results',
                json.dumps(result_data, default=str)
            )
            await self.redis_client.ltrim('nlp_results', 0, 999)
            
        except Exception as e:
            logger.error(f"Failed to save NLP result: {e}")
    
    async def get_conversation_summary(self, session_id: str) -> Dict[str, Any]:
        """获取对话摘要"""
        try:
            context = await self._get_conversation_context(session_id, '')
            
            if not context.conversation_history:
                return {'summary': '暂无对话记录'}
            
            # 统计意图分布
            intent_counts = {}
            for item in context.conversation_history:
                intent = item.get('intent', 'unknown')
                intent_counts[intent] = intent_counts.get(intent, 0) + 1
            
            # 提取关键实体
            key_entities = {}
            for item in context.conversation_history:
                for entity in item.get('entities', []):
                    entity_type = entity['type']
                    if entity_type not in key_entities:
                        key_entities[entity_type] = []
                    key_entities[entity_type].append(entity['value'])
            
            return {
                'session_id': session_id,
                'total_interactions': len(context.conversation_history),
                'intent_distribution': intent_counts,
                'key_entities': key_entities,
                'last_updated': context.last_updated.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get conversation summary: {e}")
            return {'error': str(e)}

# 全局NLP服务实例
nlp_service = None

def initialize_nlp_service(config: Dict[str, Any]):
    """初始化NLP服务"""
    global nlp_service
    nlp_service = NLPService(config)
    return nlp_service

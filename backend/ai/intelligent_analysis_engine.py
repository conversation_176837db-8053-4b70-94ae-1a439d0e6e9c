# AI驱动的智能分析引擎
import asyncio
import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, asdict
import tensorflow as tf
from transformers import pipeline, AutoTokenizer, AutoModel
import torch
from sklearn.ensemble import IsolationForest, RandomForestRegressor
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
import openai
import aioredis
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class AnalysisResult:
    """分析结果"""
    analysis_id: str
    analysis_type: str
    timestamp: datetime
    confidence_score: float
    insights: List[str]
    recommendations: List[str]
    data_summary: Dict[str, Any]
    visualizations: List[Dict[str, Any]]
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class AnomalyDetection:
    """异常检测结果"""
    anomaly_id: str
    timestamp: datetime
    anomaly_type: str
    severity: str  # low, medium, high, critical
    description: str
    affected_metrics: List[str]
    confidence_score: float
    suggested_actions: List[str]
    context_data: Dict[str, Any]

@dataclass
class PredictionResult:
    """预测结果"""
    prediction_id: str
    target_metric: str
    prediction_horizon: str
    predicted_values: List[float]
    confidence_intervals: List[Tuple[float, float]]
    accuracy_metrics: Dict[str, float]
    feature_importance: Dict[str, float]
    business_impact: str

class IntelligentAnalysisEngine:
    """智能分析引擎"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.redis_client = None

        # AI模型配置
        self.models = {}
        self.tokenizers = {}
        self.pipelines = {}

        # 分析历史
        self.analysis_history = []
        self.anomaly_history = []
        self.prediction_history = []

        # 数据处理器
        self.scalers = {}
        self.feature_extractors = {}

        # 初始化
        asyncio.create_task(self._initialize())

    async def _initialize(self):
        """初始化智能分析引擎"""
        try:
            # Redis连接
            redis_url = self.config.get('redis_url', 'redis://redis:6379')
            self.redis_client = await aioredis.from_url(redis_url)

            # 初始化AI模型
            await self._initialize_ai_models()

            # 加载预训练的分析器
            await self._load_analyzers()

            logger.info("Intelligent analysis engine initialized")

        except Exception as e:
            logger.error(f"Failed to initialize intelligent analysis engine: {e}")
            raise

    async def _initialize_ai_models(self):
        """初始化AI模型"""
        try:
            # 文本分析模型
            self.pipelines['sentiment'] = pipeline(
                "sentiment-analysis",
                model="cardiffnlp/twitter-roberta-base-sentiment-latest"
            )

            self.pipelines['text_classification'] = pipeline(
                "text-classification",
                model="microsoft/DialoGPT-medium"
            )

            # 时间序列预测模型
            self.models['time_series'] = self._create_lstm_model()

            # 异常检测模型
            self.models['anomaly_detection'] = IsolationForest(
                contamination=0.1,
                random_state=42
            )

            # 聚类模型
            self.models['clustering'] = KMeans(
                n_clusters=5,
                random_state=42
            )

            # 回归预测模型
            self.models['regression'] = RandomForestRegressor(
                n_estimators=100,
                random_state=42
            )

            # OpenAI GPT集成
            if self.config.get('openai_api_key'):
                openai.api_key = self.config['openai_api_key']

            logger.info("AI models initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize AI models: {e}")
            raise

    def _create_lstm_model(self) -> tf.keras.Model:
        """创建LSTM时间序列预测模型"""
        model = tf.keras.Sequential([
            tf.keras.layers.LSTM(50, return_sequences=True, input_shape=(60, 1)),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.LSTM(50, return_sequences=True),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.LSTM(50),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.Dense(1)
        ])

        model.compile(
            optimizer='adam',
            loss='mean_squared_error',
            metrics=['mae']
        )

        return model

    async def _load_analyzers(self):
        """加载预训练的分析器"""
        try:
            # 财务数据分析器
            self.scalers['financial'] = StandardScaler()

            # 用户行为分析器
            self.scalers['user_behavior'] = StandardScaler()

            # 特征提取器
            self.feature_extractors['pca'] = PCA(n_components=10)

            logger.info("Analyzers loaded successfully")

        except Exception as e:
            logger.error(f"Failed to load analyzers: {e}")

    async def analyze_financial_data(self, data: Dict[str, Any]) -> AnalysisResult:
        """分析财务数据"""
        try:
            analysis_id = f"financial_analysis_{int(datetime.utcnow().timestamp())}"

            # 数据预处理
            df = pd.DataFrame(data.get('transactions', []))
            if df.empty:
                return self._create_empty_result(analysis_id, "financial_analysis")

            # 基础统计分析
            insights = []
            recommendations = []
            visualizations = []

            # 收入支出分析
            income_data = df[df['amount'] > 0]
            expense_data = df[df['amount'] < 0]

            total_income = income_data['amount'].sum()
            total_expense = abs(expense_data['amount'].sum())
            net_flow = total_income - total_expense

            insights.append(f"总收入: ¥{total_income:,.2f}")
            insights.append(f"总支出: ¥{total_expense:,.2f}")
            insights.append(f"净现金流: ¥{net_flow:,.2f}")

            # 现金流趋势分析
            if len(df) > 30:
                trend_analysis = await self._analyze_cash_flow_trend(df)
                insights.extend(trend_analysis['insights'])
                recommendations.extend(trend_analysis['recommendations'])
                visualizations.extend(trend_analysis['visualizations'])

            # 支出类别分析
            if 'category' in df.columns:
                category_analysis = await self._analyze_expense_categories(expense_data)
                insights.extend(category_analysis['insights'])
                recommendations.extend(category_analysis['recommendations'])
                visualizations.extend(category_analysis['visualizations'])

            # 异常交易检测
            anomalies = await self._detect_transaction_anomalies(df)
            if anomalies:
                insights.append(f"检测到 {len(anomalies)} 笔异常交易")
                recommendations.append("建议审查标记的异常交易")

            # AI驱动的洞察
            ai_insights = await self._generate_ai_insights(df)
            insights.extend(ai_insights)

            # 智能建议
            smart_recommendations = await self._generate_smart_recommendations(df)
            recommendations.extend(smart_recommendations)

            result = AnalysisResult(
                analysis_id=analysis_id,
                analysis_type="financial_analysis",
                timestamp=datetime.utcnow(),
                confidence_score=0.85,
                insights=insights,
                recommendations=recommendations,
                data_summary={
                    'total_transactions': len(df),
                    'total_income': total_income,
                    'total_expense': total_expense,
                    'net_flow': net_flow,
                    'analysis_period': data.get('period', 'unknown')
                },
                visualizations=visualizations
            )

            # 保存分析结果
            await self._save_analysis_result(result)

            return result

        except Exception as e:
            logger.error(f"Failed to analyze financial data: {e}")
            raise

    async def _analyze_cash_flow_trend(self, df: pd.DataFrame) -> Dict[str, List]:
        """分析现金流趋势"""
        insights = []
        recommendations = []
        visualizations = []

        try:
            # 按日期聚合
            df['date'] = pd.to_datetime(df['date'])
            daily_flow = df.groupby(df['date'].dt.date)['amount'].sum()

            # 计算移动平均
            ma_7 = daily_flow.rolling(window=7).mean()
            ma_30 = daily_flow.rolling(window=30).mean()

            # 趋势分析
            recent_trend = ma_7.tail(7).mean()
            historical_avg = ma_30.mean()

            if recent_trend > historical_avg * 1.1:
                insights.append("现金流呈上升趋势，财务状况良好")
                recommendations.append("可考虑增加投资或扩大业务规模")
            elif recent_trend < historical_avg * 0.9:
                insights.append("现金流呈下降趋势，需要关注")
                recommendations.append("建议审查支出结构，优化成本控制")
            else:
                insights.append("现金流保持稳定")

            # 季节性分析
            if len(daily_flow) > 90:
                seasonal_pattern = await self._detect_seasonal_patterns(daily_flow)
                if seasonal_pattern:
                    insights.append(f"检测到季节性模式: {seasonal_pattern}")

            # 可视化数据
            visualizations.append({
                'type': 'line_chart',
                'title': '现金流趋势',
                'data': {
                    'dates': daily_flow.index.tolist(),
                    'values': daily_flow.values.tolist(),
                    'ma_7': ma_7.values.tolist(),
                    'ma_30': ma_30.values.tolist()
                }
            })

        except Exception as e:
            logger.error(f"Failed to analyze cash flow trend: {e}")

        return {
            'insights': insights,
            'recommendations': recommendations,
            'visualizations': visualizations
        }

    async def _analyze_expense_categories(self, expense_data: pd.DataFrame) -> Dict[str, List]:
        """分析支出类别"""
        insights = []
        recommendations = []
        visualizations = []

        try:
            if 'category' not in expense_data.columns:
                return {'insights': [], 'recommendations': [], 'visualizations': []}

            # 按类别统计
            category_totals = expense_data.groupby('category')['amount'].sum().abs()
            category_counts = expense_data.groupby('category').size()

            # 找出最大支出类别
            top_category = category_totals.idxmax()
            top_amount = category_totals.max()
            total_expense = category_totals.sum()

            insights.append(f"最大支出类别: {top_category} (¥{top_amount:,.2f})")
            insights.append(f"占总支出比例: {(top_amount/total_expense)*100:.1f}%")

            # 支出分布分析
            for category, amount in category_totals.head(5).items():
                percentage = (amount / total_expense) * 100
                insights.append(f"{category}: ¥{amount:,.2f} ({percentage:.1f}%)")

            # 异常支出检测
            avg_expense = category_totals.mean()
            std_expense = category_totals.std()

            for category, amount in category_totals.items():
                if amount > avg_expense + 2 * std_expense:
                    recommendations.append(f"关注 {category} 类别支出，金额异常偏高")

            # 可视化数据
            visualizations.append({
                'type': 'pie_chart',
                'title': '支出类别分布',
                'data': {
                    'categories': category_totals.index.tolist(),
                    'values': category_totals.values.tolist()
                }
            })

            visualizations.append({
                'type': 'bar_chart',
                'title': '各类别支出金额',
                'data': {
                    'categories': category_totals.index.tolist(),
                    'values': category_totals.values.tolist()
                }
            })

        except Exception as e:
            logger.error(f"Failed to analyze expense categories: {e}")

        return {
            'insights': insights,
            'recommendations': recommendations,
            'visualizations': visualizations
        }

    async def _detect_transaction_anomalies(self, df: pd.DataFrame) -> List[Dict]:
        """检测交易异常"""
        anomalies = []

        try:
            if len(df) < 10:
                return anomalies

            # 准备数据
            features = ['amount']
            if 'hour' in df.columns:
                features.append('hour')

            X = df[features].values

            # 异常检测
            isolation_forest = self.models['anomaly_detection']
            anomaly_scores = isolation_forest.fit_predict(X)

            # 找出异常点
            anomaly_indices = np.where(anomaly_scores == -1)[0]

            for idx in anomaly_indices:
                anomaly = {
                    'transaction_id': df.iloc[idx].get('id', idx),
                    'amount': df.iloc[idx]['amount'],
                    'date': df.iloc[idx].get('date', 'unknown'),
                    'anomaly_score': isolation_forest.score_samples([X[idx]])[0],
                    'reason': self._determine_anomaly_reason(df.iloc[idx], df)
                }
                anomalies.append(anomaly)

        except Exception as e:
            logger.error(f"Failed to detect transaction anomalies: {e}")

        return anomalies

    def _determine_anomaly_reason(self, transaction: pd.Series, df: pd.DataFrame) -> str:
        """确定异常原因"""
        amount = abs(transaction['amount'])
        avg_amount = df['amount'].abs().mean()
        std_amount = df['amount'].abs().std()

        if amount > avg_amount + 3 * std_amount:
            return "金额异常偏大"
        elif amount < avg_amount - 3 * std_amount:
            return "金额异常偏小"
        else:
            return "模式异常"

    async def _detect_seasonal_patterns(self, daily_flow: pd.Series) -> Optional[str]:
        """检测季节性模式"""
        try:
            # 简单的季节性检测
            if len(daily_flow) < 90:
                return None

            # 按月份分组
            monthly_avg = daily_flow.groupby(daily_flow.index.month).mean()

            # 检测是否有明显的季节性变化
            max_month = monthly_avg.idxmax()
            min_month = monthly_avg.idxmin()

            if monthly_avg.max() / monthly_avg.min() > 1.5:
                month_names = {
                    1: '一月', 2: '二月', 3: '三月', 4: '四月',
                    5: '五月', 6: '六月', 7: '七月', 8: '八月',
                    9: '九月', 10: '十月', 11: '十一月', 12: '十二月'
                }
                return f"{month_names[max_month]}表现最佳，{month_names[min_month]}表现最差"

            return None

        except Exception as e:
            logger.error(f"Failed to detect seasonal patterns: {e}")
            return None

    async def _generate_ai_insights(self, df: pd.DataFrame) -> List[str]:
        """生成AI洞察"""
        insights = []

        try:
            # 使用GPT生成洞察
            if self.config.get('openai_api_key'):
                # 准备数据摘要
                summary = {
                    'total_transactions': len(df),
                    'avg_amount': df['amount'].mean(),
                    'total_amount': df['amount'].sum(),
                    'date_range': f"{df['date'].min()} to {df['date'].max()}"
                }

                prompt = f"""
                基于以下财务数据，提供3个关键洞察：
                - 总交易数: {summary['total_transactions']}
                - 平均金额: {summary['avg_amount']:.2f}
                - 总金额: {summary['total_amount']:.2f}
                - 时间范围: {summary['date_range']}

                请提供简洁、实用的财务洞察。
                """

                response = await self._call_openai_api(prompt)
                if response:
                    insights.extend(response.split('\n'))

        except Exception as e:
            logger.error(f"Failed to generate AI insights: {e}")

        return insights

    async def _generate_smart_recommendations(self, df: pd.DataFrame) -> List[str]:
        """生成智能建议"""
        recommendations = []

        try:
            # 基于数据模式的建议
            if len(df) > 0:
                # 现金流建议
                net_flow = df['amount'].sum()
                if net_flow < 0:
                    recommendations.append("建议制定节支计划，改善现金流状况")
                elif net_flow > df['amount'].abs().mean() * 10:
                    recommendations.append("现金流充裕，可考虑投资理财产品")

                # 交易频率建议
                transaction_frequency = len(df) / 30  # 假设30天数据
                if transaction_frequency > 10:
                    recommendations.append("交易频率较高，建议使用自动化记账工具")

                # 支出优化建议
                if 'category' in df.columns:
                    expense_data = df[df['amount'] < 0]
                    if not expense_data.empty:
                        category_totals = expense_data.groupby('category')['amount'].sum().abs()
                        top_category = category_totals.idxmax()
                        recommendations.append(f"重点关注 {top_category} 类别支出的优化空间")

        except Exception as e:
            logger.error(f"Failed to generate smart recommendations: {e}")

        return recommendations

    async def _call_openai_api(self, prompt: str) -> Optional[str]:
        """调用OpenAI API"""
        try:
            response = await openai.ChatCompletion.acreate(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "你是一个专业的财务分析师，提供简洁实用的财务建议。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.7
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"Failed to call OpenAI API: {e}")
            return None

    def _create_empty_result(self, analysis_id: str, analysis_type: str) -> AnalysisResult:
        """创建空的分析结果"""
        return AnalysisResult(
            analysis_id=analysis_id,
            analysis_type=analysis_type,
            timestamp=datetime.utcnow(),
            confidence_score=0.0,
            insights=["暂无足够数据进行分析"],
            recommendations=["请增加更多交易数据"],
            data_summary={},
            visualizations=[]
        )

    async def _save_analysis_result(self, result: AnalysisResult):
        """保存分析结果"""
        try:
            # 添加到历史记录
            self.analysis_history.append(result)

            # 限制历史记录数量
            if len(self.analysis_history) > 1000:
                self.analysis_history = self.analysis_history[-500:]

            # 保存到Redis
            result_data = asdict(result)
            result_data['timestamp'] = result.timestamp.isoformat()

            await self.redis_client.lpush(
                'analysis_results',
                json.dumps(result_data, default=str)
            )
            await self.redis_client.ltrim('analysis_results', 0, 999)

        except Exception as e:
            logger.error(f"Failed to save analysis result: {e}")
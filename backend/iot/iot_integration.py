# IoT设备集成系统
import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import aioredis
import aiohttp
import paho.mqtt.client as mqtt
from cryptography.fernet import Fernet

logger = logging.getLogger(__name__)

class DeviceType(Enum):
    """设备类型"""
    POS_TERMINAL = "pos_terminal"
    RECEIPT_PRINTER = "receipt_printer"
    BARCODE_SCANNER = "barcode_scanner"
    SMART_SCALE = "smart_scale"
    TEMPERATURE_SENSOR = "temperature_sensor"
    SECURITY_CAMERA = "security_camera"
    ACCESS_CONTROL = "access_control"
    PAYMENT_TERMINAL = "payment_terminal"

class DeviceStatus(Enum):
    """设备状态"""
    ONLINE = "online"
    OFFLINE = "offline"
    ERROR = "error"
    MAINTENANCE = "maintenance"
    UPDATING = "updating"

@dataclass
class IoTDevice:
    """IoT设备"""
    device_id: str
    device_name: str
    device_type: DeviceType
    manufacturer: str
    model: str
    firmware_version: str
    ip_address: str
    mac_address: str
    location: str
    status: DeviceStatus
    last_seen: datetime
    capabilities: List[str]
    configuration: Dict[str, Any]
    security_key: str
    created_at: datetime
    updated_at: datetime

@dataclass
class DeviceData:
    """设备数据"""
    data_id: str
    device_id: str
    data_type: str
    payload: Dict[str, Any]
    timestamp: datetime
    processed: bool
    processing_result: Optional[Dict[str, Any]]

@dataclass
class DeviceCommand:
    """设备命令"""
    command_id: str
    device_id: str
    command_type: str
    parameters: Dict[str, Any]
    issued_at: datetime
    executed_at: Optional[datetime]
    status: str  # pending, executing, completed, failed
    result: Optional[Dict[str, Any]]

@dataclass
class DeviceAlert:
    """设备告警"""
    alert_id: str
    device_id: str
    alert_type: str
    severity: str  # low, medium, high, critical
    message: str
    details: Dict[str, Any]
    created_at: datetime
    acknowledged: bool
    resolved: bool

class IoTIntegrationSystem:
    """IoT集成系统"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.redis_client = None
        
        # 设备管理
        self.devices: Dict[str, IoTDevice] = {}
        self.device_data: List[DeviceData] = []
        self.device_commands: Dict[str, DeviceCommand] = {}
        self.device_alerts: List[DeviceAlert] = []
        
        # 通信协议
        self.mqtt_client = None
        self.http_server = None
        
        # 组件
        self.device_manager = DeviceManager()
        self.data_processor = DataProcessor()
        self.command_executor = CommandExecutor()
        self.alert_manager = AlertManager()
        self.security_manager = IoTSecurityManager()
        
        # 事件处理器
        self.event_handlers: Dict[str, List[Callable]] = {}
        
        # 初始化
        asyncio.create_task(self._initialize())
    
    async def _initialize(self):
        """初始化IoT集成系统"""
        try:
            # Redis连接
            redis_url = self.config.get('redis_url', 'redis://redis:6379')
            self.redis_client = await aioredis.from_url(redis_url)
            
            # 初始化MQTT客户端
            await self._initialize_mqtt_client()
            
            # 初始化组件
            await self.device_manager.initialize(self.config)
            await self.data_processor.initialize(self.config, self.redis_client)
            await self.command_executor.initialize(self.config)
            await self.alert_manager.initialize(self.config, self.redis_client)
            await self.security_manager.initialize(self.config)
            
            # 注册默认设备
            await self._register_default_devices()
            
            # 启动监控任务
            asyncio.create_task(self._start_device_monitoring())
            asyncio.create_task(self._start_data_processing())
            
            logger.info("IoT integration system initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize IoT integration system: {e}")
            raise
    
    async def _initialize_mqtt_client(self):
        """初始化MQTT客户端"""
        try:
            mqtt_config = self.config.get('mqtt', {})
            broker_host = mqtt_config.get('host', 'localhost')
            broker_port = mqtt_config.get('port', 1883)
            
            self.mqtt_client = mqtt.Client()
            self.mqtt_client.on_connect = self._on_mqtt_connect
            self.mqtt_client.on_message = self._on_mqtt_message
            self.mqtt_client.on_disconnect = self._on_mqtt_disconnect
            
            # 设置认证
            username = mqtt_config.get('username')
            password = mqtt_config.get('password')
            if username and password:
                self.mqtt_client.username_pw_set(username, password)
            
            # 连接到MQTT代理
            self.mqtt_client.connect_async(broker_host, broker_port, 60)
            self.mqtt_client.loop_start()
            
            logger.info(f"MQTT client initialized: {broker_host}:{broker_port}")
            
        except Exception as e:
            logger.error(f"Failed to initialize MQTT client: {e}")
    
    def _on_mqtt_connect(self, client, userdata, flags, rc):
        """MQTT连接回调"""
        if rc == 0:
            logger.info("Connected to MQTT broker")
            # 订阅设备主题
            client.subscribe("devices/+/data")
            client.subscribe("devices/+/status")
            client.subscribe("devices/+/alerts")
        else:
            logger.error(f"Failed to connect to MQTT broker: {rc}")
    
    def _on_mqtt_message(self, client, userdata, msg):
        """MQTT消息回调"""
        try:
            topic_parts = msg.topic.split('/')
            if len(topic_parts) >= 3:
                device_id = topic_parts[1]
                message_type = topic_parts[2]
                payload = json.loads(msg.payload.decode())
                
                # 异步处理消息
                asyncio.create_task(self._handle_mqtt_message(device_id, message_type, payload))
                
        except Exception as e:
            logger.error(f"Failed to process MQTT message: {e}")
    
    def _on_mqtt_disconnect(self, client, userdata, rc):
        """MQTT断开连接回调"""
        logger.warning(f"Disconnected from MQTT broker: {rc}")
    
    async def _handle_mqtt_message(self, device_id: str, message_type: str, payload: Dict[str, Any]):
        """处理MQTT消息"""
        try:
            if message_type == "data":
                await self._handle_device_data(device_id, payload)
            elif message_type == "status":
                await self._handle_device_status(device_id, payload)
            elif message_type == "alerts":
                await self._handle_device_alert(device_id, payload)
                
        except Exception as e:
            logger.error(f"Failed to handle MQTT message: {e}")
    
    async def _register_default_devices(self):
        """注册默认设备"""
        try:
            default_devices = [
                IoTDevice(
                    device_id="pos_001",
                    device_name="收银台POS机",
                    device_type=DeviceType.POS_TERMINAL,
                    manufacturer="POS Tech",
                    model="PT-2000",
                    firmware_version="1.2.3",
                    ip_address="*************",
                    mac_address="00:11:22:33:44:55",
                    location="前台收银区",
                    status=DeviceStatus.ONLINE,
                    last_seen=datetime.utcnow(),
                    capabilities=["payment_processing", "receipt_printing", "barcode_scanning"],
                    configuration={
                        "currency": "CNY",
                        "tax_rate": 0.13,
                        "receipt_template": "standard"
                    },
                    security_key=Fernet.generate_key().decode(),
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                ),
                IoTDevice(
                    device_id="scanner_001",
                    device_name="条码扫描器",
                    device_type=DeviceType.BARCODE_SCANNER,
                    manufacturer="Scan Corp",
                    model="SC-100",
                    firmware_version="2.1.0",
                    ip_address="*************",
                    mac_address="00:11:22:33:44:56",
                    location="仓库入口",
                    status=DeviceStatus.ONLINE,
                    last_seen=datetime.utcnow(),
                    capabilities=["barcode_scanning", "qr_code_scanning"],
                    configuration={
                        "scan_mode": "continuous",
                        "beep_enabled": True
                    },
                    security_key=Fernet.generate_key().decode(),
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                ),
                IoTDevice(
                    device_id="scale_001",
                    device_name="智能电子秤",
                    device_type=DeviceType.SMART_SCALE,
                    manufacturer="Smart Scales Inc",
                    model="SS-500",
                    firmware_version="1.0.5",
                    ip_address="*************",
                    mac_address="00:11:22:33:44:57",
                    location="包装区",
                    status=DeviceStatus.ONLINE,
                    last_seen=datetime.utcnow(),
                    capabilities=["weight_measurement", "price_calculation"],
                    configuration={
                        "unit": "kg",
                        "precision": 0.01,
                        "auto_zero": True
                    },
                    security_key=Fernet.generate_key().decode(),
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
            ]
            
            for device in default_devices:
                self.devices[device.device_id] = device
                await self._save_device(device)
            
            logger.info(f"Registered {len(self.devices)} default devices")
            
        except Exception as e:
            logger.error(f"Failed to register default devices: {e}")
    
    async def register_device(self, device_data: Dict[str, Any]) -> str:
        """注册新设备"""
        try:
            device_id = device_data.get('device_id') or f"device_{int(time.time())}"
            
            # 安全验证
            security_check = await self.security_manager.verify_device(device_data)
            if not security_check['verified']:
                raise ValueError(f"Device security verification failed: {security_check['reason']}")
            
            device = IoTDevice(
                device_id=device_id,
                device_name=device_data['device_name'],
                device_type=DeviceType(device_data['device_type']),
                manufacturer=device_data.get('manufacturer', ''),
                model=device_data.get('model', ''),
                firmware_version=device_data.get('firmware_version', ''),
                ip_address=device_data.get('ip_address', ''),
                mac_address=device_data.get('mac_address', ''),
                location=device_data.get('location', ''),
                status=DeviceStatus.OFFLINE,
                last_seen=datetime.utcnow(),
                capabilities=device_data.get('capabilities', []),
                configuration=device_data.get('configuration', {}),
                security_key=Fernet.generate_key().decode(),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            self.devices[device_id] = device
            await self._save_device(device)
            
            # 发送设备配置
            await self._send_device_configuration(device)
            
            logger.info(f"Device registered: {device_id}")
            return device_id
            
        except Exception as e:
            logger.error(f"Failed to register device: {e}")
            return ""
    
    async def send_command(self, device_id: str, command_type: str, 
                          parameters: Dict[str, Any]) -> str:
        """发送设备命令"""
        try:
            if device_id not in self.devices:
                raise ValueError(f"Device not found: {device_id}")
            
            command_id = f"cmd_{int(time.time())}"
            
            command = DeviceCommand(
                command_id=command_id,
                device_id=device_id,
                command_type=command_type,
                parameters=parameters,
                issued_at=datetime.utcnow(),
                executed_at=None,
                status="pending",
                result=None
            )
            
            self.device_commands[command_id] = command
            
            # 通过MQTT发送命令
            topic = f"devices/{device_id}/commands"
            payload = {
                "command_id": command_id,
                "command_type": command_type,
                "parameters": parameters,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            self.mqtt_client.publish(topic, json.dumps(payload))
            
            logger.info(f"Command sent to device {device_id}: {command_type}")
            return command_id
            
        except Exception as e:
            logger.error(f"Failed to send command: {e}")
            return ""
    
    async def get_device_data(self, device_id: str, data_type: Optional[str] = None,
                            start_time: Optional[datetime] = None,
                            end_time: Optional[datetime] = None) -> List[DeviceData]:
        """获取设备数据"""
        try:
            filtered_data = []
            
            for data in self.device_data:
                if data.device_id != device_id:
                    continue
                
                if data_type and data.data_type != data_type:
                    continue
                
                if start_time and data.timestamp < start_time:
                    continue
                
                if end_time and data.timestamp > end_time:
                    continue
                
                filtered_data.append(data)
            
            return filtered_data
            
        except Exception as e:
            logger.error(f"Failed to get device data: {e}")
            return []
    
    async def get_device_status(self, device_id: str) -> Dict[str, Any]:
        """获取设备状态"""
        try:
            if device_id not in self.devices:
                return {"error": f"Device not found: {device_id}"}
            
            device = self.devices[device_id]
            
            # 获取最近的数据
            recent_data = await self.get_device_data(
                device_id, 
                start_time=datetime.utcnow() - timedelta(hours=1)
            )
            
            # 获取未完成的命令
            pending_commands = [
                cmd for cmd in self.device_commands.values()
                if cmd.device_id == device_id and cmd.status == "pending"
            ]
            
            # 获取未解决的告警
            active_alerts = [
                alert for alert in self.device_alerts
                if alert.device_id == device_id and not alert.resolved
            ]
            
            return {
                "device_id": device_id,
                "device_name": device.device_name,
                "device_type": device.device_type.value,
                "status": device.status.value,
                "last_seen": device.last_seen.isoformat(),
                "location": device.location,
                "firmware_version": device.firmware_version,
                "capabilities": device.capabilities,
                "recent_data_count": len(recent_data),
                "pending_commands": len(pending_commands),
                "active_alerts": len(active_alerts),
                "uptime": self._calculate_uptime(device),
                "health_score": await self._calculate_health_score(device)
            }
            
        except Exception as e:
            logger.error(f"Failed to get device status: {e}")
            return {"error": str(e)}
    
    async def _handle_device_data(self, device_id: str, payload: Dict[str, Any]):
        """处理设备数据"""
        try:
            data = DeviceData(
                data_id=f"data_{int(time.time() * 1000)}",
                device_id=device_id,
                data_type=payload.get('type', 'unknown'),
                payload=payload,
                timestamp=datetime.utcnow(),
                processed=False,
                processing_result=None
            )
            
            self.device_data.append(data)
            
            # 更新设备最后见到时间
            if device_id in self.devices:
                self.devices[device_id].last_seen = datetime.utcnow()
                self.devices[device_id].status = DeviceStatus.ONLINE
            
            # 异步处理数据
            asyncio.create_task(self.data_processor.process_data(data))
            
        except Exception as e:
            logger.error(f"Failed to handle device data: {e}")
    
    async def _handle_device_status(self, device_id: str, payload: Dict[str, Any]):
        """处理设备状态"""
        try:
            if device_id in self.devices:
                device = self.devices[device_id]
                device.status = DeviceStatus(payload.get('status', 'offline'))
                device.last_seen = datetime.utcnow()
                await self._save_device(device)
                
        except Exception as e:
            logger.error(f"Failed to handle device status: {e}")
    
    async def _handle_device_alert(self, device_id: str, payload: Dict[str, Any]):
        """处理设备告警"""
        try:
            alert = DeviceAlert(
                alert_id=f"alert_{int(time.time() * 1000)}",
                device_id=device_id,
                alert_type=payload.get('type', 'unknown'),
                severity=payload.get('severity', 'medium'),
                message=payload.get('message', ''),
                details=payload.get('details', {}),
                created_at=datetime.utcnow(),
                acknowledged=False,
                resolved=False
            )
            
            self.device_alerts.append(alert)
            await self.alert_manager.process_alert(alert)
            
        except Exception as e:
            logger.error(f"Failed to handle device alert: {e}")

# 设备管理器
class DeviceManager:
    """设备管理器"""
    
    async def initialize(self, config: Dict[str, Any]):
        """初始化设备管理器"""
        self.config = config

# 数据处理器
class DataProcessor:
    """数据处理器"""
    
    async def initialize(self, config: Dict[str, Any], redis_client):
        """初始化数据处理器"""
        self.config = config
        self.redis_client = redis_client
    
    async def process_data(self, data: DeviceData):
        """处理设备数据"""
        try:
            # 根据数据类型进行处理
            if data.data_type == "transaction":
                await self._process_transaction_data(data)
            elif data.data_type == "sensor":
                await self._process_sensor_data(data)
            elif data.data_type == "scan":
                await self._process_scan_data(data)
            
            data.processed = True
            data.processing_result = {"status": "success", "processed_at": datetime.utcnow().isoformat()}
            
        except Exception as e:
            logger.error(f"Failed to process device data: {e}")
            data.processed = True
            data.processing_result = {"status": "error", "error": str(e)}
    
    async def _process_transaction_data(self, data: DeviceData):
        """处理交易数据"""
        # 处理POS交易数据
        pass
    
    async def _process_sensor_data(self, data: DeviceData):
        """处理传感器数据"""
        # 处理传感器数据
        pass
    
    async def _process_scan_data(self, data: DeviceData):
        """处理扫描数据"""
        # 处理条码扫描数据
        pass

# 命令执行器
class CommandExecutor:
    """命令执行器"""
    
    async def initialize(self, config: Dict[str, Any]):
        """初始化命令执行器"""
        self.config = config

# 告警管理器
class AlertManager:
    """告警管理器"""
    
    async def initialize(self, config: Dict[str, Any], redis_client):
        """初始化告警管理器"""
        self.config = config
        self.redis_client = redis_client
    
    async def process_alert(self, alert: DeviceAlert):
        """处理设备告警"""
        try:
            # 根据告警严重程度处理
            if alert.severity == "critical":
                await self._handle_critical_alert(alert)
            elif alert.severity == "high":
                await self._handle_high_alert(alert)
            
            # 记录告警
            await self._save_alert(alert)
            
        except Exception as e:
            logger.error(f"Failed to process alert: {e}")
    
    async def _handle_critical_alert(self, alert: DeviceAlert):
        """处理严重告警"""
        # 发送紧急通知
        pass
    
    async def _handle_high_alert(self, alert: DeviceAlert):
        """处理高级告警"""
        # 发送告警通知
        pass

# IoT安全管理器
class IoTSecurityManager:
    """IoT安全管理器"""
    
    async def initialize(self, config: Dict[str, Any]):
        """初始化IoT安全管理器"""
        self.config = config
    
    async def verify_device(self, device_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证设备"""
        try:
            # 简化的设备验证
            required_fields = ['device_name', 'device_type', 'mac_address']
            
            for field in required_fields:
                if field not in device_data:
                    return {"verified": False, "reason": f"Missing required field: {field}"}
            
            return {"verified": True, "reason": "Device verification passed"}
            
        except Exception as e:
            return {"verified": False, "reason": str(e)}

# 全局IoT集成系统
iot_integration_system = None

def initialize_iot_integration(config: Dict[str, Any]):
    """初始化IoT集成系统"""
    global iot_integration_system
    iot_integration_system = IoTIntegrationSystem(config)
    return iot_integration_system

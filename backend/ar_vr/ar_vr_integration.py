# AR/VR集成系统
import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import aioredis
import numpy as np

logger = logging.getLogger(__name__)

class ARVRDeviceType(Enum):
    """AR/VR设备类型"""
    AR_GLASSES = "ar_glasses"
    VR_HEADSET = "vr_headset"
    MIXED_REALITY = "mixed_reality"
    MOBILE_AR = "mobile_ar"
    WEB_AR = "web_ar"

class ContentType(Enum):
    """内容类型"""
    DATA_VISUALIZATION = "data_visualization"
    FINANCIAL_DASHBOARD = "financial_dashboard"
    INTERACTIVE_REPORT = "interactive_report"
    TRAINING_MODULE = "training_module"
    VIRTUAL_MEETING = "virtual_meeting"
    DOCUMENT_VIEWER = "document_viewer"

@dataclass
class ARVRDevice:
    """AR/VR设备"""
    device_id: str
    device_name: str
    device_type: ARVRDeviceType
    user_id: str
    capabilities: List[str]
    resolution: Tuple[int, int]
    refresh_rate: int
    tracking_type: str  # 6dof, 3dof, inside_out, outside_in
    status: str  # connected, disconnected, error
    battery_level: Optional[int]
    last_active: datetime
    session_id: Optional[str]

@dataclass
class ARVRContent:
    """AR/VR内容"""
    content_id: str
    title: str
    description: str
    content_type: ContentType
    data_source: str
    visualization_config: Dict[str, Any]
    interaction_config: Dict[str, Any]
    assets: List[str]  # 3D模型、纹理、音频等资源
    created_at: datetime
    updated_at: datetime
    version: str

@dataclass
class ARVRSession:
    """AR/VR会话"""
    session_id: str
    user_id: str
    device_id: str
    content_id: str
    started_at: datetime
    ended_at: Optional[datetime]
    duration: Optional[int]  # 秒
    interactions: List[Dict[str, Any]]
    performance_metrics: Dict[str, Any]
    user_feedback: Optional[Dict[str, Any]]

@dataclass
class Visualization3D:
    """3D可视化"""
    viz_id: str
    name: str
    chart_type: str  # bar_3d, pie_3d, scatter_3d, surface, volume
    data: List[Dict[str, Any]]
    styling: Dict[str, Any]
    animation: Dict[str, Any]
    interaction_rules: Dict[str, Any]
    position: Tuple[float, float, float]
    rotation: Tuple[float, float, float]
    scale: Tuple[float, float, float]

class ARVRIntegrationSystem:
    """AR/VR集成系统"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.redis_client = None
        
        # 设备和会话管理
        self.devices: Dict[str, ARVRDevice] = {}
        self.active_sessions: Dict[str, ARVRSession] = {}
        self.content_library: Dict[str, ARVRContent] = {}
        
        # 组件
        self.device_manager = ARVRDeviceManager()
        self.content_manager = ContentManager()
        self.visualization_engine = Visualization3DEngine()
        self.interaction_handler = InteractionHandler()
        self.performance_monitor = PerformanceMonitor()
        
        # 初始化
        asyncio.create_task(self._initialize())
    
    async def _initialize(self):
        """初始化AR/VR集成系统"""
        try:
            # Redis连接
            redis_url = self.config.get('redis_url', 'redis://redis:6379')
            self.redis_client = await aioredis.from_url(redis_url)
            
            # 初始化组件
            await self.device_manager.initialize(self.config)
            await self.content_manager.initialize(self.config)
            await self.visualization_engine.initialize(self.config)
            await self.interaction_handler.initialize(self.config)
            await self.performance_monitor.initialize(self.config, self.redis_client)
            
            # 创建默认内容
            await self._create_default_content()
            
            # 启动监控任务
            asyncio.create_task(self._start_session_monitoring())
            
            logger.info("AR/VR integration system initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize AR/VR integration system: {e}")
            raise
    
    async def _create_default_content(self):
        """创建默认内容"""
        try:
            default_contents = [
                ARVRContent(
                    content_id="financial_dashboard_3d",
                    title="3D财务仪表板",
                    description="沉浸式3D财务数据可视化仪表板",
                    content_type=ContentType.FINANCIAL_DASHBOARD,
                    data_source="financial_api",
                    visualization_config={
                        "layout": "circular",
                        "charts": [
                            {"type": "bar_3d", "data_field": "monthly_revenue", "position": [0, 0, 0]},
                            {"type": "pie_3d", "data_field": "expense_categories", "position": [2, 0, 0]},
                            {"type": "line_3d", "data_field": "cash_flow_trend", "position": [0, 0, 2]}
                        ],
                        "color_scheme": "financial",
                        "animation_speed": 1.0
                    },
                    interaction_config={
                        "gestures": ["tap", "pinch", "swipe"],
                        "voice_commands": ["show details", "filter by month", "export data"],
                        "eye_tracking": True
                    },
                    assets=["models/chart_base.obj", "textures/financial_theme.png"],
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    version="1.0"
                ),
                ARVRContent(
                    content_id="expense_analysis_vr",
                    title="VR支出分析环境",
                    description="虚拟现实支出分析和预测环境",
                    content_type=ContentType.DATA_VISUALIZATION,
                    data_source="expense_api",
                    visualization_config={
                        "environment": "office_space",
                        "data_representations": [
                            {"type": "floating_bubbles", "data_field": "transactions"},
                            {"type": "timeline_tunnel", "data_field": "expense_history"},
                            {"type": "category_islands", "data_field": "expense_categories"}
                        ],
                        "lighting": "dynamic",
                        "physics": True
                    },
                    interaction_config={
                        "controllers": ["hand_tracking", "voice", "gaze"],
                        "manipulation": ["grab", "throw", "resize"],
                        "navigation": ["teleport", "smooth_locomotion"]
                    },
                    assets=["environments/office.fbx", "sounds/ambient.wav"],
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    version="1.0"
                ),
                ARVRContent(
                    content_id="ar_receipt_scanner",
                    title="AR收据扫描器",
                    description="增强现实收据识别和数据提取",
                    content_type=ContentType.DOCUMENT_VIEWER,
                    data_source="ocr_api",
                    visualization_config={
                        "overlay_type": "information_panel",
                        "recognition_indicators": True,
                        "data_extraction_highlights": True,
                        "real_time_processing": True
                    },
                    interaction_config={
                        "camera_controls": ["focus", "zoom", "capture"],
                        "touch_gestures": ["tap_to_select", "drag_to_crop"],
                        "feedback": ["haptic", "visual", "audio"]
                    },
                    assets=["ui/ar_overlay.png", "sounds/scan_success.wav"],
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    version="1.0"
                )
            ]
            
            for content in default_contents:
                self.content_library[content.content_id] = content
                await self._save_content(content)
            
            logger.info(f"Created {len(self.content_library)} default AR/VR contents")
            
        except Exception as e:
            logger.error(f"Failed to create default content: {e}")
    
    async def register_device(self, device_data: Dict[str, Any]) -> str:
        """注册AR/VR设备"""
        try:
            device_id = device_data.get('device_id') or f"arvr_{int(time.time())}"
            
            device = ARVRDevice(
                device_id=device_id,
                device_name=device_data['device_name'],
                device_type=ARVRDeviceType(device_data['device_type']),
                user_id=device_data['user_id'],
                capabilities=device_data.get('capabilities', []),
                resolution=tuple(device_data.get('resolution', [1920, 1080])),
                refresh_rate=device_data.get('refresh_rate', 60),
                tracking_type=device_data.get('tracking_type', '6dof'),
                status="connected",
                battery_level=device_data.get('battery_level'),
                last_active=datetime.utcnow(),
                session_id=None
            )
            
            self.devices[device_id] = device
            await self._save_device(device)
            
            logger.info(f"AR/VR device registered: {device_id}")
            return device_id
            
        except Exception as e:
            logger.error(f"Failed to register AR/VR device: {e}")
            return ""
    
    async def start_session(self, user_id: str, device_id: str, content_id: str) -> str:
        """开始AR/VR会话"""
        try:
            if device_id not in self.devices:
                raise ValueError(f"Device not found: {device_id}")
            
            if content_id not in self.content_library:
                raise ValueError(f"Content not found: {content_id}")
            
            session_id = f"session_{int(time.time())}"
            
            session = ARVRSession(
                session_id=session_id,
                user_id=user_id,
                device_id=device_id,
                content_id=content_id,
                started_at=datetime.utcnow(),
                ended_at=None,
                duration=None,
                interactions=[],
                performance_metrics={},
                user_feedback=None
            )
            
            self.active_sessions[session_id] = session
            
            # 更新设备状态
            self.devices[device_id].session_id = session_id
            self.devices[device_id].last_active = datetime.utcnow()
            
            # 准备内容
            content = self.content_library[content_id]
            prepared_content = await self.content_manager.prepare_content(content, self.devices[device_id])
            
            # 启动性能监控
            await self.performance_monitor.start_session_monitoring(session_id)
            
            logger.info(f"AR/VR session started: {session_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"Failed to start AR/VR session: {e}")
            return ""
    
    async def end_session(self, session_id: str, user_feedback: Optional[Dict[str, Any]] = None) -> bool:
        """结束AR/VR会话"""
        try:
            if session_id not in self.active_sessions:
                return False
            
            session = self.active_sessions[session_id]
            session.ended_at = datetime.utcnow()
            session.duration = int((session.ended_at - session.started_at).total_seconds())
            session.user_feedback = user_feedback
            
            # 获取性能指标
            session.performance_metrics = await self.performance_monitor.get_session_metrics(session_id)
            
            # 清理设备状态
            if session.device_id in self.devices:
                self.devices[session.device_id].session_id = None
            
            # 移动到历史记录
            del self.active_sessions[session_id]
            await self._save_session_history(session)
            
            logger.info(f"AR/VR session ended: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to end AR/VR session: {e}")
            return False
    
    async def create_financial_visualization(self, data: Dict[str, Any], 
                                           viz_config: Dict[str, Any]) -> Visualization3D:
        """创建财务数据3D可视化"""
        try:
            viz_id = f"viz_{int(time.time())}"
            
            visualization = await self.visualization_engine.create_visualization(
                viz_id=viz_id,
                data=data,
                config=viz_config
            )
            
            return visualization
            
        except Exception as e:
            logger.error(f"Failed to create financial visualization: {e}")
            raise
    
    async def handle_interaction(self, session_id: str, interaction_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理用户交互"""
        try:
            if session_id not in self.active_sessions:
                return {"error": "Session not found"}
            
            session = self.active_sessions[session_id]
            
            # 记录交互
            interaction = {
                "timestamp": datetime.utcnow().isoformat(),
                "type": interaction_data.get("type"),
                "data": interaction_data.get("data", {}),
                "position": interaction_data.get("position"),
                "rotation": interaction_data.get("rotation")
            }
            
            session.interactions.append(interaction)
            
            # 处理交互
            response = await self.interaction_handler.process_interaction(
                session, interaction_data
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Failed to handle interaction: {e}")
            return {"error": str(e)}
    
    async def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """获取会话状态"""
        try:
            if session_id not in self.active_sessions:
                return {"error": "Session not found"}
            
            session = self.active_sessions[session_id]
            device = self.devices.get(session.device_id)
            content = self.content_library.get(session.content_id)
            
            current_time = datetime.utcnow()
            duration = int((current_time - session.started_at).total_seconds())
            
            return {
                "session_id": session_id,
                "user_id": session.user_id,
                "device_name": device.device_name if device else "Unknown",
                "content_title": content.title if content else "Unknown",
                "duration": duration,
                "interactions_count": len(session.interactions),
                "device_status": device.status if device else "Unknown",
                "battery_level": device.battery_level if device else None,
                "performance_metrics": await self.performance_monitor.get_current_metrics(session_id)
            }
            
        except Exception as e:
            logger.error(f"Failed to get session status: {e}")
            return {"error": str(e)}
    
    async def get_available_content(self, device_type: ARVRDeviceType) -> List[Dict[str, Any]]:
        """获取可用内容"""
        try:
            available_content = []
            
            for content in self.content_library.values():
                # 检查设备兼容性
                if await self._is_content_compatible(content, device_type):
                    available_content.append({
                        "content_id": content.content_id,
                        "title": content.title,
                        "description": content.description,
                        "content_type": content.content_type.value,
                        "version": content.version
                    })
            
            return available_content
            
        except Exception as e:
            logger.error(f"Failed to get available content: {e}")
            return []
    
    async def _is_content_compatible(self, content: ARVRContent, device_type: ARVRDeviceType) -> bool:
        """检查内容兼容性"""
        try:
            # 简化的兼容性检查
            compatibility_matrix = {
                ARVRDeviceType.AR_GLASSES: [ContentType.DATA_VISUALIZATION, ContentType.DOCUMENT_VIEWER],
                ARVRDeviceType.VR_HEADSET: [ContentType.FINANCIAL_DASHBOARD, ContentType.DATA_VISUALIZATION, ContentType.VIRTUAL_MEETING],
                ARVRDeviceType.MIXED_REALITY: [ContentType.DATA_VISUALIZATION, ContentType.FINANCIAL_DASHBOARD, ContentType.DOCUMENT_VIEWER],
                ARVRDeviceType.MOBILE_AR: [ContentType.DOCUMENT_VIEWER, ContentType.DATA_VISUALIZATION],
                ARVRDeviceType.WEB_AR: [ContentType.DATA_VISUALIZATION, ContentType.INTERACTIVE_REPORT]
            }
            
            compatible_types = compatibility_matrix.get(device_type, [])
            return content.content_type in compatible_types
            
        except Exception as e:
            logger.error(f"Failed to check content compatibility: {e}")
            return False

# AR/VR设备管理器
class ARVRDeviceManager:
    """AR/VR设备管理器"""
    
    async def initialize(self, config: Dict[str, Any]):
        """初始化设备管理器"""
        self.config = config

# 内容管理器
class ContentManager:
    """内容管理器"""
    
    async def initialize(self, config: Dict[str, Any]):
        """初始化内容管理器"""
        self.config = config
    
    async def prepare_content(self, content: ARVRContent, device: ARVRDevice) -> Dict[str, Any]:
        """准备内容"""
        try:
            # 根据设备能力调整内容
            prepared_content = {
                "content_id": content.content_id,
                "title": content.title,
                "visualization_config": content.visualization_config.copy(),
                "interaction_config": content.interaction_config.copy(),
                "assets": content.assets.copy()
            }
            
            # 根据设备分辨率调整
            if device.resolution[0] < 1920:
                prepared_content["visualization_config"]["quality"] = "medium"
            else:
                prepared_content["visualization_config"]["quality"] = "high"
            
            # 根据设备能力调整交互
            if "hand_tracking" not in device.capabilities:
                prepared_content["interaction_config"]["controllers"] = ["gaze", "voice"]
            
            return prepared_content
            
        except Exception as e:
            logger.error(f"Failed to prepare content: {e}")
            return {}

# 3D可视化引擎
class Visualization3DEngine:
    """3D可视化引擎"""
    
    async def initialize(self, config: Dict[str, Any]):
        """初始化可视化引擎"""
        self.config = config
    
    async def create_visualization(self, viz_id: str, data: Dict[str, Any], 
                                 config: Dict[str, Any]) -> Visualization3D:
        """创建3D可视化"""
        try:
            chart_type = config.get("chart_type", "bar_3d")
            
            # 处理数据
            processed_data = await self._process_data_for_3d(data, chart_type)
            
            visualization = Visualization3D(
                viz_id=viz_id,
                name=config.get("name", f"Visualization {viz_id}"),
                chart_type=chart_type,
                data=processed_data,
                styling=config.get("styling", {}),
                animation=config.get("animation", {}),
                interaction_rules=config.get("interaction_rules", {}),
                position=tuple(config.get("position", [0, 0, 0])),
                rotation=tuple(config.get("rotation", [0, 0, 0])),
                scale=tuple(config.get("scale", [1, 1, 1]))
            )
            
            return visualization
            
        except Exception as e:
            logger.error(f"Failed to create 3D visualization: {e}")
            raise
    
    async def _process_data_for_3d(self, data: Dict[str, Any], chart_type: str) -> List[Dict[str, Any]]:
        """为3D可视化处理数据"""
        try:
            if chart_type == "bar_3d":
                return await self._process_bar_3d_data(data)
            elif chart_type == "pie_3d":
                return await self._process_pie_3d_data(data)
            elif chart_type == "scatter_3d":
                return await self._process_scatter_3d_data(data)
            else:
                return data.get("values", [])
                
        except Exception as e:
            logger.error(f"Failed to process data for 3D: {e}")
            return []
    
    async def _process_bar_3d_data(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """处理3D柱状图数据"""
        processed = []
        values = data.get("values", [])
        
        for i, value in enumerate(values):
            processed.append({
                "x": i,
                "y": 0,
                "z": 0,
                "height": value.get("amount", 0),
                "color": value.get("color", "#3498db"),
                "label": value.get("label", f"Item {i}")
            })
        
        return processed
    
    async def _process_pie_3d_data(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """处理3D饼图数据"""
        processed = []
        values = data.get("values", [])
        total = sum(v.get("amount", 0) for v in values)
        
        current_angle = 0
        for value in values:
            amount = value.get("amount", 0)
            percentage = amount / total if total > 0 else 0
            angle = percentage * 360
            
            processed.append({
                "start_angle": current_angle,
                "end_angle": current_angle + angle,
                "percentage": percentage,
                "amount": amount,
                "color": value.get("color", "#3498db"),
                "label": value.get("label", "Unknown")
            })
            
            current_angle += angle
        
        return processed
    
    async def _process_scatter_3d_data(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """处理3D散点图数据"""
        processed = []
        values = data.get("values", [])
        
        for value in values:
            processed.append({
                "x": value.get("x", 0),
                "y": value.get("y", 0),
                "z": value.get("z", 0),
                "size": value.get("size", 1),
                "color": value.get("color", "#3498db"),
                "label": value.get("label", "Point")
            })
        
        return processed

# 交互处理器
class InteractionHandler:
    """交互处理器"""
    
    async def initialize(self, config: Dict[str, Any]):
        """初始化交互处理器"""
        self.config = config
    
    async def process_interaction(self, session: ARVRSession, 
                                interaction_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理交互"""
        try:
            interaction_type = interaction_data.get("type")
            
            if interaction_type == "gesture":
                return await self._handle_gesture(session, interaction_data)
            elif interaction_type == "voice":
                return await self._handle_voice_command(session, interaction_data)
            elif interaction_type == "gaze":
                return await self._handle_gaze_interaction(session, interaction_data)
            else:
                return {"status": "unknown_interaction", "type": interaction_type}
                
        except Exception as e:
            logger.error(f"Failed to process interaction: {e}")
            return {"status": "error", "message": str(e)}
    
    async def _handle_gesture(self, session: ARVRSession, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理手势交互"""
        gesture = data.get("gesture")
        
        if gesture == "tap":
            return {"status": "tap_processed", "action": "select_object"}
        elif gesture == "pinch":
            return {"status": "pinch_processed", "action": "zoom"}
        elif gesture == "swipe":
            return {"status": "swipe_processed", "action": "navigate"}
        
        return {"status": "gesture_unknown", "gesture": gesture}
    
    async def _handle_voice_command(self, session: ARVRSession, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理语音命令"""
        command = data.get("command", "").lower()
        
        if "show details" in command:
            return {"status": "voice_processed", "action": "show_details"}
        elif "filter" in command:
            return {"status": "voice_processed", "action": "apply_filter"}
        elif "export" in command:
            return {"status": "voice_processed", "action": "export_data"}
        
        return {"status": "voice_unknown", "command": command}
    
    async def _handle_gaze_interaction(self, session: ARVRSession, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理凝视交互"""
        target = data.get("target")
        duration = data.get("duration", 0)
        
        if duration > 2.0:  # 凝视超过2秒
            return {"status": "gaze_processed", "action": "select_by_gaze", "target": target}
        
        return {"status": "gaze_tracking", "target": target}

# 性能监控器
class PerformanceMonitor:
    """性能监控器"""
    
    async def initialize(self, config: Dict[str, Any], redis_client):
        """初始化性能监控器"""
        self.config = config
        self.redis_client = redis_client
        self.session_metrics = {}
    
    async def start_session_monitoring(self, session_id: str):
        """开始会话监控"""
        self.session_metrics[session_id] = {
            "start_time": time.time(),
            "frame_times": [],
            "interaction_latency": [],
            "memory_usage": [],
            "cpu_usage": []
        }
    
    async def get_session_metrics(self, session_id: str) -> Dict[str, Any]:
        """获取会话指标"""
        if session_id not in self.session_metrics:
            return {}
        
        metrics = self.session_metrics[session_id]
        
        return {
            "average_fps": 60.0,  # 模拟数据
            "frame_time_ms": 16.7,
            "interaction_latency_ms": 50.0,
            "memory_usage_mb": 512.0,
            "cpu_usage_percent": 45.0,
            "total_frames": len(metrics["frame_times"]),
            "dropped_frames": 0
        }
    
    async def get_current_metrics(self, session_id: str) -> Dict[str, Any]:
        """获取当前指标"""
        return await self.get_session_metrics(session_id)

# 全局AR/VR集成系统
ar_vr_integration_system = None

def initialize_ar_vr_integration(config: Dict[str, Any]):
    """初始化AR/VR集成系统"""
    global ar_vr_integration_system
    ar_vr_integration_system = ARVRIntegrationSystem(config)
    return ar_vr_integration_system

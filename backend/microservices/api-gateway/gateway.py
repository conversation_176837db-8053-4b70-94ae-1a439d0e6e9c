# API网关主服务
import asyncio
import json
import time
import logging
from typing import Dict, List, Optional
from fastapi import FastAPI, Request, Response, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import httpx
import redis
from prometheus_client import Counter, Histogram, generate_latest
import jwt
from datetime import datetime, timedelta
import hashlib
import aiofiles

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="代理记账 API 网关",
    description="微服务架构的统一入口",
    version="1.0.0"
)

# 中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"]
)

# Redis连接
redis_client = redis.Redis(host='redis', port=6379, decode_responses=True)

# Prometheus指标
REQUEST_COUNT = Counter('gateway_requests_total', 'Total requests', ['method', 'endpoint', 'status'])
REQUEST_DURATION = Histogram('gateway_request_duration_seconds', 'Request duration')
SERVICE_REQUESTS = Counter('service_requests_total', 'Service requests', ['service', 'status'])

# 服务配置
SERVICES = {
    'user': {
        'url': 'http://user-service:8001',
        'health_check': '/health',
        'timeout': 30,
        'retry_count': 3,
        'circuit_breaker': {
            'failure_threshold': 5,
            'recovery_timeout': 60,
            'half_open_max_calls': 3
        }
    },
    'finance': {
        'url': 'http://finance-service:8002',
        'health_check': '/health',
        'timeout': 30,
        'retry_count': 3,
        'circuit_breaker': {
            'failure_threshold': 5,
            'recovery_timeout': 60,
            'half_open_max_calls': 3
        }
    },
    'report': {
        'url': 'http://report-service:8003',
        'health_check': '/health',
        'timeout': 60,
        'retry_count': 2,
        'circuit_breaker': {
            'failure_threshold': 3,
            'recovery_timeout': 120,
            'half_open_max_calls': 2
        }
    },
    'notification': {
        'url': 'http://notification-service:8004',
        'health_check': '/health',
        'timeout': 15,
        'retry_count': 3,
        'circuit_breaker': {
            'failure_threshold': 5,
            'recovery_timeout': 30,
            'half_open_max_calls': 3
        }
    },
    'ai': {
        'url': 'http://ai-service:8005',
        'health_check': '/health',
        'timeout': 120,
        'retry_count': 1,
        'circuit_breaker': {
            'failure_threshold': 3,
            'recovery_timeout': 300,
            'half_open_max_calls': 1
        }
    },
    'file': {
        'url': 'http://file-service:8006',
        'health_check': '/health',
        'timeout': 60,
        'retry_count': 2,
        'circuit_breaker': {
            'failure_threshold': 3,
            'recovery_timeout': 60,
            'half_open_max_calls': 2
        }
    },
    'audit': {
        'url': 'http://audit-service:8007',
        'health_check': '/health',
        'timeout': 30,
        'retry_count': 3,
        'circuit_breaker': {
            'failure_threshold': 5,
            'recovery_timeout': 60,
            'half_open_max_calls': 3
        }
    }
}

# 路由配置
ROUTES = {
    '/api/v1/auth': 'user',
    '/api/v1/users': 'user',
    '/api/v1/profile': 'user',
    '/api/v1/accounts': 'finance',
    '/api/v1/transactions': 'finance',
    '/api/v1/invoices': 'finance',
    '/api/v1/reports': 'report',
    '/api/v1/analytics': 'report',
    '/api/v1/notifications': 'notification',
    '/api/v1/ai': 'ai',
    '/api/v1/predictions': 'ai',
    '/api/v1/files': 'file',
    '/api/v1/uploads': 'file',
    '/api/v1/audit': 'audit',
    '/api/v1/logs': 'audit'
}

# 熔断器状态
circuit_breakers = {}

class CircuitBreaker:
    def __init__(self, service_name: str, config: Dict):
        self.service_name = service_name
        self.failure_threshold = config['failure_threshold']
        self.recovery_timeout = config['recovery_timeout']
        self.half_open_max_calls = config['half_open_max_calls']
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
        self.half_open_calls = 0

    def can_execute(self) -> bool:
        if self.state == 'CLOSED':
            return True
        elif self.state == 'OPEN':
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = 'HALF_OPEN'
                self.half_open_calls = 0
                return True
            return False
        elif self.state == 'HALF_OPEN':
            return self.half_open_calls < self.half_open_max_calls

    def record_success(self):
        if self.state == 'HALF_OPEN':
            self.half_open_calls += 1
            if self.half_open_calls >= self.half_open_max_calls:
                self.state = 'CLOSED'
                self.failure_count = 0
        elif self.state == 'CLOSED':
            self.failure_count = 0

    def record_failure(self):
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.state == 'HALF_OPEN':
            self.state = 'OPEN'
        elif self.state == 'CLOSED' and self.failure_count >= self.failure_threshold:
            self.state = 'OPEN'

# 初始化熔断器
for service_name, config in SERVICES.items():
    circuit_breakers[service_name] = CircuitBreaker(service_name, config['circuit_breaker'])

# 限流器
class RateLimiter:
    def __init__(self, redis_client):
        self.redis = redis_client

    async def is_allowed(self, key: str, limit: int, window: int) -> bool:
        """滑动窗口限流"""
        current_time = int(time.time())
        pipeline = self.redis.pipeline()
        
        # 清理过期的记录
        pipeline.zremrangebyscore(key, 0, current_time - window)
        # 添加当前请求
        pipeline.zadd(key, {str(current_time): current_time})
        # 获取当前窗口内的请求数
        pipeline.zcard(key)
        # 设置过期时间
        pipeline.expire(key, window)
        
        results = pipeline.execute()
        request_count = results[2]
        
        return request_count <= limit

rate_limiter = RateLimiter(redis_client)

# JWT验证
def verify_token(token: str) -> Optional[Dict]:
    try:
        payload = jwt.decode(token, "your-secret-key", algorithms=["HS256"])
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

# 中间件
@app.middleware("http")
async def gateway_middleware(request: Request, call_next):
    start_time = time.time()
    
    # 请求ID
    request_id = hashlib.md5(f"{time.time()}{request.client.host}".encode()).hexdigest()
    request.state.request_id = request_id
    
    # 记录请求日志
    logger.info(f"Request {request_id}: {request.method} {request.url}")
    
    # 限流检查
    client_ip = request.client.host
    rate_limit_key = f"rate_limit:{client_ip}"
    
    if not await rate_limiter.is_allowed(rate_limit_key, 100, 60):  # 每分钟100次请求
        REQUEST_COUNT.labels(method=request.method, endpoint=str(request.url.path), status="429").inc()
        return JSONResponse(
            status_code=429,
            content={"error": "Rate limit exceeded", "request_id": request_id}
        )
    
    # 处理请求
    response = await call_next(request)
    
    # 记录指标
    duration = time.time() - start_time
    REQUEST_DURATION.observe(duration)
    REQUEST_COUNT.labels(
        method=request.method, 
        endpoint=str(request.url.path), 
        status=str(response.status_code)
    ).inc()
    
    # 添加响应头
    response.headers["X-Request-ID"] = request_id
    response.headers["X-Response-Time"] = str(duration)
    
    logger.info(f"Response {request_id}: {response.status_code} in {duration:.3f}s")
    
    return response

# 服务发现
def get_service_for_path(path: str) -> Optional[str]:
    for route_prefix, service_name in ROUTES.items():
        if path.startswith(route_prefix):
            return service_name
    return None

# 负载均衡
async def get_service_url(service_name: str) -> str:
    # 简单的轮询负载均衡
    # 在生产环境中，这里应该从服务注册中心获取健康的服务实例
    return SERVICES[service_name]['url']

# 服务代理
async def proxy_request(request: Request, service_name: str) -> Response:
    # 检查熔断器
    circuit_breaker = circuit_breakers[service_name]
    if not circuit_breaker.can_execute():
        SERVICE_REQUESTS.labels(service=service_name, status="circuit_open").inc()
        raise HTTPException(
            status_code=503, 
            detail=f"Service {service_name} is temporarily unavailable"
        )
    
    service_config = SERVICES[service_name]
    service_url = await get_service_url(service_name)
    
    # 构建目标URL
    target_url = f"{service_url}{request.url.path}"
    if request.url.query:
        target_url += f"?{request.url.query}"
    
    # 准备请求头
    headers = dict(request.headers)
    headers['X-Request-ID'] = request.state.request_id
    headers['X-Forwarded-For'] = request.client.host
    
    # 读取请求体
    body = await request.body()
    
    # 发送请求
    async with httpx.AsyncClient(timeout=service_config['timeout']) as client:
        try:
            response = await client.request(
                method=request.method,
                url=target_url,
                headers=headers,
                content=body
            )
            
            # 记录成功
            circuit_breaker.record_success()
            SERVICE_REQUESTS.labels(service=service_name, status="success").inc()
            
            # 返回响应
            return Response(
                content=response.content,
                status_code=response.status_code,
                headers=dict(response.headers)
            )
            
        except Exception as e:
            # 记录失败
            circuit_breaker.record_failure()
            SERVICE_REQUESTS.labels(service=service_name, status="error").inc()
            
            logger.error(f"Service {service_name} error: {str(e)}")
            raise HTTPException(
                status_code=502,
                detail=f"Service {service_name} error: {str(e)}"
            )

# 健康检查
@app.get("/health")
async def health_check():
    """网关健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": await check_services_health()
    }

async def check_services_health():
    """检查所有服务健康状态"""
    health_status = {}
    
    async with httpx.AsyncClient(timeout=5) as client:
        for service_name, config in SERVICES.items():
            try:
                health_url = f"{config['url']}{config['health_check']}"
                response = await client.get(health_url)
                health_status[service_name] = {
                    "status": "healthy" if response.status_code == 200 else "unhealthy",
                    "response_time": response.elapsed.total_seconds(),
                    "circuit_breaker": circuit_breakers[service_name].state
                }
            except Exception as e:
                health_status[service_name] = {
                    "status": "unhealthy",
                    "error": str(e),
                    "circuit_breaker": circuit_breakers[service_name].state
                }
    
    return health_status

# 指标端点
@app.get("/metrics")
async def metrics():
    """Prometheus指标"""
    return Response(generate_latest(), media_type="text/plain")

# 主要路由处理
@app.api_route("/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"])
async def gateway_handler(request: Request, path: str):
    """主要的网关路由处理器"""
    
    # 获取目标服务
    service_name = get_service_for_path(f"/{path}")
    if not service_name:
        raise HTTPException(status_code=404, detail="Service not found")
    
    # 身份验证（某些路径需要）
    if path.startswith("api/v1") and not path.startswith("api/v1/auth/login"):
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Authentication required")
        
        token = auth_header.split(" ")[1]
        user_info = verify_token(token)
        if not user_info:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        # 将用户信息添加到请求头
        request.headers.__dict__["_list"].append(
            (b"x-user-id", str(user_info.get("user_id", "")).encode())
        )
        request.headers.__dict__["_list"].append(
            (b"x-user-role", str(user_info.get("role", "")).encode())
        )
    
    # 代理请求
    return await proxy_request(request, service_name)

# 启动事件
@app.on_event("startup")
async def startup_event():
    logger.info("API Gateway starting up...")
    
    # 初始化Redis连接
    try:
        redis_client.ping()
        logger.info("Redis connection established")
    except Exception as e:
        logger.error(f"Redis connection failed: {e}")
    
    # 启动健康检查任务
    asyncio.create_task(periodic_health_check())

async def periodic_health_check():
    """定期健康检查"""
    while True:
        try:
            health_status = await check_services_health()
            # 将健康状态存储到Redis
            redis_client.setex(
                "services_health", 
                60, 
                json.dumps(health_status)
            )
        except Exception as e:
            logger.error(f"Health check error: {e}")
        
        await asyncio.sleep(30)  # 每30秒检查一次

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

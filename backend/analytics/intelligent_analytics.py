# 智能分析系统
import asyncio
import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict
import aioredis
from scipy import stats
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler

logger = logging.getLogger(__name__)

@dataclass
class AnalysisReport:
    """分析报告"""
    report_id: str
    user_id: str
    report_type: str
    title: str
    summary: str
    insights: List[Dict[str, Any]]
    recommendations: List[Dict[str, Any]]
    charts: List[Dict[str, Any]]
    data_period: Dict[str, str]
    created_at: datetime
    confidence_score: float

@dataclass
class FinancialInsight:
    """财务洞察"""
    insight_id: str
    insight_type: str  # trend, pattern, anomaly, opportunity
    title: str
    description: str
    impact_level: str  # high, medium, low
    confidence: float
    supporting_data: Dict[str, Any]
    action_items: List[str]

@dataclass
class SpendingPattern:
    """支出模式"""
    pattern_id: str
    pattern_type: str  # seasonal, weekly, monthly, category
    description: str
    frequency: str
    amount_range: Tuple[float, float]
    categories: List[str]
    time_periods: List[str]
    confidence: float

class IntelligentAnalyticsEngine:
    """智能分析引擎"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.redis_client = None
        
        # 分析器
        self.trend_analyzer = TrendAnalyzer()
        self.pattern_analyzer = PatternAnalyzer()
        self.anomaly_analyzer = AnomalyAnalyzer()
        self.behavior_analyzer = BehaviorAnalyzer()
        
        # 洞察生成器
        self.insight_generator = InsightGenerator()
        
        # 推荐引擎
        self.recommendation_engine = RecommendationEngine()
        
        # 可视化生成器
        self.visualization_generator = VisualizationGenerator()
        
        # 报告缓存
        self.report_cache = {}
        
        # 初始化
        asyncio.create_task(self._initialize())
    
    async def _initialize(self):
        """初始化智能分析引擎"""
        try:
            # Redis连接
            redis_url = self.config.get('redis_url', 'redis://redis:6379')
            self.redis_client = await aioredis.from_url(redis_url)
            
            # 初始化组件
            await self.trend_analyzer.initialize(self.config)
            await self.pattern_analyzer.initialize(self.config)
            await self.anomaly_analyzer.initialize(self.config)
            await self.behavior_analyzer.initialize(self.config)
            await self.insight_generator.initialize(self.config)
            await self.recommendation_engine.initialize(self.config)
            await self.visualization_generator.initialize(self.config)
            
            logger.info("Intelligent analytics engine initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize intelligent analytics engine: {e}")
            raise
    
    async def generate_financial_analysis(self, user_id: str, 
                                        analysis_type: str = "comprehensive") -> AnalysisReport:
        """生成财务分析报告"""
        try:
            report_id = f"report_{int(datetime.utcnow().timestamp())}"
            
            # 获取用户财务数据
            financial_data = await self._get_user_financial_data(user_id)
            
            if not financial_data:
                raise ValueError("No financial data available for analysis")
            
            # 执行各种分析
            insights = []
            recommendations = []
            charts = []
            
            # 趋势分析
            trend_insights = await self.trend_analyzer.analyze_trends(financial_data)
            insights.extend(trend_insights)
            
            # 模式分析
            pattern_insights = await self.pattern_analyzer.analyze_patterns(financial_data)
            insights.extend(pattern_insights)
            
            # 异常分析
            anomaly_insights = await self.anomaly_analyzer.detect_anomalies(financial_data)
            insights.extend(anomaly_insights)
            
            # 行为分析
            behavior_insights = await self.behavior_analyzer.analyze_behavior(financial_data)
            insights.extend(behavior_insights)
            
            # 生成推荐
            recommendations = await self.recommendation_engine.generate_recommendations(
                financial_data, insights
            )
            
            # 生成可视化
            charts = await self.visualization_generator.generate_charts(
                financial_data, insights
            )
            
            # 生成摘要
            summary = await self._generate_analysis_summary(insights, recommendations)
            
            # 计算置信度
            confidence_score = await self._calculate_analysis_confidence(insights)
            
            # 创建报告
            report = AnalysisReport(
                report_id=report_id,
                user_id=user_id,
                report_type=analysis_type,
                title=f"财务分析报告 - {datetime.now().strftime('%Y年%m月')}",
                summary=summary,
                insights=insights,
                recommendations=recommendations,
                charts=charts,
                data_period={
                    "start_date": (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d'),
                    "end_date": datetime.now().strftime('%Y-%m-%d')
                },
                created_at=datetime.utcnow(),
                confidence_score=confidence_score
            )
            
            # 缓存报告
            self.report_cache[report_id] = report
            await self._save_analysis_report(report)
            
            return report
            
        except Exception as e:
            logger.error(f"Failed to generate financial analysis: {e}")
            raise
    
    async def analyze_spending_behavior(self, user_id: str) -> Dict[str, Any]:
        """分析支出行为"""
        try:
            financial_data = await self._get_user_financial_data(user_id)
            
            # 支出行为分析
            behavior_analysis = await self.behavior_analyzer.analyze_spending_behavior(financial_data)
            
            # 支出模式识别
            spending_patterns = await self.pattern_analyzer.identify_spending_patterns(financial_data)
            
            # 支出预测
            spending_forecast = await self._forecast_spending(financial_data)
            
            return {
                "behavior_analysis": behavior_analysis,
                "spending_patterns": spending_patterns,
                "spending_forecast": spending_forecast,
                "analysis_date": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze spending behavior: {e}")
            raise
    
    async def detect_financial_opportunities(self, user_id: str) -> List[FinancialInsight]:
        """检测财务机会"""
        try:
            financial_data = await self._get_user_financial_data(user_id)
            
            opportunities = []
            
            # 节省机会
            saving_opportunities = await self._identify_saving_opportunities(financial_data)
            opportunities.extend(saving_opportunities)
            
            # 投资机会
            investment_opportunities = await self._identify_investment_opportunities(financial_data)
            opportunities.extend(investment_opportunities)
            
            # 优化机会
            optimization_opportunities = await self._identify_optimization_opportunities(financial_data)
            opportunities.extend(optimization_opportunities)
            
            return opportunities
            
        except Exception as e:
            logger.error(f"Failed to detect financial opportunities: {e}")
            return []
    
    async def _identify_saving_opportunities(self, financial_data: List[Dict]) -> List[FinancialInsight]:
        """识别节省机会"""
        opportunities = []
        
        try:
            df = pd.DataFrame(financial_data)
            expense_df = df[df['type'] == 'expense']
            
            # 分析各类别支出
            category_spending = expense_df.groupby('category')['amount'].agg(['sum', 'mean', 'count'])
            
            for category, stats in category_spending.iterrows():
                # 如果某类别支出过高
                if stats['sum'] > expense_df['amount'].sum() * 0.3:  # 超过总支出30%
                    opportunity = FinancialInsight(
                        insight_id=f"saving_{category}_{int(datetime.utcnow().timestamp())}",
                        insight_type="opportunity",
                        title=f"{category}支出优化机会",
                        description=f"您在{category}类别的支出占总支出的{stats['sum']/expense_df['amount'].sum()*100:.1f}%，存在优化空间",
                        impact_level="medium",
                        confidence=0.8,
                        supporting_data={
                            "category": category,
                            "total_amount": stats['sum'],
                            "percentage": stats['sum']/expense_df['amount'].sum()*100,
                            "transaction_count": stats['count']
                        },
                        action_items=[
                            f"审查{category}支出明细",
                            "寻找更经济的替代方案",
                            f"设置{category}预算限制"
                        ]
                    )
                    opportunities.append(opportunity)
            
        except Exception as e:
            logger.error(f"Failed to identify saving opportunities: {e}")
        
        return opportunities
    
    async def _identify_investment_opportunities(self, financial_data: List[Dict]) -> List[FinancialInsight]:
        """识别投资机会"""
        opportunities = []
        
        try:
            df = pd.DataFrame(financial_data)
            
            # 计算现金流
            income = df[df['type'] == 'income']['amount'].sum()
            expense = df[df['type'] == 'expense']['amount'].sum()
            surplus = income - expense
            
            # 如果有盈余
            if surplus > 0:
                opportunity = FinancialInsight(
                    insight_id=f"investment_{int(datetime.utcnow().timestamp())}",
                    insight_type="opportunity",
                    title="投资理财机会",
                    description=f"您有{surplus:.2f}元的资金盈余，可以考虑投资理财",
                    impact_level="high" if surplus > 10000 else "medium",
                    confidence=0.9,
                    supporting_data={
                        "surplus_amount": surplus,
                        "income": income,
                        "expense": expense,
                        "surplus_rate": surplus/income*100 if income > 0 else 0
                    },
                    action_items=[
                        "考虑定期存款或理财产品",
                        "了解基金投资选项",
                        "制定投资计划"
                    ]
                )
                opportunities.append(opportunity)
            
        except Exception as e:
            logger.error(f"Failed to identify investment opportunities: {e}")
        
        return opportunities
    
    async def _identify_optimization_opportunities(self, financial_data: List[Dict]) -> List[FinancialInsight]:
        """识别优化机会"""
        opportunities = []
        
        try:
            df = pd.DataFrame(financial_data)
            
            # 分析支出频率
            daily_expenses = df[df['type'] == 'expense'].groupby(
                pd.to_datetime(df['date']).dt.date
            )['amount'].sum()
            
            # 检测支出波动
            expense_std = daily_expenses.std()
            expense_mean = daily_expenses.mean()
            
            if expense_std > expense_mean * 0.5:  # 波动较大
                opportunity = FinancialInsight(
                    insight_id=f"optimization_{int(datetime.utcnow().timestamp())}",
                    insight_type="opportunity",
                    title="支出稳定性优化",
                    description="您的日常支出波动较大，建议制定更稳定的消费计划",
                    impact_level="medium",
                    confidence=0.7,
                    supporting_data={
                        "expense_volatility": expense_std/expense_mean,
                        "daily_average": expense_mean,
                        "daily_std": expense_std
                    },
                    action_items=[
                        "制定月度预算计划",
                        "设置日常支出限额",
                        "建立应急资金"
                    ]
                )
                opportunities.append(opportunity)
            
        except Exception as e:
            logger.error(f"Failed to identify optimization opportunities: {e}")
        
        return opportunities
    
    async def _generate_analysis_summary(self, insights: List[Dict], 
                                       recommendations: List[Dict]) -> str:
        """生成分析摘要"""
        try:
            insight_count = len(insights)
            recommendation_count = len(recommendations)
            
            # 统计洞察类型
            insight_types = defaultdict(int)
            for insight in insights:
                insight_types[insight.get('insight_type', 'unknown')] += 1
            
            summary_parts = [
                f"本次分析共发现{insight_count}个财务洞察，提供{recommendation_count}条建议。"
            ]
            
            if insight_types['trend'] > 0:
                summary_parts.append(f"发现{insight_types['trend']}个趋势变化。")
            
            if insight_types['pattern'] > 0:
                summary_parts.append(f"识别{insight_types['pattern']}个支出模式。")
            
            if insight_types['anomaly'] > 0:
                summary_parts.append(f"检测到{insight_types['anomaly']}个异常情况。")
            
            if insight_types['opportunity'] > 0:
                summary_parts.append(f"发现{insight_types['opportunity']}个优化机会。")
            
            return " ".join(summary_parts)
            
        except Exception as e:
            logger.error(f"Failed to generate analysis summary: {e}")
            return "分析摘要生成失败"
    
    async def _calculate_analysis_confidence(self, insights: List[Dict]) -> float:
        """计算分析置信度"""
        try:
            if not insights:
                return 0.0
            
            # 基于洞察数量和质量计算置信度
            total_confidence = sum(insight.get('confidence', 0.5) for insight in insights)
            average_confidence = total_confidence / len(insights)
            
            # 根据数据量调整置信度
            data_quality_factor = min(len(insights) / 10, 1.0)  # 最多10个洞察为满分
            
            final_confidence = average_confidence * data_quality_factor
            
            return min(max(final_confidence, 0.0), 1.0)

        except Exception as e:
            logger.error(f"Failed to calculate analysis confidence: {e}")
            return 0.5

    async def _get_user_financial_data(self, user_id: str) -> List[Dict]:
        """获取用户财务数据"""
        try:
            # 这里应该从数据库获取用户的财务数据
            # 目前返回模拟数据
            return [
                {
                    "id": "1",
                    "user_id": user_id,
                    "amount": 100.50,
                    "type": "expense",
                    "category": "餐饮",
                    "description": "午餐",
                    "date": "2024-01-15",
                    "merchant": "餐厅A"
                },
                {
                    "id": "2",
                    "user_id": user_id,
                    "amount": 5000.00,
                    "type": "income",
                    "category": "工资",
                    "description": "月薪",
                    "date": "2024-01-01",
                    "merchant": ""
                },
                {
                    "id": "3",
                    "user_id": user_id,
                    "amount": 200.00,
                    "type": "expense",
                    "category": "交通",
                    "description": "地铁卡充值",
                    "date": "2024-01-10",
                    "merchant": "地铁站"
                }
            ]
        except Exception as e:
            logger.error(f"Failed to get user financial data: {e}")
            return []

    async def _forecast_spending(self, financial_data: List[Dict]) -> Dict[str, Any]:
        """预测支出"""
        try:
            df = pd.DataFrame(financial_data)
            expense_df = df[df['type'] == 'expense']

            if len(expense_df) < 7:
                return {"error": "Insufficient data for forecasting"}

            # 按日期聚合支出
            expense_df['date'] = pd.to_datetime(expense_df['date'])
            daily_expenses = expense_df.groupby(expense_df['date'].dt.date)['amount'].sum()

            # 简单的移动平均预测
            window = min(7, len(daily_expenses))
            recent_avg = daily_expenses.tail(window).mean()

            # 预测未来7天
            forecast_dates = []
            forecast_amounts = []

            for i in range(1, 8):
                future_date = datetime.now().date() + timedelta(days=i)
                forecast_dates.append(future_date.strftime('%Y-%m-%d'))
                # 添加一些随机波动
                forecast_amount = recent_avg * (0.8 + 0.4 * np.random.random())
                forecast_amounts.append(round(forecast_amount, 2))

            return {
                "forecast_period": "7_days",
                "forecast_dates": forecast_dates,
                "forecast_amounts": forecast_amounts,
                "average_daily_spending": round(recent_avg, 2),
                "total_forecast": round(sum(forecast_amounts), 2),
                "confidence": 0.7
            }

        except Exception as e:
            logger.error(f"Failed to forecast spending: {e}")
            return {"error": str(e)}

    async def _save_analysis_report(self, report: AnalysisReport):
        """保存分析报告"""
        try:
            report_data = asdict(report)
            report_data['created_at'] = report.created_at.isoformat()

            await self.redis_client.hset(
                'analysis_reports',
                report.report_id,
                json.dumps(report_data, default=str)
            )
        except Exception as e:
            logger.error(f"Failed to save analysis report: {e}")

# 趋势分析器
class TrendAnalyzer:
    """趋势分析器"""
    
    async def initialize(self, config: Dict[str, Any]):
        """初始化趋势分析器"""
        self.config = config
    
    async def analyze_trends(self, financial_data: List[Dict]) -> List[Dict]:
        """分析财务趋势"""
        trends = []
        
        try:
            df = pd.DataFrame(financial_data)
            df['date'] = pd.to_datetime(df['date'])
            
            # 月度趋势分析
            monthly_data = df.groupby([df['date'].dt.to_period('M'), 'type'])['amount'].sum().unstack(fill_value=0)
            
            if 'income' in monthly_data.columns and len(monthly_data) > 1:
                # 收入趋势
                income_trend = self._calculate_trend(monthly_data['income'])
                trends.append({
                    'insight_type': 'trend',
                    'title': '收入趋势分析',
                    'description': f"收入呈{'上升' if income_trend > 0 else '下降' if income_trend < 0 else '稳定'}趋势",
                    'confidence': 0.8,
                    'trend_value': income_trend
                })
            
            if 'expense' in monthly_data.columns and len(monthly_data) > 1:
                # 支出趋势
                expense_trend = self._calculate_trend(monthly_data['expense'])
                trends.append({
                    'insight_type': 'trend',
                    'title': '支出趋势分析',
                    'description': f"支出呈{'上升' if expense_trend > 0 else '下降' if expense_trend < 0 else '稳定'}趋势",
                    'confidence': 0.8,
                    'trend_value': expense_trend
                })
            
        except Exception as e:
            logger.error(f"Failed to analyze trends: {e}")
        
        return trends
    
    def _calculate_trend(self, data: pd.Series) -> float:
        """计算趋势值"""
        try:
            if len(data) < 2:
                return 0.0
            
            # 使用线性回归计算趋势
            x = np.arange(len(data))
            slope, _, r_value, _, _ = stats.linregress(x, data.values)
            
            # 返回标准化的趋势值
            return slope * r_value
            
        except Exception as e:
            logger.error(f"Failed to calculate trend: {e}")
            return 0.0

# 模式分析器
class PatternAnalyzer:
    """模式分析器"""
    
    async def initialize(self, config: Dict[str, Any]):
        """初始化模式分析器"""
        self.config = config
    
    async def analyze_patterns(self, financial_data: List[Dict]) -> List[Dict]:
        """分析支出模式"""
        patterns = []
        
        try:
            df = pd.DataFrame(financial_data)
            df['date'] = pd.to_datetime(df['date'])
            expense_df = df[df['type'] == 'expense']
            
            # 周模式分析
            weekly_pattern = expense_df.groupby(expense_df['date'].dt.dayofweek)['amount'].mean()
            peak_day = weekly_pattern.idxmax()
            
            patterns.append({
                'insight_type': 'pattern',
                'title': '周支出模式',
                'description': f"您在{'周一' if peak_day == 0 else '周二' if peak_day == 1 else '周三' if peak_day == 2 else '周四' if peak_day == 3 else '周五' if peak_day == 4 else '周六' if peak_day == 5 else '周日'}的支出最高",
                'confidence': 0.7,
                'pattern_data': weekly_pattern.to_dict()
            })
            
            # 类别模式分析
            category_pattern = expense_df.groupby('category')['amount'].agg(['sum', 'count'])
            top_category = category_pattern['sum'].idxmax()
            
            patterns.append({
                'insight_type': 'pattern',
                'title': '支出类别模式',
                'description': f"您的主要支出类别是{top_category}",
                'confidence': 0.9,
                'pattern_data': category_pattern.to_dict()
            })
            
        except Exception as e:
            logger.error(f"Failed to analyze patterns: {e}")
        
        return patterns
    
    async def identify_spending_patterns(self, financial_data: List[Dict]) -> List[SpendingPattern]:
        """识别支出模式"""
        patterns = []
        
        try:
            df = pd.DataFrame(financial_data)
            expense_df = df[df['type'] == 'expense']
            
            # 使用聚类识别支出模式
            if len(expense_df) > 10:
                features = expense_df[['amount']].values
                scaler = StandardScaler()
                features_scaled = scaler.fit_transform(features)
                
                kmeans = KMeans(n_clusters=3, random_state=42)
                clusters = kmeans.fit_predict(features_scaled)
                
                expense_df['cluster'] = clusters
                
                for cluster_id in range(3):
                    cluster_data = expense_df[expense_df['cluster'] == cluster_id]
                    
                    pattern = SpendingPattern(
                        pattern_id=f"pattern_{cluster_id}",
                        pattern_type="amount_based",
                        description=f"支出模式{cluster_id + 1}",
                        frequency="regular",
                        amount_range=(cluster_data['amount'].min(), cluster_data['amount'].max()),
                        categories=cluster_data['category'].unique().tolist(),
                        time_periods=[],
                        confidence=0.7
                    )
                    patterns.append(pattern)
            
        except Exception as e:
            logger.error(f"Failed to identify spending patterns: {e}")
        
        return patterns

# 异常分析器
class AnomalyAnalyzer:
    """异常分析器"""
    
    async def initialize(self, config: Dict[str, Any]):
        """初始化异常分析器"""
        self.config = config
    
    async def detect_anomalies(self, financial_data: List[Dict]) -> List[Dict]:
        """检测异常"""
        anomalies = []
        
        try:
            df = pd.DataFrame(financial_data)
            expense_df = df[df['type'] == 'expense']
            
            if len(expense_df) > 10:
                # 使用IQR方法检测异常
                Q1 = expense_df['amount'].quantile(0.25)
                Q3 = expense_df['amount'].quantile(0.75)
                IQR = Q3 - Q1
                
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                anomaly_transactions = expense_df[
                    (expense_df['amount'] < lower_bound) | 
                    (expense_df['amount'] > upper_bound)
                ]
                
                if len(anomaly_transactions) > 0:
                    anomalies.append({
                        'insight_type': 'anomaly',
                        'title': '异常支出检测',
                        'description': f"检测到{len(anomaly_transactions)}笔异常支出",
                        'confidence': 0.8,
                        'anomaly_count': len(anomaly_transactions),
                        'anomaly_details': anomaly_transactions.to_dict('records')
                    })
            
        except Exception as e:
            logger.error(f"Failed to detect anomalies: {e}")
        
        return anomalies

# 行为分析器
class BehaviorAnalyzer:
    """行为分析器"""
    
    async def initialize(self, config: Dict[str, Any]):
        """初始化行为分析器"""
        self.config = config
    
    async def analyze_behavior(self, financial_data: List[Dict]) -> List[Dict]:
        """分析财务行为"""
        behaviors = []
        
        try:
            df = pd.DataFrame(financial_data)
            
            # 分析支出频率
            expense_df = df[df['type'] == 'expense']
            daily_transactions = expense_df.groupby(
                pd.to_datetime(expense_df['date']).dt.date
            ).size()
            
            avg_daily_transactions = daily_transactions.mean()
            
            behaviors.append({
                'insight_type': 'behavior',
                'title': '交易频率分析',
                'description': f"您平均每天进行{avg_daily_transactions:.1f}笔支出交易",
                'confidence': 0.9,
                'avg_daily_transactions': avg_daily_transactions
            })
            
        except Exception as e:
            logger.error(f"Failed to analyze behavior: {e}")
        
        return behaviors
    
    async def analyze_spending_behavior(self, financial_data: List[Dict]) -> Dict[str, Any]:
        """分析支出行为"""
        try:
            df = pd.DataFrame(financial_data)
            expense_df = df[df['type'] == 'expense']
            
            return {
                'total_transactions': len(expense_df),
                'total_amount': expense_df['amount'].sum(),
                'average_transaction': expense_df['amount'].mean(),
                'spending_frequency': len(expense_df) / 30,  # 假设30天数据
                'top_categories': expense_df.groupby('category')['amount'].sum().nlargest(5).to_dict()
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze spending behavior: {e}")
            return {}

# 洞察生成器
class InsightGenerator:
    """洞察生成器"""
    
    async def initialize(self, config: Dict[str, Any]):
        """初始化洞察生成器"""
        self.config = config

# 推荐引擎
class RecommendationEngine:
    """推荐引擎"""
    
    async def initialize(self, config: Dict[str, Any]):
        """初始化推荐引擎"""
        self.config = config
    
    async def generate_recommendations(self, financial_data: List[Dict], 
                                     insights: List[Dict]) -> List[Dict]:
        """生成推荐"""
        recommendations = []
        
        try:
            # 基于洞察生成推荐
            for insight in insights:
                if insight.get('insight_type') == 'trend':
                    if insight.get('trend_value', 0) > 0:
                        recommendations.append({
                            'type': 'optimization',
                            'title': '支出控制建议',
                            'description': '您的支出呈上升趋势，建议制定预算计划',
                            'priority': 'high',
                            'actions': ['制定月度预算', '设置支出提醒', '分析支出明细']
                        })
                
                elif insight.get('insight_type') == 'anomaly':
                    recommendations.append({
                        'type': 'alert',
                        'title': '异常支出提醒',
                        'description': '检测到异常支出，请核实交易详情',
                        'priority': 'high',
                        'actions': ['核实异常交易', '检查账户安全', '设置交易限额']
                    })
            
        except Exception as e:
            logger.error(f"Failed to generate recommendations: {e}")
        
        return recommendations

# 可视化生成器
class VisualizationGenerator:
    """可视化生成器"""
    
    async def initialize(self, config: Dict[str, Any]):
        """初始化可视化生成器"""
        self.config = config
    
    async def generate_charts(self, financial_data: List[Dict], 
                            insights: List[Dict]) -> List[Dict]:
        """生成图表"""
        charts = []
        
        try:
            df = pd.DataFrame(financial_data)
            
            # 生成支出趋势图
            expense_trend_chart = await self._generate_expense_trend_chart(df)
            if expense_trend_chart:
                charts.append(expense_trend_chart)
            
            # 生成类别分布图
            category_chart = await self._generate_category_chart(df)
            if category_chart:
                charts.append(category_chart)
            
        except Exception as e:
            logger.error(f"Failed to generate charts: {e}")
        
        return charts
    
    async def _generate_expense_trend_chart(self, df: pd.DataFrame) -> Optional[Dict]:
        """生成支出趋势图"""
        try:
            expense_df = df[df['type'] == 'expense']
            if expense_df.empty:
                return None
            
            # 简化实现，返回图表配置
            return {
                'chart_id': 'expense_trend',
                'title': '支出趋势',
                'type': 'line',
                'data': {
                    'labels': ['1月', '2月', '3月'],
                    'datasets': [{
                        'label': '支出金额',
                        'data': [1000, 1200, 900]
                    }]
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to generate expense trend chart: {e}")
            return None
    
    async def _generate_category_chart(self, df: pd.DataFrame) -> Optional[Dict]:
        """生成类别分布图"""
        try:
            expense_df = df[df['type'] == 'expense']
            if expense_df.empty:
                return None
            
            category_data = expense_df.groupby('category')['amount'].sum()
            
            return {
                'chart_id': 'category_distribution',
                'title': '支出类别分布',
                'type': 'pie',
                'data': {
                    'labels': category_data.index.tolist(),
                    'datasets': [{
                        'data': category_data.values.tolist()
                    }]
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to generate category chart: {e}")
            return None

# 全局智能分析引擎
intelligent_analytics_engine = None

def initialize_intelligent_analytics(config: Dict[str, Any]):
    """初始化智能分析引擎"""
    global intelligent_analytics_engine
    intelligent_analytics_engine = IntelligentAnalyticsEngine(config)
    return intelligent_analytics_engine

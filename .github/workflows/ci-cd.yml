# CI/CD 流水线配置
name: 代理记账系统 CI/CD

on:
  push:
    branches: [ main, develop, release/* ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'

jobs:
  # 代码质量检查
  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 设置Python环境
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: 安装Python依赖
        run: |
          python -m pip install --upgrade pip
          pip install flake8 black isort mypy bandit safety
          pip install -r backend/requirements.txt

      - name: Python代码格式检查
        run: |
          black --check backend/
          isort --check-only backend/

      - name: Python代码风格检查
        run: |
          flake8 backend/ --max-line-length=88 --extend-ignore=E203,W503

      - name: Python类型检查
        run: |
          mypy backend/ --ignore-missing-imports

      - name: Python安全检查
        run: |
          bandit -r backend/ -f json -o bandit-report.json
          safety check --json --output safety-report.json

      - name: 设置Node.js环境
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'frontend/package-lock.json'

      - name: 安装前端依赖
        working-directory: ./frontend
        run: npm ci

      - name: 前端代码检查
        working-directory: ./frontend
        run: |
          npm run lint
          npm run type-check

      - name: 上传代码质量报告
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: code-quality-reports
          path: |
            bandit-report.json
            safety-report.json

  # 单元测试
  unit-tests:
    name: 单元测试
    runs-on: ubuntu-latest
    needs: code-quality
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_dailijizhang
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Python环境
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: 安装Python依赖
        run: |
          python -m pip install --upgrade pip
          pip install -r backend/requirements.txt
          pip install pytest pytest-cov pytest-asyncio

      - name: 运行后端测试
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_dailijizhang
          REDIS_URL: redis://localhost:6379
          ENVIRONMENT: test
        run: |
          cd backend
          pytest tests/ -v --cov=. --cov-report=xml --cov-report=html

      - name: 设置Node.js环境
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'frontend/package-lock.json'

      - name: 安装前端依赖
        working-directory: ./frontend
        run: npm ci

      - name: 运行前端测试
        working-directory: ./frontend
        run: |
          npm run test:unit -- --coverage

      - name: 上传测试覆盖率
        uses: codecov/codecov-action@v3
        with:
          files: ./backend/coverage.xml,./frontend/coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

      - name: 上传测试报告
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-reports
          path: |
            backend/htmlcov/
            frontend/coverage/

  # 集成测试
  integration-tests:
    name: 集成测试
    runs-on: ubuntu-latest
    needs: unit-tests
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_dailijizhang
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Python环境
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: 安装依赖
        run: |
          python -m pip install --upgrade pip
          pip install -r backend/requirements.txt
          pip install pytest pytest-asyncio

      - name: 运行数据库迁移
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_dailijizhang
        run: |
          cd backend
          python manage.py migrate

      - name: 运行集成测试
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_dailijizhang
          REDIS_URL: redis://localhost:6379
          ENVIRONMENT: test
        run: |
          cd backend
          pytest tests/integration/ -v

  # 构建Docker镜像
  build-images:
    name: 构建Docker镜像
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    if: github.event_name != 'pull_request'
    
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 登录容器注册表
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 提取元数据
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: 构建并推送Docker镜像
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

      - name: 生成SBOM
        uses: anchore/sbom-action@v0
        with:
          image: ${{ steps.meta.outputs.tags }}
          format: spdx-json
          output-file: sbom.spdx.json

      - name: 安全扫描
        uses: anchore/scan-action@v3
        with:
          image: ${{ steps.meta.outputs.tags }}
          fail-build: false
          severity-cutoff: high

      - name: 上传扫描结果
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: results.sarif

  # 部署到测试环境
  deploy-staging:
    name: 部署到测试环境
    runs-on: ubuntu-latest
    needs: build-images
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: 配置Kubernetes
        run: |
          echo "${{ secrets.KUBE_CONFIG_STAGING }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig

      - name: 部署到Kubernetes
        run: |
          export KUBECONFIG=kubeconfig
          export IMAGE_TAG="${{ needs.build-images.outputs.image-tag }}"
          
          # 替换镜像标签
          envsubst < k8s/staging/deployment.yaml | kubectl apply -f -
          envsubst < k8s/staging/service.yaml | kubectl apply -f -
          envsubst < k8s/staging/ingress.yaml | kubectl apply -f -
          
          # 等待部署完成
          kubectl rollout status deployment/dailijizhang-app -n staging --timeout=300s

      - name: 运行健康检查
        run: |
          export KUBECONFIG=kubeconfig
          
          # 等待服务就绪
          kubectl wait --for=condition=ready pod -l app=dailijizhang-app -n staging --timeout=300s
          
          # 健康检查
          STAGING_URL="https://staging.dailijizhang.com"
          curl -f $STAGING_URL/health || exit 1

      - name: 运行端到端测试
        run: |
          cd e2e-tests
          npm ci
          npm run test:staging

  # 部署到生产环境
  deploy-production:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: build-images
    if: github.event_name == 'release'
    environment: production
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: 配置Kubernetes
        run: |
          echo "${{ secrets.KUBE_CONFIG_PRODUCTION }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig

      - name: 数据库备份
        run: |
          export KUBECONFIG=kubeconfig
          
          # 创建数据库备份
          kubectl create job --from=cronjob/db-backup db-backup-$(date +%Y%m%d%H%M%S) -n production
          
          # 等待备份完成
          kubectl wait --for=condition=complete job -l job-name=db-backup --timeout=600s -n production

      - name: 蓝绿部署
        run: |
          export KUBECONFIG=kubeconfig
          export IMAGE_TAG="${{ needs.build-images.outputs.image-tag }}"
          
          # 部署新版本到绿色环境
          envsubst < k8s/production/deployment-green.yaml | kubectl apply -f -
          
          # 等待绿色环境就绪
          kubectl rollout status deployment/dailijizhang-app-green -n production --timeout=600s
          
          # 健康检查
          kubectl wait --for=condition=ready pod -l app=dailijizhang-app,version=green -n production --timeout=300s
          
          # 切换流量到绿色环境
          kubectl patch service dailijizhang-service -n production -p '{"spec":{"selector":{"version":"green"}}}'
          
          # 等待一段时间确保稳定
          sleep 60
          
          # 删除蓝色环境
          kubectl delete deployment dailijizhang-app-blue -n production --ignore-not-found=true
          
          # 重命名绿色环境为蓝色环境
          kubectl patch deployment dailijizhang-app-green -n production -p '{"metadata":{"name":"dailijizhang-app-blue"},"spec":{"selector":{"matchLabels":{"version":"blue"}},"template":{"metadata":{"labels":{"version":"blue"}}}}}'

      - name: 生产环境验证
        run: |
          # 健康检查
          PRODUCTION_URL="https://dailijizhang.com"
          curl -f $PRODUCTION_URL/health || exit 1
          
          # 关键功能测试
          curl -f $PRODUCTION_URL/api/v1/health || exit 1

      - name: 通知部署结果
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          fields: repo,message,commit,author,action,eventName,ref,workflow

  # 性能测试
  performance-tests:
    name: 性能测试
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: github.ref == 'refs/heads/develop'
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: 安装K6
        run: |
          sudo gpg -k
          sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6

      - name: 运行性能测试
        run: |
          cd performance-tests
          k6 run --out json=results.json load-test.js

      - name: 分析性能结果
        run: |
          cd performance-tests
          python analyze-results.py results.json > performance-report.txt

      - name: 上传性能报告
        uses: actions/upload-artifact@v3
        with:
          name: performance-report
          path: performance-tests/performance-report.txt

  # 安全扫描
  security-scan:
    name: 安全扫描
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 运行Trivy漏洞扫描
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: 上传Trivy扫描结果
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: 运行CodeQL分析
        uses: github/codeql-action/init@v2
        with:
          languages: python, javascript

      - name: 自动构建
        uses: github/codeql-action/autobuild@v2

      - name: 执行CodeQL分析
        uses: github/codeql-action/analyze@v2

  # 清理
  cleanup:
    name: 清理资源
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    
    steps:
      - name: 清理旧的Docker镜像
        run: |
          # 保留最近10个版本的镜像
          echo "清理旧的Docker镜像..."
          
      - name: 清理测试数据
        run: |
          echo "清理测试环境数据..."
